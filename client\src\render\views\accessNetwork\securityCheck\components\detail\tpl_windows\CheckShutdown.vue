<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.Shutdown.h_3_rs") }}
          </p>
          <div class="pc-info">
            <img :src="cpImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="optional-item margin-style">
                {{ $t("check.Shutdown.h_9") }}
                <span>{{ computerInfo.AllowTime }} {{ $t('check.Shutdown.h_18') }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.Shutdown.h_7") }}
                <span>{{ computerInfo.RunTime }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.Shutdown.h_8") }}
                <span>{{ computerInfo.TimeOut }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
export default {
  name: 'CheckShutdown',
  components: {
    checkResult,
    howToFix
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      imgSrc: '',
      fixData: {},
      cpImgSrc: require('@/render/assets/CheckComputerName.gif')
    }
  },
  computed: {
    computerInfo() {
      const CheckType = _.get(this.checkData, 'CheckResult.CheckType')
      const policyData = _.get(this.checkData, 'CheckType.Option')
      return {
        RunTime: CheckType.RunTime,
        TimeOut: CheckType.TimeOut,
        AllowTime: policyData.Cycle
      }
    },
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkItemData() {
      return this.checkData
    }
  },
  mounted() {
    this.fixData = {
      modelTitle: this.$t('check.OSVersion.h_3_rs'),
      fixSteps: [this.$t('check.Shutdown.h_11_rs')]
    }
  }
}
</script>
