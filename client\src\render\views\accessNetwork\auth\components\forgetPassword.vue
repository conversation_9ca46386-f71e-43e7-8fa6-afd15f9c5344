<template>
  <div class="forget-password-page">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="80px" label-position="top">
      <el-form-item :label="$t('forgetpassword.userName')" prop="User">
        <el-input
          v-model="ruleForm.User"
          maxlength="50"
          :placeholder="$t('forgetpassword.inputUserName')"
        />
      </el-form-item>
      <el-form-item :label="$t('forgetpassword.sendCodeText')" prop="codeType">
        <el-select v-model="ruleForm.codeType" style="width: 242px">
          <el-option
            v-for="item in ruleForm.codeTypeArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div
          v-if="!smsCodeIsDisabled"
          class="sms-wrapper public-line-medium-btn inline"
          @click="sendCode"
        >
          {{ $t('auth.phoneCode') }}
        </div>

        <div
          v-else
          class="sms-wrapper public-line-medium-btn sms-disabled inline"
        >
          {{ getSmsCodeButtonMsg }}
        </div>
      </el-form-item>
      <label style="margin-left:8px;line-height: 18px;color: rgb(104,110,132)">{{ messagecode }}</label>
      <el-form-item :label="$t('forgetpassword.varifycode')" prop="verifycode">
        <el-input
          v-model="ruleForm.verifycode"
          type="text"
          maxlength="6"
          :placeholder="$t('forgetpassword.inputvarifycode')"
        />
      </el-form-item>

      <el-form-item :label="$t('forgetpassword.Pass')" prop="newpassword">
        <el-input
          v-model="ruleForm.newpassword"
          type="password"
          :show-password="true"
          maxlength="50"
          :placeholder="$t('forgetpassword.newpassword')"
        />
      </el-form-item>
      <el-form-item :label="$t('forgetpassword.checkPass')" prop="newpassword2">
        <el-input
          v-model="ruleForm.newpassword2"
          type="password"
          :show-password="true"
          maxlength="50"
          :placeholder="$t('forgetpassword.confirmpassword')"
        />
      </el-form-item>
      <el-form-item style="text-align: right;margin-top: 18px">
        <p class="public-line-medium-btn sms-wrapper" style="display:inline-block" @click="cancelForm()">{{ $t('forgetpassword.cancelsub') }}</p>
        <p class="public-medium-btn sms-wrapper" style="display:inline-block;margin-left: 8px;" @click="submitForm('ruleForm')">
          {{ $t('forgetpassword.submitsub') }}
        </p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'
import JSEncrypt from 'jsencrypt'

export default {
  props: {
    userName: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateUserName = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('forgetpassword.inputUserName')))
      } else {
        if (!regular.rules.accountTitle.test(value)) {
          callback(new Error(this.$t('forgetpassword.userNameTips')))
        }
        callback()
      }
    }
    const validateVerifyCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('forgetpassword.inputvarifycode')))
      } else {
        const patrn = /^\d{6}$/
        if (!patrn.test(value)) {
          callback(new Error(this.$t('forgetpassword.varifyCodeTip')))
        }
        callback()
      }
    }
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('forgetpassword.newpassword')))
      } else {
        if (this.ruleForm.newpassword2 !== '') {
          this.$refs.ruleForm.validateField('newpassword2')
        }
        const { ChangePassword } = this.computeServeEntiretyConfig
        const {
          PassWordType,
          PassWordsetType,
          PassWordLength,
          errPassword,
          dbUserName,
          dbStr
        } = ChangePassword
        const errChar = errPassword && errPassword.split('||||')
        switch (PassWordType) {
          // 默认模式
          case '0':
            if (
              !regular.lenRange(value, 2, 50) ||
              regular.hasChars(value, [',', '"']) ||
              regular.flEmpty(value)
            ) {
              callback(new Error(this.$t('forgetpassword.defaultErrTip')))
            }
            break
          // 复杂密码模式
          case '1':
            // 长度校验
            if (value.length < PassWordLength) {
              callback(
                new Error(
                  this.$t('forgetpassword.lengthTip', { PassWordLength })
                )
              )
            }
            // 不包含完整用户名
            if (dbUserName === '1' && this.username) {
              if (regular.hasChars(value, [this.username])) {
                callback(new Error(this.$t('forgetpassword.excludeNameTip')))
              }
            }
            // 不使用重复的数字和字母
            if (dbStr === '1') {
              if (regular.haveRepeat(value)) {
                callback(new Error(this.$t('forgetpassword.norepeateTip')))
              }
            }
            // 不可等于设置字符
            if (errChar && errChar.length) {
              if (errChar.indexOf(value) > -1) {
                callback(
                  new Error(this.$t('forgetpassword.noeqTip', { value }))
                )
              }
            }
            // 密码字符组成种类
            switch (PassWordsetType) {
              // 数字+字母
              case '1':
                if (!regular.numAndChart(value)) {
                  callback(new Error(this.$t('forgetpassword.numAndChat')))
                }
                break
              case '2':
                // 必须由大写字母、小写字母、数字、特殊字符中三者组合
                if (!regular.threeTypePass(value)) {
                  callback(new Error(this.$t('forgetpassword.complexPassTip')))
                }
            }
        }
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('forgetpassword.confirmpassword')))
      } else {
        if (value !== this.ruleForm.newpassword && this.ruleForm.newpassword) {
          callback(new Error(this.$t('forgetpassword.comfirPassErrTip')))
        }
        callback()
      }
    }
    const codeTypeAll = [
      { label: this.$t('forgetpassword.phone'), value: 'phone' },
      { label: this.$t('forgetpassword.email'), value: 'email' }
    ]
    return {
      maxGetSmsCodeInterval: 60,
      timer: null,
      nowTime: 0,
      messagecode: '',
      ruleForm: {
        User: this.userName,
        codeTypeArr: codeTypeAll,
        verifycode: '',
        codeType: 'phone',
        newpassword: '',
        newpassword2: ''
      },
      rules: {
        User: [
          {
            required: true,
            validator: validateUserName,
            trigger: 'blur'
          }
        ],
        codeType: [
          {
            required: true
          }
        ],
        verifycode: [
          {
            required: true,
            validator: validateVerifyCode,
            trigger: 'blur'
          }
        ],
        newpassword: [
          {
            required: true,
            validator: validateNewPassword,
            trigger: 'blur'
          }
        ],
        newpassword2: [
          {
            required: true,
            validator: validateConfirmPassword,
            trigger: 'blur'
          }
        ]
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'authInfo',
      'clientInfo'
    ]),
    // 验证码按钮文字
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    },
    // 是否禁用获取验证码按钮
    smsCodeIsDisabled() {
      if (this.getSmsCodeLoading) {
        return true
      }
      return this.nowTime > 0
    },
    getdeviceid() {
      return (
        _.get(this.clientInfo, 'detail.DeviceID') ||
        _.get(this.clientInfo, 'basic.AgentID')
      )
    }
  },
  mounted() {},
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          if (this.loading) {
            return
          }
          this.loading = true
          const crypt = new JSEncrypt()
          crypt.setPublicKey(this.computeServeEntiretyConfig.pubKey)

          const data = {
            deviceid: this.getdeviceid,
            user: this.ruleForm.User,
            codeType: this.ruleForm.codeType,
            check_code: this.ruleForm.verifycode,
            newpassword: crypt.encrypt(this.ruleForm.newpassword),
            newpassword2: crypt.encrypt(this.ruleForm.newpassword)
          }
          try {
            const result = await proxyApi.forgetPass(data)
            this.loading = false
            if (result.errcode === '0') {
              this.$message.success(result.errmsg)
              this.$emit('closeDraw', false)
            }
          } catch (error) {
            console.log(error)
          }
        }
      })
    },
    cancelForm() {
      this.$emit('closeDraw', false)
    },
    // 发送验证码
    async sendCode() {
      const username = this.ruleForm.User
      if (!regular.rules.accountTitle.test(username)) {
        this.$message.error(this.$t('forgetpassword.userNameTips'))
        return false
      }
      try {
        const data = {
          deviceid: this.getdeviceid,
          user: username,
          codeType: this.ruleForm.codeType
        }
        this.getSmsCodeLoading = true
        const res = await proxyApi.sendUserCode(data)
        this.getSmsCodeLoading = false
        this.messagecode = ''
        if (parseInt(res.errcode) === 0) {
          if (res.data.code_message && res.data.code_message !== undefined) {
            this.messagecode = this.$t('forgetpassword.messageCodeText') + res.data.code_message
          }
          this.$message.success(res.errmsg)
          this.countDown(true)
        }
      } catch (error) {
        console.log(error)
      }
    },
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.forget-password-page {
  padding: 8px 32px 32px 32px;
}
.inline {
  display: inline-block;
}
.small-select {
  width: 175px;
}
.sms-wrapper {
  width: 100px;
  height: 40px;
  line-height: 38px;
}

.sms-disabled {
  border-color: $line-color;
  background: $line-color;
  color: $disabled-color;
  cursor: not-allowed;
}

.el-form-item {
  margin-bottom: 5px;
}

</style>
