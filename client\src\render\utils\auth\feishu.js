/* eslint-disable */
!(function(e, t) {
  typeof exports === 'object' && typeof module === 'object' ? module.exports = t() : typeof define === 'function' && define.amd ? define([], t) : typeof exports === 'object' ? exports.LarkSSOSDKWebQRCode = t() : e.LarkSSOSDKWebQRCode = t()
}(this, (function() {
  return (function(e) {
    var t = {}; function o(n) {
      if (t[n]) {
        return t[n].exports
      } var r = t[n] = { i: n, l: !1, exports: {}}; return e[n].call(r.exports, r, r.exports, o), r.l = !0, r.exports
    } return o.m = e, o.c = t, o.d = function(e, t, n) {
      o.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: n })
    }, o.r = function(e) {
      typeof Symbol !== 'undefined' && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }), Object.defineProperty(e, '__esModule', { value: !0 })
    }, o.t = function(e, t) {
      if (1 & t && (e = o(e)), 8 & t) {
        return e
      } if (4 & t && typeof e === 'object' && e && e.__esModule) {
        return e
      } var n = Object.create(null); if (o.r(n), Object.defineProperty(n, 'default', { enumerable: !0, value: e }), 2 & t && typeof e !== 'string') {
        for (var r in e) {
          o.d(n, r, function(t) {
            return e[t]
          }.bind(null, r))
        }
      } return n
    }, o.n = function(e) {
      var t = e && e.__esModule ? function() {
        return e.default
      } : function() {
        return e
      }; return o.d(t, 'a', t), t
    }, o.o = function(e, t) {
      return Object.prototype.hasOwnProperty.call(e, t)
    }, o.p = '', o(o.s = 0)
  }([function(e, t) {
    window.QRLogin = function(e) {
      var t, o, n, r, i = e.id, u = e.goto, c = e.width, f = e.height, s = e.style, d = /^https:\/\/(\w*\.)?(feishu(-boe|-pre)?\.cn|larksuite(-boe|-pre)?\.com)/, a = ''

      if (!d.test(u) && !e.mode) {
        throw new Error('The param "goto" is not valid.')
      }
      o = e.mode ? e.manageIp : u.match(d)[0]
      return o, n = function(e) {
        //   return o = e.manageIp, n = function(e) {
        return d.test(e)
      }, a = (/suite\/passport/.test(u) && !e.mode) ? o + '/suite/passport/sso/qr?goto=' + encodeURIComponent(u) : o + '/accounts/auth_login/qr?goto=' + encodeURIComponent(u), (r = document.createElement('iframe')).setAttribute('width', c), r.setAttribute('height', f), r.setAttribute('style', s), r.setAttribute('src', a), (t = document.getElementById(i)) === null || void 0 === t || t.appendChild(r), { matchOrigin: n }
    }
  }]))
})))
