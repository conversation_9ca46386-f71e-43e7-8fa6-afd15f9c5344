
/*
 * @Author: <EMAIL>
 * @Date: 2022-09-05 14:10
 * @LastEditors: gening <EMAIL>
 * @LastEditTime: 2023-09-06 16:12:56
 * @Description: 场景计算的辅助函数
 */

import store from '@/render/store/index'
import proxyApi from '@/service/api/proxyApi'
import { i18n } from '@/render/lang'
import Vue from 'vue'
import _ from 'lodash'

const scene = {

  // 获取设备场景
  /**
   *
   * @param {*} userType 手动切换需要传。
   * @param {*} showGuestErr 获取不到来宾场景是否展示错误提示
   * @returns
   */
  async getDeviceScene(userType, showGuestErr = true) {
    // 不在线的情况下，不请求
    // 1. 不在线时
    if (!_.get(store.state.clientInfo, 'online', false)) {
      return false
    }

    if (_.isEmpty(_.get(store.state.clientInfo, 'detail.DeviceID'), '')) {
      return false
    }

    const params = {
      deviceId: _.get(store.state.clientInfo, 'detail.DeviceID')
    }

    if (userType) {
      params['userType'] = userType
    }

    const ret = await proxyApi.getDeviceScene(params)
    const isFail = parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data', '')) || !_.isObject(_.get(ret, 'data', ''))
    store.commit('setServeEntiretyConfig', {
      ...store.state.serveEntiretyConfig,
      ... {
        scene: isFail ? {} : _.get(ret, 'data', {})
      }
    })
    if (isFail) {
      const err = ret.errmsg || i18n.t('getDeviceSceneFail')

      // 如果当前确实是离线,这不弹出提示，产品要求已入网获取不到来宾场景不提示错误
      if (_.get(store.state.clientInfo, 'online', false) && (parseInt(_.get(ret, 'errcode', 200)) !== 21103018 || showGuestErr)) {
        Vue.prototype.$msg({
          type: 'error',
          message: err
        })
      }
      return {}
    }
    await this.getSceneConfig()
    return _.get(ret, 'data')
  },

  // 获取详细的场景配置信息(控件安装设置等)
  async getSceneConfig() {
    // 先判断状态里面的场景id是否发生变化
    if (parseInt(_.get(store.state, 'serveEntiretyConfig.scene.SceneID', 0)) === 0) {
      return
    }

    const apiParam = {
      sceneId: _.get(store.state, 'serveEntiretyConfig.scene.SceneID', '')
    }
    const ret = await proxyApi.getSceneInfo(apiParam)
    if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data', '')) || !_.isObject(_.get(ret, 'data', ''))) {
      return false
    }

    store.commit('setServeEntiretyConfig', {
      ...store.state.serveEntiretyConfig,
      ... {
        sceneConfig: _.get(ret, 'data', {})
      }
    })
  }
}

export default scene

