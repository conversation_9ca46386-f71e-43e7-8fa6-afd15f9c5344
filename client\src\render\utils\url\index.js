/*
 * @Author: <EMAIL>
 * @Date: 2021-11-08 14:31:50
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-10 14:15:12
 * @Description: url辅助函数
 */
import store from '@/render/store'
import regular from '@/render/utils/regular'
import _ from 'lodash'

const urlUtils = {

  /**
   * 获取基础http链接
   * forceHttp 表示就算当前asm采用的是https入网，仍然返回http的地址(某些客户端例如linux，不支持https的二维码图片)
   * @returns
   */
  getBaseIPPort(forceHttp = false, isdomain = false) {
    const isHttps = forceHttp ? false : urlUtils.isServerHttps()
    const dot1xManagerIP = _.get(store.getters.serveEntiretyConfig, 'client.server.MANAGER_IP', window.location.hostname)
    const managerIP = _.get(store.getters.serveEntiretyConfig, 'server.SERVERIP', dot1xManagerIP)
    let serverIP = _.get(store.getters.clientInfo, isdomain ? 'basic.ServerIPOriginal' : 'basic.ServerIP', managerIP)
    // ServerIPOriginal获取失败、开发环境还是使用服务器管理口IP
    if (_.isEmpty(serverIP) || process.env.NODE_ENV === 'development') {
      serverIP = managerIP
    }
    // ipv6环境使用ipv6地址
    if (regular.isIPV6(serverIP)) {
      serverIP = `[${serverIP}]`
    }

    const portArr = []
    if (isHttps) {
      portArr.push(
        _.get(store.getters.clientInfo, 'basic.WebPort'),
        _.get(store.getters.serveEntiretyConfig, 'server.https_Port'),
        _.get(store.getters.serveEntiretyConfig, 'client.server.https_Port'),
        '443'
      )
    } else {
      portArr.push(
        _.get(store.getters.clientInfo, 'basic.WebPort'),
        _.get(store.getters.serveEntiretyConfig, 'server.http_Port'),
        _.get(store.getters.serveEntiretyConfig, 'client.server.http_Port'),
        '80'
      )
    }

    const port = _.find(portArr, (v) => {
      return !_.isEmpty(v)
    })
    if (!isHttps) {
      if (parseInt(port) !== 80) {
        serverIP += ':' + port
      }
    } else {
      if (parseInt(port) !== 443) {
        serverIP += ':' + port
      }
    }
    return (isHttps ? 'https:' : 'http:') + '//' + serverIP
  },
  /**
   * 服务器是否https
   * @returns
   */
  isServerHttps() {
    let serverProtocol = _.get(store.getters.serveEntiretyConfig, 'server.protocol', '')
    serverProtocol = serverProtocol.replace(':', '')
    const protocol = _.get(store.getters.clientInfo, 'basic.WebProtocol', serverProtocol)
    return protocol === 'https'
  },
  // 把参数对象转化为url字符串拼接参数
  /**
   * {a:1,b:2} => ?a=1&b=2
   * @param {*} params
   * @returns
   */
  urlParamsStringfy(url, params) {
    if (!_.isObject(params)) {
      return url
    }
    Object.keys(params).forEach(key => {
      if (!_.isUndefined(params[key])) {
        const link = url.indexOf('?') !== -1 ? '&' : '?'
        url += `${link}${key}=${decodeURIComponent(params[key])}`
      }
    })
    return url
  }
}

export default urlUtils
