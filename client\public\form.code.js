// 用http://jshaman.com/工具压缩代码为form.js
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        /*AMD. Register as an anonymous module.
        *define([], factory); */
        define([], factory());
    } else if (typeof module === 'object' && module.exports) {
        /*Node. Does not work with strict CommonJS, but
        // only CommonJS-like environments that support module.exports,
        // like Node.*/
        module.exports = factory();

    } else {
        /*Browser globals (root is window)*/
        root['FormFill'] = factory();
    }
}(this, function () {

    /*Just return a value to define the module export.*/
    var FormFill = {
        State: 0, // 0表示未执行，1表示已执行
        Interval: 0, // 定时器
        inputEles: null,
    };

    // 编码
    function myInfogoBase64() {
        // private property
        _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

        // public method for decoding
        this.decode = function (input) {
            var output = "";
            var chr1, chr2, chr3;
            var enc1, enc2, enc3, enc4;
            var i = 0;
            input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
            while (i < input.length) {
                enc1 = _keyStr.indexOf(input.charAt(i++));
                enc2 = _keyStr.indexOf(input.charAt(i++));
                enc3 = _keyStr.indexOf(input.charAt(i++));
                enc4 = _keyStr.indexOf(input.charAt(i++));
                chr1 = (enc1 << 2) | (enc2 >> 4);
                chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                chr3 = ((enc3 & 3) << 6) | enc4;
                output = output + String.fromCharCode(chr1);
                if (enc3 != 64) {
                    output = output + String.fromCharCode(chr2);
                }
                if (enc4 != 64) {
                    output = output + String.fromCharCode(chr3);
                }
            }
            output = _utf8_decode(output);
            return output;
        }
        // private method for UTF-8 decoding
        _utf8_decode = function (utftext) {
            var string = "";
            var i = 0;
            var c = c1 = c2 = 0;
            while (i < utftext.length) {
                c = utftext.charCodeAt(i);
                if (c < 128) {
                    string += String.fromCharCode(c);
                    i++;
                } else if ((c > 191) && (c < 224)) {
                    c2 = utftext.charCodeAt(i + 1);
                    string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                    i += 2;
                } else {
                    c2 = utftext.charCodeAt(i + 1);
                    c3 = utftext.charCodeAt(i + 2);
                    string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                    i += 3;
                }
            }
            return string;
        }
    }

    // 自定义base64加密解密
    function Base64Decode(str) {
        this.myInfogoBase64 = new myInfogoBase64();
        var s = str.replace(/,/g, '+')
        s = s.replace(/:/g, '=')
        s = s.replace(/[.]/g, '\/')
        s = this.myInfogoBase64.decode(s)
        return s
    }

    /*加载表单代填*/
    var loadForm = function (settings) {
        if (FormFill.State === 1) {
            return;
        }
        FormFill.inputEles = document.getElementsByTagName('input');
        if (settings.APPSubType === 'custom_fill') {
            var accountArr = (settings.AccountInputType === 'system_try') ? settings.autoAccountInputArr : [settings.AccountInputValue];
            var passwdArr = (settings.PwdInputType === 'system_try') ? settings.autoPwdInputArr : [settings.PwdInputValue];
            fillInput(accountArr, Base64Decode(settings.AccountDataValue));
            fillInput(passwdArr, Base64Decode(settings.PwdDataValue));
        } else {
            fillInput(settings.autoAccountInputArr, Base64Decode(settings.AccountDataValue));
            fillInput(settings.autoPwdInputArr, Base64Decode(settings.PwdDataValue));
        }
        var result = true;
        if (settings.APPSubType === 'custom_fill') {
            // 自定义代填自动提交
            var submitArr = (settings.SubmitInputType === 'system_try') ? settings.autoSubmitInputArr : [settings.SubmitInputValue];
            setTimeout(function () {
              result = autoSubmit(submitArr, 0);
            }, 3000);
        } else if (settings.APPSubType === 'auto_fill' && settings.AutoLogin === '1') {
            // 智能代填自动提交
            setTimeout(function () {
              result = autoSubmit(settings.autoSubmitInputArr, 1);
            }, 3000);
            
        }
        if (FormFill.inputEles.length >= 2) {
            finishForm();
        }
    }

    // 完成表单
    var finishForm = function () {
        addCookie('ZTP_FormInit', 1, 3600 * 24 * 30);
        FormFill.State = 1;
        clearInterval(FormFill.Interval);
    }

    // 搜索匹配元素
    var searchElement = function (search, ele) {
        if (typeof search !== 'string') {
            return false;
        }
        search = search.toLowerCase();
        let id = ele.id;
        let name = ele.name;
        let type = ele.type;
        if (typeof id !== 'undefined' && id.toLowerCase().indexOf(search) !== -1 ||
            typeof name !== 'undefined' && name.toLowerCase().indexOf(search) !== -1 ||
            typeof type !== 'undefined' && type.toLowerCase().indexOf(search) !== -1 ||
            typeof placeholder !== 'undefined' && placeholder.toLowerCase().indexOf(search) !== -1) {
            return ele;
        }
        return false;
    }

    // 填充输入框
    var fillInput = function (inputArr, val) {
        if (inputArr.length <= 0 || FormFill.inputEles.length <= 0) {
            return;
        }
        for (let i in inputArr) {
            for (let j in FormFill.inputEles) {
                let ele = searchElement(inputArr[i], FormFill.inputEles[j]);
                if (ele) {
                    ele.value = val;
                    try {
                        ele.dispatchEvent(new Event('input', { bubbles: true })); // 触发 input 事件/ 触发 input 事件
                    } catch (error) {
                        console.log(error, 8888);
                    }
                }
            }
        }
    }
    var submitClick = function(ele) {
        ele.click();
        const clickEvent = new Event('click');
        ele.dispatchEvent(clickEvent);
    }
    // 自动提交
    var autoSubmit = function (inputArr, isAuto) {
        if (inputArr.length <= 0) {
            return false;
        }
        let find = false;
        let buttonEles = document.getElementsByTagName('button');
        if (FormFill.inputEles.length > 0) {
            for (let i in inputArr) {
                for (let j in FormFill.inputEles) {
                    let ele = searchElement(inputArr[i], FormFill.inputEles[j]);
                    if (ele) {
                        find = true;
                        ele.click();
                    }
                }
            }
        }
        for (let i in inputArr) {
            for (let j in buttonEles) {
                let ele = searchElement(inputArr[i], buttonEles[j]);
                if (ele) {
                    find = true;
                    setTimeout(() => {
                    }, 10000);
                    ele.click();
                }
            }
            let dom = document.getElementsByClassName(inputArr[i])[0]
            let dom2 = document.getElementById(inputArr[i])
            if (dom) {
                find = true;
                dom.click();
            }
            if (dom2) {
                find = true;
                dom2.click();
            }
        }
        if (isAuto === 1 && buttonEles.length > 0) {
            ele = buttonEles[0]
            find = true;
            ele.click();
            // const clickEvent = new Event('click');
            // ele.dispatchEvent(clickEvent);
        }
        return find;
    }

    /*加载表单代填，添加load事件*/
    FormFill.init = function (settings) {
        if (settings.APPType !== '301') {
            finishForm();
            return;
        }
        page_ready(function () {
            loadForm(settings);
        });
        FormFill.Interval = window.setInterval(function () {
            loadForm(settings)
        }, 1000);
    };

    // 组装参数
    function params(json) {
        let paramArr = []
        for (let p in json) {
            paramArr.push(p + '=' + json[p])
        }
        return paramArr.join('&')
    };

    // 获取cookie值
    function getCookie(objname) {//获取指定名称的cookie的值
        var arrstr = document.cookie.split("; ");
        for (var i = 0; i < arrstr.length; i++) {
            var temp = arrstr[i].split("=");
            if (temp[0] == objname) return unescape(temp[1]);
        }
        return '';
    }

    //添加cookie
    function addCookie(name, value, seconds) {
        var expire = "";
        if (seconds != null) {
            expire = new Date((new Date()).getTime() + seconds * 1000);
            expire = ";  expires=" + expire.toGMTString() + ";";
        }
        //设置cookie的名称、值、失效时间
        document.cookie = name + "=" + value + expire + "; path=/";
    }

    // 页面准备完成
    function page_ready(fn) {
        if (document.addEventListener) {		//标准浏览器
            document.addEventListener('DOMContentLoaded', function () {
                //注销时间，避免重复触发
                document.removeEventListener('DOMContentLoaded', arguments.callee, false);
                fn();		//运行函数
            }, false);
        } else if (document.attachEvent) {		//IE浏览器
            document.attachEvent('onreadystatechange', function () {
                if (document.readyState == 'complete') {
                    document.detachEvent('onreadystatechange', arguments.callee);
                    fn();		//函数运行
                }
            });
        }
    }

    // 请求接口
    function ajax_request(url, data, callback) {
        //创建异步对象
        var xhr = null
        if (window.XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else if (window.ActiveXObject) {//IE6及以下
            xhr = new ActiveXObject('Microsoft.XMLHTTP');
        }
        //判断data是否为空
        if (data) {
            url = url + '?' + params(data);
        }
        //设置请求行
        xhr.open('get', url);
        //设置请求头(get可以省略)
        //注册回调函数
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 4 && xhr.status == 200) {
                //调用传递的回调函数
                callback(xhr.responseText);
            }
        }
        //发送请求主体
        xhr.send(null);
    };

    FormFill.config = [];

    FormFill.run = function () {
        if (getCookie('ZTP_FormInit') === '1') {
            return true;
        }
        ajax_request('/access/gateway/getForm', {url: encodeURIComponent(window.location.href)}, function (response) {
            var res = JSON.parse(response);
            if (res.errcode === '0' | false) {
                FormFill.init(res.data);
            }
        });
    }
    FormFill.run();
    return FormFill;
}));
