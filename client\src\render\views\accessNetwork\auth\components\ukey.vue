<template>
  <!-- ukey登录 -->
  <div class="ukey-auth-page">
    <el-form ref="ruleForm" :model="authData" :rules="rules" label-width="0px" class="rule-form">
      <el-form-item v-if="show.type === 1" label="" prop="cert" class="select-form-box">
        <i class="iconfont slect-icon icon-zhengshu" />
        <el-select id="ui-accessNetwork-ukey-input-ukeytype" v-model="authData.cert" :placeholder="$t('auth.select')">
          <el-option v-for="item in certOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="select-ukey">
          <span>{{ certInfo['Subject'] ? certInfo['Subject'] : '' }}</span>
          <span v-show="show.needRightfulUKey" class="not-key-tip">{{ this.$t('auth.needRightfulUKey') }}</span>
        </div>
      </el-form-item>

      <el-form-item v-if="show.type === 2" label="" prop="uKeyPassword">
        <el-input v-model="authData.uKeyPassword" type="password" maxlength="50" :placeholder="$t('auth.needPassword')">
          <i slot="prefix" class="iconfont icon-mima" /></el-input>
      </el-form-item>

      <el-form-item v-if="show.type === 3" label="" prop="uKeyPin">
        <el-input v-model="authData.uKeyPin" maxlength="50" :placeholder="$t('auth.needPin')"><i
          slot="prefix"
          class="iconfont icon-mima"
        />
        </el-input>
      </el-form-item>

      <networkList ref="networkList" />

      <div class="checkbox-wrapper">
        <el-checkbox v-model="authData.uKeyAutoAuth" @change="changeAutoAuth">
          {{ $t('auth.remenberCount') }}
        </el-checkbox>
        <el-checkbox v-model="authData.uKeyAutoLogin" @change="changeAutoLogin">
          {{ $t('auth.autoLogin') }}
        </el-checkbox>
      </div>
      <el-form-item>
        <p class="public-btn" :disabed="forbidSubmit" @click="submitForm('ruleForm')">{{ $t('auth.submit') }}</p>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import authMixin from '../mixins'
import _ from 'lodash'
import opform from '@/render/utils/opform'
import { Base64Encode, sleep } from '@/render/utils/global'
import agentApi from '@/service/api/agentApi'
import networkList from './networkList'
import { mapGetters } from 'vuex'
import UKey from '@/render/utils/auth/uKey'
import os_browser_info from '@/render/utils/os_browser_info'
import { EventBus } from '@/render/eventBus'

export default {
  name: 'UkeyAuth',
  components: {
    networkList
  },
  mixins: [authMixin],
  props: {
    serverInfo: {
      type: Object,
      default: function() {
        return {}
      }
    },
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    const validateCert = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('auth.selectCert')))
      }
      callback()
    }
    return {
      rules: {
        uKeyPassword: [{ required: false, message: this.$t('auth.needPassword'), trigger: 'blur' }],
        uKeyPin: [{ required: false, message: this.$t('auth.needPin'), trigger: 'blur' }],
        cert: [{ validator: validateCert, trigger: 'change' }]
      },
      show: {
        type: 0,
        needRightfulUKey: true// 需要UKey
      },
      defaultCert: '', // 上次选择证书
      certOptions: [], // 证书可选项
      certList: '', // 可选择证书原始信息（json）
      forbidSubmit: false,
      timer: null,
      uKeyAuthInstance: new UKey(),
      destroyedStatus: false,
      needDefaultUkeyTypeStart: false // 获取certList完成后回调
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeServeEntiretyConfig']),
    certInfo() {
      let cert = _.find(this.certOptions, (o) => {
        return o['value'] === this.authData.cert
      })
      if (_.isUndefined(cert)) {
        cert = {
          Subject: ''
        }
      }
      return cert
    }
  },
  watch: {
    // 中英文切换时重新校验失败条目
    '$i18n.locale': function() {
      this.$refs['ruleForm'].fields.forEach(item => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    }
  },
  mounted() {
    this.initUKey()
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
  },
  // 组件卸载,如果卸载后,还有异步请求在执行,则取消请求
  destroyed() {
    this.destroyedStatus = true
  },
  methods: {
    async initUKey() {
      if (!this.checkOsType()) {
        this.forbidSubmit = true
        return
      }
      // 只是根证书和LDAP证书认证才出现证书选择
      const uKeyType = parseInt(_.get(this.computeServeEntiretyConfig, 'UKey.UKeyType', -1))
      if (uKeyType === 0 || uKeyType === 1 || uKeyType === 2) {
        // 加载证书认证下拉项
        this.getCurrentCert()
        this.show.type = 1
      } else if (uKeyType === 3) {
        // 如果是序列号证书认证，则显示忘记密码输入框 todo
        this.show.type = 2
      } else if (uKeyType === 4) {
        this.show.type = 3
      }

      // 是否保存用户名密码
      const serverInfo = this.serverInfo
      if ((this.authData.uKeyAutoAuth === '' || this.authData.uKeyAutoAuth === undefined) && serverInfo.clientSaveName) {
        this.authData.uKeyAutoAuth = true
      }
    },
    /**
     * 检查系统是否支持UKey
     */
    checkOsType() {
      const osType = os_browser_info.os_type || ''
      if (osType === 'mac') {
        this.$message.warning(this.$t('auth.macNoUKey'))
        return false
      }
      if (osType === 'linux') {
        this.$message.warning(this.$t('auth.linuNoUKey'))
        return false
      }
      return true
    },
    /**
     * 获取当前插入ukey的证书
     */
    async getCurrentCert() {
      if (this.destroyedStatus) {
        return
      }

      let certList = []
      try {
        certList = await this.uKeyAuthInstance.getCert()
      } catch (error) {
        console.log(error)
      }

      if (_.isArray(certList) && !_.isEmpty(certList)) {
        this.certOptions = certList

        // 已加载完证书，根据缓存的序列号来判断是否能自动认证
        const defaultCert = _.get(this.authData, 'uKeyCertDefault', '')
        if (_.isString(defaultCert) && defaultCert !== '') {
          const idx = certList.findIndex(item => item.value === defaultCert)
          if (idx !== -1) {
            // 下个周期赋值，防止赋值前点击下拉，导致select展示成value而不是label
            this.$nextTick(() => {
              this.authData.cert = defaultCert
            })
          }
        }
        this.forbidSubmit = false
        this.show.needRightfulUKey = false
      } else {
        // 没有找到证书列表，就sleep后继续查询
        await sleep(2000)
        this.forbidSubmit = true
        this.getCurrentCert()
      }
    },
    /**
     * 触发校验，校验通过则调用实际认证
     * @params {String} autoLogin 是否是自动登录
     */
    submitForm(formName = 'ruleForm', autoLogin = false) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          this.auth()
        } else {
          // 如果是自动登录的情况下，这里校验失败了，可能是还没有加载完证书，这里延迟几秒等证书循环下，再去请求一次
          if (autoLogin) {
            await sleep(6000)
            this.submitForm()
            return false
          }
          EventBus.$emit('client:show')
        }
      })
    },
    /**
     * 实际认证
     */
    async auth() {
      this.$emit('loading', true)
      let ret = { result: false }
      try {
        // 网卡列表
        const accessNetwork = this.$refs.networkList.accessNetwork
        // 是否是无线网卡
        const isWireLess = this.$refs.networkList.isWireLess
        const certInfo = this.certInfo
        ret = await this.uKeyAuthInstance.authParamsPrepare(this.authData, accessNetwork, isWireLess, certInfo)
      } catch (error) {
        console.error('[UKey认证]参数错误')
        console.error(error)
      }

      console.log('[UKey认证]组织参数返回=', ret)

      if (!_.get(ret, 'result', false) || _.isEmpty(_.get(ret, 'params'))) {
        const msg = _.get(ret, 'msg') || this.$t('auth.insertUKey')
        this.$message.error(msg)
        EventBus.$emit('client:show')
        this.$emit('loading', false)
        return false
      }

      // 开始调用真正的UKey认证
      const authRet = await this.uKeyAuthInstance.auth(_.get(ret, 'params'))
      if (authRet === false) {
        this.$emit('loading', false)
        EventBus.$emit('client:show')
        return false
      } else if (_.get(authRet, 'revoke', false) === true) {
        // 超出最大可登录数量
        this.$emit('loading', false)
        this.$emit('emitHandle', { type: 'revoke:show', value: _.get(authRet, 'data', {}) })
        return false
      }

      await this.uKeyAuthSuccess(this.certInfo['IssueTo'], '', authRet)
    },
    /**
     * UKey认证成功
     */
    async uKeyAuthSuccess(user_name, uKey_password, res) {
      const uKeyCert = this.authData.cert,
        uKeyPin = this.authData.uKeyPin,
        uKeySaveChecked = this.authData.uKeyAutoAuth ? 1 : 0,
        uKeyAutologinChecked = this.authData.uKeyAutoLogin ? 1 : 0
      const authTypeTmp = {}
      authTypeTmp['user_name'] = opform.user_name
      authTypeTmp['password'] = opform.password || ''
      authTypeTmp['ukeyPin'] = uKeyPin
      authTypeTmp['ukeyCert'] = uKeyCert
      authTypeTmp['ukeySaveChecked'] = uKeySaveChecked
      authTypeTmp['ukeyAutologinChecked'] = uKeyAutologinChecked
      await agentApi.fileTools.DeleteOneFileNever('WebAuthTypeTmp')
      await agentApi.fileTools.ActionLocalFile('WebAuthTypeTmp', 'save',
        Base64Encode('UKey') + '###' + Base64Encode(JSON.stringify(authTypeTmp)))
      this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
    },

    /**
     * 勾选自动登录，需要同时勾选记住信息
     */
    changeAutoLogin() {
      const autoLogin = this.authData.uKeyAutoLogin
      if (autoLogin) {
        this.authData.uKeyAutoAuth = true
      }
    },
    /**
     * 取消记住信息，需要同时取消自动登录
     */
    changeAutoAuth() {
      const autoAuth = this.authData.uKeyAutoAuth
      if (!autoAuth) {
        this.authData.uKeyAutoLogin = false
      }
    }
  }
}
</script>
<style lang="scss">
.ukey-auth-page {
  width: 360px;
  margin: 0 auto;

  .rule-form {
    .select-ukey {
      font-size: 12px;
      color: $disabled-color;
      line-height: 18px;

      .not-key-tip {
        color: #D65E0E
      }
    }
  }

  .checkbox-wrapper {
    display: flex;
    justify-content: space-between;
    padding-top: 8px;
    padding-bottom: 32px;
  }

  .el-input .iconfont {
    font-size: 16px;
  }

  .select-form-box {
    .icon-zhengshu {
      font-size: 16px !important;
    }
  }
}
</style>

