const { app } = require('electron')
const path = require('path')
const windowStateKeeper = require('electron-window-state')

const windowStateKeep = {
  mainWindowState: null,
  init() {
    this.mainWindowState = windowStateKeeper({
      defaultWidth: 1000,
      defaultHeight: 800,
      path: path.dirname(app.getPath('exe')),
      file: 'window-state.json'
    })

    return this.mainWindowState
  }

}

module.exports = windowStateKeep
