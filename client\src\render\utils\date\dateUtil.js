// 关于日期和时间的辅助函数
const dateHelper = {

  // 获取格式化的完整日期
  // 2021年12月21日 12点32分15秒
  getFullFormateDate() {
    const date = new Date()
    const year = date.getFullYear()
    let month = date.getMonth() + 1
    month = month < 10 ? '0' + month : month
    let dates = date.getDate()
    dates = dates < 10 ? '0' + dates : dates

    let hour = date.getHours()
    const half = hour >= 12 ? '下午' : '上午'
    hour = hour <= 12 ? hour : (hour - 12)
    hour = hour < 10 ? '0' + hour : hour
    let min = date.getMinutes()
    min = min < 10 ? '0' + min : min
    let sed = date.getSeconds()
    sed = sed < 10 ? '0' + sed : sed
    return (year + '年' + month + '月' + dates + '日 ' + half + hour + '点' + min + '分' + sed + '秒')
  },
  formatDate(datestr) {
    if (!datestr) {
      return false
    }
    var _splitStr = datestr.split(' ')
    var _Date = _splitStr[0].split('-')
    var _Time = _splitStr[1].split(':')
    return { getDates: _Date, getTimes: _Time }
  },
  // 时间戳转标准日期格式
  timeStampToDate(timeStamp) {
    var date = new Date(timeStamp)
    var YY = date.getFullYear() + '-'
    var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
    var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())
    var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
    var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
    var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
    return YY + MM + DD + ' ' + hh + mm + ss
  }
}

export default dateHelper
