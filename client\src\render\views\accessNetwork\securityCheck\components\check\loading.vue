<template>
  <div class="check-loading-wrapper">
    <img :src="imgSrc" class="loading-img" alt="">
  </div>
</template>
<script>
export default {
  data() {
    return {
      imgSrc: require('@/render/assets/checkLoading.png')
    }
  }
}
</script>
<style lang="scss" scoped>
.check-loading-wrapper{
   height: 100%;
   display: flex;
   padding-right: 24px;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   background: $row-bg;
   flex: 1 1 auto;
   border-top: 1px solid $line-color;
   border-bottom: 1px solid $line-color;
   .loading-img{
       width: 300px;
       height: 200px;
   }
   .loading-text{
       text-align: center;
       color: $default-color;
       font-size: 12px;
       margin-top: 16px;
   }
}
</style>
