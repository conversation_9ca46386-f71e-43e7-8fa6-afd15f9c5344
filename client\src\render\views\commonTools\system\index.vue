<!-- 系统信息 -->
<template>
  <div class="sys-page">
    <tab v-if="isWindows" v-model="activeCom" />
    <div :class="isWindows ? 'com-wrapper':''">
      <component :is="activeCom" />
    </div>
  </div>
</template>

<script>
import tab from './components/tab'
import devInfo from './components/devInfo'
import sysInfo from './components/sysInfo'
// import os_browser_info from '@/render/utils/os_browser_info'
export default {
  name: 'System',
  components: {
    tab,
    sysInfo,
    devInfo
  },
  data() {
    return {
      activeCom: 'devInfo',
      // isWindows: os_browser_info.os_type === 'windows'
      isWindows: true
    }
  },
  computed: {
  },

  created() {
  },

  methods: {
  }
}
</script>
<style lang="scss" scope>
.sys-page {
  padding: 24px;
  height: 100%;
  .com-wrapper{
    padding: 24px 0;
    height: calc(100% - 34px);
    padding-bottom: 0;
  }
}
</style>
