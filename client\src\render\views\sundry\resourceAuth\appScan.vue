
<template>
  <div id="f-resource-app-scan">
    <div id="ewm-box" class="ewm-box" @click="refresh">
      <img v-if="qrCodeSrc.length > 0" class="nac-ewm" :src="qrCodeSrc" alt="二维码">
    </div>
    <div class="u-tips" v-html="scanTips" />
  </div>
</template>

<script>
import proxyApi from '@/service/api/proxyApi'
import qs from 'qs'
import { mapGetters } from 'vuex'
import { Message } from 'element-ui'
import urlUtils from '@/render/utils/url'
export default {
  data() {
    return {
      qrCodeSrc: '',
      qrCodeConf: {
        drawTime: 0,
        overTime: (5 * 60 - 5) * 1000 // 二维码过期时间
      }
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    scanTips() {
      return this.$i18n.locale === 'en' ? 'Please use<span class="u-nac-name">NAC APP</span> scan authentication' : '请使用<span class="u-nac-name">NAC APP</span>扫一扫'
    },
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }
  },
  created() {
    this.init()
  },
  beforeDestroy() {
    this.clearQueryScanTask()
  },
  methods: {
    async init() {
      await this.drawQrCode()
      this.queryScanRes()
    },

    // 点击刷新二维码
    refresh() {
      this.drawQrCode()
    },

    async drawQrCode() {
      const firstUrl = _.get(qs.parse(location.search.substring(1)), 'firsturl', '')
      const checkParam = {
        deviceid: this.deviceid,
        action: 'imgCheck',
        random: Math.random(),
        resourceAddr: firstUrl
      }

      // 请求接口获取二维码
      const ret = await proxyApi.getResourceAuthQrcode(checkParam)

      if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data.url', ''))) {
        const err = _.get(ret, 'errmsg') || this.$t('resourceAuth.drawQrCodeFail')
        Message({ message: err, type: 'error', duration: 6000, offset: 60 })
        return
      }

      // 二维码图片地址
      const now = new Date().getTime()
      this.qrCodeSrc = urlUtils.getBaseIPPort(true) + '/access/qrcode?code=' + _.get(ret, 'data.url') + '&cache=' + now
      this.qrCodeConf.drawTime = now
    },
    // 周期轮询是否扫描
    queryScanRes() {
      // 判断二维码是否过期，如果二维码过期就重新去刷新二维码
      if (_.isEmpty(this.qrCodeSrc)) {
        return
      }

      this.queryScanTaskId = setInterval(async() => {
        const now = new Date().getTime()
        // 判断二维码是否超时,超时刷新.
        if (now - this.qrCodeConf.drawTime > this.qrCodeConf.overTime) {
          this.drawQrCode()
        }

        // 判断是否扫描成功
        const apiParams = {
          deviceid: this.deviceid,
          action: 'usedCheck'
        }

        const ret = await proxyApi.queryScanResult(apiParams)
        if (parseInt(_.get(ret, 'errcode', 200)) !== 0) {
          const err = _.get(ret, 'errmsg') || this.$t('resourceAuth.sanQrcodeFail')
          Message({ message: err, type: 'error', duration: 30000, offset: 60 })

          this.clearQueryScanTask()
          return
        }

        // 如果扫描成功，则弹出消息
        if (_.get(ret, 'data.state', false)) {
          this.$emit('authSuccess', { type: 'Qrcode' })
          this.clearInterval()
        }
      }, 3000)
    },
    // 组件销毁的时候取消定时任务
    clearQueryScanTask() {
      if (this.queryScanTaskId) {
        clearInterval(this.queryScanTaskId)
      }
    }
  }
}
</script>

<style lang="scss">
#f-resource-app-scan {
  .ewm-box {
    margin: 20px auto;
    width: 240px;
    height: 240px;
    background: url('../../../assets/frame.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      border-radius: 10px;
      opacity: 0.7;
    }

    .nac-ewm {
      width: 200px;
      height: 200px;
    }
  }

  .u-tips {
    margin-top: 30px;
    text-align: center;

    .u-nac-name {
      color: #37c6fa;
      padding: 2px;
    }
  }

}</style>
