<template>
  <div class="tree-select-box" :title="selectLabel">
    <el-select
      :id="popIdName"
      ref="select"
      :placeholder="placeholder"
      :clearable="clearable"
      :accordion="accordion"
      :value="selectLabel"
      popper-class="yg-tree-select"
      :popper-append-to-body="popperAppendToBody"
      @clear="selectClear"
      @visible-change="visibleHandle"
    >
      <el-option
        class="option-style"
        :label="selectLabel"
        :value="selectValue"
      >
        <div class="search-box" @click.stop>
          <el-input
            :id="`ui-${treeName}-search_input`"
            v-model="tempKeyword"
            size="small"
            :placeholder="$t('reg.searchPlaceHolder')"
            @input="searchHandle"
            @click.stop
          />
        </div>

        <div class="tree-content" :style="{height: dropContentHeight}">
          <div v-show="showEmpty&&!loading" :id="`ui-${treeName}-empty`" class="empty-wrapper">
            {{ emptyText }}
          </div>
          <div v-show="!showEmpty&&!loading" :id="'scroll'+treeBoxId" ref="scrollBox" class="list-wrapper">
            <!-- 搜索 -->
            <ul v-show="keyword" :id="`ui-${treeName}-search_list`" class="searchList" @click="selectSearch($event)">
            <!-- <li v-for="item in searchList" :id="item.DepartID" :key="item.id" class="text-clamp" :title="item.fullLabel || item.label">{{ item.fullLabel || item.label }}</li> -->
            </ul>
            <!-- 树 -->
            <div v-show="!keyword" :id="treeBoxId" class="yg-tree-wrapper" @click.stop="delegate($event)" />
            <div v-if="isMore && keyword && lazy" :id="`ui-${treeName}-search_load_more`" class="loadMore">{{ $t('reg.loadMore') }}</div>
          </div>
          <div v-show="loading&&keyword" class="search-loading">
            <i class="el-icon-loading" />
          </div>
        </div>

      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    /*
    节点必须包含参数
    {
      pid:'', // 上级id
      id: '', // id
      label: '', // 显示名称
      children: [] // 叶子节点
      fullLabel: '' // 全路径名称
    }
  */
    options: { // 树状数据
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number],
      default: () => {
        return ''
      }
    },
    // 懒加载模式默认值必须包含名称和id
    defaultValue: {
      default: '',
      validator: function(value) {
        if (value) {
          return (value.id !== '' && value.id !== undefined) && (value.label !== '' && value.label !== undefined)
        }
        return true
      }
    },
    /* 可清空选项 */
    clearable: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    /* 自动收起 */
    accordion: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    placeholder: {
      type: String,
      default: () => {
        return '请选择'
      }
    },
    lazy: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    load: {
      type: Function,
      default: null
    },
    search: {
      type: Function,
      default: null
    },
    disabledValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    treeName: {
      type: String,
      default: ''
    },
    popIdName: {
      type: String,
      default: ''
    },
    popperAppendToBody: {
      type: Boolean,
      default: false
    },
    dropHeight: {
      type: Number,
      default: 270
    }
  },
  data() {
    return {
      requestingId: '',
      treeBoxId: 'treeBox',
      keyword: null, // 搜索关键字
      tempKeyword: '',
      selectValue: null, // 下拉框显示ID
      noSubsetList: [], // 无子元素列表
      searchList: [],
      loadList: [], // 异步数据
      timer: null,
      loading: false, // 正在加载
      loadSelect: {}, // 异步搜索选中项
      sStart: 0,
      limit: 100,
      moreLoading: false,
      isMore: true,
      searchTimer: null,
      searchDebounceTime: 300 // 搜索防抖时间
    }
  },
  computed: {
    emptyText() {
      return this.$t('reg.noData')
    },
    showEmpty() {
      if ((!this.options.length && !this.loadList.length) || (this.keyword && !this.searchList.length)) {
        return true
      } else {
        return false
      }
    },
    selectLabel() { // 下拉框显示名称
      if (this.selectValue === '') {
        return ''
      } else {
        if (this.lazy) {
          const obj = this.loadList.find(item => item.id === this.selectValue)
          if (obj) {
            return obj.fullLabel || obj.label
          } else if (this.defaultValue.id === this.selectValue) {
            return this.defaultValue.label
          } else if (this.loadSelect && this.loadSelect.id === this.selectValue) {
            return this.loadSelect.fullLabel || this.loadSelect.label
          }
        } else {
          const obj = this.noSubsetList.find(item => item.id === this.selectValue)
          if (obj) {
            return obj.fullLabel || obj.label
          } else { // 不是最底层节点从树内获取节点名称
            const obj = this.getLabelFromTree(this.selectValue) || {}
            return obj.fullLabel || obj.label
          }
        }

        return ''
      }
    },
    dropContentHeight() {
      return this.dropHeight - 45 + 'px'
    }
  },
  watch: {
    value(val) {
      this.selectValue = val
    },
    options: {
      handler: function(val) {
        this.asyncInit(val)
      },
      deep: false,
      immediate: false
    },
    defaultValue: {
      handler: function(val) {
        if (val && val.label && val.id) {
          this.setSelectVal(val.id)
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.setTreeId() // 设置下拉组件id
    this.init()
    this.addScroll()
  },
  methods: {
    addScroll() {
      if (this.$refs.scrollBox && this.lazy) {
        console.log(this.$refs.scrollBox)
        this.$refs.scrollBox.addEventListener('scroll', this.scroll())
      }
    },
    scroll() {
      let timeOut = null // 初始化空定时器
      return () => {
        clearTimeout(timeOut) // 频繁操作，一直清空先前的定时器
        timeOut = setTimeout(() => { // 只执行最后一次事件
          console.log('执行事件')
          if (this.moreLoading || !this.isMore || !this.keyword) {
            return
          }
          const dom = this.$refs.scrollBox
          console.log(dom.scrollHeight, dom.clientHeight, dom.scrollTop)
          if (dom.scrollHeight - dom.clientHeight - dom.scrollTop < 10) {
            this.sStart = this.sStart + this.limit
            this.moreLoading = true
            this.search({ params: { keyword: this.keyword, start: this.sStart, limit: this.limit }, resolve: this.searchResolve })
          }
        }, 1000)
      }
    },
    // 设置id 保证一个页面每个树组件id唯一
    setTreeId() {
      const id = this.getUniqueId()
      this.treeBoxId = 'treeBox' + id
    },
    getUniqueId() {
      let str = Math.random().toString(36)
      str = str.substr(str.length - 4)
      if (window.treeIds) {
        const idx = window.treeIds.indexOf(str)
        if (idx > -1 && window.treeIds[idx] === str) {
          str += window.treeIds.length
        }
      } else {
        window.treeIds = []
      }
      window.treeIds.push(str)
      return str
    },
    init() {
      this.selectValue = this.value
      if (this.lazy) {
        this.fireLoad()
      } else {
        this.$nextTick(() => { // 下一个事件环再初始化
          this.asyncInit(this.options)
        })
      }
    },
    // 同步初始化
    asyncInit(options) {
      if (options && options.length) {
        const root = document.querySelector(`#${this.treeBoxId}`)
        if (root) {
          root.innerHTML = ''
        }
        this.getNoSubItme(options)
        this.createAddDom(options)
        this.$nextTick(() => {
          const children = options[0].children
          if (children && children.length) { // 再展开一级
            this.clickHandle(`tr-${options[0].id}-1`)
          }
        })
      }
    },

    // 事件代理
    delegate(e) {
      // 获取到目标阶段指向的元素
      var target = e.target || e.srcElement

      // 获取到代理事件的函数
      var currentTarget = e.currentTarget

      // 遍历外层并且匹配
      while (target !== currentTarget) {
        // 判断是否匹配到我们所需要的元素上
        if (target.matches('.tr-se-item')) {
          this.clickHandle(target.id)
        }
        target = target.parentNode
      }
    },
    // 点击选项
    clickHandle(idLevel) {
      if (!idLevel || this.requestingId) {
        return
      }
      const arr = idLevel.split('-')
      const id = arr[1]
      const thisElement = document.querySelector(`#${this.treeBoxId} #${idLevel}`)
      if (!thisElement) {
        console.log('出错了获取不到元素', this.treeBoxId, idLevel)
        return
      }
      const bottomIcon = thisElement.querySelector('.el-icon-caret-bottom')
      const leftIcon = thisElement.querySelector('.el-icon-caret-right')
      if (!bottomIcon && !leftIcon) { // 无子节点
        this.setSelectVal(id)
      } else {
        this.toggle(bottomIcon)
        this.toggle(leftIcon)
        if (leftIcon && leftIcon.style.display !== 'none') { // 收起
          if (thisElement.nextElementSibling) {
            thisElement.nextElementSibling.style.display = 'none'
          }
        } else if (bottomIcon && bottomIcon.style.display !== 'none') { // 展开
          if (thisElement.nextElementSibling) {
            thisElement.nextElementSibling.style.display = ''
          } else {
            if (this.lazy) { // 异步加载
              const level = this.getLevelById(id) + 1
              this.appendLoading(thisElement)
              this.fireLoad(level, id)
            } else {
              this.requestingId = id
              const children = this.getChildren(this.options, id)
              const childLevel = arr[2] * 1 + 1
              this.createAddDom(children, childLevel)
            }
          }
        }
      }
    },
    // 清除选中项
    selectClear() {
      this.selectValue = ''
      this.$emit('input', '')
      this.$emit('change', '')
    },
    createAddDom: function(arr, level) {
      var treeBoxId = this.treeBoxId
      let parentDom = document.querySelector(
        `#${treeBoxId} #tree-${treeBoxId}-li-${this.requestingId}`
      )
      if (!parentDom) {
        // 首次加载
        level = 1
        parentDom = document.querySelector(`#${treeBoxId}`)
      }
      if (!parentDom) {
        return
      }
      const strArr = this.createLiHtmlArr(arr, level)
      strArr.unshift(`<ul class="tree-gather tree-level-${level}">`)
      strArr.push('</ul>')
      this.apend(parentDom, strArr.join(''))
      this.requestingId = ''
    },
    createLiHtmlArr(arr, level) {
      const treeBoxId = this.treeBoxId
      const strArr = []
      const len = arr.length
      const lazy = this.lazy
      for (let i = 0; i < len; i++) {
        const item = arr[i]
        let iconStr = `<i class="el-icon-caret-bottom" style="display: none"></i>
                    <i class="el-icon-caret-right"></i> `
        if (!lazy && (!item.children || !item.children.length)) {
          iconStr = ''
        }
        const str = `<li id="tree-${treeBoxId}-li-${item.id}" class="tr-se-li">
        <div class="tr-se-item" id=tr-${item.id}-${level} 
         style="padding-left:${level * 18 + 'px'}" >
          <div class="item-name">
            <div class="icon-wrapper">
              ${iconStr}
            </div>
            ${item.label}
          </div>
        </div>
      </li>`
        strArr[i] = str
      }
      return strArr
    },
    renderSearch(list, type = 'add') {
      const parentDom = this.$refs.scrollBox
      const dom = parentDom.querySelector('.searchList')
      if (type === 'clear' || type === 'add') {
        dom.innerHTML = ''
      }
      const strArr = []
      const len = list.length
      for (let i = 0; i < len; i++) {
        strArr[i] = `<li  id="${list[i].id}" class="text-clamp" title="${list[i].fullLabel || list[i].label}">${list[i].fullLabel || list[i].label}</li>`
      }
      var divTemp = document.createElement('div'), nodes = null,
        // 文档片段，一次性append，提高性能
        fragment = document.createDocumentFragment()
      divTemp.innerHTML = strArr.join('')
      nodes = divTemp.childNodes
      for (var i = 0, length = nodes.length; i < length; i += 1) {
        fragment.appendChild(nodes[i].cloneNode(true))
      }
      dom.appendChild(fragment)
      nodes = null
      fragment = null
    },
    searchHandle() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
        this.searchTimer = null
      }

      this.searchTimer = setTimeout(() => {
        this.keyword = this.tempKeyword
        this.searchChange(this.tempKeyword)
      }, this.searchDebounceTime)
    },
    // 搜索
    searchChange(val) {
      this.timer && clearTimeout(this.timer)
      this.timer = null
      this.loading = true
      this.sStart = 0
      this.moreLoading = false
      this.isMore = true
      this.timer = setTimeout(() => {
        clearTimeout(this.timer)
        this.timer = null
        if (!val) {
          this.loading = false
          this.searchList = []
          this.renderSearch([], 'clear')
          return
        }
        if (this.lazy) {
          this.search({ params: { keyword: val, start: this.sStart, limit: this.limit }, resolve: this.searchResolve })
        } else {
          const searchList = this.noSubsetList.filter(item => item.label.indexOf(val) > -1)
          this.searchList = searchList
          this.renderSearch(searchList, 'add')
          this.loading = false
        }
      }, 200)
    },
    // 异步搜索
    searchResolve(data) {
      this.loading = false
      this.moreLoading = false
      if (data && data.length) {
        if (this.sStart > 0) {
          const list = this.searchList
          this.searchList = [...list, ...data]
          this.renderSearch(data, 'update')
          if (data.length < this.limit) {
            this.isMore = false
          }
        } else {
          if (data.length < this.limit) {
            this.isMore = false
          }
          this.searchList = data
          this.renderSearch(data, 'add')
        }
      } else {
        if (this.sStart > 0) {
          this.isMore = false
        } else {
          this.searchList = []
          this.renderSearch([], 'clear')
        }
      }
    },
    // 选中搜索项
    selectSearch(e) {
      try {
        const id = e.target.id
        const arr = this.searchList.filter(item => item.id === id)
        if (arr.length) {
          const item = arr[0]
          this.loadSelect = item
          this.setSelectVal(item.id)
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 触发异步加载
    fireLoad(level = 0, id = '') {
      this.requestingId = id
      this.load({ level, id, resolve: this.loadResolve })
    },
    // 异步加载
    loadResolve(arr) {
      const id = this.requestingId
      if (arr && arr.length) {
        this.loadList = [...this.loadList, ...arr]
        let childLevel = ''
        if (id) {
          childLevel = this.getLevelById(id) + 1
        }
        this.createAddDom(arr, childLevel)
      } else {
        this.requestingId = ''
        const iconWrapper = document.querySelector(`#tree-${this.treeBoxId}-li-${id} .icon-wrapper`)
        if (iconWrapper) {
          iconWrapper.innerHTML = ''
        }
      }
      this.deleteLoading(id) // 清除loading
    },
    setSelectVal(value) {
      this.$refs.select && this.$refs.select.blur()
      if (this.isSelectDisableItem(value)) {
        return
      }
      this.selectValue = value
      this.$emit('input', value)
      this.$emit('change', value)
    },
    // 是否选择了禁止选择项
    isSelectDisableItem(id) {
      const disabledValue = this.disabledValue
      if (disabledValue && disabledValue.length) {
        const idx = disabledValue.indexOf(id)
        if (idx > -1) {
          this.$emit('select-disabled', id)
          return true
        }
      }
      return false
    },

    // 收集无子集数据
    getNoSubItme(arr) {
      const noSubsetList = []
      function expand(arr) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].children && arr[i].children.length) {
            expand(arr[i].children)
          } else {
            const obj = {}
            obj.id = arr[i].id
            obj.pid = arr[i].pid
            obj.label = arr[i].label
            obj.fullLabel = arr[i].fullLabel
            noSubsetList.push(obj)
          }
        }
      }
      if (arr && arr.length) {
        expand(arr)
      }
      this.noSubsetList = noSubsetList
    },
    // 获取level
    getLevelById(id) {
      const dom = document.querySelector(`#tree-${this.treeBoxId}-li-${id} .tr-se-item`)
      if (dom) {
        let str = dom.getAttribute('id')
        if (str) {
          str = str.split('-')
          return str[2] * 1
        }
      }
      return 0
    },
    getChildren(data, pid) {
      let newArr = []
      function getTree(arr) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].id === pid) {
            newArr = arr[i].children
            return
          }
          if (arr[i].children && arr[i].children.length > 0) {
            getTree(arr[i].children)
          }
        }
      }
      getTree(data)
      return newArr
    },
    appendLoading(upDom) {
      if (!upDom) {
        return
      }
      const iconWrapper = upDom.querySelector('.icon-wrapper')
      if (iconWrapper) {
        const pEl = document.createElement('i')
        pEl.setAttribute('class', 'el-icon-loading')
        this.insertAfter(pEl, iconWrapper)
      }
    },
    visibleHandle(bool) {
      if (!bool) {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null
          // 处于性能考虑下拉选择框收起时，收起所有展开节点，只展示第一二节节点
          const uls = document.querySelectorAll(`#${this.treeBoxId} .tree-gather`)
          if (uls.length) {
            uls.forEach(item => {
              const classList = item.getAttribute('class')
              const sibling = item.previousElementSibling
              if (sibling) {
                const bottom = sibling.querySelector('.el-icon-caret-bottom')
                const right = sibling.querySelector('.el-icon-caret-right')
                if (classList.indexOf('tree-level-1') > -1 || classList.indexOf('tree-level-2') > -1) {
                  if (bottom && right) {
                    bottom.style.display = ''
                    right.style.display = 'none'
                  }
                  item.style.display = 'block'
                } else {
                  item.style.display = 'none'
                  if (bottom && right) {
                    bottom.style.display = 'none'
                    right.style.display = ''
                  }
                }
              }
            })
          }
          const scrollWrapper = document.querySelector(`#scroll${this.treeBoxId}`)
          if (scrollWrapper) {
            scrollWrapper.scrollTop = 0
          }
        }, 50)
      }
    },
    deleteLoading() {
      const deleteDom = document.querySelector(`#${this.treeBoxId} .el-icon-loading`)
      if (deleteDom) {
        deleteDom.parentNode.removeChild(deleteDom)
      }
    },
    apend(parentDom, html) {
      var divTemp = document.createElement('div'), nodes = null,
        // 文档片段，一次性append，提高性能
        fragment = document.createDocumentFragment()
      divTemp.innerHTML = html
      nodes = divTemp.childNodes
      for (var i = 0, length = nodes.length; i < length; i += 1) {
        fragment.appendChild(nodes[i].cloneNode(true))
      }
      parentDom.appendChild(fragment)
      // 据说下面这样子世界会更清净
      nodes = null
      fragment = null
    },
    insertAfter(newElement, targentElement) {
      var parent = targentElement.parentNode
      if (parent.lastChild === targentElement) {
        parent.appendChild(newElement)
      } else {
        parent.insertBefore(newElement, targentElement.nextSibling)
      }
    },
    toggle(el) {
      if (el.style.display === 'none') {
        el.style.display = ''
      } else {
        el.style.display = 'none'
      }
    },
    loadMore() {
      this.sStart = this.sStart + this.limit
      this.search({ params: { keyword: this.keyword, start: this.sStart, limit: this.limit }, resolve: this.searchResolve })
    },
    getLabelFromTree(id) {
      let obj = {}
      const loop = (list) => {
        const items = list.filter(item => item.id === id)
        if (items.length) {
          obj = items[0]
          return
        }
        list.forEach(item => {
          item.children && loop(item.children)
        })
      }
      if (!this.options || !this.options.length) {
        return obj
      }
      loop(this.options)
      return obj
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-select-box{
    width: 100%;
    height: 100%;
}
.list-wrapper{
  height: 100%;
  width: 100%;
  overflow-x: auto;
  overflow-y: auto;
  background: white;
}

.search-box{
  padding: 0.4rem;
  background: white;
}
.loadMore{
  text-align: center;
  color: $default-color;
}
.option-style{
  padding: 0;
  width: 360px;
  height: 100%;
  background-color: #FFFFFF;
}
.empty-wrapper{
  height: 100%;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
  cursor: default;
  background: white;
  text-align: center;
}
.search-loading{
  height: 100%;
  text-align: center;
  padding: 20px 0;
  background: white;
}
.search ::v-deep .el-input__inner:focus{
  border-color: $scroll-color;
}
</style>
<style lang="scss">
.yg-tree-wrapper {
  .tree-level-1{
    display: flex;
    li{
      flex: 1;
    }
  }
  .tr-se-li {
    .tr-se-item {
      &:hover {
        background: #f2f4fd;
      }
      .item-name {
        position: relative;
        padding-left: 20px;
        height: 32px;
        line-height: 32px;
        user-select: none;
        color: #686e84;
        white-space: nowrap;
        &:hover {
          background: #f2f4fd;
        }
        .icon-wrapper {
          position: absolute;
          left: 0;
          width: 20px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          i {
            color: $scroll-color;
            font-size: 14px;
          }
        }
      }
    }
  }
  .item-inner {
    line-height: 30px;
    background: white;
    display: flex;
    &:hover {
      background: #f2f4fd;
    }
    i {
      font-size: 14px;
      color: $disabled-color;
    }
    .icon-box {
      i {
        font-size: 15px;
        line-height: 30px;
        color: $scroll-color;
      }
      width: 20px;
      text-align: center;
    }
    .loading-box {
      padding-right: 5px;
    }
    .name {
      color: $default-color;
    }
  }
}
.yg-tree-select {  // 覆盖select样式
  .el-scrollbar{
    overflow: hidden;
    .el-scrollbar__bar{
      display: none;
    }
    .el-scrollbar__wrap{
      overflow: initial;
      max-height: 2740px;
      margin: 0!important;
    }
  }
  .tree-content{
    background: white;
  }
  .el-scrollbar .el-select-dropdown__item{
    margin: 0;
  }
  .searchList{
    background: white;
    width: 100%;
    li{
      line-height: 30px;
      white-space:nowrap;
      font-size: 14px;
      color: #686E84;
      padding: 0 18px;
      &:hover{
        background:#F2F4FD;
      }
    }
  }
}
</style>
