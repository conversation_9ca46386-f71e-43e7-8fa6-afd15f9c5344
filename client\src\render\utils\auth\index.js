import store from '@/render/store'
import common from './common'
import dot1x from './dot1x'
import processController from '../processController'
import agentApi from '@/service/api/agentApi'
import _ from 'lodash'
import authTypes from './authTypes'
import { Base64Encode, Base64Decode, GetUrlParam } from '@/render/utils/global'
import { Base64 } from 'js-base64'
import JSEncrypt from 'jsencrypt'
import proxyApi from '@/service/api/proxyApi'
import authIndex from '@/render/utils/auth'
import { Message } from 'element-ui'
import { i18n } from '@/render/lang'
import { compareIP } from '@/render/utils/global'
import { EventBus } from '@/render/eventBus'
import localStorage from '@/render/utils/cache/localStorage'
import jas from '@/render/utils/auth/common/JAS'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import qs from 'qs'
import { saveToken, readTokenAsync } from '@/render/utils/token'

const readClientFile = {
  /**
   * 读取WebAuthTypeTmp记录的上一次认证信息
   */
  async WebAuthTypeTmp() {
    return agentApi.fileTools.ActionLocalFile('WebAuthTypeTmp', 'read')
  },
  async updateWebAuthConfig(params) {
    var type = params.authType,
      savePass = _.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', '0'),
      hasPassword = _.isString(params.password) && params.password !== '',
      autoLoginChecked = !!params.userAutoLogin,
      ukeySaveChecked = !!params.uKeyAutoLogin,
      ukeyAutologinChecked = !!params.uKeyAutoAuth,
      AuthServer = ''

    const isAccountAuthType = params.isAccountAuthType
    // 如果选中的是认证服务器，则记录认证服务器
    if (isAccountAuthType && type !== authTypes.User) {
      AuthServer = type
    }
    // 用户名密码别名认证修改
    if (isAccountAuthType) {
      type = authTypes.User
    }

    let enableAutoLogin = '0'
    if (type === authTypes.User && savePass && hasPassword && autoLoginChecked && !index.config.g_isQrLogin) {
      enableAutoLogin = '1'
    }
    // 是ukey认证并且勾选了自动登录
    // 如果是ukey认证勾选了记住账户和自动登录，则记录当前的证书以及Pin码
    if (type === 'UKey' && ukeyAutologinChecked && ukeySaveChecked) {
      enableAutoLogin = '1'
    }

    const config = {}
    config['AuthType'] = type
    config['AuthServer'] = AuthServer
    config['EnableAutoLogin'] = enableAutoLogin

    // runResult = '{"AuthType":"User","AuthServer":"AdDomain","EnableAutoLogin":"0"}'
    await agentApi.fileTools.ActionLocalFile('WebAuthConfigTmp', 'save', JSON.stringify(config))
  },
  /**
   * 读取小助手存储WebAuthConfigTmp配置文件内容
   * @returns {Promise}
   */
  async applyWebAuthConfig() {
    const runResult = await agentApi.fileTools.ActionLocalFile('WebAuthConfigTmp', 'read')
    // runResult = '{"AuthType":"User","AuthServer":"AdDomain","EnableAutoLogin":"0"}'

    if (!_.isObject(runResult) || _.isEmpty(runResult)) {
      return {}
    }
    return runResult
  }
}

const index = {
  config: {
    /**
     * 是否开启AD域单点登录
     */
    adDomainAutoLoginFlag: false,
    /**
     * 默认记录日志
     */
    isRecord: 1,
    callfrom: '',
    from: '',
    isbase64encode: '',
    g_isQrLogin: '',
    outonLineIp: '',
    AutoLogin: 0,
    is_guest: false,
    DevRegSubmitted: '',
    IsServerIPChanged: false
  },
  async authReady() {
    await this.setAuthFlag()
    this.getAdDomainAutoLoginFlag()
    this.config.IsServerIPChanged = false
    console.log('authCommon', this.config)
  },
  /**
   * 从场景化的返回里面判断是否免认证(如果没有开启认证方式的也算免认证)
   */
  async setAuthFlag() {
    // 开启认证且不是802.1x模式则判断是否在免认证IP段中
    if (!this.isDot1xMode()) {
      const isAuth = parseInt(_.get(store.state.serveEntiretyConfig, 'scene.IsAuth', 1)) === 1
      store.commit('setAuthInfo', { ...store.state.authInfo, ...{ isAuth }})
    }
  },

  /*
   * 获取上一次认证相关的本地缓存
   * @tips:复杂的历史问题,不知道为啥要存两个地方的缓存,这名字也很令人费解,为了兼容版本升级也没办法优化了
   * WebAuthConfigTmp缓存记录的上一次认证方式相关的信息格式类似于
    {
      "AuthServer": "Localhost",
      "AuthType": "User",[User|Ueky]
      "EnableAutoLogin": "1"
     }
   * WebAuthTypeTmp 记录的是保存的账户名和密码(ukey的证书之类 的),格式类似于:VXNlcg::###bGl1Ync:###ZkJzekhhNWh5Sg::||||xK8JHMTEx4r2FS
   */
  async lastAuthCache() {
    let authData = {}
    // 读取上次的配置(是否自动认证，AuthServer等)信息[来自缓存：WebAuthTypeTmp]
    const authConfigFromWebAuthConfigTmp = await this.getLastAuthConfig()
    console.log('【小助手保存的配置信息：lastAuthInfo=】', JSON.stringify(authConfigFromWebAuthConfigTmp))

    // 读取上次账号密码信息[来自缓存：WebAuthTypeTmp]
    const authTypeFromWebAuthTypeTmp = await this.getLastAuthInfo(authConfigFromWebAuthConfigTmp)
    // 打印的时候脱敏操作，把秘密去掉
    let authConsole = _.clone(authTypeFromWebAuthTypeTmp)
    authConsole = JSON.stringify(_.assign(authConsole, { authData: { password: '****' }}))
    console.log('【小助手保存的账户名密码信息：lastAuthInfo=】', authConsole)

    if (!_.isEmpty(_.get(authTypeFromWebAuthTypeTmp, 'authData')) && _.isObject(_.get(authTypeFromWebAuthTypeTmp, 'authData'))) {
      authData = _.get(authTypeFromWebAuthTypeTmp, 'authData', {})
    }

    // 802.1x自动登录时没有账户名或者密码补充账户密码
    if (this.dot1xAutoAuth() && (_.isEmpty(authData.userName) || _.isEmpty(authData.password))) {
      const lastAuthType = _.get(store.state.serveEntiretyConfig, 'client.LastAuthType', authTypes.User)
      console.log('【当前为8021.x自动认证】dot1xAutoAuth:lastAuthType', lastAuthType)
      if (lastAuthType === authTypes.User) {
        authData.userName = Base64Decode(_.get(store.state.serveEntiretyConfig, 'client.UserName'), '')
        authData.password = Base64Decode(_.get(store.state.serveEntiretyConfig, 'client.Password'), '')
      }
    }

    return { authConfigFromWebAuthConfigTmp, authData, authTypeFromWebAuthTypeTmp }
  },

  queryDefaultAuthType(authConfigFromWebAuthConfigTmp, authTypeFromWebAuthTypeTmp) {
    let authType = null
    const allow = _.get(store.getters.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType', [])
    // 由于WebAuthConfigTmp里面也记录了上一次认证方式，优先取这个认证方式
    if (_.get(authConfigFromWebAuthConfigTmp, 'AuthType')) {
      authType = _.get(authConfigFromWebAuthConfigTmp, 'AuthType')
    } else {
      authType = _.get(authTypeFromWebAuthTypeTmp, 'authType')
    }

    if (authType && allow.indexOf(authType) === -1) {
      authType = null
    }
    // 当上一次认证类型为来宾且没有开启来宾短信和来宾码时点击会直接通过，这种情况下选择缺省用户认证方式
    if (authType === authTypes.Guest) {
      authType = null
    }

    if (!authType) {
      const openSso = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'SSO.State', 0)) === 1
      const MainAuthMethod = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'SSO.MainAuthMethod', 0)) === 1 // sso认证优先
      if (openSso && MainAuthMethod && !this.isDot1xMode()) {
        authType = 'Sso'
      } else {
        authType = _.get(store.getters.computeServeEntiretyConfig, 'AUTHPARAM.DefaultAuthType', allow[0])
      }
    }

    return authType
  },
  /**
  * 查询当前是否符合自动认证的条件
  * @params cacheAuthConf {Object} 缓存的上次认证的配置信息
  * @params defaultAuthType {String} 计算出来的本次的默认认证方式(这个默认的认证方式也是根据本地缓存的配置计算得来的)
  */
  whetherAllowAutoAuth(cacheAuthConf, defaultAuthType, authData, fromeBootAutoAuthCheck = false) {
    if (authIndex.config.AutoLogin === -1 && !fromeBootAutoAuthCheck) {
      console.log('[auth]不符合自动认证的条件，上次注销了小助手，这次不执行自动登录')
      return false
    }

    if (_.isEmpty(cacheAuthConf) || !_.isObject(cacheAuthConf)) {
      console.log('[auth]不符和自动认证的条件，小助手缓存的认证方式为为空')
      console.log(cacheAuthConf)
      return false
    }

    // 如果默认的认证方式不是User或者Ukey则不自动认证(这种一般都是user或者ukey认证已经在配置文件里面关闭了)
    if (defaultAuthType !== authTypes.UKey && defaultAuthType !== authTypes.User) {
      console.log('[auth]不符和自动认证的条件，默认认证方不为ukey或者user,defaultAuthType=', defaultAuthType)
      return false
    }

    // @todo mac和linux不支持自动认证
    if (defaultAuthType === authTypes.UKey) {
      if (fromeBootAutoAuthCheck) {
        if (!_.get(authData, 'uKeyAutoAuth', false)) {
          console.log('[auth]不符和自动认证的条件,上次没有勾选记住账户')
          return false
        }
      } else {
        if (!_.get(authData, 'uKeyAutoLogin', false)) {
          console.log('[auth]不符和自动认证的条件,上次没有勾选自动登录')
          return false
        }
      }
    }

    // 如果为User认证，还要判断认证服务器(别名认证)是不是仍然可用
    if (defaultAuthType === authTypes.User) {
      const authServerAlias = _.get(store.getters.computeServeEntiretyConfig, 'User.AuthServerAlias', '')
      const authServer = _.get(store.getters.computeServeEntiretyConfig, 'User.AuthServer')
      const authServerList = _.isString(authServer) ? authServer.split('|') : []

      if (!_.isEmpty(authServerAlias) &&
        (_.isEmpty(cacheAuthConf, 'AuthServer', '') || authServerList.indexOf(_.get(cacheAuthConf, 'AuthServer', '')) === -1)) {
        console.log('[auth]不符和自动认证的条件,小助手缓存的认证信息里面不存在AuthServer配置')
        return false
      }

      // 8021.x强制自动认证的，符合上面的条件的可以自动认证了
      if (this.isForceDot1xAuth() && !sessionStorage.getItem('dot1xAuthed')) {
        sessionStorage.setItem('dot1xAuthed', true)
        return true
      }

      // 开启了验证码确认的不能自动登录
      // 8021.xx和零信任不需要校验验证码
      if ((authIndex.isDot1xMode() || _.get(store.state.clientInfo, 'webSlot.isKnockPort')) && parseInt(_.get(store.getters.computeServeEntiretyConfig, 'AUTHPARAM.verifyCode', 0)) === 1) {
        console.log('[auth]不符和自动认证的条件,开启了验证码功能')
        return false
      }

      // 判断上次是否勾选了自动登录，没勾选则不自动登录
      // @todo 需要判断客户端开机自动安检的是不是需要勾选这个

      if (fromeBootAutoAuthCheck) {
        if (!_.get(authData, 'userAutoAuth', false)) {
          console.log('[auth]不符和自动认证的条件,上次没有勾选自动保存用户名')
          return false
        }
      } else {
        if (!_.get(authData, 'userAutoLogin', false)) {
          console.log('[auth]不符和自动认证的条件,上次没有勾选自动登录')
          return false
        }
      }

      // 服务器配置不允许记住用户名和密码的时候不自动登录
      if (parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0)) !== 1 ||
        parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', 0)) !== 1) {
        console.log('[auth]不符和自动认证的条件，ASM服务器没有开启保存账户和密码的开关,config=', _.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK', {}))
        return false
      }

      if (_.isEmpty(authData.userName) || _.isEmpty(authData.password)) {
        console.log('[auth]不符和自动认证的条件，本地保存的账户名和密码为空')
        return false
      }
    }

    return true
  },
  // 开机自动认证的时候强制不做8021.x认证
  noDot1xAutoAuth() {
    const query = qs.parse(location.search.substring(1))
    return parseInt(_.get(query, 'notDoDot1x')) === 1
  },
  /*
  * 是否是8021.x自动认证(也叫8021.x无感知认证(有线切无线自动认证等))
  */
  dot1xAutoAuth() {
    const query = qs.parse(location.search.substring(1))
    return _.get(query, 'ToDo') === 'dot1xAutoAuth' && this.isDot1xMode()
  },

  isForceDot1xAuth() {
    const query = qs.parse(location.search.substring(1))
    return parseInt(_.get(query, 'IsForceAuth')) === 1 && this.isDot1xMode()
  },

  /**
   * 开启用户名密码认证且ad域开启单点登录
   */
  getAdDomainAutoLoginFlag() {
    if (_.indexOf(_.get(store.getters.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType', []), authTypes.User) === -1) {
      return
    }
    const authServerAliasConfig = _.get(store.getters.computeServeEntiretyConfig, 'User.AuthServer', '')
    if (!_.isString(authServerAliasConfig) || _.isEmpty(authServerAliasConfig)) {
      return
    }
    if (authServerAliasConfig.indexOf(authTypes.AdDomain) !== -1) {
      this.config.adDomainAutoLoginFlag = true
    }
  },
  /**
   * 保存url的值
   */
  setUrlValue() {
    let mode = GetUrlParam('isbase64encode')
    this.config.isbase64encode = mode !== '' ? mode : ''
    mode = GetUrlParam('isRecord')
    this.config.isRecord = mode !== '' ? mode : 1
    mode = GetUrlParam('callfrom')
    this.config.callfrom = mode !== '' ? mode : ''
    mode = GetUrlParam('from')
    this.config.from = mode !== '' ? mode : ''
  },
  /**
   * 保存802.1xUrl的参数
   */
  getConfigUrl() {
    const urlParam = []
    if (this.config.isRecord !== '') {
      urlParam['isRecord'] = this.config.isRecord
    }
    if (this.config.callfrom !== '') {
      urlParam['callfrom'] = this.config.callfrom
    }
    if (this.config.from !== '') {
      urlParam['from'] = this.config.from
    }
    return urlParam
  },
  async getIsMustChangePass(user_name) {
    const apiParam = {
      username: Base64.encode(user_name)
    }
    const mustChange = await proxyApi.getUserFlag(apiParam)
    const isMustChangePasswrod = parseInt(_.get(mustChange, 'data.isMustChangePasswrod', 0)) === 1
    if (mustChange.errcode === '0' && isMustChangePasswrod) {
      return true
    }
    return false
  },
  /**
   * 密码加密
   * 802.1x认证时使用Base64
   * 如果RSA加密失败则使用Base64Encode
   * @param password
   * @returns {string|*}
   */
  passwordEncrypt(password) {
    if (this.isDot1xMode()) {
      password = Base64Encode(password)
    } else {
      const crypt = new JSEncrypt()
      const currPass = password
      const pubKey = _.get(store.state.serveEntiretyConfig.server, 'pubKey')
      if (_.isString(pubKey) && !_.isEmpty(pubKey)) {
        crypt.setPublicKey(pubKey)
        password = crypt.encrypt(password)
        password = password || Base64.encode(currPass)
      } else {
        password = Base64.encode(currPass)
      }
    }
    return password
  },
  /**
   * 是否802.1x模式
   * @returns
   */
  isDot1xMode() {
    return parseInt(_.get(store.state.clientInfo.basic, 'AgentMode', 0)) === 1
  },
  getDeviceId() {
    return _.get(store.state.clientInfo.detail, 'DeviceID', '') || _.get(store.state.clientInfo, 'basic.AgentID', 0)
  },
  /**
   * 获取普通认证、802.1x认证对象
   * @returns
   */
  get() {
    if (this.isDot1xMode()) {
      return dot1x
    }
    return common
  },
  /**
   * 处理认证成功
   * @param {*} params
   * @returns
   */
  async authSuccess(params) {
    console.log('数据', store.getters.authInfo)
    store.commit('setGateInfos', { state: 2, gateWayMap: {}, total: 0, VPNStatus: 0 })
    localStorage.setItem('authType', params.authType, 36000)
    const basic = _.get(store, 'getters.authInfo.basic', {})
    await saveToken({ token: basic.Token, tokenTimestamp: basic.TokenTimestamp, deviceId: index.getDeviceId(), UserID: basic.UserID })
    localStorage.setItem('UserID', basic.UserID)
    // vpn 踢下线-袁亦征要加的
    await commonUtil.setLoginRet({ token: '', UserID: '', LoginRet: '0', TokenReNew: params.fromBootAuth ? '1' : '0' })

    const needAuth = _.get(store.getters.authInfo, 'isAuth', true)
    const beforeAuthType = _.get(store.getters.authInfo, 'basic.BeforeAuthType', '')
    const isNeedValidatePassword = beforeAuthType === authTypes.User || beforeAuthType === '' ? 1 : 0
    const autoLogion = _.get(store.getters.computeServeEntiretyConfig, 'ADDOMAINSVR.AutoLogion')

    if (isNeedValidatePassword && !this.config.is_guest && needAuth &&
      (_.isUndefined(autoLogion) || autoLogion !== 1)
    ) {
      // 不是来宾且需要验证的情况
      // 认证成功后验证密码是否合规
      const isChangePassword = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'ChangePassword.IsChangePassword', 0))
      const passWordAuth = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'ChangePassword.PassWordAuth', 0))
      if (isChangePassword && passWordAuth === 1 && params.authType === authTypes.User) {
        // 启用密码修改策略
        if (!this.passPolicy()) {
          return false
        }
      }
    }
    // 重新赋值注册状态，如果开启了自动注册，走完认证之后会修改设备注册状态
    const registered = _.get(store.getters.authInfo, 'basic.Registered')
    if (!_.isUndefined(registered)) {
      store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { detail: { Registered: registered }}))
      store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { detail: { ReRegReason: _.get(store.getters.authInfo, 'basic.ReRegReason') }}))
    }

    // 是否再次注册
    const isAgainReg = parseInt(_.get(store.getters.authInfo, 'basic.IsAgainReg', 0))
    if (isAgainReg === 1 && this.config.DevRegSubmitted !== 1) {
      // 已经提交过一次注册信息（重注册），无需再次填写注册信息
      store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { detail: { Registered: -2 }}))
    }

    if (!_.get(params, 'fromBootAuth', false)) {
      await readClientFile.updateWebAuthConfig(params)
    }

    // 认证成功断开指纹连接
    jas.close()

    // 服务器IP变化，不继续入网，等待重启
    if (authIndex.config.IsServerIPChanged) {
      Message.warning(i18n.t('auth.serverIPChanged'))
      return false
    }
    if (parseInt(_.get(store, 'getters.authInfo.basic.BindAuth', 0)) === 1) {
      return { BindAuth: true }
    }

    // 是否是开机自动认证安检调用
    if (!_.get(params, 'fromBootAuth', false)) {
      await processController.next({ fromAuth: true })
    } else {
      return true
    }
  },
  passPolicy() {
    var authData = _.get(store.state.authInfo, 'basic', {})

    var isPassTest = true, passwordErrText = ''
    if (parseInt(authData['pwdCheckResCode']) === 0) {
      passwordErrText = authData['pwdCheckResMsg']
      isPassTest = false
    }

    passwordErrText = passwordErrText + '，' + i18n.t('auth.pfix')
    if (isPassTest === false) {
      // 弹窗修改密码
      EventBus.$emit('openPassword', '')
      Message.error(passwordErrText)
      return false
    }
    return true
  },
  /**
   * 获取上次认证信息
   */
  async getLastAuthInfo(authConfigFromWebAuthConfigTmp) {
    const authData = {}
    let authType = ''
    const clientSaveName = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0))
    const clientSavePass = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', 0))

    // 格式如下：VXNlcg::###bGl1Ync:###Y3czeFpHNHBjTg::||||GNBAkMTExipXfP
    const authInfo = await readClientFile.WebAuthTypeTmp()

    if (_.isString(authInfo) && authInfo !== '') {
      const authContent = authInfo.split('###')
      authContent[0] = Base64Decode(authContent[0])
      authContent[1] = Base64Decode(authContent[1])
      if (authContent.length < 2 && authContent[0] !== 'UKey') {
        return false
      }

      // 客户端加强加密
      if (authContent[2] && authContent[2].indexOf('||||') !== -1) {
        const nepassw = authContent[2].split('||||')
        if (Base64Decode(nepassw[0]).length === 10) {
          const passwordlength = nepassw[1].length - 10
          nepassw[1] = nepassw[1].substr(5, passwordlength)
          authContent[2] = nepassw[1]
        }
      }

      authContent[2] = authContent.length >= 3 ? Base64Decode(authContent[2]) : ''

      // 如果是AD域修改密码返回这清空密码(估计没用到了，暂时放在这里)
      const changedPwd = GetUrlParam('changedPwd')
      if (!_.isUndefined(changedPwd) && changedPwd === 'true') {
        authContent[2] = ''
      }

      if (authContent[0] === authTypes.User) {
        // 是否记住用户名
        if (clientSaveName === 1) {
          authData.userName = authContent[1]
          authData.userAutoAuth = true
        }

        // 是否记住密码(也就是自动登录)
        if (clientSaveName === 1 && clientSavePass === 1) {
          authData.password = authContent[2]
          // 认证后，又注销的情况下AutoLogin改为-1
          if (parseInt(_.get(authConfigFromWebAuthConfigTmp, 'EnableAutoLogin', 0)) === 1) {
            authData.userAutoLogin = true
          }
        }
      }

      if (authContent[0] === authTypes.UKey) {
        const authTypeTmp = JSON.parse(authContent[1])
        if (authTypeTmp && !_.isEmpty(authTypeTmp)) {
          if (authTypeTmp.user_name) {
            authData.userName = authTypeTmp.user_name
          }
          if (authTypeTmp.password) {
            authData.password = authTypeTmp.password
          }
          if (authTypeTmp.ukeyPin) {
            authData.uKeyPin = authTypeTmp.ukeyPin
          }

          if (authTypeTmp.ukeyCert) {
            // 记录选择证书的默认值,因为证书是异步读取的，不知道用户什么时候插入证书
            authData.uKeyCertDefault = authTypeTmp.ukeyCert
          }

          if (clientSaveName === 1) {
            authData.uKeyAutoAuth = parseInt(authTypeTmp.ukeySaveChecked) === 1
          }

          // 是否记住密码(也就是自动登录)
          if (clientSaveName === 1 && clientSavePass === 1) {
            // 认证后，又注销的情况下AutoLogin改为-1
            if (parseInt(_.get(authConfigFromWebAuthConfigTmp, 'EnableAutoLogin', 0)) === 1) {
              authData.uKeyAutoLogin = parseInt(authTypeTmp.ukeyAutologinChecked) === 1
            }
          }
        }
      }

      authType = authContent[0]
    }
    return {
      authData,
      authType
    }
  },
  async getLastAuthConfig() {
    const authConfigFromWebAuthConfigTmp = await readClientFile.applyWebAuthConfig()
    return authConfigFromWebAuthConfigTmp
  },
  /**
   * 获取设备ID
   * 802.1x模式下 basic 否则 detail
   * @returns
   */
  getDevice() {
    if (this.isDot1xMode()) {
      return _.get(store.state.clientInfo, 'basic.AgentID', 0)
    } else {
      return _.get(store.state.clientInfo, 'detail.DeviceID', 0)
    }
  },
  /**
    * 格式化电话
    * @param tel
    * @returns {string|*}
    */
  formatPhoneNumber(tel) {
    if (_.isString(tel) && tel !== '' && /^1[3456789]\d{9}$/.test(tel)) {
      return '*******' + tel.substr(tel.length - 4)
    }
    return tel
  },

  /**
   * 是否开启来宾认证
   * @returns true 开启来宾认证
   */
  isOpenGuestAuth() {
    const isOnline = _.get(store.state.clientInfo, 'online', true)
    // 802.1x模式不支持来宾
    if (authIndex.isDot1xMode()) {
      return false
    }
    // 敲端口模式且没入网
    if (_.get(store.state, 'clientInfo.webSlot.isKnockPort', false) && !parseInt(_.get(store.state, 'clientInfo.accessStatus.deviceStatus')) && !isOnline) {
      return false
    }
    return true
  },
  isShowGuestRecive() {
    // 来宾账号
    if (_.get(store.state, 'clientInfo.accessStatus.lastAuthType') === 'Guest') {
      return false
    }
    // 场景是否有权限
    const { NetCode, QrCode, IsNeedAppoint, GuestApplySelf, AllowGuestTeam } = _.get(store.state, 'clientInfo.accessStatus', {})

    if (
      parseInt(NetCode) !== 1 && parseInt(QrCode) !== 1 && parseInt(IsNeedAppoint) !== 1 && parseInt(GuestApplySelf) !== 1 && parseInt(AllowGuestTeam) !== 1
    ) {
      return false
    }
    return true
  },
  // 设备绑定用户不允许来宾认证
  isBindUser() {
    return parseInt(_.get(store.state, 'clientInfo.detail.isBindUser', 0)) === 1
  },
  // 当前IP是否在来宾IP段里面(如果在则自动设置为来宾认证)
  whetherInGuestIPRange() {
    if (parseInt(_.get(store.state, 'serveEntiretyConfig.server.GUESTAUTH.GuestIPState')) === 0) { // 没开启ip段配置
      return false
    }
    const guestApplyIP = _.get(store.state, 'serveEntiretyConfig.server.GUESTAUTH.GuestApplyIP') || ''
    const localIP = _.get(store.state, 'clientInfo.basic.Ip', false) || _.get(store.state, 'clientInfo.detail.IP') || ''

    if (guestApplyIP !== '' && guestApplyIP !== 0 && localIP !== '' & localIP !== 0) {
      const validate = guestApplyIP.split(',').some(item => {
        const applyStartIP = item.split('-')[0]
        const applyEndIP = item.split('-')[1]
        if (compareIP(localIP, applyStartIP) > -1 && compareIP(applyEndIP, localIP) > -1) {
          return true
        } else {
          return false
        }
      })
      return validate
    }
    return false
  },
  isOpenSDCLinkAge() {
    const accessStatus = _.get(store.state, 'clientInfo.accessStatus', {})
    if (parseInt(_.get(store.state, 'serveEntiretyConfig.server.sdcState.ItemValue', 0)) === 1 && parseInt(_.get(accessStatus, 'IsOpenSDCLinkage', 0)) === 1 && parseInt(_.get(accessStatus, 'deviceStatus', 0)) === 1) {
      return true
    }
    return false
  },
  // 是否显示资源平台，已授权、已登录、零信任账户则显示
  isOpenZeroTrust() {
    const accessStatus = _.get(store.state, 'clientInfo.accessStatus', {})
    if (!_.get(accessStatus, 'Token', '') || parseInt(_.get(accessStatus, 'SessionStatus', 0)) === 2) {
      return false
    }
    const { token } = readTokenAsync()
    return parseInt(_.get(store.state, 'serveEntiretyConfig.server.ZtpModule', 0)) === 1 && parseInt(_.get(accessStatus, 'ZtpUser.ZtpUser', 0)) === 1 && parseInt(_.get(accessStatus, 'deviceStatus', 0)) === 1 && token
  },
  // 安检中调用还未调用入网状态时，拿不到验证过的Token时使用
  isSecurityZeroTrust() {
    const accessStatus = _.get(store.state, 'clientInfo.accessStatus', {})
    const { token } = readTokenAsync()
    return parseInt(_.get(store.state, 'serveEntiretyConfig.server.ZtpModule', 0)) === 1 && parseInt(_.get(accessStatus, 'ZtpUser.ZtpUser', 0)) === 1 && parseInt(_.get(accessStatus, 'deviceStatus', 0)) === 1 && token
  },
  // 场景默认展示来宾入口
  whetherDefaultGuest() {
    const sceneinfo = _.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo', {})
    const { UserType, DefaultEntry, UserEntry } = sceneinfo
    if (parseInt(UserType) === 2 && (parseInt(UserEntry) !== 1 || parseInt(DefaultEntry) === 2)) {
      return true
    }
    return false
  },
  // 应用对象是来宾默认展示员工
  useGuestDefStaff() {
    const sceneinfo = _.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo', {})
    const { UserType, DefaultEntry, UserEntry } = sceneinfo
    if (parseInt(UserType) === 2 && parseInt(UserEntry) === 1 && parseInt(DefaultEntry) === 1) {
      return true
    }
    return false
  }
}

export default index
