import { i18n } from '@/render/lang'
const regular = {
  // 首尾是否有空格
  flEmpty: (value) => {
    const reg = /^[\s]|[ ]$/gi
    return reg.test(value)
  },
  // 字符串长度范围校验
  lenRange: (str, min, max) => {
    str += ''
    if (str.length >= min && str.length <= max) {
      return true
    } else {
      return false
    }
  },
  // 是否包含指定字符
  hasChars: (str, arr) => {
    str += ''
    for (var i = 0; i < arr.length; i++) {
      if (str.indexOf(arr[i]) > -1) {
        return true
      }
    }
    return false
  },
  // 是否包含重复数字和字母
  haveRepeat: (str) => {
    return /([a-zA-Z0-9])\1/.test(str)
  },
  // 包含数字和字母且必须两个同时存在
  numAndChart: (str) => {
    const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{2,51}$/
    return reg.test(str)
  },
  testPassword: (str) => {
    const reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{3,51}$/
    return reg.test(str)
  },
  // 必须由大写字母、小写字母、数字、特殊字符中三者组合
  threeTypePass: (passVal) => {
    const nun = /[0-9]+/
    const b_en = /[A-Z]+/
    const s_en = /[a-z]+/
    const p_title = /[#.,\-_;%@\$\*~!^&()+\{\}:"<>?`=[\]'/\\]+/
    let isSetNub = 0
    if (nun.test(passVal)) {
      isSetNub = isSetNub + 1
    }
    if (b_en.test(passVal)) {
      isSetNub = isSetNub + 1
    }
    if (s_en.test(passVal)) {
      isSetNub = isSetNub + 1
    }
    if (p_title.test(passVal)) {
      isSetNub = isSetNub + 1
    }
    return isSetNub > 2
  },
  rules: {
    'Default': /^[^<>%\*'"&]{1,50}$/, /* 不能包含<>%*'"&号且长度不超过50位*/
    'Tel': /^1(3|4|5|6|7|8|9)\d{9}$/,
    'Email': /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
    'IdCard': /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    'HKMIdCard': /^([A-Z]\d{6,10}(\(\w{1}\))?)$/,
    'TaiwanIdCard': /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/,
    'Number': /^[0-9]*$/,
    'Letter': /^[A-Za-z]+$/,
    'Chinese': /^[\u4e00-\u9fa5]{0,}$/,
    'accountTitle': /^[A-Za-z\s\u4e00-\u9fa5-_*\.@\d]{1,50}$/,
    'looseTel': /^[-()+*#（） \d]{0,25}$/,
    '1-999Num': /^[1-9][0-9]{0,2}$/,
    '1-100Num': /^([1-9][0-9]{0,1}|100)$/,
    'windowsAccount': /^[^/\\"\[\]:\|<>+=;,?\*@]{1,50}$/ /* 不能包含\/"[]:|<>+=;,?*@长度不超过50 */
  },
  errorTipMap: () => ({
    'Default': i18n.t('reg.Default'),
    'Tel': i18n.t('reg.Tel'),
    'Email': i18n.t('reg.Email'),
    'IdCard': i18n.t('reg.IdCard'),
    'HKMIdCard': i18n.t('reg.HKMIdCard'),
    'TaiwanIdCard': i18n.t('reg.TaiwanIdCard'),
    'Number': i18n.t('reg.Number'),
    'Letter': i18n.t('reg.Letter'),
    'Chinese': i18n.t('reg.Chinese'),
    'accountTitle': i18n.t('rulesLangObj.accountTitle'),
    'looseTel': i18n.t('reg.Tel'),
    '1-999Num': i18n.t('reg.1-999Num'),
    '1-100Num': i18n.t('reg.1-100Num'),
    'onlySpace': i18n.t('reg.space')
  }),
  phone: (value) => {
    if (!value) {
      return false
    }
    var reg = /^[0-9]*$/
    if (!reg.test(value)) {
      return false
    }
    return true
  },
  // 宽松规则手机号校验支持区号
  looseMobile: (mobile) => {
    if (!mobile) {
      return false
    }
    var reg = /^[-()+*#（） \d]{0,25}$/
    if (!reg.test(mobile)) {
      return false
    }
    return true
  },
  isIPV6: function(ip) {
    if (!ip) {
      return false
    }

    const reg = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/
    if (!reg.test(ip)) {
      return false
    }

    return true
  }

}
export default regular
