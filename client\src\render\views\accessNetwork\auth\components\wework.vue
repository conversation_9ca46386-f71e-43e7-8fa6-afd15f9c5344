<template>
  <div id="wework_qrcode_login" :class="['u-wework-qr-img', authData.authFrom === 'addition' ? 'addition-style': '' ]" />
</template>
<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import proxyAjax from '@/service/utils/proxyAjax'
import proxyApi from '@/service/api/proxyApi'
import qrCodeCommon from '@/render/utils/auth/qrCodeCommon'
import qs from 'qs'
import { EventBus } from '@/render/eventBus'

export default {
  props: {
    isQrcode: {
      type: Boolean,
      default: false
    },
    authData: { // 双因子认证时有值
      type: Object,
      default: function() {
        return {}
      }
    },
    bindData: { // 主账号绑定时有值
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      weWorkUrl: 'https://open.work.weixin.qq.com/wwopen/sso/qrConnect',
      username: '',
      password: '',
      time: '',
      ServerProxy: false,
      redHost: ''
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'authInfo']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID', 0)
    }
  },
  watch: {
    isQrcode: function(value) {
      if (value) {
        this.drawQrCode()
      }
    }
  },
  mounted() {
    this.drawQrCode()
    EventBus.$on('revoke:refresh', this.drawQrCode)
  },
  beforeDestroy() {
    EventBus.$off('revoke:refresh', this.drawQrCode)
    if (typeof window.addEventListener !== 'undefined') {
      window.removeEventListener('message', this.handleMessage, false)
    } else if (typeof window.attachEvent !== 'undefined') {
      window.detachEvent('onmessage', this.handleMessage)
    }
  },
  methods: {
    // 绘制来宾二维码
    drawQrCode() {
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      this.time = new Date().getTime()
      const authData = this.authData
      const appid = authData.CorpID || _.get(this.serveEntiretyConfig, 'server.WeWorkConfig.corpId', '')
      const agentid = authData.AgentID || _.get(this.serveEntiretyConfig, 'server.WeWorkConfig.agentid', '')
      this.ServerProxy = authData.ServerProxy || _.get(this.serveEntiretyConfig, 'server.WeWorkConfig.ServerProxy', true)
      const urlParam = {
        deviceid: this.deviceid,
        time: this.time
      }
      const redHost = qrCodeCommon.serverHost() || _.get(this.serveEntiretyConfig, 'server.ControlUrl')
      if (this.ServerProxy) {
        this.weWorkUrl = redHost + '/wwopen/sso/qrConnect'
      }
      this.redHost = redHost
      const url = encodeURIComponent(redHost +
          proxyAjax.formatUrl('wework/user?' + qs.stringify(urlParam, { encode: false })))
      document.getElementById('wework_qrcode_login').innerHTML = ''
      /* href参数用于修改企业微信二维码样式，但是采用https嵌入会引起浏览器证书告警 */
      // 注意 此处企业微信要求必须https的css文件才能修改样式
      // const href = encodeURIComponent('https://' + host + '/a/css/wework.css')
      this.WWLogin({
        id: 'wework_qrcode_login',
        appid: appid,
        agentid: agentid,
        redirect_uri: url,
        state: '',
        href: '',
        urlid: 'wework'
      })

      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', this.handleMessage, false)
      } else if (typeof window.attachEvent !== 'undefined') {
        window.attachEvent('onmessage', this.handleMessage)
      }
    },
    /**
     * 处理企业微信返回
     */
    async handleMessage(event) {
      console.log('handleMessage')
      const origin = event.origin

      if (origin.indexOf('work.weixin.qq.com') > -1 || this.ServerProxy) {
        const result = await proxyAjax.post(event.data)
        // 成功才处理
        if (parseInt(result.errcode) === 0) {
          this.qrCheck()
        }
      }
    },
    /**
     * 查询扫码结果
     */
    async qrCheck() {
      const apiParam = {
        deviceid: this.deviceid,
        action: 'check',
        time: this.time
      }
      const result = await proxyApi.getWeWorkUser(apiParam, qrCodeCommon.serverHost())
      if (_.get(result, 'data.state') === false) {
        this.$message.warning(_.get(result, 'data.message'), result.errmsg)
        this.drawQrCode()
      } else if (_.get(result, 'data.state') === true) {
        // 扫描成功
        this.username = result.data.username
        this.password = result.data.token
        this.submitForm()
      } else {
        this.$message.warning(this.$t('interfaceErr'))
        this.drawQrCode()
      }
    },
    async confirmBind(type) {
      return new Promise((resolve, reject) => {
        this.$emit('bind', { resolve, type })
      })
    },
    /**
     * 提交（支持撤销登录）
     */
    async submitForm() {
      const params = {
        guestType: 'weworkqrlogin',
        username: this.username,
        password: this.password,
        isQrcode: this.isQrcode
      }
      if (this.authData.isTwoFactors) { // 双因子
        params.factorType = 'WeWork'
        const notBind = _.get(this.authInfo, 'basic.User.FactorAuthRelation.WeWork') === false
        let autoBind = false
        if (notBind) {
          autoBind = await this.confirmBind('wework')
        }
        if (autoBind !== false) {
          params.autoBind = autoBind
        }
      }
      const res = await qrCodeCommon.auth({ ...params, ...this.authData, ...this.bindData })
      console.log(res)
      if (res) {
        if (_.isObject(res) && _.get(res, 'revoke', false) === true) {
          this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
          return
        }
        if (this.authData.isTwoFactors) { // 双因子认证
          this.$emit('towFactorSuccess')
          return
        }
        this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      } else {
        this.drawQrCode()
      }
    },
    WWLogin(c) {
      const weWorkUrl = this.weWorkUrl
      const d = document.createElement('iframe')
      const timestamp = new Date().getTime()
      let e = weWorkUrl +
        '?appid=' + c.appid +
        '&agentid=' + c.agentid +
        '&redirect_uri=' + c.redirect_uri +
        '&state=' + c.state +
        '&login_type=jssdk' +
        '&t=' + timestamp

      e += c.style ? '&style=' + c.style : ''
      e += c.href ? '&href=' + c.href : ''
      d.src = e
      d.frameBorder = '0'
      d.allowTransparency = 'true'
      d.scrolling = 'no'
      d.width = '300px'
      d.height = '400px'
      var f = document.getElementById(c.id)
      f.innerHTML = ''
      f.appendChild(d)
      d.onload = function() {
        d.contentWindow.postMessage && d.contentWindow.postMessage('ask_usePostMessage', '*')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.u-wework-qr-img{
  padding-top: 15px;
}
.addition-style{
  padding-top: 24px;
  text-align: center;
}
</style>
