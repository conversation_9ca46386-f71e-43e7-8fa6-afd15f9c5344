<!--团队接待-->
<template>
  <div id="team-receive-page" @keydown="keyDown">
    <div class="form-wrapper">
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-width="14px"
        class="guest-apply-form"
        :validate-on-rule-change="false"
      >
        <!--input输入框-->
        <el-form-item prop="TeamName">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_70')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_70') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-input
            id="ui-guest-receive-team-input-team_name"
            v-model="form.TeamName"
            :placeholder="$t('guestAuth.guest.info_77')"
          >
            <i slot="prefix" class="iconfont icon-laibinrenzheng" />
          </el-input>
        </el-form-item>
        <el-form-item prop="MaxNumber">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_41')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_41') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-input
            id="ui-guest-receive-team-input-recept_number"
            v-model="form.MaxNumber"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_41')"
            class="hour-input"
          >
            <i slot="prefix" class="iconfont icon-laibinrenzheng" />
          </el-input>
          <span class="input-info">{{ $t('guestAuth.guest.info_45') }}</span>
        </el-form-item>
        <el-form-item prop="GuestStartTime">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_42')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_42') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-date-picker
            id="ui-guest-receive-team-date-timetable"
            v-model="form.GuestStartTime"
            type="daterange"
            value-format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            :range-separator="$t('guestAuth.guest.info_48')"
            :start-placeholder="$t('guestAuth.guest.info_49')"
            :end-placeholder="$t('guestAuth.guest.info_50')"
            :picker-options="pickerOptions"
            prefix-icon="iconfont icon-shangwangshichang"
          />
        </el-form-item>
        <el-form-item prop="AllowTime">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_43')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_43') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-input
            id="ui-guest-receive-team-input-network_time"
            v-model="form.AllowTime"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_43')"
            class="hour-input"
          >
            <i slot="prefix" class="iconfont icon-shangwangshichang" />
          </el-input>
          <span class="input-info">{{ $t('guestAuth.guest.info_46') }}</span>
        </el-form-item>
        <el-form-item
          prop="AllowRegionIDs"
          class="select-form-box"
        >
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_44')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_44') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <i class="iconfont icon-wangluoyu" />
          <el-select
            id="ui-guest-receive-team-select-network_domain"
            v-model="form.AllowRegionIDs"
            v-title="true"
            clearable
            :placeholder="$t('guestAuth.guest.info_47')+$t('guestAuth.guest.info_44')"
            multiple
            class="g-mult-se"
            collapse-tags
          >
            <el-option v-for="item in propRegions" :id="`ui-guest-receive-team-option-network_${item.id}`" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="submit-wrapper">
      <p id="ui-guest-receive-team-p-submit" class="public-btn" @click="submitForm()"><i v-if="loading" class="el-icon-loading" />{{ $t('reg.submit') }}</p>
    </div>

  </div>
</template>
<script>
import { mapState } from 'vuex'
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'
import guestFormUtils from '@/render/utils/bussiness/guestFormUtils'
export default {
  directives: {
    title: {
      inserted: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      },
      componentUpdated: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      }

    }
  },
  props: {
    propRegions: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      form: {
        AllowRegionIDs: [],
        TeamName: '',
        AllowTime: 8,
        MaxNumber: 1,
        GuestStartTime: ''
      },
      rules: {},
      loading: false,
      pickerOptions: {
        disabledDate(time) {
          const oneDay = 60 * 60 * 24 * 1000
          return time.getTime() < Date.now() - oneDay
        }
      }
    }
  },
  computed: {
    ...mapState(['clientInfo'])
  },
  watch: {
    '$i18n.locale': function() {
      this.getRules()
      this.$nextTick(() => {
        this.$refs['ruleForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['ruleForm'].validateField(item.labelFor)
          }
        })
      })
    }
  },
  mounted() {
    this.getRules()
    this.initFormVal()
  },
  methods: {
    addTitle(el) {
      this.$nextTick(() => {
        const tags = el.querySelectorAll('.el-tag')
        for (let i = 0; i < tags.length; i++) {
          tags[i].title = tags[i].querySelector('.el-select__tags-text').innerHTML
        }
      })
    },
    getRules() {
      var validateName = (rule, value, callback) => {
        if (value && !regular.rules.Default.test(value)) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_70')} ] ${regular.errorTipMap().Default}`))
        } else {
          callback()
        }
      }
      var validateRegion = (rule, value, callback) => {
        if (value && value.length === 0) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_44')} ] ${this.$t('auth.emptyErr')} `))
        } else {
          callback()
        }
      }
      var validateTime = (rule, value, callback) => {
        if (value && !regular.rules['1-999Num'].test(value)) {
          callback(new Error(`[${this.$t('guestAuth.guest.info_43')}] ${this.$t('changeRegInfo.styleErro')} ${regular.errorTipMap()['1-999Num']}`))
        } else {
          callback()
        }
      }
      var validateNumber = (rule, value, callback) => { // 1-100
        if (value && (!regular.rules['1-100Num'].test(value) || value > 100)) {
          callback(new Error(`[${this.$t('guestAuth.guest.info_41')}] ${this.$t('changeRegInfo.styleErro')} ${regular.errorTipMap()['1-100Num']}`))
        } else {
          callback()
        }
      }

      this.rules = {
        TeamName: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_70')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }
        ],
        AllowTime: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_43')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateTime, trigger: 'blur' }
        ],
        MaxNumber: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_41')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateNumber, trigger: 'blur' }
        ],
        GuestStartTime: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_42')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' }
        ],
        AllowRegionIDs: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_44')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateRegion, trigger: 'change' }
        ]
      }
    },
    initFormVal() {
      const { propRegions } = this
      const newForm = {
        AllowRegionIDs: [],
        TeamName: '',
        AllowTime: 8,
        MaxNumber: 1,
        GuestStartTime: ''
      }
      let AllowRegionIDs = []
      const defaultRegion = propRegions[0].DefRoleID
      if (defaultRegion) {
        const defaultItem = propRegions.find(item => item.RID === defaultRegion)
        AllowRegionIDs = defaultItem ? [defaultRegion] : []
      }
      newForm.AllowRegionIDs = AllowRegionIDs
      this.form = newForm
    },
    async submitForm() {
      this.$refs['ruleForm'].validate(async(valid, obj) => {
        if (valid) {
          const params = guestFormUtils.formateFormParams(this.form)
          console.log()
          const { UserID } = _.get(this.clientInfo, 'accessStatus')
          const { DeviceID } = _.get(this.clientInfo, 'detail')
          const fixedParams = {
            deviceId: DeviceID,
            UserID
          }
          this.loading = true
          const ret = await proxyApi.reqBatchNetcode({ ...params, ...fixedParams })
          this.loading = false
          if (parseInt(ret.errcode) === 0) {
            this.$emit('applySuccess', {
              type: 'team',
              data: ret.data
            })
          }
        } else {
          // 滚动到校验不通过项位置
        }
      })
    },
    // 回车提交
    keyDown(event) {
      if (parseInt(event.keyCode) !== 13 || event.srcElement.type === 'textarea') {
        return
      }
      // 非加载中
      if (!this.loading) {
        this.submitForm()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
    #team-receive-page {
        width: calc(100% + 48px);
        margin: 0 auto;
        height: 100%;
        margin-right: -24px;
        margin-left: -24px;
        .el-form-item:last-child{
            margin-bottom: 0;
        }
        ::v-deep .el-form-item__label{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: $title-color;
        }
        ::v-deep .el-form-item__content{
            width: 360px;
            text-align: left;
            .el-radio{
                line-height: 40px;
            }
            .el-select{
                .el-input__inner{
                    padding-left: 15px;
                }
                .el-select__tags > span{
                    display: flex;
                }
            }
            .el-range-editor.el-input__inner{
                width: 100%;
                .el-input__icon{
                  margin-left: 0;
                  color: $disabled-color;
                }
            }
            .el-range-separator{
                box-sizing: content-box;
            }
            // .hour-input{
            //     width: 320px;
            //     margin-right: 10px;
            // }
            .input-info{
              position: absolute;
              right: 15px;
              color: $title-color;
            }
            .el-range-input{
                line-height: 32px;
                color: $title-color;
            }
        }
        .form-wrapper{
            max-height: calc(100% - 77px);
            padding: 0 16px;
            overflow: auto;
            // &::-webkit-scrollbar{
            //   width: 8px !important;
            //   height: 4px !important;
            // }
            &::-webkit-scrollbar-track{
              background-color: #fff;
            }
            &::-webkit-scrollbar-button{
              background-color: #fff;
            } /* 滑轨两头的监听按钮颜色 */
            &::-webkit-scrollbar-corner{
              background-color: #fff;
            }
            .guest-apply-form{
              width: 380px;
              margin: 0 auto;
            }
        }
        .submit-wrapper{
            padding-top: 24px;
            text-align: center;
            padding-left: 0;
            display: flex;
            justify-content: center;
            button{
                width: 360px;
            }
        }
        ::v-deep .g-mult-se{
          .el-tag{
            max-width: 100px;
          }
        }
}
</style>
