
import Auth from './auth'
import { Message } from 'element-ui'
import store from '@/render/store'
import authIndex from '@/render/utils/auth/index'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import proxyApi from '@/service/api/proxyApi'
import common from './common'
import { Base64Encode, GetUrlParam } from '@/render/utils/global'
import dot1xCommon from './dot1x'
import authTypes from './authTypes'
import { i18n } from '@/render/lang'
import md5 from 'js-md5'
import { EventBus } from '@/render/eventBus'
import { isMac } from '../os_browser_info'
import { saveToken } from '@/render/utils/token'
import commonUtil from '@/render/utils/bussiness/commonUtil'

class Account extends Auth {
  constructor() {
    super()
    this.type = authTypes.User
  }

  /**
   * 普通认证
   * @returns {Promise<boolean>}
   */
  async common(params, noAuthEnd = false) {
    const apiParam = {
      user_name: Base64Encode(params.userName),
      deviceid: this.getDeviceId(),
      idp: Base64Encode(GetUrlParam('idp')),
      tokenId: Base64Encode(GetUrlParam('tokenId')),
      remoteIp: GetUrlParam('remoteIp'),
      password: authIndex.passwordEncrypt(params.password),
      hintOver: G_VARIABLE.g_hintOver,
      autoAuth: 0,
      verify_code: params.verifyCode || '',
      type: this.type
    }

    if (params.authFrom === 'bind') { // 账号绑定
      apiParam.authFrom = params.authFrom
      apiParam.bindAuthServer = params.bindAuthServer
      apiParam.userid = params.userid
    }

    if (_.get(store, 'state.clientInfo.webSlot.isKnockPort')) { // noAuthCode=1可以不传图形验证码
      apiParam.noAuthCode = 1
    }

    if (_.isString(params.authType) && params.authType !== '' && params.authType !== this.type) {
      apiParam['authserver'] = Base64Encode(params.authType)
    }

    if (authIndex.config.isRecord !== '') {
      apiParam['isRecord'] = authIndex.config.isRecord
    }
    if (authIndex.config.callfrom !== '') {
      apiParam['callfrom'] = authIndex.config.callfrom
    }
    if (authIndex.config.from !== '') {
      apiParam['from'] = authIndex.config.from
    }

    if (!_.isEmpty(_.get(params, 'callfrom', ''))) {
      apiParam['callfrom'] = params.callfrom
    }

    // mac的8021.x的第二次普通认证。不记录认证提交记录【否则会产生两条记录】
    if (params.NoRecordLog === 1) {
      apiParam['isRecord'] = 0
      apiParam['NoRecordLog'] = 1
    }

    // 8021.x无线双因子认证，绕过处理
    if (params.noFactorAuth === 1) {
      apiParam['noFactorAuth'] = 1
    }

    const fakeIsInDomain = await common.isDomainNoFake()
    if (fakeIsInDomain !== false) {
      apiParam.fakeIsInDomain = Base64Encode(fakeIsInDomain)
    }

    const res = await proxyApi.authIndex(apiParam, { showError: false })
    if (parseInt(_.get(res, 'errcode', -1)) === 0) {
      res.data.passwordMd5 = md5(params.password)
      store.commit('setAuthInfo', _.merge(
        {},
        store.state.authInfo,
        { basic: res.data }
      ))
      const token = _.get(res, 'data.Token')
      if (token) {
        saveToken({ token, tokenTimestamp: _.get(res, 'data.TokenTimestamp'), deviceId: this.getDeviceId(), UserID: _.get(res, 'data.UserID', '') })
      }

      // mac的8021.x认证需要走一次普通认证，但是不需再请求authEnd了
      if (!noAuthEnd) {
        await common.authEnd({
          type: this.type,
          password: params.password,
          autoAuth: this.isAutoAuth({
            ...params,
            FactorAuth: parseInt(_.get(store.state.authInfo, 'basic.FactorAuth')) === 1
          }) ? '1' : '0'
        })
      }

      Message.success(res.errmsg)
    }
    return res
  }

  /**
   * 802.1x认证
   * @returns {Promise<boolean>}
   */
  async dot1x(params) {
    // 是否别名
    const isUseAlias = _.isString(params.authType) && params.authType !== '' && params.authType !== this.type

    const apiParam = {
      AuthParam: {
        AccessNetwork: params.accessNetwork,
        IsWireLess: params.isWireLess,
        AuthType: this.type,
        ServerUseAlias: isUseAlias ? '1' : '0',
        ServerInsideName: isUseAlias ? Base64Encode(params.authType) : '',
        UserName: params.userName,
        Password: authIndex.passwordEncrypt(params.password)
      }
    }

    const res = await dot1xCommon.auth(apiParam)
    const result = parseInt(_.get(res, 'ASM.Result', -1))
    if (result !== 0) {
      // 认证失败，关闭双因子输入框
      EventBus.$emit('TwoFactors:show', false, true)

      const message = _.get(res, 'ASM.Message', i18n.t('auth.dot1xAuthFail'))
      switch (result) {
        // 修改密码
        case 21133017:
          EventBus.$emit('openPassword', {
            username: params.userName,
            msg: message
          })
          break
        default:
          Message.error(message)
      }
      return false
    }

    const authResponse = _.get(res, 'ASM.AuthResponse', '')
    let authResponseJson = dot1xCommon.handleAuthResponse(authResponse)

    // MAC 无线需要走再走普通认证
    if (this.isMacWirelessEnv(params)) {
      commonUtil.detail()
      authResponseJson = await this.macWirelessNoramlAuth(params)
    } else {
      store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: authResponseJson }))
      if (authResponseJson === false) {
        return false
      }
    }

    await dot1xCommon.authEnd({
      type: this.type,
      authResponse: false,
      autoAuth: this.isAutoAuth({
        ...params,
        // 正常情况认证，这里返回ture, false.8021.x认证返回'0'或者'1'
        FactorAuth: parseInt(_.get(store.state.authInfo, 'basic.FactorAuth')) === 1
      }) ? '1' : '0'
    })

    return authResponseJson
  }

  // 是否是mac的8021.x无线环境
  isMacWirelessEnv(params) {
    if (isMac() && parseInt(params.isWireLess) === 1) {
      store.commit('setAuthInfo', _.merge({}, store.state.authInfo, { dot1x: { isMacWireLessAuth: true }}))
      return true
    }

    store.commit('setAuthInfo', _.merge({}, store.state.authInfo, { dot1x: { isMacWireLessAuth: false }}))
    return false
  }

  // MAC 无线需要走再走普通认证(mac的无线8021.x认证走得是普通的协议，非自主协议。因此还需要在走一遍普通认证)
  async macWirelessNoramlAuth(params) {
    store.commit('setAuthInfo', _.merge({}, store.state.authInfo, { dot1x: { isMacWireLessAuth: true }}))
    params['NoRecordLog'] = 1
    params['callfrom'] = '8021X'
    params['noFactorAuth'] = 1
    return await this.common(params, true)
  }

  /**
   * 是否自动认证
   * 1.勾选记住账户
   * 2.未开启验证码
   * 3.允许记住账户、密码
   * 4.未开启双因子
   * @returns bool
   */
  isAutoAuth({ autoAuth, isVerifyCode, FactorAuth }) {
    return autoAuth &&
        isVerifyCode === false &&
        parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0)) === 1 &&
        parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', 0)) === 1 &&
        !FactorAuth
  }
}

export default Account
