
import Auth from './auth'
import { Message } from 'element-ui'
import store from '@/render/store'
import proxyApi from '@/service/api/proxyApi'
import common from './common'
import _ from 'lodash'
import dot1xCommon from '@/render/utils/auth/dot1x'
import authTypes from './authTypes'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { i18n } from '@/render/lang'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'
import { TestQtModule } from '@/render/utils/global'

class Mobile extends Auth {
  constructor() {
    super()
    this.type = authTypes.Mobile
  }
  // 发送短信
  async smsSend(params) {
    if (this.isDot1xAuth()) {
      return this.dot1xSmsSend(params)
    } else if (_.get(store, 'state.clientInfo.webSlot.isKnockPort') && TestQtModule('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer')) { // 敲端口模式
      return commonUtil.knockPortSms(params)
    }
    return this.commonSmsSend(params)
  }

  async dot1xSmsSend(params) {
    const apiParam = {
      AuthParam: {
        AccessNetwork: params.accessNetwork,
        IsWireLess: params.isWireLess,
        AuthType: this.type,
        AuthPhase: '1',
        Phone: params.phone
      }
    }
    const res = await dot1xCommon.auth(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', '失败'))
      return false
    }
    return true
  }

  async commonSmsSend(params) {
    const apiParam = {
      mobile_phone: params.phone,
      deviceid: _.get(store.state.clientInfo.detail, 'DeviceID', 0),
      isGuestAuth: _.get(params, 'isGuestAuth', 0)
    }
    // 双因子
    if (params.twoFactor === true) {
      apiParam.userid = _.get(store.state.authInfo.basic, 'ID', 0)
      apiParam.username = _.get(store.state.authInfo.basic, 'UserName', '')
    }
    const res = await proxyApi.smsSend(apiParam)
    if (parseInt(_.get(res, 'errcode', -1)) === 0) {
      Message.success(res.errmsg)
      return true
    }
    return false
  }

  /**
   * 普通
   * @returns {Promise<boolean>}
   */
  async common(params) {
    const apiParam = {
      mobile_phone: params.phone,
      deviceid: this.getDeviceId(),
      check_code: params.checkCode,
      hintOver: G_VARIABLE.g_hintOver,
      type: this.type
    }
    if (params.authFrom) {
      apiParam.authFrom = params.authFrom
    }
    const res = await proxyApi.authIndex(apiParam, { showError: false })

    const errorCode = parseInt(_.get(res, 'errcode', -1))
    if (errorCode === 0) {
      if (params.authFrom !== 'addition') { // 强化认证不需要存数据等后续操作
        store.commit('setAuthInfo', { ...store.state.authInfo, ...{ basic: res.data }})

        await common.authEnd({
          type: this.type,
          password: apiParam.password
        })

        Message.success(res.errmsg)
      }

      return res.data
    }
    switch (errorCode) {
      case 21120030:
        // 超出最大可登录设备数
        return { revoke: true, data: {
          msg: res.errmsg,
          ...res.data
        }}
      default:
        Message.error(res.errmsg, i18n.t('auth.dot1xAuthFail'))
        return false
    }
  }

  /**
   * 802.1x
   * @returns {Promise<boolean>}
   */
  async dot1x(params) {
    const apiParam = {
      AuthParam: {
        AccessNetwork: params.accessNetwork,
        IsWireLess: params.isWireLess,
        AuthType: this.type,
        AuthPhase: '2',
        Phone: params.phone,
        CheckCode: params.checkCode
      }
    }
    const res = await dot1xCommon.auth(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', '失败'))
      return false
    }

    const authResponse = _.get(res, 'ASM.AuthResponse', '')
    const authResponseJson = dot1xCommon.handleAuthResponse(authResponse)
    store.commit('setAuthInfo', { ...store.state.authInfo, ...{ basic: authResponseJson }})

    await dot1xCommon.authEnd({
      type: this.type,
      authResponse: false
    })
    return authResponseJson
  }
}

export default Mobile
