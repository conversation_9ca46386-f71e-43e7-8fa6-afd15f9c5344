const { app, crashReporter } = require('electron')
const path = require('path')
const fs = require('fs')
const log = require('electron-log/main')
const { queryArgv } = require('../../../utils/argv')

require('../../../utils/env')

const beforeCreate = async() => {
  const logDirectory = initLogConf()
  errCatch(log, logDirectory)
  // 单例模式的运行
  const argv = pareseAgs()
  singleInstanceLock(argv)

  // 是否关闭硬件加速。这个必须在"BrowserWindow"实例化之前调用
  disableGPU(argv)

  return { log, argv }
}

// 在electron的安装目录的Log下写日志文件
const initLogConf = () => {
  // 获取应用程序的安装目录
  let installDirectory = path.dirname(app.getPath('exe'))
  // 因为windows平台的权限问题,存放目录放到Users目录下
  if (process.platform === 'win32') {
    installDirectory = app.getPath('userData')
  }

  const logDirectory = path.join(installDirectory, 'Log')
  // 创建日志目录（如果不存在）
  console.log(logDirectory)
  try {
    if (!fs.existsSync(logDirectory)) {
      fs.mkdirSync(logDirectory)
    }
  } catch (error) {
    console.error(error)
  }

  if (log.transports.file && log.transports.file.resolvePathFn) {
    log.transports.file.level = 'info'
    log.transports.file.resolvePathFn = () => {
      return path.join(logDirectory, `${new Date().toISOString().slice(0, 10)}.log`)
    }
  }

  return logDirectory
}

const errCatch = (log, logDirectory) => {
  const ignoreErrorMessage = [
    'Possible side-effect in debug-evaluate',
    'Unexpected end of input'
  ]

  process.on('uncaughtException', err => {
    const message = err.message || ''
    if (ignoreErrorMessage.includes(message)) {
      return
    }
    log.error('捕获错误,类型=uncaughtException:')
    log.error(err)

    app.quit()
  })

  process.on('unhandledRejection', (reason, p) => {
    log.error('捕获错误,类型=unhandledRejection:')
    log.error('Unhandled Rejection at: Promise ', p)
    log.error(' reason: ', reason)

    app.quit()
  })

  // 奔溃错误日志记录
  crashReporter.start({
    productName: 'AssUI',
    companyName: 'infogo.com.cn',
    uploadToServer: false,
    ignoreSystemCrashHandler: true,
    submitURL: '',
    extra: {
      someExtraData: 'Custom data'
    },
    crashesDirectory: path.join(logDirectory, 'crash_logs')
  })
}

// 单例模式锁定
const singleInstanceLock = (argv) => {
  // 多进程模式下不去判断这个进程锁,因此多进程模式下一定要自己控制好任务的退出
  const supportMutilProcess = argv['supportMutilProcess'] || false
  if (supportMutilProcess) {
    return true
  }

  const gotTheLock = app.requestSingleInstanceLock()
  if (!gotTheLock) {
    app.quit()
    throw new Error('单例锁锁定失败!')
  }
}

// 获取所有的命令行参数
const pareseAgs = () => {
  const disableGPU = queryArgv('disableGPU') // 关闭硬件加速
  const target = queryArgv('target') // 资源文件目录 +/index.html
  const devTools = queryArgv('devTools') // 调试工具
  const supportMutilProcess = queryArgv('SupportMutilProcess') // 多进程
  const hideWindow = queryArgv('HideWindow') // 隐藏窗口
  const width = queryArgv('Width', 'number')
  const height = queryArgv('Height', 'number')
  const position = queryArgv('Position')
  const title = queryArgv('Title')
  const frame = queryArgv('Frame')
  const mainWindow = queryArgv('MainWindow')

  return { disableGPU, target, devTools, supportMutilProcess, hideWindow, width, height, title, position, frame, mainWindow }
}

const disableGPU = ({ disableGPU }) => {
  if (disableGPU) {
    app.disableHardwareAcceleration()
  }
}

module.exports = beforeCreate
