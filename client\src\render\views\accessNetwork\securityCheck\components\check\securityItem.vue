<!-- 安检项组件 -->
<template>
  <div ref="itemTabWrapper" class="item-tab-wrap">
    <div class="item-tab-main">
      <el-row
        v-for="(checkModel, ind) in checkResultList"
        :key="ind"
        class="item-box"
      >
        <el-col
          v-if="checkModel.children && checkModel.children.length > 0"
          class="item"
        >
          <!-- classResultType 1安检项不通过-->
          <div v-if="checkModel.classResultType === 1" id="ui-check-detail-div-check_fault" class="title" @click="checkModel.showChildren = !checkModel.showChildren">
            <div class="left-wrapper">
              <i class="iconfont icon-guanjianxiangbuhegui err" />
              <span class="result-text">
                {{ $t("accessNetwork.securityCheck.info_10") }}
              </span>
              <span class="result-count">
                {{ $t("check.total")
                }}<b class="err">{{ checkModel.children.length }}</b>{{ $t("check.item") }}
              </span>
            </div>
            <span
              class="icon-box"
            >
              <i
                :class="
                  checkModel.showChildren
                    ? 'el-icon-arrow-down'
                    : 'el-icon-arrow-right'
                "
              />
            </span>
          </div>
          <!-- 安检项存在隐患 -->
          <div v-else-if="checkModel.classResultType === 2" id="ui-check-detail-div-check_false" class="title" @click="checkModel.showChildren = !checkModel.showChildren">
            <div class="left-wrapper">
              <i class="iconfont icon-guanjianxiangbuhegui warn" />
              <span class="result-text">
                {{ $t("accessNetwork.securityCheck.info_11") }}
              </span>
              <span class="result-count">
                {{ $t("check.total")
                }}<b class="warn">{{ checkModel.children.length }}</b>{{ $t("check.item") }}
              </span>
            </div>
            <span
              class="icon-box"
            >
              <i
                :class="
                  checkModel.showChildren
                    ? 'el-icon-arrow-down'
                    : 'el-icon-arrow-right'
                "
              />
            </span>
          </div>
          <!-- 安检通过项 -->
          <div v-else-if="checkModel.classResultType === 3" id="ui-check-detail-div-check_pass" class="title" @click="checkModel.showChildren = !checkModel.showChildren">
            <div class="left-wrapper">
              <i class="iconfont icon-anjianheguixiang success" />
              <span class="result-text">
                {{ $t("accessNetwork.securityCheck.info_12") }}
              </span>
              <span class="result-count">
                {{ $t("check.total")
                }}<b class="success">{{ checkModel.children.length }}</b>{{ $t("check.item") }}
              </span>
            </div>
            <span
              class="icon-box"
            >
              <i
                :class="
                  checkModel.showChildren
                    ? 'el-icon-arrow-down'
                    : 'el-icon-arrow-right'
                "
              />
            </span>
          </div>
        </el-col>
        <el-col>
          <el-collapse-transition>
            <div v-show="checkModel.showChildren">
              <el-row>
                <el-col
                  v-for="item in checkModel.children"
                  :key="item.CheckType.OutsideName"
                  class="item check-item"
                >
                  <div :id="`ui-check-detail-children-item_${item.InsideName}`" class="children-item" @click="previewDetailFn(item)">
                    <div class="left-wrapper" :class="checkModel.classResultType === 1 ? 'check-fault' : checkModel.classResultType === 2 ? 'check-false' : 'check-pass' ">
                      <i
                        :class="[
                          'iconfont',
                          item.CheckType.InsideName.indexOf('CheckCustom_') ===
                            -1
                            ? 'icon-' + calcIcon(item.CheckType.InsideName)
                            : 'icon-CheckCustom',
                        ]"
                      />
                      <span class="check-title">{{ $i18n.locale === 'zh' ? item.CheckType.OutsideName :item.OutsideName_en }}</span>
                      <span class="check-info" :title="item.CheckType.Desc">{{ item.CheckType.Desc }}</span>
                      <span
                        v-if="item.downLoadRate && batchFixing"
                        class="rate-wrapper"
                      >{{ item.downLoadRate }}</span>
                    </div>
                    <p>{{ item.Desc }}</p>
                    <span :id="`ui-check-detail--to_detail_${item.InsideName}`" class="see-detail" @click="previewDetailFn(item)">
                      <i
                        v-if="securityCheckType !== 5"
                        :title="$t('accessNetwork.securityCheck.info_9')"
                        class="iconfont icon-chakanxiangqing"
                      />
                    </span>
                    <span
                      v-if="
                        securityCheckType === 5 &&
                          checkModel.classResultType !== 3
                      "
                      class="repair-type"
                      :class="
                        item.repairStatus === 0
                          ? 'repairing'
                          : item.repairStatus === 1
                            ? 'repair-success'
                            : 'repair-fail'
                      "
                    >
                      {{
                        item.repairStatus === 0
                          ? $t("accessNetwork.securityCheck.info_13")
                          : item.repairStatus === 1
                            ? $t("accessNetwork.securityCheck.info_14")
                            : $t("accessNetwork.securityCheck.info_28")
                      }}
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-collapse-transition>
        </el-col>
      </el-row>
    </div>
    <!-- 查看详情 -->
    <el-drawer
      :visible.sync="previewDetailDrawer"
      direction="rtl"
      size="566px"
      class="preview-detail-drawer"
      :destroy-on-close="true"
      :before-close="drawerCloseHandle"
    >
      <div slot="title" class="detail-drawer-title">
        <span>{{ $t('accessNetwork.securityCheck.info_15') }}</span>
        <i v-show="detailData && detailData.InsideName === 'CheckPatch'" id="ui-check-patch-i-close" class="el-icon-minus min-client-wrapper" @click="minusHandle" />
      </div>
      <component
        :is="checkDetailComponent"
        :check-data="detailData"
        :progress-info="progressInfo"
        :can-close.sync="drawerCanClose"
        @progress="progressHandle"
      />
    </el-drawer>
    <!-- 手动修复进度状态提示 -->
    <el-dialog
      custom-class="yg-handle-fix-modal"
      width="320px"
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      :visible.sync="dialogProccessVisible"
    >
      <p slot="title" class="fix-title">
        {{
          $t("check.Fixing") + ' '+
            fixProccessData.OutsideName +
            $t("check.item")
        }}
      </p>
      <div v-if="fixProccessData.state === 0" class="fix-searching">
        {{ $t("check.js_8_s") }}
      </div>
      <div v-if="fixProccessData.state === 1" class="fix-content">
        <img :src="downLoadImg" alt="">
        <div class="right-wrapper">
          <p>{{ $t("check.js_16_s") + fixProccessData.down }}</p>
          <p>{{ $t("check.js_20_s") + fixProccessData.speed }}</p>
          <p>{{ $t("check.js_22_s") + fixProccessData.rate }}</p>
          <div class="rate-wrapper">
            <div class="rate" :style="{ width: fixProccessData.rate }" />
          </div>
        </div>
      </div>
      <div v-if="fixProccessData.state === 2" class="fix-searching">
        {{ $t("check.js_12_s") }}
      </div>
    </el-dialog>
    <!-- 重新安检提示 -->
    <ConfirmDialog id="c-check-dialog" :show.sync="dialogCheckVisible" :show-confirm-button="false" width="285px" @no="closeHandle">
      <p class="c-tip-content">{{ reCheckTip }}</p>
    </ConfirmDialog>
  </div>
</template>

<script>
import '@/render/styles/securityCheckDetail.scss'
import agentApi from '@/service/api/agentApi'
import agentListening from '@/service/api/agentListening'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { checkItemAlias } from '@/render/utils/bussiness/localDic'
export default {
  props: {
    // 数据
    checkResultList: {
      type: Array,
      default: () => []
    },
    // 状态
    securityCheckType: {
      type: Number,
      default: () => 2
    },
    fixedNum: {
      type: Number,
      default: () => 0
    },
    proccess: {
      // 进度条进度
      type: Number,
      default: 0
    },
    showProccess: {
      // 是否显示进度条
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null, // 定时器
      progressStep: 0, // 进度条步长
      repairList: [], // 需要修复的列表
      successList: [], // 不需要修复的列表
      previewDetailDrawer: false, // 查看详情抽屉
      detailData: {}, // 当前条目信息
      stopLen: 0,
      step: 0.5,
      dataIndex: 0,
      dialogCheckVisible: false,
      reCheckTip: '',
      fixProccessData: {
        OutsideName: '',
        down: 0, // 已下载
        speed: 0, // 下载速度
        rate: 0, // 下载进度
        state: 0, // 0 搜索资源 1下载资源 2安装
        preData: {
          // 前一次记录，用于计算下载速度
          down: 0,
          time: 0
        }
      },
      isAutoFix: false,
      batchFixing: false, // 是否正在一建、自动修复
      dialogProccessVisible: false,
      downLoadImg: require('@/render/assets/PatchDown.png'),
      progressInfo: '', // 进度信息
      drawerCanClose: true // 详情页是否可以关闭
    }
  },
  computed: {
    // 详情组件
    checkDetailComponent() {
      let currentInsideName = this.detailData && this.detailData.InsideName
      console.log(currentInsideName)
      let myComponent = ''
      // windows 和mac同一安检项名称不一致处理，组件匹配统一使用windows名称
      if (checkItemAlias[currentInsideName]) {
        currentInsideName = checkItemAlias[currentInsideName]
      }
      if (currentInsideName) {
        try {
          if (currentInsideName.indexOf('CheckCustom_') > -1) {
            myComponent = () => import(`../detail/tpl_windows/CheckCustom.vue`)
          } else {
            const componentName = currentInsideName.split('_')[0]
            myComponent = () =>
              import(`../detail/tpl_windows/${componentName}.vue`)
          }
          return myComponent
        } catch (e) {
          console.log(e)
          console.log('找不到对应模块')
          return ''
        }
      }
      return myComponent
    }
  },
  destroyed() {
    this.batchFixing = false
    this.timer && clearInterval(this.timer)
  },
  mounted() {
    this.initRepairList()
    this.listenProccess()
  },
  methods: {
    startFix(isAuto) {
      this.isAutoFix = isAuto
      this.repairInit()
      this.fixItem(isAuto)
    },
    // 自动修复数据初始化
    repairInit() {
      this.dataIndex = 0
      this.batchFixing = true
      this.progressStep = 100 / this.repairList.length
      this.stopLen = this.progressStep * 0.92
      if (this.progressStep > 10) {
        this.step = this.progressStep / 10
      }
      if (this.step > this.stopLen) {
        this.step = this.stopLen
      }
      this.repairList.forEach((item) => {
        item.repairStatus = 0
      })
      this.$emit('update:proccess', 0)
      this.$emit('update:showProccess', true) // 显示进度条
    },
    // 获取需要修复数据列表和不需要修复列表
    initRepairList() {
      const list = []
      this.checkResultList.forEach((v) => {
        if (v.classResultType === 1 || v.classResultType === 2) {
          const children = v.children
          children.forEach((obj) => {
            this.$set(obj, 'repairStatus', 0)
            list.push(obj)
          })
        } else {
          this.successList = v.children
        }
      })
      this.repairList = list
    },
    // 修复安检项
    async fixItem() {
      if (!this.batchFixing) {
        return
      }
      const repairList = this.repairList
      let res = {}
      if (
        repairList[this.dataIndex].IsRepairItem === '1' &&
        (!this.isAutoFix || repairList[this.dataIndex].IsAutoRepair === '1')
      ) {
        // 自动设置进度条
        this.startProccess()
        const params = {
          ItemID: repairList[this.dataIndex].ItemID,
          InsideName: repairList[this.dataIndex].InsideName,
          RepairType: 1,
          RepairMode: 0,
          CreateProgress: 1 // 参数值无效，客户端默认创建进度条
        }
        // 弱口令参数特殊处理
        if (params.InsideName.indexOf('CheckVulnerablePassword') > -1) {
          params.RepairMode = 3
        }
        res = await agentApi.repairSecCheckItem(params)
        if (_.get(res, 'ASM.errcode') === '0') {
          const currentFixed = this.fixedNum
          this.$emit('update:fixedNum', currentFixed + 1)
        }
      }
      this.setScrollPostion(this.$refs.itemTabWrapper, this.dataIndex * 41)
      this.endProccess()
      this.repairList[this.dataIndex].repairStatus = _.get(res, 'ASM.errcode') === '0' ? 1 : 2
      if (this.repairList[this.dataIndex].downLoadRate !== undefined) {
        delete this.repairList[this.dataIndex].downLoadRate
      }
      if (this.dataIndex < this.repairList.length - 1) {
        this.dataIndex++
        this.fixItem()
      } else {
        this.batchFixing = false
        this.timer = setInterval(() => {
          clearInterval(this.timer)
          this.timer = null
          this.$emit('update:showProccess', false)
          this.$emit('update:proccess', 0)
          this.$parent.againCheckFn()
        }, 800)
      }
    },
    // 进度条开始
    startProccess() {
      let currentItemProcess = 0
      this.timer = setInterval(() => {
        if (currentItemProcess + this.step < this.stopLen) {
          currentItemProcess += this.step
          const current = this.proccess + this.step
          this.$emit('update:proccess', current)
        } else {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 150)
    },
    // 进度条结束
    endProccess() {
      this.timer && clearInterval(this.timer)
      this.$emit('update:proccess', this.progressStep * (this.dataIndex + 1))
      if (this.dataIndex === this.repairList.length - 1) {
        this.$emit('update:proccess', 100)
      }
    },
    // 滚动到目标位置
    setScrollPostion(dom, y) {
      if (
        dom.scrollHeight > dom.clientHeight ||
        dom.offsetHeight > dom.clientHeight
      ) {
        dom.scrollTop = y
      }
    },
    // 关闭重新安检
    closeHandle() {
      console.log('关闭')
      this.dialogCheckVisible = false
      this.$parent.cancelRepairFn()
    },
    // 取消修复
    cancelRepairFn() {
      this.batchFixing = false
      this.timer && clearInterval(this.timer)
    },
    // 查看详情
    previewDetailFn(val) {
      this.detailData = val
      this.previewDetailDrawer = true
      console.log('previewDetailDrawer', this.previewDetailDrawer, val)
    },
    // 监听安检项自动修复下载进度
    listenProccess() {
      const _this = this
      agentListening.addStrWhatToDo('ProcessNotice', function(qMsgRequest) {
        const ASM = _.get(qMsgRequest, 'result.ASM', '')
        console.log('ASM', ASM)
        if (ASM.Progress) {
          console.log('修复进度', ASM.Progress, _this.previewDetailDrawer)
          _this.progressInfo = JSON.stringify(ASM)
          if (!_this.previewDetailDrawer) {
            _this.autoFixProccess(ASM)
          } else {
            // 手工修复
            _this.handleFixProccess(ASM.Progress)
          }
        }
      })
    },
    // 自动修复进度设置
    autoFixProccess(ASM) {
      const Progress = ASM.Progress
      console.log('Progress', Progress)
      const resArr = Progress.split('|')
      console.log(resArr)
      let rate = resArr[resArr.length - 1]
      if (rate.indexOf('Rate') > -1) {
        rate = rate.split(':')[1]
        console.log(rate)
        if (rate) {
          if (rate > 99) {
            let pro = this.$t('check.js_12_s')
            if (ASM.total) {
              pro = ASM.total + '/' + ASM.current + ' ' + pro
            }
            this.setRepairePro(this.dataIndex, pro)
          } else {
            let downLoadRate = this.$t('check.downLoading') + rate + '%'
            if (ASM.total) {
              downLoadRate = ASM.total + '/' + ASM.current + ' ' + downLoadRate
              console.log(downLoadRate)
            }
            this.setRepairePro(this.dataIndex, downLoadRate)
          }
        }
      } else {
        let pro = rate
        if (ASM.total) {
          pro = ASM.total + '/' + ASM.current + ' ' + pro
        }
        this.setRepairePro(this.dataIndex, pro)
      }
    },
    setRepairePro(idx, pro) {
      try {
        console.log('设置进度', idx, pro, 'downLoad', this.repairList[idx].downLoadRate)
      } catch (e) {
        const info = {
          from: '安检设置进度报错',
          repairList: this.repairList,
          idx
        }
        console.error(JSON.stringify(info))
      }
      if (this.repairList[idx].downLoadRate) {
        this.repairList[idx].downLoadRate = pro
      } else {
        console.log('set', this.repairList[idx], idx)
        this.$set(this.repairList[idx], 'downLoadRate', pro)
      }
    },
    // 手工修复进度设置
    handleFixProccess(Progress) {
      if (!this.dialogProccessVisible) {
        // 不显示弹出进度不进行计算
        return
      }
      this.fixProccessData.state = 1
      const Res = Progress.split('|')
      const rate = parseFloat(Res[2].split(':')[1].replace('%', ''))
      if (rate > 99) {
        this.fixProccessData.state = 2
      } else {
        const down = Res[1].split(':')[1]
        const file = this.fileSize(Res[0].split(':')[1])
        const preDown = this.fixProccessData.preData.down
        const preTime = this.fixProccessData.preData.time
        // 客户端隔1s发送进度
        if (preTime === 0) {
          this.fixProccessData.speed = this.fileSize(down)
        } else {
          const speed =
            ((down - preDown) / (new Date().getTime() - preTime)) * 1000
          this.fixProccessData.speed = this.fileSize(speed) + '/s'
        }
        this.fixProccessData.down = file + '/' + this.fileSize(down)
        this.fixProccessData.rate = rate + '%'
        this.fixProccessData.preData.down = down
        this.fixProccessData.preData.time = new Date().getTime()
      }
    },
    fileSize(size) {
      size = parseFloat(size)
      var return_size = 0
      if (size !== '' && size > 0) {
        if (size / 1024 > 1 && size / 1024 < 1024) {
          return_size = parseInt(size / 1024) + 'KB'
        } else if (size / (1024 * 1024) > 1 && size / (1024 * 1024) < 1024) {
          return_size = (size / (1024 * 1024)).toFixed(2) + 'M'
        } else if (
          size / (1024 * 1024 * 1024) > 1 &&
          size / (1024 * 1024 * 1024) < 1024
        ) {
          return_size = (size / (1024 * 1024 * 1024)).toFixed(3) + 'G'
        } else {
          return_size = Math.round(size * 100) / 100 + 'B'
        }
      }
      return return_size
    },
    // 安检详情控制进度组件开关
    progressHandle(flag) {
      if (flag) {
        this.fixProccessData.OutsideName = this.$i18n.locale === 'zh' ? this.detailData.CheckType.OutsideName : this.detailData.OutsideName_en
        this.fixProccessData.state = 0
        this.dialogProccessVisible = true
      } else {
        console.log('关闭事件')
        this.dialogProccessVisible = false
      }
    },
    // 安检详情页关闭前回调
    drawerCloseHandle(done) {
      if (this.drawerCanClose) {
        done()
      } else {
        this.$message({
          message: this.$t('check.Patch.js_1_s'),
          type: 'warning'
        })
      }
    },
    minusHandle() {
      this.$ipcSend('UIPlatform_Window', 'MinimizeWnd')
    },
    calcIcon(InsideName) {
      const deviceType = _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
      if (deviceType === 'linux' && InsideName) {
        return InsideName.replace(/_linux/, '')
      } else if (deviceType === 'mac' && InsideName) {
        return checkItemAlias[InsideName] || InsideName
      } else {
        return InsideName
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.item-tab-wrap {
  overflow-y: auto;
  height: 100%;
  padding-right: 24px;
  .item-tab-main {
    border-top: 1px solid $line-color;
  }
  .el-col {
    height: 100%;
    &.item {
      border-bottom: 1px solid $line-color;
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      background: $row-bg;
      padding: 0 7px 0 16px;
      &.check-item{
        &:hover{
          background: $gray-3;
        }
      }
      .children-item {
        cursor: pointer;
        padding-left: 28px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left-wrapper {
          // display: flex;
          // align-items: center;
          // color: $title-color;
          color: $disabled-color;
          font-size: 14px;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          &.check-false{
            .iconfont {
              color: $yellow-1;
            }
            .check-title {
              color: $yellow-1;
            }
          }
          &.check-fault{
            .iconfont {
              color: $error-1;
            }
            .check-title {
              color: $error-1;
            }
          }
          &.check-pass{
            .check-title{
              color: $title-color;
            }
          }
          .iconfont {
            font-size: 16px;
            color: $disabled-color;
            margin-right: 7px;
          }
          .rate-wrapper {
            padding-left: 10px;
            font-size: 12px;
            color: $default-color;
          }
          .check-info{
            padding-left: 24px;
            font-size: 13px;
            color: $disabled-color;
            flex: 1;
          }
        }
        .see-detail {
          font-size: 16px;
          // color: $disabled-color;
          color: $--color-primary;
          cursor: pointer;
          width: 50px;
          height: 40px;
          text-align: right;
          padding-right: 9px;
          line-height: 40px;
          &:hover {
            color: $--color-primary;
          }
        }
        .repair-type {
          line-height: 39px;
          font-size: 13px;
          padding-right: 17px;
        }
        .repairing {
          color: $disabled-color;
        }
        .repair-success {
          color: $default-color;
        }
        .repair-fail {
          color: $error;
        }
      }
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: $title-color;
        .left-wrapper {
          display: flex;
          align-items: center;
          .iconfont {
            font-size: 16px;
            margin-right: 10px;
          }
          .result-text {
            font-weight: 500;
            font-size: 14px;
            color: $title-color;
          }
          .result-count {
            margin-left: 10px;
            color: $default-color;
            font-size: 14px;
          }
          b {
            font-weight: 500;
          }
          .err {
            color: $error;
          }
          .warn {
            color: $waring;
          }
          .success {
            color: $success;
          }
        }
        .icon-box {
          color: $default-color;
          font-size: 14px;
          cursor: pointer;
          width: 50px;
          height: 40px;
          line-height: 40px;
          text-align: right;
          padding-right: 9px;
        }
      }
    }
  }
  .preview-detail-drawer {
    ::v-deep .el-drawer {
      height: 100%;
      .el-drawer__header {
        padding: 24px 32px;
        span {
          font-weight: 500;
          color: $title-color;
          line-height: 20px;
        }
      }
      .el-drawer__body {
        padding: 0 26px 0 32px;
        margin-bottom: 30px;
      }
      .detail-drawer-title{
        position: relative;
        span{
          font-weight: 600;
        }
        .min-client-wrapper{
          position: absolute;
          right: 15px;
          top:-7px;
          cursor: pointer;
        }
      }
    }
  }
}

::v-deep .yg-handle-fix-modal .el-dialog__header {
  padding: 18px;
  padding-bottom: 10px;
  font-size: 13px;
  color: $title-color;
  font-weight: 500;
}
::v-deep .yg-handle-fix-modal .el-dialog__body {
  padding-top: 0;
  padding-bottom: 44px;
  font-size: 13px;
  color: $title-color;
  .fix-content {
    padding-top: 22px;
    display: flex;
    justify-content: center;
    img {
      width: 44px;
      height: 44px;
      flex-shrink: 0;
      margin-right: 20px;
    }
    p {
      margin-bottom: 4px;
      line-height: 18px;
    }
    .rate-wrapper {
      margin-top: 10px;
      width: 160px;
      height: 16px;
      border: 1px solid $line-color;
      position: relative;
      .rate {
        position: absolute;
        left: 0;
        top: -1px;
        height: 16px;
        background: linear-gradient(132deg, #72cbff 10%, #546ff8 82%);
      }
    }
  }
  .fix-searching {
    text-align: center;
  }
  ::v-deep .c-tip-content{
    padding: 24px;
  }
}
</style>
