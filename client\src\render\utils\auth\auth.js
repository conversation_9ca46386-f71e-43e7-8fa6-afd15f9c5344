import index from './index'

/**
 * 认证父类：普通认证、802.1x认证
 * 不同认证方式继承实现
 * 根据运行模式调用普通认证、802.1x认证中认证方法
 */

class Auth {
  /**
   * 认证
   * @returns {Promise<boolean|Object>}
   */
  async auth(params) {
    if (this.isDot1xAuth()) {
      return this.dot1x(params)
    }
    return this.common(params)
  }

  /**
   * 是否802.1x认证
   * 如果子类不支持802.1x认证返回false即可
   * @returns {boolean}
   */
  isDot1xAuth() {
    return index.isDot1xMode() && !index.noDot1xAutoAuth()
  }

  // 设备id
  getDeviceId() {
    return index.getDeviceId()
  }

  /**
   * 普通认证
   * @returns {Promise<boolean|Object>}
   */
  async common(params) {
    return false
  }

  /**
   * 802.1x认证
   * @returns {Promise<boolean|Object>}
   */
  async dot1x(params) {
    return false
  }
}

export default Auth
