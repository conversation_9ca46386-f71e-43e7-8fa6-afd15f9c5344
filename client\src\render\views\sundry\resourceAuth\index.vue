<template>
  <div id="f-resource-auth" v-loading="initLoading">
    <div class="tab-pane-nev" :class="{'u-one-type': authTypes.length===1 }">
      <span
        v-for="tabs in authTypes"
        :key="tabs.label"
        :class="{ 'is-active': activeType === tabs.value }"
        @click="changeGeneralAuthWay(tabs.value)"
      >
        {{ tabs.label }}
      </span>
    </div>
    <div class="g-auth-warp">
      <component
        :is="activeType"
        :user-name="userName"
        @authSuccess="authSuccess"
        @authFaild="authFaild"
      />
    </div>
    <div v-if="!clientInfo.online && !initLoading" class="u-offlineTips">
      <div class="off-tip-content">
        <i class="iconfont icon-guanjianxiangbuhegui" /> {{ $t('header.offline') }}
      </div>
    </div>

  </div>
</template>

<script>
import user from './user.vue'
import appScan from './appScan.vue'
import noAuth from './noAuth.vue'
import qs from 'qs'
import scene from '@/render/utils/bussiness/scene'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import { mapGetters } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
import { Message } from 'element-ui'
import agentApi from '@/service/api/agentApi'
export default {
  components: { user, appScan, noAuth },
  data() {
    return {
      activeType: 'user',
      initLoading: true,
      allowAuthTypes: '',
      needAuth: true,
      userName: '',
      authTypes: []
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }

  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      try {
        await commonUtil.basic()
        const ret = await Promise.all([commonUtil.detail('other', false), commonUtil.server('other', false), this.getResourceAuthConf()])
        const resourceAuthInfo = this.handleBasicInfo(ret)
        const sceneAuthConf = await this.getAuthFlag()
        this.authReady(sceneAuthConf, resourceAuthInfo)
      } catch (error) {
        console.error(error)
        Message({ message: this.$t('resourceAuth.resourceInitErr'), type: 'error' })
      }

      this.initLoading = false
    },

    handleBasicInfo(ret) {
      const deviceInfo = _.get(ret, 0, false)
      const serverInfo = _.get(ret, 1, false)
      const resourceAuthInfo = _.get(ret, 2, false)
      if (_.isEmpty(deviceInfo) || _.isEmpty(serverInfo) || _.isEmpty(resourceAuthInfo)) {
        Message({ message: this.$t('resourceAuth.resourceInitErr'), type: 'error', duration: 6000, offset: 60 })
        return false
      }

      return resourceAuthInfo
    },

    // 判断是否需要认证
    async getAuthFlag() {
      try {
        const sceneRet = await scene.getDeviceScene()
        return parseInt(_.get(sceneRet, 'IsAuth', 1)) === 1
      } catch (error) {
        return true
      }
    },
    // 获取高级动态身份认证相关的配置
    async getResourceAuthConf() {
      const ret = await proxyApi.getResourceAuthConf()
      if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data', '')) || !_.isObject(_.get(ret, 'data', ''))) {
        return {}
      }
      return _.get(ret, 'data')
    },
    queryFristUrl() {
      const query = qs.parse(location.search.substring(1))
      let firstUrl = _.get(query, 'firsturl', '')
      const ret = /^http(s)?:\/\//i
      if (!ret.test(firstUrl) && !_.isEmpty(firstUrl)) {
        firstUrl = 'http://' + firstUrl
      }
      return firstUrl
    },
    // 切换认证方式[user和app认证]
    changeGeneralAuthWay(val) {
      this.activeType = val
    },
    // 准备认证前的初始化(渲染配置的认证方式，是否免认证等)
    async authReady(sceneAuthConf, resourceAuthInfo) {
      if (!resourceAuthInfo) {
        return
      }

      this.allowAuthTypes = _.get(resourceAuthInfo, 'resourceAuthConfig.authType', '')
      // 如果场景返回的是免认证，那么走免认证流程
      // 如果没有配置认证方式则走免认证(理论上是不可以这么配的)
      if (!sceneAuthConf || _.isEmpty(this.allowAuthTypes)) {
        this.activeType = 'noAuth'
        return
      }

      // 可信设备是否需要认证
      if (parseInt(_.get(this.clientInfo, 'detail.IsTrustDev', 0)) === 1 && parseInt(_.get(resourceAuthInfo, 'resourceAuthConfig.trustNoAuth', 0)) === 1) {
        this.activeType = 'noAuth'
        return
      }

      const isOnlneAndAuthed = await this.getResourceAuthUser()

      if (isOnlneAndAuthed) {
        this.handleAuthTypes()
        this.handleDefaultAuth()
      }
    },

    handleAuthTypes() {
      const allows = []

      // 支持输入密码认证
      if (this.allowAuthTypes.indexOf('Pap') !== -1) {
        allows.push({ label: this.$t('resourceAuth.user'), value: 'user' })
      }

      // 支持App扫码认证
      if (this.allowAuthTypes.indexOf('Qrcode') !== -1) {
        allows.push({ label: this.$t('resourceAuth.app'), value: 'appScan' })
      }

      this.authTypes = allows
    },
    // 设置默认的认证方式
    handleDefaultAuth() {
      if (!_.isEmpty(this.authTypes) && !_.isEmpty(_.get(this.authTypes, '0.value', ''))) {
        this.activeType = _.get(this.authTypes, '0.value')
      }
    },

    // 查询当前认证的用户
    async getResourceAuthUser() {
      const apiParams = {
        device_id: this.deviceid
      }
      const ret = await proxyApi.getResourceAuthUser(apiParams)
      if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data.val', ''))) {
        Message({ message: this.$t('resourceAuth.noAuth'), type: 'error', duration: 12000, offset: 60 })
        return false
      } else {
        this.userName = _.get(ret, 'data.val')
        return true
      }
    },

    // 认证成功的处理
    async authSuccess(params) {
      const { type } = params
      const apiParams = {
        ASM: {
          AuthType: type,
          AuthResult: 1
        }
      }

      try {
        const initParam = {
          WhereIsModule: 'LcfCriticalResourceProtect',
          WhatFuncToCall: 'NotifyResourceAuthResult',
          RequestParam: apiParams
        }
        await agentApi.callAgentOneFunc(initParam)
      } catch (error) {
        console.error(error)
        setInterval(() => {
          agentApi.terminateWnd()
        }, 5000)

        return false
      }

      // 认证成功,自动关闭
      Message({ message: this.$t('resourceAuth.success'), type: 'success', offset: 60 })
      setInterval(() => {
        agentApi.terminateWnd()
      }, 5000)
    },
    // 认证失败的处理
    authFaild(parasm) {

    }
  }

}
</script>
<style lang="scss">
#app{
 background: none;
}
#f-resource-auth {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  background: none;
  overflow: auto;
  flex-direction: column;
  justify-content: center;
  align-items: center; display: flex;

  .u-offlineTips{
    width:60%;
    padding: 10px;
    display: flex;
    justify-content: center;
    .off-tip-content{
      display: flex;
      line-height:20px;
      font-size: 14px;
      color:rgba(230,83,83,1);
      i{
        padding-right: 10px;
        font-size:14px;
      }
    }
  }

  .tab-pane-nev {
    text-align: center;
    height: 40px;
    span {
      display: inline-block;
      font-size: 16px;
      line-height: 40px;
      color: $disabled-color;
      cursor: pointer;
      &.is-active,
      &:hover {
        font-size: 18px;
        font-weight: 500;
        color: $--color-primary;
      }
      &:nth-of-type(1) {
        padding-right: 16px;
        width: 245px;
        text-align: right;
      }
      &:nth-of-type(2) {
        padding-left: 16px;
        width: 245px;
        text-align: left;
      }
    }

    .u-one-type{
      &:nth-of-type(1) {
        text-align: center!important;
      }
    }
  }
  .g-auth-warp{
    margin-top: 30px;
  }
}

</style>
