<template>
  <div id="f-no-receive-permission">
    <layout-state
      :state-img="stateImg"
      :state-msg="stateMsg"
    />
  </div>
</template>

<script>
import state from '@/render/components/layout/state'
import ztpUtils from '@/render/utils/auth/ztp/index'
const empty = require('@/render/assets/stateIllustration/empty.png')
export default {
  name: 'NoReceivePermission',
  components: {
    layoutState: state
  },
  data() {
    return {
      stateImg: empty
    }
  },
  computed: {
    stateMsg() {
      return ztpUtils.whetherZTPMode() ? this.$t('guestAuth.emptyAuthType') : this.$t('guestAuth.administrator.info_21')
    }
  }

}
</script>
<style lang="scss">
    #f-no-receive-permission{
        width:100%;
        height:100%;
    }
</style>
