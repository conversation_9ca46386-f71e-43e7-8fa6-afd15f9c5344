<template>
  <div v-loading="!inited && loading" class="guest-audit-page">
    <empty v-if="inited && !tableData.length" />

    <div v-else ref="auditContent" class="content">
      <el-table
        v-show="tableData.length"
        v-loading="inited && loading"
        :data="tableData"
        border
        stripe
        :max-height="maxHeight"
        style="width: 100%"
        class="guest-formate-style-table"
      >
        <el-table-column
          prop="Name"
          fixed
          show-overflow-tooltip
          width="105"
          :label="$t('guestAuth.guest.info_58')"
        />
        <el-table-column
          prop="Tel"
          width="120"
          :label="$t('guestAuth.guest.info_59')"
        />
        <el-table-column
          prop="IP"
          :label="$t('guestAuth.guest.info_60')"
          width="130"
        />
        <el-table-column
          prop="Unit"
          show-overflow-tooltip
          :label="$t('guestAuth.guest.info_61')"
          min-width="180"
        />
        <el-table-column
          prop="AccessType"
          :label="$t('guestAuth.guest.info_62')"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="InsertTime"
          width="180"
          :label="$t('guestAuth.guest.info_63')"
        />
        <el-table-column
          fixed="right"
          :label="$t('guestAuth.guest.info_64')"
          width="100"
        >
          <template slot-scope="scope">
            <i :id="`ui-guest-receive-audit-i-opt_apply_pass_${scope.row.ID}`" :class="['iconfont', 'icon-shenhetongguo', !haveAuditPermission? 'not-allow':'']" :title="haveAuditPermission ? $t('guestAuth.guest.info_65') : $t('guestAuth.notAuditPermision')" @click="auditPass(scope.row)" />
            <i :id="`ui-guest-receive-audit-i-opt_apply_refuse_${scope.row.ID}`" :class="['iconfont', 'icon-shenhejujue', !haveAuditPermission? 'not-allow':'']" :title="haveAuditPermission ? $t('guestAuth.guest.info_66'):$t('guestAuth.notAuditPermision')" @click="auditReject(scope.row)" />
          </template>
        </el-table-column>
      </el-table>

      <verticalDialog
        :title="$t('guestAuth.guest.info_65')"
        :show="showSetDialog"
        :show-close="false"
        :show-foot="true"
        width="400px"
        :loading="submiting"
        class="guest-audit-dialog"
        pop-name="guest-receive-audit-pass"
        @cancel="showSetDialog = false"
        @confirm="setConfirm"
      >
        <div class="g-s-diaolog-content">
          <div class="form-content">
            <el-form
              ref="ruleForm"
              :model="form"
              :rules="rules"
              label-width="125px"
              :validate-on-rule-change="false"
            >
              <el-form-item
                prop="AllowTime"
              >
                <el-tooltip
                  slot="label"
                  effect="dark"
                  :content="$t('guestAuth.guest.info_43')"
                  placement="top-start"
                >
                  <span class="text-clamp">{{ $t('guestAuth.guest.info_43') }}：</span>
                </el-tooltip>
                <el-input
                  id="ui-guest-receive-audit-input-network_time"
                  v-model="form.AllowTime"
                  class="hour-input"
                />
                <span>{{ $t('guestAuth.guest.info_46') }}</span>
              </el-form-item>
              <el-form-item
                prop="AllowRegionIDs"
                class="select-form-box"
              >
                <el-tooltip
                  slot="label"
                  effect="dark"
                  :content="$t('guestAuth.guest.info_44')"
                  placement="top-start"
                >
                  <span class="text-clamp">{{ $t('guestAuth.guest.info_44') }}：</span>
                </el-tooltip>
                <el-select
                  id="ui-guest-receive-audit-select-network_domain"
                  v-model="form.AllowRegionIDs"
                  clearable
                  :placeholder="$t('guestAuth.guest.info_47')"
                  multiple
                  collapse-tags
                >
                  <el-option v-for="item in regionList" :id="`ui-guest-receive-audit-option-network_${item.id}`" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </verticalDialog>

      <verticalDialog
        :title="$t('guestAuth.guest.info_66')"
        :show="showRejectDialog"
        :show-close="false"
        :show-foot="true"
        width="368px"
        class="guest-audit-dialog"
        :loading="submiting"
        pop-name="guest-receive-audit-refuse"
        @cancel="showRejectDialog = false"
        @confirm="rejectConfirm"
      >
        <div class="g-s-diaolog-content">
          <div class="form-content">
            <p class="reject-title">{{ $t('guestAuth.guest.info_67') }}</p>
            <el-input
              id="ui-guest-receive-audit-input-refuse_remark"
              v-model="rejectReason"
              type="textarea"
              :rows="4"
              maxlength="200"
              show-word-limit
              class="reject-textare"
              :placeholder="$t('guestAuth.guest.info_68')"
            />
          </div>
        </div>
      </verticalDialog>
    </div>
  </div>
</template>
<script>
import '@/render/styles/guestTable.scss'
import empty from './components/empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapState } from 'vuex'
import regular from '@/render/utils/regular'
import guestFormUtils from '@/render/utils/bussiness/guestFormUtils'
import scene from '@/render/utils/bussiness/scene'
export default {
  components: {
    empty
  },
  data() {
    var validateTime = (rule, value, callback) => {
      if (value && !regular.rules['1-999Num'].test(value)) {
        callback(new Error(`[${this.$t('guestAuth.guest.info_43')}] ${this.$t('changeRegInfo.styleErro')} ${regular.errorTipMap()['1-999Num']}`))
      } else {
        callback()
      }
    }
    return {
      inited: false,
      maxHeight: 0,
      showSetDialog: false,
      showRejectDialog: false,
      rejectReason: '',
      tableData: [],
      form: {
        AllowTime: 8,
        AllowRegionIDs: []
      },
      loading: false,
      regionList: [],
      rules: {
        AllowTime: [
          { required: true, message: this.$t('guestAuth.guest.info_53'), trigger: 'blur' },
          { validator: validateTime, trigger: 'blur' }
        ],
        AllowRegionIDs: [
          { required: true, message: this.$t('guestAuth.guest.info_54'), trigger: 'change' }
        ]
      },
      currentRow: {},
      submiting: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig']),
    haveAuditPermission() {
      const receiveAccess = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.receiveAccess', '')
      return receiveAccess.indexOf('GuestApplySelf') > -1
    }
  },
  mounted() {
    this.initTableHeight()
    this.initData()
  },
  methods: {
    async initData() {
      this.loading = true
      await this.getScene()
      Promise.all([
        proxyApi.reqApproveInfoList({ GreetUserID: _.get(this.clientInfo, 'accessStatus.UserID'), GreetDeviceID: _.get(this.clientInfo, 'detail.DeviceID') }),
        guestFormUtils.getNetcode()
      ]).then(ret => {
        console.log('数据', ret)
        this.tableData = ret[0].data
        this.regionList = ret[1]
        const defaultRegion = _.get(this.regionList, '[0].DefRoleID', '')
        if (defaultRegion) {
          this.$set(this.form, 'AllowRegionIDs', [defaultRegion])
        }
      }).finally(() => {
        this.inited = true
        this.loading = false
      })
    },
    async getScene() {
      if (!_.get(this.serveEntiretyConfig, 'scene.SceneID', false)) {
        await scene.getDeviceScene()
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.maxHeight = this.$refs.auditContent.offsetHeight - 16
      })
    },
    initTableHeight() {
      this.setTableHeight()
      window.addEventListener('resize', this.setTableHeight)
      this.$once('hook:destroyed', () => {
        window.removeEventListener('resize', this.setTableHeight)
      })
    },
    auditPass(data) {
      if (!this.haveAuditPermission) {
        return
      }
      console.log(data)
      this.currentRow = data
      this.showSetDialog = true
      this.$refs.ruleForm && this.$refs.ruleForm.resetFields()
    },
    auditReject(data) {
      if (!this.haveAuditPermission) {
        return
      }
      console.log(data)
      this.currentRow = data
      this.showRejectDialog = true
    },
    setConfirm() {
      this.$refs['ruleForm'].validate(async(valid, obj) => {
        if (valid) {
          const { UserID } = _.get(this.clientInfo, 'accessStatus')
          const { DeviceID } = _.get(this.clientInfo, 'detail')
          const { AllowTime, AllowRegionIDs } = this.form
          const params = {
            action: 'Recept',
            state: 1,
            guestselfid: this.currentRow.ID,
            deviceid: DeviceID,
            AccessTypeCode: this.currentRow.AccessTypeCode,
            UserID,
            AllowTime,
            AllowRegionIDs: AllowRegionIDs.sort().join(',')
          }
          this.submiting = true
          const ret = await proxyApi.reqAuditGuest(params)
          if (parseInt(ret.errcode) === 0) {
            this.$message({
              message: this.$t('guestAuth.guest.info_80'),
              type: 'success'
            })
            this.initData()
          }
          this.submiting = false
          this.showSetDialog = false
        }
      })
    },

    async rejectConfirm() {
      const { UserID } = _.get(this.clientInfo, 'accessStatus')
      const { DeviceID } = _.get(this.clientInfo, 'detail')
      const params = {
        action: 'Recept',
        state: -1,
        guestselfid: this.currentRow.ID,
        deviceid: DeviceID,
        UserID,
        Reason: this.rejectReason
      }
      this.submiting = true
      const ret = await proxyApi.reqAuditGuest(params)
      if (parseInt(ret.errcode) === 0) {
        this.$message({
          message: this.$t('guestAuth.guest.info_81'),
          type: 'success'
        })
        this.initData()
      }
      this.submiting = false
      this.showRejectDialog = false
      this.rejectReason = ''
    }
  }
}
</script>
<style lang="scss" scoped>
  .guest-audit-page{
    height: 100%;
    .content{
      padding-top: 16px;
      height: 100%;
      .icon-shenhetongguo{
        font-size: 16px;
        color: $--color-primary;
        padding: 0 6px;
        margin-right: 2px;
        cursor: pointer;
      }
      .icon-shenhejujue{
        font-size: 16px;
        color: $error-1;
        padding: 0 6px;
        cursor: pointer;
      }
      .not-allow{
        color: $disabled-color;
        cursor: not-allowed;
      }
    }
    &::v-deep .el-table__header .el-table__cell{
      padding: 8px 0;
      color: $title-color;
    }
  }
  .guest-audit-dialog .el-dialog .dialog-header{
    padding: 12px 24px;
  }
  .guest-audit-dialog ::v-deep .el-dialog__body{
    .el-form-item__label{
      color: $title-color;
      justify-content: flex-end;
    }
    .dialog-header{
     font-weight: 600;
    }
  }
  .g-s-diaolog-content{
    .form-content{
      padding: 24px 24px 0 24px;
      ::v-deep .el-select{
        .el-input__inner{
            padding-left: 15px;
        }
        .el-select__tags > span{
            display: flex;
        }
      }
      ::v-deep .hour-input{
        width: 140px;
        margin-right: 10px;
      }
      ::v-deep .el-form-item__label{
        display: flex;
      }
      .reject-title{
        margin-bottom: 10px;
        font-size: 14px;
        color: $title-color;
      }
      .reject-textare{
        margin-bottom: 24px;
      }
    }
  }
</style>
