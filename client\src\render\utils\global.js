import { Base64 } from 'js-base64'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import _ from 'lodash'
import { isElectron } from '@/service/utils/ipcPlugin/utils/index'

// 自定义base64加密解密
export function Base64Encode(str) {
  var s = Base64.encode(str)
  s = s.replace(/[+]/g, ',')
  s = s.replace(/=/g, ':')
  s = s.replace(/\//g, '.')
  return s
}
export function Base64Decode(str) {
  var s = str.replace(/,/g, '+')
  s = s.replace(/:/g, '=')
  s = s.replace(/[.]/g, '\/')
  s = Base64.decode(s)
  return s
}

/**
 * IP地址比较大小
 * return int 大于返回1;等于返回0;小于返回-1;
 */
export function compareIP(ipBegin, ipEnd) {
  var temp1
  var temp2
  temp1 = ipBegin.split('.')
  temp2 = ipEnd.split('.')
  for (var i = 0; i < 4; i++) {
    if (parseInt(temp1[i]) > parseInt(temp2[i])) {
      return 1
    } else if (parseInt(temp1[i]) < parseInt(temp2[i])) {
      return -1
    }
  }
  return 0
}

// 扩展字符串截取函数
export function GetSubStr(Str, First, Last) {
  var nStart = Str.indexOf(First)
  if (nStart === -1) {
    return ''
  }
  nStart = nStart + First.length
  var nStop = Str.indexOf(Last, nStart)
  if (nStop === -1) {
    return ''
  }
  return Str.substring(nStart, nStop)
}

/**
 * 获取g_aServer
 * @param path
 * @param defaultValue
 * @param type
 */
export function getGaServer(path, defaultValue, type) {
  let value = _.get(G_VARIABLE.g_aServer, path, defaultValue)
  if (_.isUndefined(type)) {
    return value
  }
  switch (type) {
    case 'int':
      value = parseInt(value)
      break
    case 'string':
      value = value + ''
      break
    // 不支持则不处理
    default:
      break
  }
  return value
}

export function isMobile(mobile) {
  if (!mobile) {
    return false
  }
  var reg = /^[0-9]*$/
  if (!reg.test(mobile)) {
    return false
  }
  return true
}

export function stringIsUndefined(strs) {
  if (typeof (strs) === 'undefined') {
    return true
  }
  return false
}

export function stringIsEmpty(strs) {
  if (stringIsUndefined(strs) || typeof (strs) !== 'string' || !strs || strs === 'undefined') {
    return true
  }
  return false
}

export function stringIsEmptyZero(strs) {
  if (stringIsEmpty(strs) || strs === '0') {
    return true
  }
  return false
}

export function randomString(len) {
  len = len || 32
  var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  /** **默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  var maxPos = $chars.length
  var pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

export function GetUrlParam(paraName) {
  var url = document.location.toString()
  var arrObj = url.split('?')
  if (arrObj.length > 1) {
    var arrPara = arrObj[1].split('&')
    var arr
    for (var i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split('=')

      if (arr != null && arr[0] === paraName) {
        return arr[1]
      }
    }
    return ''
  } else {
    return ''
  }
}

/**
 * 检测qt模块是否存在
 * @param module 模块名
 * @param action action名称
 */
export function TestQtModule(module, action = '') {
  // electron环境下不需要去判断这个模块是否需要存在
  if (isElectron()) {
    return true
  }

  if (!module || !action) {
    return false
  }
  const QtServer = _.get(window, 'webChannel.objects', window)
  if (typeof QtServer[module] === 'undefined') {
    return false
  }
  if (typeof QtServer[module][action] === 'undefined') {
    return false
  }
  return true
}

export function sleep(ms) {
  let timer = null
  return new Promise((resolve) => {
    timer = setTimeout(() => {
      clearTimeout(timer)
      resolve()
    }, ms)
  })
}

export function addEvent(element, event, handler) {
  if (element.addEventListener) { // 现代浏览器
    element.addEventListener(event, handler, false)
  } else if (element.attachEvent) { // IE8及以下
    element.attachEvent('on' + event, function() {
      // 修复this指向问题
      handler.call(element, window.event)
    })
  }
}

export function removeEvent(element, event, handlerWrapper) {
  if (element.removeEventListener) {
    element.removeEventListener(event, handlerWrapper)
  } else if (element.detachEvent) {
    element.detachEvent('on' + event, handlerWrapper)
  }
}

// 版本号比较
// return 0 相等  小于零表示b>a
export function compareVersions(a, b) {
  // 预处理：移除开头的V并分割成数组
  const parsePart = part => {
    const [, alpha = '', num = '0'] = part.match(/([A-Za-z]*)(\d*)/) || []
    return {
      alpha: alpha.toLowerCase(),
      num: parseInt(num || '0', 10) || 0
    }
  }

  const partsA = a.replace(/^V/i, '').split('.').map(parsePart)
  const partsB = b.replace(/^V/i, '').split('.').map(parsePart)

  // 逐级比较版本号
  const maxLength = Math.max(partsA.length, partsB.length)
  for (let i = 0; i < maxLength; i++) {
    const pa = partsA[i] || { alpha: '', num: 0 }
    const pb = partsB[i] || { alpha: '', num: 0 }

    // 先比较字母部分（不区分大小写）
    if (pa.alpha !== pb.alpha) {
      return pa.alpha.localeCompare(pb.alpha)
    }

    // 字母相同则比较数字部分
    if (pa.num !== pb.num) {
      return pa.num - pb.num
    }
  }

  return 0
}
