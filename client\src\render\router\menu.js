import accessNetwok from './module/accessNetwok'
import commonTools from './module/commonTools'
import patchManagment from './module/patchManagment'
import thirdLinkage from './module/thirdLinkage'
import sourcePlatform from './module/sourcePlatform'

export default [
  ...accessNetwok, // 常用功能 【入网状态，立即入网，来宾认证等】
  ...commonTools, // 常用工具 【远程协助，故障诊断】
  ...patchManagment, // 补丁管理
  ...thirdLinkage, // 第三方客户端联动
  ...sourcePlatform // 资源平台
]
