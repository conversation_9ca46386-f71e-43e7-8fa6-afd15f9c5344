<template>
  <div v-loading="initLoading" class="guest-req-page">
    <div class="u-current-tag">
      <span>
        <i class="iconfont icon-renzheng" />{{ $t('guestAuth.guest.info_1') }}
      </span>
      <span class="go-back-btn" @click="goBack">
        <i class="iconfont icon-fanhui" />{{ $t('guestAuth.guest.info_82') }}
      </span>
    </div>
    <p class="p-title">{{ $t('guestAuth.guest.info_6') }}</p>
    <div class="active-content">
      <activeForm ref="activeForm" class="guest-reg-form" form-name="guest-regist" :form-fields="formFields" :form="form" :loading="loading" @submit="submitForm" />
    </div>
  </div>
</template>
<script>
import activeForm from '@/render/views/accessNetwork/guestAuth/guestReceive/components/activeForm.vue'
import { mapState, mapMutations } from 'vuex'
import guestFormUtils from '@/render/utils/bussiness/guestFormUtils'
import proxyAjax from '@/service/api/proxyApi'
import processController from '@/render/utils/processController'
export default {
  components: {
    activeForm
  },
  data() {
    return {
      loading: false,
      formFields: [],
      form: {},
      defaultRegion: '',
      regionList: [],
      initLoading: false,
      QRCode: '', // 接待码
      regSuccess: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig', 'authInfo']),
    needAuditType() {
      const authType = _.get(this.authInfo, 'basic.guestAccessType')
      const AuditAccessType = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.AuditAccessType')
      if (!authType || !AuditAccessType) {
        return false
      }
      if (AuditAccessType.indexOf(authType) > -1) {
        return true
      }
      return false
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.form = {}
      this.formFields = []
      this.$refs.activeForm.$refs.ruleForm.resetFields()
      this.initForm()
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.initLoading = false
  },
  methods: {
    ...mapMutations(['setAuthInfo']),
    init() {
      this.initForm()
      if (this.regAndAuditIsComplete()) {
        this.submitForm()
        return
      }
    },
    async initForm() {
      const CLIENTCHECK = _.get(this.serveEntiretyConfig, 'server.CLIENTCHECK')
      const params = _.cloneDeep(CLIENTCHECK)
      const { formFields, form } = guestFormUtils.createForm(params, { isApply: false, sortFields: this.getAuditItem() })

      this.initFormVal(form, formFields)
      this.formFields = formFields
      console.log(formFields)
      this.form = form
    },
    initFormVal(form, formFields) {
      const formData = _.get(this.authInfo, 'basic.guestRelation', '')
      if (!formData) {
        return
      }
      const keyMap = {
        guestmobile: 'Tel',
        guestcompany: 'Unit',
        guestname: 'Name',
        content: 'Remark'
      }
      for (const k in form) {
        const defaultVal = form[k]
        let value = ''
        if (k.indexOf('GuestRequireExpand_') > -1) { // 拓展项
          const _key = k.replace('GuestRequireExpand_', 'GuestExpand_')
          value = formData[_key]
        } else {
          value = keyMap[k] ? formData[keyMap[k]] : formData[k] || ''
        }
        form[k] = Array.isArray(defaultVal) ? value ? value.split(',') : [] : value
      }

      formFields.forEach(item => {
        const value = form[item.Name]
        const valType = typeof value
        if ((valType === 'string' && value) || (valType === 'object' && value.length > 0)) {
          item.disabled = true
        }
      })
    },
    getAuditItem() {
      const regKeys = ['GuestRequireUser', 'GuestRequireUnit', 'GuestRequireTel', 'GuestRequireReason', 'GuestRequireExpand']
      const keyMap = {
        name: 'AuditRequireUser',
        mobile: 'AuditRequireTel'
      }
      let auditKeys = []
      const receptType = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.ReceptType')
      if (receptType) {
        const arr = receptType.split('|')
        auditKeys = arr.map(item => keyMap[item])
      }
      const needAudit = parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAudit'), 0) === 1 && this.needAuditType
      let renderKeys = []
      if (parseInt(_.get(this.serveEntiretyConfig, 'scene.IsRegistered')) === 1) {
        renderKeys = renderKeys.concat(regKeys)
      }
      if (needAudit) {
        renderKeys = renderKeys.concat(auditKeys)
      }
      console.log(renderKeys)
      return renderKeys
    },
    regAndAuditIsComplete() {
      const regComplete = _.get(this.authInfo, 'basic.guestRelation.IsComplete')
      const needReg = parseInt(_.get(this.serveEntiretyConfig, 'scene.IsRegistered')) === 1
      const needAudit = parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAudit'), 0) === 1 && this.needAuditType
      if ((!needReg || regComplete) && !needAudit) {
        return true
      }
      return false
    },
    async submitForm() {
      const _params = guestFormUtils.formateFormParams(this.form)
      const { UserID, guestSelfID } = _.get(this.authInfo, 'basic', {})
      const { DeviceID } = _.get(this.clientInfo, 'detail', {})

      const fixedParams = {
        device_id: DeviceID,
        guestSelfID,
        UserID,
        action: 'Submit'
      }
      if (this.initLoading) {
        return
      }
      this.initLoading = true
      const ret = await proxyAjax.reqGuestRequired({ ..._params, ...fixedParams })
      if (parseInt(_.get(ret, 'errcode')) === 0) {
        this.regSuccess = true
        const IsNeedAudit = _.get(ret, 'data.IsNeedAudit')
        this.setAuthInfo(_.merge({}, this.authInfo, { basic: { IsNeedAudit }}))
        processController.next()
      } else {
        this.initLoading = false
        this.$msg({ message: ret.errmsg, type: 'error' })
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  },
  beforeRouteLeave(to, from, next) {
    if (!this.regSuccess) {
      this.setAuthInfo({ ...this.authInfo, ...{ basic: {}}})
    }
    next()
  }
}
</script>
<style lang="scss" scope>
.guest-req-page{
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
   .u-current-tag{
    display: flex;
    justify-content: space-between;
    align-content: center;
    .go-back-btn{
      background: #f5f6f8;
      border: 1px solid #ededf1;
      border-radius: 15px;
      color: $default-color;
      cursor: pointer;
      &:hover{
        background: $--color-primary;
        border-color: $--color-primary;
        color: $light-color;
      }
      &:hover i{
        color: $light-color;
      }
      i{
        color: $disabled-color;
        margin-right: 8px;
      }
    }
    .change-tag{
      color: $default-color;
      cursor: pointer;
      display: flex;
      background: white;
      padding: 0;
      font-size: 14px;
      i{
        color: $disabled-color;
        font-size: 12px;
        margin-left: 8px;
      }
    }
    .change-tag:hover{
      color: $--color-primary;
      i{
        color: $--color-primary;
      }
    }
    .disabled-change-tag{
      color: $disabled-color;
      cursor: not-allowed;
    }

    .disabled-change-tag:hover{
      color: $disabled-color;
      i{
        color: $disabled-color;
      }
    }
  }
  .p-title{
    padding-top: 24px;
    font-size: 18px;
    color: $--color-primary;
    font-weight: 500;
    padding-bottom: 32px;
    text-align: center;
  }
  .active-content{
    display: flex;
    justify-content: center;
    height: calc(100% - 105px);
  }
}
</style>
