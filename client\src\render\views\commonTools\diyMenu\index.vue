
<template>
  <div id="f-diy-menu">
    <div class="g-main">
      <template v-if="activeMenuList.length > 0">
        <div v-for="(item, key) in activeMenuList" :key="key" class="u-menu-item" @click="openUrl(item.url)">
          <div class="u-pic">
            <img :src="item.pic" @error="handleImageError">
          </div>

          <el-tooltip :key="key" class="item" :content="item.name" placement="bottom">
            <div class="u-title">{{ item.name }}</div>
          </el-tooltip>
        </div>
      </template>

      <template v-else>
        <div class="empty-info">
          <div class="empty-bg">
            <img src="@/render/assets/stateIllustration/empty.png" width="260">
          </div>
          <p>{{ $t('thirdLinkAgeMenu.empty') }}</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import urlUtils from '@/render/utils/url'
import agentApi from '@/service/api/agentApi'
const imgSrc = require('@/render/assets/diyMenuDefault.png') // loading 图片
export default {
  name: 'DiyMenu',
  data() {
    return {
      msg: 'hello',
      imgSrc: imgSrc
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    activeMenuList() {
      const menu = _.get(this.serveEntiretyConfig, 'sceneConfig.ActiveMenu', '')
      return this.parse(menu)
    }
  },
  methods: {
    parse(menu) {
      const menuArr = []
      const baseUrl = urlUtils.getBaseIPPort()
      menu = menu.split('|')
      menu.map(item => {
        const one = item.split('====')
        if (one.length === 3) {
          menuArr.push({
            name: one[0],
            url: one[1],
            pic: baseUrl + one[2]
          })
        }
      })
      return menuArr
    },
    openUrl(url) {
      agentApi.windowOpenUrl(url)
    },
    handleImageError(event) {
      console.log(event)
      event.target.src = imgSrc
    }
  }

}
</script>

<style lang="scss" scoped>
#f-diy-menu {

  padding: 40px;
  height: 100%;

  .g-main {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .u-menu-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 40px;
      cursor: pointer;

      .u-pic img {
        width: 56px;
        height: 56px;
      }

      .u-title {
        margin-top: 16px;
        font-size: 14px;
        color:#3c404d;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
      }
    }

    .empty-info {
      width: 100%;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #686E84;
      line-height: 22px;
      margin-top: 40px;
      font-weight: 500;

      .empty-bg {
        width: 100%;
        text-align: center;

        img {
          display: inline-block;
          margin-bottom: 20px;
        }
      }
    }

  }
}
</style>
