/*
 * @Author: <EMAIL>
 * @Date: 2021-08-12 16:20:23
 * @LastEditors: gening <EMAIL>
 * @LastEditTime: 2024-05-11 10:44:00
 * @Description: file content
 */
import proxyApi from '@/service/api/proxyApi'
import agentApi from '@/service/api/agentApi'
import Vue from 'vue'
import { i18n } from '@/render/lang'
import store from '@/render/store'
import dateUtil from '@/render/utils/date/dateUtil'
import dot1xCommon from '@/render/utils/auth/dot1x'
import authIndex from '@/render/utils/auth/index'
import qs from 'qs'
import { Base64 } from 'js-base64'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import { getRemoteLangPack } from '@/render/lang/index'
import $ from 'jquery'
import { destroyIframe } from '../iframe'
import { sleep, TestQtModule } from '@/render/utils/global'
import os_browser_info from '@/render/utils/os_browser_info'
import { clearToken, readToken, saveToken, readTokenAsync } from '@/render/utils/token'
import common from '@/render/utils/auth/common/index'

const util = {

  lastFromComponent: null,
  lastServerFromComponent: null,

  /**
   * 获取全局配置信息
   * @param {string} 从哪个组件请求过来的
   * @returns {Promise|boolean}
   */
  async server(fromComponent = 'other', showErr = true) {
    // 因为一进入layout组件就会请求一次，防止短时间内重复请求。可能会生成重复的deviceId
    if (this.lastServerFromComponent === 'layout') {
      this.lastServerFromComponent = fromComponent
      return false
    } else {
      this.lastServerFromComponent = fromComponent
    }

    // 1. 不在线时
    if (!_.get(store.state.clientInfo, 'online', false)) {
      this.lastServerFromComponent = null
      return false
    }
    const data = await util.getGlobalServerDispose(3, showErr)

    if (_.isEmpty(data)) {
      return
    }

    store.commit('setServeEntiretyConfig', {
      ...store.state.serveEntiretyConfig,
      ... { server: data }
    })
    return data
  },

  /**
   * 获取设备入网状态(并且存入全局状态)
   * @returns {Promise|boolean}
   */
  async netRet() {
    // 1. 不在线时
    if (!_.get(store.state.clientInfo, 'online', false)) {
      return false
    }
    const deviceId = _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store, 'state.clientInfo.basic.AgentID', 0)
    const data = await util.getDeviceNetStatus(deviceId)
    store.commit('setClientInfo', { ...store.state.clientInfo, ... { accessStatus: data }})
    // 已入网
    if (parseInt(data.deviceStatus) === 1) {
      store.commit('setAuthInfo', _.merge(
        {},
        store.state.authInfo,
        {
          basic: {
            AuthType: data.lastAuthType,
            UserName: data.userName,
            RoleCode: data.roleCode,
            RoleID: data.roleID
          }
        }
      ))
    } else if (parseInt(data.deviceStatus) === 0 && parseInt(_.get(store.state.clientInfo, 'detail.Registered')) === -2) {
      store.commit('setAuthInfo',
        {
          ...store.state.authInfo,
          ...{
            basic: null
          }
        }
      )
    }
    const platform = _.get(window, 'navigator.platform', '').toLowerCase()
    if (data && os_browser_info.os_type === 'windows' && platform.indexOf('linux') === -1) {
      agentApi.refreshDeviceStatus({ DeviceStatus: _.get(data, 'deviceStatus') })
    }

    return data
  },

  /**
   * 获取设备详细信息，并且存入store全局状态
   * @param {string} fromComponent
   * @returns {Promise|boolean}
   */
  async detail(fromComponent = 'other', showErr = true) {
    // 因为一进入layout组件就会请求一次，防止短时间内重复请求。可能会生成重复的deviceId

    if (this.lastFromComponent === 'layout') {
      this.lastFromComponent = fromComponent
      return false
    } else {
      this.lastFromComponent = fromComponent
    }

    // 如果没有deviceId，那么初始化basic

    // 1. 不在线的时候直接返回不去获取
    if (!_.get(store.state.clientInfo, 'online', false)) {
      this.lastFromComponent = null
      return false
    }
    const data = await util.getDetailDeviceInfo(3, showErr)
    if (_.isEmpty(data)) {
      return
    }
    store.commit('setClientInfo', { ...store.state.clientInfo, ... { detail: data }})
    return data
  },

  // 获取终端基础信息(deviceid,mac,ip等信息)
  async basic() {
    const basicInfo = await util.getBasicDeviceInfo()
    if (!_.get(basicInfo, 'AgentID')) {
      return false
    }
    store.commit('setClientInfo', { ...store.state.clientInfo, ... { basic: basicInfo }})
    return basicInfo
  },

  /**
   * 获取设备入网状态
   * @param {*} maxTries 最大尝试次数
   * @param {*} showErr 获取入网状态错误，是否显示错误
   * @returns
   */
  async getDeviceNetStatus(deviceId, maxTries = 3, showErr = true) {
    let err = i18n.t('getNetStatusFail')
    let netStatus = false
    const request = async() => {
      const ret = await proxyApi.getDeviceNetStatus({ deviceId }, { showError: false })
      if (!_.get(ret, 'errcode') || parseInt(_.get(ret, 'errcode')) !== 0 || !_.get(ret, 'data')) {
        err = i18n.t('getNetStatusFail') + (_.get(ret, 'errmsg') || '')
        return false
      } else {
        const isTokenEffect = await this.isLoseEffectToken(_.get(ret, 'data'))
        console.log('token失效了', isTokenEffect)
        if (isTokenEffect) { // 请求用了失效的token接口有部分数据没返回要重新请求
          store.commit('setAuthInfo', { ...store.state.authInfo, ...{ tokenInfo: '' }})
          await readToken()
          return false
        }
        util.setNetStatusToken(_.get(ret, 'data'))
        return _.get(ret, 'data')
      }
    }
    for (let i = 0; i < maxTries; i++) {
      netStatus = await request()
      if (netStatus !== false) {
        break
      }
    }

    if (netStatus === false && showErr) {
      Vue.prototype.$msg({
        type: 'error',
        message: err
      })
    }

    return netStatus
  },

  /**
   * 获取设备基本信息(通过小助手获得的基本设备信息)
   * @param {*} maxTries
   * @param {*} showErr
   * @returns
   */
  async getBasicDeviceInfo(maxTries = 3, showErr = true) {
    let basicDeviceInfo = false
    const err = i18n.t('getDeviceFail')
    const request = async() => {
      const ret = await agentApi.getClientBaseInfo(1 * 60 * 1000)
      if (!_.get(ret, 'ASM.AgentID')) {
        return false
      } else {
        return _.get(ret, 'ASM')
      }
    }

    for (let i = 0; i < maxTries; i++) {
      basicDeviceInfo = await request()
      if (basicDeviceInfo !== false) {
        break
      }
    }

    if (basicDeviceInfo === false && showErr) {
      Vue.prototype.$msg({
        type: 'error',
        message: err
      })
    }

    return basicDeviceInfo
  },

  /**
   * 获取设备详细信息
   * @param {*} maxTries
   * @param {*} showErr
   * @param {*} deviceId
   * @returns
   */
  async getDetailDeviceInfo(maxTries = 3, showErr = true) {
    let err = i18n.t('getDetailDeviceFail')
    let detailDeviceInfo = false

    // 接口请求的匿名函数
    const request = async(deviceId) => {
      if (isNaN(deviceId) || deviceId === 0) {
        throw new Error('deviceId必须存在')
      }
      // protal认证的时候需要用到的参数
      const query = qs.parse(location.search.substring(1))
      const basIP = _.get(query, 'basip', '')
      const UrlAscid = _.get(query, 'ascid', '')

      // 去远程服务请求
      const detail = await proxyApi.getDeviceinfoProcess(
        { deviceId: deviceId, form: 'AgentReg', basIP: basIP, UrlAscid },
        { showError: false, timeout: { time: 1 * 60 * 1000 }}
      )
      if (!_.get(detail, 'errcode') || parseInt(_.get(detail, 'errcode')) !== 0 || !_.get(detail, 'data')) {
        err = i18n.t('getServerInfoTimeOut') + (_.get(detail, 'errmsg') || '')
        return false
      } else {
        return _.get(detail, 'data')
      }
    }

    // 这里如果获取设备详细信息失败的话，就调取小助手接口重新上报设备信息
    for (let i = 0; i < maxTries; i++) {
      let deviceId = parseInt(_.get(store, 'state.clientInfo.basic.AgentID', 0))
      if (deviceId === 0 || isNaN(deviceId)) {
        // 请求接口获取设备id
        const ret = await util.basic()
        deviceId = parseInt(_.get(ret, 'AgentID'))
      }

      // 如果还是获取不到设备id，那就有问题了
      if (deviceId === 0 || isNaN(deviceId)) {
        Vue.prototype.$msg({ type: 'error', message: err })
        break
      }

      detailDeviceInfo = await request(deviceId)
      if (_.get(detailDeviceInfo, 'DeviceID')) {
        break
      } else {
        // 这里考虑的是后端接口删除设备id的情况下，要客户端重新上报设备信息，生产新的设备id
        const ret = await agentApi.updateClientBaseInfo()
        if (_.get(ret, 'ASM.AgentID')) {
          store.commit('setClientInfo', { ...store.state.clientInfo, ... { basic: _.get(ret, 'ASM') }})
        }
      }
    }

    if (detailDeviceInfo === false && showErr) {
      Vue.prototype.$msg({ type: 'error', message: err })
    }

    return detailDeviceInfo
  },

  /*
    获取全局状态信息，防止超时，重复请求多次
    获取全局的服务器配置信息
   * @param {*} maxTries 最大尝试次数
   * @param {*} showErr 是否显示获取错误信息
   * @returns
   */
  async getGlobalServerDispose(maxTries = 3, showErr = true) {
    let err = i18n.t('getServerInfoTimeOut')
    let globalConf = false
    const request = async() => {
      const query = qs.parse(location.search.substring(1))
      const basIP = _.get(query, 'basip', '')
      const firsturl = encodeURIComponent(_.get(query, 'firsturl', ''))
      // 3746加入了版本号
      const version = '6039.3746'

      const ret = await proxyApi.getGlobalServerDispose({ basIP, firsturl, version }, { timeout: { time: 1 * 60 * 1000 }})
      if (!_.get(ret, 'errcode') || parseInt(_.get(ret, 'errcode')) !== 0 || !_.get(ret, 'data')) {
        err = i18n.t('getServerInfoTimeOut') + (_.get(ret, 'errmsg') || '')
        return false
      } else {
        return _.get(ret, 'data')
      }
    }
    for (let i = 0; i < maxTries; i++) {
      globalConf = await request()
      if (globalConf !== false) {
        break
      }
    }

    if (globalConf === false && showErr) {
      Vue.prototype.$msg({
        type: 'error',
        message: err
      })
    }
    return globalConf
  },

  // 获取802.1x配置
  async getDot1xConfig() {
    return await dot1xCommon.initDot1xConfig()
  },

  // 核心函数。它根据服务器配置的信息，当前设备的入网状态，当前设备的设备状态来计算出欢迎页的状态
  computeNetAccessStatus(state) {
    const currentStatus = {
      isAccess: null, // 是否已入网 null表示初始化尚未完成
      isZtpAbnormal: false, // 是否零信任异常
      netLetGo: false, // 是否直接放开网络
      cutOff: false, // 是否隔离
      registered: false, // 是否注册
      ipMacBindIllegal: false, // 是IP/MAC绑定违规
      fingerIllegal: false, // 是否指纹违规
      isAjcCut: false, // 是否哨兵违规
      noRegisteredStatus: {
        showAgreement: false // 是否显示隐私协议
      },
      registeredStatus: {
        needAduit: false, // 是否需要审核状态
        neddRegisteredAgain: false, // 被要求重新注册
        auditEnd: false, // 超过使用期限
        auditEndAlarn: false // 即将过期的时间提醒
      },
      isOfflineDot1x: false, // 802.1x模式下且离线
      isOfflineKnockPort: false // 需要敲端口
    }

    const { clientInfo, serveEntiretyConfig } = state

    if (!_.get(clientInfo, 'online', false) && parseInt(_.get(clientInfo.basic, 'AgentMode', 0)) === 1) {
      currentStatus.isOfflineDot1x = true
    }

    // 离线敲端口模式
    if (parseInt(_.get(clientInfo.basic, 'AgentMode', 0)) === 0 && _.get(clientInfo.webSlot, 'isKnockPort') && !_.get(store.state.clientInfo, 'online', false)) {
      currentStatus.isOfflineKnockPort = true
    }

    if (_.get(clientInfo, 'accessStatus.deviceStatus') !== undefined) {
      // =============
      // 判断是否已入网
      // ==============
      if (parseInt(_.get(clientInfo, 'accessStatus.deviceStatus')) !== 0 && parseInt(_.get(clientInfo, 'detail.Registered')) !== 0) {
        currentStatus['isAccess'] = true
      } else {
        currentStatus['isAccess'] = false
      }

      // =============
      // 判断是否直接放开网络
      // ==============
      if (currentStatus['isAccess']) {
        currentStatus['netLetGo'] = this.whetherDirectLetGoDevice(serveEntiretyConfig, clientInfo)
      }

      // =============
      // 判断设备是否隔离
      // ==============
      if (_.get(clientInfo, 'detail.CutOffStopTime') >= _.get(serveEntiretyConfig, 'server.TIME')) {
        currentStatus.cutOff = true
        if (parseInt(_.get(clientInfo, 'detail.Registered')) === 0) { // 隔离可执行入网需要审核状态
          currentStatus.registeredStatus.needAduit = true
        }
        return currentStatus
      }

      // ====================
      // 存在IP,MAC绑定违规
      // ====================
      if (parseInt(_.get(clientInfo, 'detail.ipMacBindIllegal')) === 1) {
        currentStatus.ipMacBindIllegal = true
        return currentStatus
      }

      // ====================
      // 指纹违规
      // ====================
      if (parseInt(_.get(clientInfo, 'detail.fingerIllegal')) === 1) {
        currentStatus.fingerIllegal = true
        return currentStatus
      }

      // ====================
      // 是否安装哨兵入网
      // ====================
      if (parseInt(_.get(clientInfo, 'detail.isAjcCut')) === 1) {
        currentStatus.isAjcCut = true
        return currentStatus
      }

      // ====================
      // 判断设备是否已注册
      // ====================
      currentStatus.registered = parseInt(_.get(clientInfo, 'detail.Registered')) !== -2
      // ====================
      // 设备如果未注册，是否显示隐私协议
      // ====================
      if (!currentStatus.registered) {
        if (_.get(serveEntiretyConfig, 'server.ADMINISTRATORINFO') &&
          parseInt(_.get(serveEntiretyConfig, 'server.ADMINISTRATORINFO.ShowAgreement')) === 1 &&
          !_.isEmpty(_.get(serveEntiretyConfig, 'server.ADMINISTRATORINFO.AgreementContent'))
        ) {
          currentStatus.noRegisteredStatus.showAgreement = true
          return currentStatus
        }
      }

      // ====================
      // 设备注册但需要审核
      // ====================
      if (parseInt(_.get(clientInfo, 'detail.Registered')) === 0) {
        currentStatus.registeredStatus.needAduit = true
        return currentStatus
      }

      // ====================
      // 设备注册未超过使用期限，但是达到了报警时间
      // ====================
      currentStatus.registeredStatus.auditEndAlarn = (function() {
        var SDateObj = dateUtil.formatDate(_.get(clientInfo, 'detail.AuditStopTime'))
        var NDateObj = dateUtil.formatDate(_.get(serveEntiretyConfig, 'server.TIME'))
        if (SDateObj && NDateObj) {
          var S = new Date(SDateObj.getDates[0], SDateObj.getDates[1], SDateObj.getDates[2], SDateObj.getTimes[0], SDateObj.getTimes[1], SDateObj.getTimes[2])
          var N = new Date(NDateObj.getDates[0], NDateObj.getDates[1], NDateObj.getDates[2], NDateObj.getTimes[0], NDateObj.getTimes[1], NDateObj.getTimes[2])
          var diff = S.getTime() - N.getTime()
          var days = diff / (1000 * 60 * 60 * 24)
          if (parseInt(_.get(serveEntiretyConfig, 'server.CLIENTCHECK.IsAuditCall')) === 1 &&
            days <= parseInt(_.get(serveEntiretyConfig, 'server.CLIENTCHECK.AuditCallDay')) &&
            days > 0
          ) {
            const content = _.get(serveEntiretyConfig, `server.CLIENTCHECK.${i18n.locale === 'zh' ? 'AuditCallContent' : 'AuditCallContent_en'}`) + i18n.t('devOutTimeTips') +
              '<span style=\'color:red;font-weight:bold;\'>' + _.get(clientInfo, 'detail.AuditStopTime') + '</span>'
            return content
          } else {
            return false
          }
        }
      })()

      // ====================
      // 设备注册且超过使用期限
      // ====================
      if ((_.get(clientInfo, 'detail.AuditStopTime') < _.get(serveEntiretyConfig, 'server.TIME')) &&
        _.get(clientInfo, 'detail.AuditStopTime') !== '1970-01-01 00:00:00'
      ) {
        currentStatus.registeredStatus.auditEnd = true
        return currentStatus
      }

      // ====================
      // 设备注册并被要求重新注册，跳转到认证页面，然后跳转到注册页面并显示为啥要重新注册
      // ====================
      if (parseInt(_.get(clientInfo, 'detail.Registered')) === -1) {
        currentStatus.registeredStatus.neddRegisteredAgain = true
        return currentStatus
      }
    }

    // 判断零信任状态异常
    if (parseInt(_.get(clientInfo, 'accessStatus.ZtpUser.ZtpUser')) === 1) {
      if (!_.get(clientInfo, 'accessStatus.Token', '') || parseInt(_.get(clientInfo, 'accessStatus.SessionStatus', 0)) === 2) {
        currentStatus['isZtpAbnormal'] = true
      }
    }

    return currentStatus
  },

  // 是否直接放开网络设备
  whetherDirectLetGoDevice(serveEntiretyConfig, clientInfo) {
    // 紧急模式且开启Aruba协议
    if (parseInt(_.get(serveEntiretyConfig, 'server.isEmergency')) !== 0 &&
      _.get(serveEntiretyConfig, 'server.isArubaAccess')) {
      return true
    }
    // 漫游设备
    if (parseInt(_.get(clientInfo, 'detail.isWalkDevice')) === 1) {
      return true
    }
    // 浏览器指纹放开网络
    if (parseInt(_.get(clientInfo, 'detail.isBrowserFingerprintDevice')) === 1) {
      return true
    }
    // 极速入网
    if (parseInt(_.get(clientInfo, 'detail.IsPermit')) === 1) {
      return true
    }

    return false
  },

  // 802.1x配置（web提供客户端）
  async getDot1xInfo() {
    if (!authIndex.isDot1xMode()) {
      return
    }
    store.commit('setServeEntiretyConfig', {
      ...store.state.serveEntiretyConfig,
      ... {
        client: await util.getDot1xConfig()
      }
    })
  },

  // 告知小助手场景相关信息
  async setDevAccessInfo() {
    const params = {}
    // 全局状态变量里没有就从url里面获取
    params.SceneID = _.get(store.state.serveEntiretyConfig, 'scene.SceneID', 0)
    params.SceneName = _.get(store.state.serveEntiretyConfig, 'scene.SceneName', '')
    params.PolicyID = _.get(store.state.serveEntiretyConfig, 'scene.PolicyID', 0)
    params.IsSafeCheck = _.get(store.state.serveEntiretyConfig, 'scene.IsSafeCheck', 0)

    await agentApi.setDevAccessInfo(params)
  },
  /**
   * @description: 判断零信任连接数
   * @return {*}
   */
  async ztpUserExceed() {
    const deviceid = _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store, 'state.clientInfo.basic.AgentID', 0)
    const ret = await proxyApi.getZtpUserExceed({ deviceid })
    const ztpUserExceed = _.get(ret, 'data.val', true)
    if (!ztpUserExceed) {
      Vue.prototype.$msg({
        type: 'error',
        message: i18n.t('sourcePlatform.ztpUserExceed')
      })
    }
  },
  /**
   * 安检结果上报(通过客户端上报安检结果到服务端接口，然后服务端再调用相关逻辑放开网络)
   * @param {*} _param
   * @param {*} checkRes(true,则表示安检通过或安检存在隐患)
   */
  async securityCheckRePort(_param) {
    const basicInfo = _.get(store, 'getters.authInfo.basic', {})
    const tokenInfo = await readToken()
    const userId = _.get(basicInfo, 'UserID') || _.get(store.state, 'clientInfo.accessStatus.UserID', '')
    let params = {
      IsRecheck: 0,
      AuditStatus: 0,
      token: _.get(basicInfo, 'Token', tokenInfo.token),
      UserID: userId,
      IsAutoCheck: 0
    }
    if (_param && _.isObject(_param)) {
      params = { ...params, ..._param }
    }
    const checkRes = await agentApi.securityCheckEnd(params)
    if (_.get(checkRes, 'ASM.CheckResult', false) === 'success' || _.get(checkRes, 'ASM.CheckResult', false) === 'fault') {
      this.portalAuth()
      await commonUtil.setLoginRet()
      commonUtil.getWebGateWay()
    }
    // 判断零信任连接数
    this.ztpUserExceed()
    return checkRes
  },

  // 检测443端口或者其他自定义的端口是否连通（即能否请求web接口）
  async checkDefaultPort(isFirst) {
    if (!TestQtModule('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer')) { // 客户不存在该模块不调接口
      return
    }
    const isPass = await this.webTestNet() // 尝试调用web接口探测网络是否通
    const spaParams = { 'SPAType': 'Test', 'ServerIP': '', 'AuthData': '', 'DevData': '' }
    if (isPass) {
      this.initKnockPort({ ASM: { Result: 'true' }}, isFirst)
      agentApi.spaToAuth(spaParams).then((res) => {
        this.testNeedSwitchNet(res)
      })
    } else {
      const res = await agentApi.spaToAuth(spaParams)
      this.initKnockPort(res, isFirst)
      this.testNeedSwitchNet(res)
    }
  },
  initKnockPort(res, isFirst) { // 设置敲端口相关参数
    const ret = _.get(res, 'ASM.Result', 'true')
    const netCardOk = _.get(res, 'ASM.IsPrepared', 'true') === 'true' // 网卡状态-与离线时是否显示404页面相关
    const webSlot = { portConncet: ret === 'true' }
    if (isFirst) { // 443不通且layout页面调用才设置是否需要敲端口
      webSlot.isKnockPort = ret === 'false' ? netCardOk : false
    }
    this.setWebSlot(webSlot)
  },
  async webTestNet() {
    const ret = await proxyApi.getEnvStatus()
    return parseInt(_.get(ret, 'errcode', -1)) === 0
  },
  // 根据ASM.ChangeLink内信息判断是否需要切换网络
  testNeedSwitchNet(testInfo) {
    if (parseInt(_.get(testInfo, 'ASM.ChangeLink.NeedPrompt')) === 1) { // 当前网络不通，可切换内网或外网
      const isEx = parseInt(_.get(testInfo, 'ASM.ChangeLink.CanLink'))
      if (isEx !== 1 && isEx !== 0) {
        return
      }
      agentApi.changeNetMode({ IsUseEx: isEx })
    }
  },
  // 敲端口模式获取信息
  async getSpaToAuthInfo() {
    // 8021x 模式则敲端口无需前端处理
    if (authIndex.isDot1xMode() || !_.get(store, 'state.clientInfo.webSlot.isKnockPort', false)) {
      return false
    }
    const client = await util.getDot1xConfig() // 读取本地配置
    store.commit('setServeEntiretyConfig', {
      ...store.state.serveEntiretyConfig,
      ... { server: client.server }
    })
    return ''
  },
  // 发送短信
  async knockPortSms(params) {
    const AuthData = Base64.encode(params.phone || '') + '|' + Base64.encode(params.isGuestAuth || 0)
    const basic = _.get(store, 'state.clientInfo.basic', {})
    const DevData = `${Base64.encode(basic.Ip)}|${Base64.encode(basic.Mac)}|${Base64.encode(basic.HardId)}`
    await agentApi.spaToAuth({
      SPAType: 'Code',
      ServerIP: basic.ServerIP,
      AuthData,
      DevData
    })
    // 客户端无法知道结果，默认返回true
    return true
  },
  // 敲端口 -认证前调用敲开443端口
  async knockPort(authData) {
    if (_.get(store, 'state.clientInfo.online', false)) { // 443端口连通则不调用
      return true
    }
    if (!TestQtModule('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer')) { // 客户不存在该模块不调接口
      return true
    }
    if (authIndex.isDot1xMode()) { // 8021 不敲端口
      return true
    }
    const formateTrade = this.formateData(authData)
    const basic = _.get(store, 'state.clientInfo.basic', {})
    const DevData = `${Base64.encode(basic.Ip)}|${Base64.encode(basic.Mac)}|${Base64.encode(basic.HardId)}`
    const res = await agentApi.spaToAuth({
      SPAType: 'Auth',
      ServerIP: basic.ServerIP,
      AuthData: formateTrade,
      DevData
    })
    this.testNeedSwitchNet(res)
    if (_.get(res, 'ASM.Result') === 'false') {
      Vue.prototype.$msg({
        type: 'error',
        message: _.get(res, 'ASM.ResultMsg')
      })
      return false
    } else {
      // 设置在线
      this.setWebSlot({ portConncet: true, isKnockPort: false })
      // 更新基本信息
      await this.resetBaseInfo()
    }
    return true
  },
  // 敲端口-认证通过后调用敲开37527端口
  async setLoginRet(_params = {}) {
    if (!TestQtModule('AssUIPluginZTPModule', 'WebCall_SetLoginRet')) { // 客户不存在该接口不调接口
      return true
    }
    const basicInfo = _.get(store, 'getters.authInfo.basic', {})
    const tokenInfo = await readToken()
    const serverInfo = _.get(store.state, 'serveEntiretyConfig.server')
    const defaultParams = {
      token: _.get(basicInfo, 'Token', tokenInfo.token),
      UserID: _.get(basicInfo, 'UserID', tokenInfo.UserID),
      LoginRet: '1',
      IPIsInException: serverInfo.IPIsInException,
      PriorLink: '0',
      AllowUserSwitch: _.get(serverInfo, 'EnvSwitch.AllowUserSwitch'),
      SwitchMode: _.get(serverInfo, 'EnvSwitch.SwitchMode'),
      VpnType: _.get(serverInfo, 'ZTP.VpnType'),
      ZtpUser: util.isZtpUser() ? '1' : '0'
    }
    const params = { ...defaultParams, ..._params }
    if (params.LoginRet === '1' && !params.token) {
      return false
    }
    const res = await agentApi.setLoginRet(params)
    if (_.get(res, 'ASM.Result') === 'false') {
      return false
    }
    return true
  },
  isZtpUser() {
    const query = qs.parse(location.search.substring(1))
    const urlZtp = _.get(query, 'ZtpUser', '')
    const authZtp = _.get(store, 'getters.authInfo.basic.ZtpUser', '') + ''
    const netStatusZtp = _.get(store.state, 'clientInfo.accessStatus.ZtpUser.ZtpUser', '') + ''
    return parseInt(authZtp || netStatusZtp || urlZtp || '0') === 1
  },
  // 设置是否是敲端口模式字段
  setWebSlot(obj) {
    const webSlot = _.get(store, 'state.clientInfo.webSlot', {})
    const newWebSlot = { ...webSlot, ...obj }
    store.commit('setClientInfo', { ...store.state.clientInfo, ... { webSlot: newWebSlot }})
  },
  // 敲端口完成更新基本信息
  async resetBaseInfo() {
    await Promise.all([commonUtil.detail(), commonUtil.server(), getRemoteLangPack()])
  },
  formateData(authData) {
    if (!authData || !_.isArray(authData)) {
      return authData
    }
    const data = []
    authData.forEach(item => {
      let s = Base64.encode(item)
      s = s.replace(/[+]/g, '_')
      s = s.replace(/=/g, '-')
      s = s.replace(/\//g, '.')
      data.push(s)
    })
    return data.join('|')
  },
  // 存储浏览器带过来的token
  async initToken() {
    const clientTokenInfo = await readToken() // store读取token没有则从文件读取同时存入store

    // 看看浏览器是否带了token
    const query = qs.parse(location.search.substring(1))
    const browserTimestap = _.get(query, 'tokenTimestamp', 0)
    const token = _.get(query, 'token', '')
    const userId = _.get(query, 'userId', '')
    if (!token || !browserTimestap) {
      return
    }
    // 浏览器带过来的token必须比客户端的token新
    if (token && token !== 'undefind' && browserTimestap > clientTokenInfo.tokenTimestamp) {
      // 小助手卸载localstorage任然存在因此需要更新userid bugID=7618
      if (userId) {
        const authInfo = _.get(store, 'getters.authInfo', {})
        // store.commit('setAuthInfo', { ...authInfo, ...{ basic: { UserID: userId, ID: userId }}})
        store.commit('setAuthInfo', _.merge(
          {},
          authInfo,
          {
            basic: {
              UserID: userId,
              ID: userId
            }
          }
        ))
      }
      saveToken({ token, tokenTimestamp: browserTimestap, UserID: userId }) // 外网情况浏览器客户端各有一个设备id，以客户端为准
    }
  },
  // 浏览器认证完成跳客户端vpn拨号
  async fromBrowserVpnDial() {
    const query = qs.parse(location.search.substring(1))
    const browserToken = _.get(query, 'token', '')
    const browserUserId = _.get(query, 'userId', '')
    const browserTimestamp = _.get(query, 'tokenTimestamp', 0)
    const ztpUser = _.get(query, 'ZtpUser', '')
    if (browserToken && browserUserId) {
      const clientTokenInfo = readTokenAsync()
      const { token, tokenTimestamp, UserID } = clientTokenInfo
      if (parseInt(UserID) !== parseInt(browserUserId) && parseInt(UserID)) { // 存的数据和浏览器带过来的不等调接口获取一下
        const ret = await commonUtil.netRet()
        if (ret.Token && ret.UserID) {
          this.setLoginRet({ token: ret.Token, UserID: ret.UserID, ZtpUser: _.get(ret, 'ZtpUser.ZtpUser', '') })
          return
        } else {
          console.debug('用户id不相等且netstatus无法获取token')
          return
        }
      }
      const isClientNew = tokenTimestamp > browserTimestamp
      const params = { token: isClientNew ? token : browserToken, UserID: isClientNew ? UserID : browserUserId }
      if (ztpUser !== '') {
        params.ZtpUser = ztpUser
      }
      this.setLoginRet(params)
    }
  },
  /**
   * 思科webAuth认证登录
   * 如果开启了Cisco的poral认证，则向一个默认地址发送认证请求
   * @return {void}
   */
  async portalAuth() {
    const isCiscoAccess = _.get(store.state.serveEntiretyConfig, 'server.isCiscoAccess', false)
    if (isCiscoAccess !== false) {
      destroyIframe('ciscoForm')
      $('<iframe id="ciscoForm" style="display: none" name="ciscoForm" frameborder="0" ></iframe').prependTo('body')
      const iframe = window.frames['ciscoForm']
      const iframeDoc = iframe.document
      iframeDoc.body.innerHTML = '\n' +
        '    <form method="POST" name="ciscoSub" action="">\n' +
        '        <input type="text" name="uname" value="">\n' +
        '        <input type="password" name="pwd" value="YiPortalAP168">\n' +
        '        <input type="submit">\n' +
        '    </form>'
      const ciscoForm = iframeDoc.ciscoSub
      ciscoForm.uname.value = await this.getUserName()
      ciscoForm.action = 'http://' + isCiscoAccess
      ciscoForm.submit()
    }
  },
  async getUserName() {
    const userName = _.get(store.state, 'authInfo.basic.UserName', '')
    if (!_.isEmpty(userName)) {
      return userName
    } else {
      // 获取下入网状态，从入网状态接口获取用户名
      await this.netRet()
      return _.get(store.state, 'clientInfo.accessStatus.userName', '')
    }
  },
  setNetStatusToken(data) {
    if (data && data.Token) {
      const deviceId = _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store, 'state.clientInfo.basic.AgentID', 0)
      saveToken({ token: data.Token, tokenTimestamp: data.TokenTimestamp, deviceId, UserID: data.UserID })
    }
    // bug 10926
    if (parseInt(_.get(data, 'ZtpUser.ZtpUser')) === 1 && !data.Token && parseInt(data.deviceStatus) === 1) {
      clearToken()
      console.debug('netStatus触发上报')
      commonUtil.setLoginRet({ token: '', UserID: '', LoginRet: '0' })
      if (os_browser_info.os_type !== 'mac') { //  redis重启会走这里,标记置为1到入网成功页面补掉setLogin重新连接vpn
        const basic = { ...store.state.clientInfo.basic, ...{ NeedSetLoginRet: 1 }}
        store.commit('setClientInfo', { ...store.state.clientInfo, ...{ basic: basic }})
      }
    }
    const userId = data.UserID || ''
    const authInfo = _.get(store, 'getters.authInfo', {})
    // store.commit('setAuthInfo', { ...authInfo, ...{ basic: { UserID: userId, ID: userId }}})
    const basic = _.get(store, 'getters.authInfo.basic', {})
    const newBasic = { ...basic, ...{
      UserID: userId,
      ID: userId
    }}
    store.commit('setAuthInfo', _.merge(
      {},
      authInfo,
      {
        basic: newBasic
      }
    ))
  },
  // 本地是否传了失效token
  async isLoseEffectToken(retData = {}) {
    const { Token: apiToken, deviceStatus, SessionStatus } = retData // 接口返回
    console.log('对比数据', deviceStatus, SessionStatus, apiToken)
    if (!apiToken && (parseInt(deviceStatus) === 1 || parseInt(SessionStatus) === 2)) { // 已入网或已认证没给token，可能失效
      const { token: storeToken } = readTokenAsync() // vue store 存的
      try {
        const file_token = await agentApi.fileTools.ActionLocalFile('assui-token', 'read')
        if (file_token && file_token.token && file_token.token !== storeToken) { // 客户端文件内token和vuex内token不一致
          return true
        }
      } catch (e) {
        console.log('文件未读取到token')
      }
    }
    return false
  },
  // 重定向跳转是否在例外
  isExceptionRedirect(url) {
    const excetionUrls = [
      'www.msftncsi.com/ncsi.txt',
      'captive.apple.com',
      'www.msftconnecttest.com/connecttest.txt',
      'www.msftconnecttest.com/redirect',
      'detectportal.firefox.com/success.txt'
    ]
    let isExRedirect = false
    excetionUrls.forEach(item => {
      if (url.indexOf(item) > -1) {
        isExRedirect = true
      }
    })
    return isExRedirect
  },
  // 第一次认证通过如双因子认证取消则掉一次下线
  async towFactoryCutoff() {
    // 注销账户的操作
    await proxyApi.cutoffDevice({
      device_id: _.get(store.state.clientInfo, 'detail.DeviceID'),
      remark: 'LogOut'
    })
    clearToken()
  },
  // 来宾放开网络
  async guestSubmit({ checkResult, lastCheckId }) {
    const isGuest = _.get(store, 'state.authInfo.basic.AuthType') === 'Guest'
    // 浏览器认证唤起做安检,无authInfo通过场景判断是否是来宾
    const isGuestScene = parseInt(_.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo.UserType', 1)) === 2
    if (!isGuest && !isGuestScene) {
      return false
    }

    const UserID = _.get(store, 'state.authInfo.basic.UserID', '') || _.get(store, 'state.clientInfo.accessStatus.UserID', '')
    const DeviceId = _.get(store.state, 'clientInfo.detail.DeviceID', '')
    await proxyApi.reqGuestSubmit({ UserID, DeviceId, checkResult, lastCheckId })
    // if (parseInt(_.get(ret, 'errcode')) === 0) {
    //   await commonUtil.setLoginRet()
    //   return true
    // }
    // return false
  },
  // IPAM重定向
  IPAMRedirect() {
    const conf = _.get(store.state.serveEntiretyConfig, 'server.ipamConfig', {})
    if (parseInt(conf.linkage) === 1 && parseInt(conf.dev_regulation_location) === 1 && conf.linkage_url && !sessionStorage.ipamRedirect) {
      sessionStorage.ipamRedirect = 1
      agentApi.windowOpenUrl(conf.linkage_url)
    }
  },

  async getNetCardInfo() {
    const ret = await agentApi.getGatewayInfos()
    let Gateway = _.get(ret, 'ASM.Gateways.Gateway', '')
    const GatewayIsEmpty = _.get(ret, 'ASM.GatewayIsEmpty', 'true')
    if (Gateway && (_.isObject(Gateway) || _.isArray(Gateway))) {
      if (Object.prototype.toString.call(Gateway) === '[object Object]') {
        Gateway = [Gateway]
      }
      // return Gateway
    } else {
      Gateway = []
    }
    return {
      GatewayIsEmpty,
      Gateway
    }
  },
  async getWebGateWay(delay = 0) {
    if (!_.get(store, 'state.clientInfo.online', false) || !authIndex.isSecurityZeroTrust()) { // 不在线不是零信任不调用
      console.debug('离线或者非零信任账户跳出')
      return []
    }
    const initGateInfo = _.get(store, 'state.gateWayInfos')
    const conf = await agentApi.queryVPNInfo()
    const VPNStatus = parseInt(_.get(conf, 'ASM.VPNStatus', 0))
    if (delay > 0) { // 开机自动认证后延时调用
      await sleep(3000)
    }

    const ret = await proxyApi.getGateway()
    if (parseInt(_.get(ret, 'errcode')) !== 0 || _.get(ret, 'data.val') === false) {
      if (_.get(ret, 'data.val') === false) {
        store.commit('setGateInfos', { ...initGateInfo, ...{ total: 0, VPNStatus }})
      }
      return []
    } else {
      const list = _.get(ret, 'data', [])
      store.commit('setGateInfos', { ...initGateInfo, ...{ total: list.length, VPNStatus }})
      return list
    }
  },
  // setLoginRet补刀
  setLoginRetPatch() {
    const NeedSetLoginRet = parseInt(_.get(store, 'state.clientInfo.basic.NeedSetLoginRet', 0)) === 1

    if (NeedSetLoginRet) {
      // 重置
      const basic = { ..._.get(store, 'state.clientInfo.basic', {}), ...{ NeedSetLoginRet: 0 }}
      store.commit('setClientInfo', { ...store.state.clientInfo, ...{ basic: basic }})
      console.debug('补刀触发上报')
      commonUtil.setLoginRet()
    }
  },
  async updateAuthInfo() {
    const deviceId = _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store, 'state.clientInfo.basic.AgentID', 0)
    const ret = await proxyApi.getAuthResult({ deviceid: deviceId })
    if (parseInt(_.get(ret, 'errcode')) !== 0 || !_.get(ret, 'data.Token')) {
      return false
    }
    store.commit('setAuthInfo', { basic: ret.data })
    await common.authEnd({
      type: _.get(ret, 'data.AuthType'),
      autoAuth: '0'
    })
    return _.get(ret, 'data')
  }
}

export default util
