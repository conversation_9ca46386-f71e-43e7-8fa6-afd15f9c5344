<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("accessNetwork.securityCheck.info_21") }}
          </p>
          <div v-for="item in softInfo" :key="item.AntiVirusName" class="pc-info">
            <img id="VirusSoftImg" :src="item.localImg" alt="">
            <div class="pc-info-rit">
              <div class="optional-item margin-style">
                {{ $t("accessNetwork.securityCheck.info_25") }}
                <span>{{ item.AntiVirusName }}</span>
              </div>
              <div class="optional-item margin-style ">
                {{ $t("accessNetwork.securityCheck.info_26") }}
                <span>{{ item.SoftVersion }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("accessNetwork.securityCheck.info_24") }}
                <span>{{ item.DatabaseVersion }}</span>
              </div>
              <div class="optional-item">
                {{ $t("accessNetwork.securityCheck.info_27") }}
                <span :class="{'error-color' : item.IsRuning !== 'Yes' }">{{ item.IsRuning === 'Yes'? $t('check.antivirussoft.js_5_rs') : $t('check.antivirussoft.js_6_rs') }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 达标值 -->
    <stand :stand-data="standData" />
    <!-- 如何修复 -->
    <howToFix :check-data="checkItemData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import stand from '../stand/VirusSoft.vue'
import howToFix from '../howToFix/VirusSoft.vue'
export default {
  name: 'CheckAntiVirusSoft',
  components: {
    checkResult,
    stand,
    howToFix
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message'),
        infoFlag: _.get(this.checkData, 'CheckResult.CheckType.Info')
      }
    },
    softInfo() {
      let softCheckInfo = _.get(this.checkData, 'CheckResult.CheckType.Info')
      const defaultImg = 'tyong'
      if (!softCheckInfo) {
        return []
      }
      if (!_.isArray(softCheckInfo)) {
        softCheckInfo = [softCheckInfo]
      }
      softCheckInfo.forEach(item => {
        const url = item.Image || defaultImg
        item.localImg = require(`@/render/assets/${url}.gif`)
      })
      return softCheckInfo
    },
    standData() {
      const _tableData = _.get(this.checkData, 'CheckType.Option.BaseCheckItem')
      if (!_tableData) {
        return []
      } else {
        if (!_.isArray(_tableData)) {
          return [_tableData]
        }
        return _tableData
      }
    },
    checkItemData() {
      return this.checkData
    }
  },
  mounted() {
    console.log(55)
  }
}
</script>
<style lang="scss" scoped>
#VirusSoftImg{
  width: 92px;
  height: 92px;
}
.optional-item .error-color{
  color: $error
}
</style>
