<template>
  <div v-loading="loading" class="drawer-content">
    <el-form ref="ruleForm" class="apply-res-form" :model="ruleForm" label-position="top" :rules="rules" :label-width="draLabelWidth">
      <el-form-item :label="$t('applyRes.name')" prop="resId">
        <el-cascader
          id="ui-ztp-res_application-select-choice_resource"
          v-model="ruleForm.resId"
          :placeholder="$t('applyRes.selectRes')"
          :options="resOptions"
          filterable
          :show-all-levels="false"
          popper-class="apply-res-cascader"
        >
          <template slot-scope="{ node, data }">
            <div :id="`ui-ztp-res_application-div-res_${data.value}`" class="text-clamp" style="max-width: 120px">
              <el-tooltip
                effect="light"
                :visible-arrow="false"
                popper-class="source-tool-tip"
                :content="data.label"
                placement="bottom-start"
                :open-delay="1000"
              >
                <span>{{ data.label }}</span>
              </el-tooltip>
            </div>
          </template>
          <template slot="empty">
            <div class="cascader-empty">
              <as-emptyCom img-width="110px" class="empty-com" :msg="$t('sourcePlatform.searchNull')" />
            </div>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item :label="$t('applyRes.time')" prop="apply_days">
        <el-select id="ui-ztp-res_application-select-choice_usetime" v-model="ruleForm.apply_days" :placeholder="$t('applyRes.selectTime')">
          <el-option v-for="item in days" :id="`ui-ztp-res_application-li-time_${item}`" :key="item" :label="item + $t('applyRes.day')" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('applyRes.reson')" prop="remark">
        <el-input id="ui-ztp-res_application-input-reason" v-model="ruleForm.remark" :rows="4" maxlength="100" show-word-limit type="textarea" :placeholder="$t('applyRes.preson')" />
      </el-form-item>
    </el-form>
    <div class="otp-wrapper">
      <button id="ui-ztp-res_application-div-cancel" class="public-line-medium-btn" @click="cancelHandle">{{ $t('applyRes.cancel') }}</button>
      <div id="ui-ztp-res_application-div-submit" class="public-medium-btn" @click="submitHandle">{{ $t('applyRes.submit') }}</div>
    </div>
  </div>

</template>
<script>
import emptyCom from './empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapGetters } from 'vuex'
import { Base64 } from 'js-base64'
export default {
  components: {
    'as-emptyCom': emptyCom
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      days: [
        '1', '3', '7', '14', '30'
      ],
      resOptions: [],
      ruleForm: {
        resId: '',
        apply_days: '1',
        remark: ''
      },
      rules: {
        resId: [
          {
            required: true,
            message: this.$t('applyRes.selectRes'),
            trigger: 'change'
          }
        ],
        apply_days: [
          {
            required: true,
            message: this.$t('applyRes.selectTime'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'authInfo'
    ]),
    draLabelWidth() {
      if (this.$i18n.locale === 'zh') {
        return '80px'
      }
      return '126px'
    }
  },
  mounted() {
    this.init()
    console.log(this.info)
  },
  methods: {
    async init() {
      this.loading = true
      await this.getResList()
      this.loading = false
      this.ruleForm.resId = [this.info.GroupResID, this.info.ResID]
    },
    async getResList() {
      const ret = await proxyApi.getApplyList({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID')
      })
      if (parseInt(ret.errcode) === 0) {
        const list = _.get(ret, 'data.resApplyList', [])
        const groups = {}
        const groMap = {}
        list.forEach((item) => {
          const obj = {
            value: item.ResID,
            label: item.ResName
          }
          if (!groups[item.GroupResID]) {
            groups[item.GroupResID] = []
          }
          groups[item.GroupResID].push(obj)
          groMap[item.GroupResID] = item.GroupName
        })
        const resList = []
        for (const k in groups) {
          resList.push({
            value: k,
            label: groMap[k],
            children: groups[k]
          })
        }
        console.log(resList)
        this.resOptions = resList
      }
    },
    cancelHandle() {
      this.$emit('changeVisible', false)
    },
    submitHandle() {
      this.$refs['ruleForm'].validate(async(valid, obj) => {
        if (valid) {
          this.loading = true
          const _params = { deviceid: _.get(this.clientInfo, 'detail.DeviceID') }
          const params = { ..._params, ...this.ruleForm }
          params.resId = params.resId[1]
          params.remark = Base64.encode(params.remark)
          const ret = await proxyApi.reqApply(params)
          this.loading = false
          if (parseInt(ret.errcode) === 0) {
            this.$message.success(this.$t('msgCenter.appOk'))
            this.$emit('changeVisible', false)
          } else {
            if (parseInt(ret.errcode) === 21148024) {
              this.getResList()
            }
            this.$message({
              message: ret.errmsg || this.$t('serverError'),
              type: 'error',
              offset: 15,
              customClass: 'apl-self-msg'
            })
          }
          console.log(this.ruleForm)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-content{
  padding: 10px 32px;
  position: relative;
  height: 100%;
  .otp-wrapper{
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      padding: 24px 32px;
      justify-content: flex-end;
      div{
          width: 88px;
      }
      .public-medium-btn{
          margin-left: 10px;
      }
      .public-line-medium-btn{
        width: 88px;
      }
  }
}
.cascader-empty{
 height: 180px;
}
.apply-res-form ::v-deep .el-form-item__label{
    line-height: 20px;
    color: $title-color;
}
.apply-res-form ::v-deep .el-input__inner{
    border-color: $gray-2;
}
.el-select, .el-cascader{
    width: 100%;
  }

</style>
<style lang="scss">
.apl-self-msg{
    right: 225px;
    left: initial;
    transform: translateX(50%);
}
.source-tool-tip {
  padding: 4px 8px;
  background: $gray-3;
  border: 1px solid $gray-2 !important;
  border-radius: 1px;
  box-shadow: 0px 0px 6px 0px rgba(16, 36, 66, 0.2);
  font-size: 12px;
  color: $title-color;
  line-height: 17px;
  max-width: 170px;
}
.apply-res-cascader{
  .el-cascader-menu{
    width: 193px;
  }
}
</style>
