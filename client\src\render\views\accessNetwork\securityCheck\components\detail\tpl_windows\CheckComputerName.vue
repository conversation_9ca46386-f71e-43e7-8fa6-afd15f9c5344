<template>
  <div class="check-computer-name-page">
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.computerName.h_3_rs") }}
          </p>
          <div class="pc-info">
            <img
              :src="imgSrc"
              alt=""
            >
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.computerName.h_5_rd") }}
                  <span>{{ computerInfo.ComputerName }}</span>
                </div>
                <button :class="['btn-small',checkData.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="openDialog">
                  {{ $t("check.computerName.js_3_d") }}
                </button>
              </div>
              <div class="optional-item rule-desc">
                {{ $t("check.computerName.h_7_rd") }}
                <span>{{ computerInfo.RegularDesc }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />

    <!-- 计算机名称修改 -->
    <VerticalDialog
      :show.sync="passwordVisible"
      :title="$t('check.computerName.js_3_d')"
      @beforeClose="closeHanlde"
    >
      <div id="checkChangeComputerName" class="dialog-content">
        <div class="form-content">
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            label-width="0px"
            class="rule-form"
          >
            <el-form-item label="" prop="computerName">
              <el-input
                v-model="ruleForm.computerName"
                :placeholder="$t('check.computerName.h_26_rs')"
              ><i
                slot="prefix"
                class="iconfont icon-CheckComputerName"
              /></el-input>
            </el-form-item>
          </el-form>

          <div class="rule-content">
            <div>{{ $t("check.computerName.h_27_rs") }}</div>
            <div>{{ computerInfo.RegularDesc }}</div>
          </div>
        </div>

        <div class="dialog-footer">
          <div class="cancel-btn btn" @click="cancelHandle">
            {{ $t("check.VulnerablePassword.h_18_rd") }}
          </div>

          <div class="confirm-btn btn" @click="submitForm('ruleForm')">
            {{ $t("check.VulnerablePassword.h_19_rd") }}
          </div>
        </div>
      </div>
    </VerticalDialog>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'
import store from '@/render/store'

export default {
  name: 'CheckComputerName',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    const user_name = _.get(store.state.clientInfo, 'accessStatus.UserName', '') || _.get(store.state.authInfo, 'basic.UserName', '')
    const str_pos = user_name.indexOf('@')
    let username = user_name
    const validateComputerName = (rule, value, callback) => {
      console.log(this.ComputerNameRegular)
      if (username && this.ComputerNameRegular === '#adUser') {
        if (str_pos > 0 && this.ComputerNameRegular === '#adUser') {
          username = user_name.substring(0, str_pos)
        }
        this.ComputerNameRegular = username
      }
      if (!value) {
        callback(new Error(this.$t('check.computerName.h_3_rs')))
      } else {
        if (this.ComputerNameRegular) {
          const rules = this.ComputerNameRegular.replace(/\\\\/g, '\\')
          const reg = new RegExp(rules, 'i')
          if (!reg.test(value)) {
            callback(new Error(this.$t('check.computerName.h_3_rs')))
          }
        }
        callback()
      }
    }
    return {
      collapseFlag: true,
      passwordVisible: false,
      ruleForm: {
        computerName: ''
      },
      rules: {
        computerName: [{ validator: validateComputerName, trigger: 'blur' }]
      },
      ComputerNameRegular: '',
      imgSrc: require('@/render/assets/CheckComputerName.gif')
    }
  },
  computed: {
    computerInfo() {
      const info = _.get(this.checkData, 'CheckResult.Info')
      return {
        ComputerName: info.ComputerName,
        RegularDesc: info.RegularDesc
      }
    },
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    },
    fixData() {
      const regular = _.get(this.checkData, 'CheckType.Option.RegularDesc')
      return {
        modelTitle: this.$t('check.OSVersion.h_3_rs'),
        fixSteps: [
          this.$t('check.computerName.h_17_rs'),
          this.$t('check.computerName.h_19_rd') + regular,
          this.$t('check.computerName.h_21_rs'),
          this.$t('check.computerName.h_25_rs')
        ]
      }
    }
  },
  mounted() {
    this.getComputerNameRegular()
  },
  methods: {
    getComputerNameRegular() {
      this.ComputerNameRegular = _.get(this.checkData, 'CheckType.Option.ComputerNameRegular')
    },
    openDialog() {
      if (this.checkData.hasFixed) {
        return
      }
      this.passwordVisible = true
    },
    closeHanlde() {
      this.$refs.ruleForm.resetFields()
    },
    cancelHandle() {
      this.passwordVisible = false
      this.closeHanlde()
    },
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          const params = {
            ItemID: this.checkData.ItemID,
            InsideName: this.checkData.InsideName,
            RepairParam: {
              computer_name: this.ruleForm.computerName
            },
            RepairType: 0,
            CreateProgress: 1
          }
          const res = await this.submitHandle({
            params,
            CheckItem: this.checkData,
            tip: this.$t('check.computerName.js_3_d')
          })
          console.log(res)
          this.cancelHandle()
        }
      })
    }
  }
}
</script>
<style lang="scss">
.check-computer-name-page {
  .rule-desc {
    margin-top: 4px;
  }
}
#checkChangeComputerName{
  .form-content {
    padding: 24px;
    .rule-content {
      & > div {
        color: $default-color;
        font-size: 12px;
      }
      .mutil-rule {
        margin-left: 13px;
      }
    }

    .old-password-tip {
      line-height: 17px;
      font-size: 12px;
      color: $disabled-color;
      margin-top: 4px;
      margin-bottom: 3px;
    }
  }
}

.dialog-footer {
  display: flex;
  .btn {
    width: 50%;
    height: 32px;
    line-height: 31px;
    text-align: center;
    font-size: 13px;
    color: $default-color;
    border-top: 1px solid $line-color;
    cursor: pointer;
    &:hover {
      background: $--color-primary;
      color: white;
      border-left-color: $--color-primary;
      border-top-color: $--color-primary;
    }
  }
  .cancel-btn {
    border-bottom-left-radius: 5px;
  }
  .confirm-btn {
    border-left: 1px solid $line-color;
    color: $--color-primary;
    border-bottom-right-radius: 5px;
  }
}
#checkChangeComputerName .el-form-item__error {
  position: absolute;
  top: 100%;
}

#checkChangeComputerName .el-form-item {
  margin-bottom: 24px;
}
</style>
