<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle check-profile">
      <p class="model-title">
        {{ $t("check.CheckProfile.js_4_s")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <div v-for="(item, index) in fileList" :key="item.ProfileUUID" class="pc-info">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.CheckProfile.js_5_s") }}
                  <span>{{ item.ProfileUUID }}</span>
                </div>
                <span v-if="item.RepairType === 'url'" :class="[checkData.hasFixed ? 'disable-link-btn': 'link-btn']" @click="fixHandle(item, index)">{{ $t('check.CheckProfile.js_2_s') }}</span>

                <button v-if="item.RepairType === 'path'" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t('check.CheckProfile.js_3_s') }}
                </button>
              </div>
              <div class="optional-item">
                {{ $t("check.CheckProfile.js_1_s") }}
                {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckSoftInstallStat',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      fileList: []
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getFileList(this.checkData)
  },
  methods: {
    async fixHandle(item, index) {
      if (item.RepairType === 'url') {
        if (item.Url) {
          this.openUrl(item.Url)
        }
      } else if (item.RepairType === 'path') {
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            Path: item.Path,
            Param: item.Param
          },
          RepairType: 0,
          CreateProgress: 0
        }
        await this.submitHandle({
          params,
          CheckItem: item,
          needProccess: true,
          tip: this.$t('check.CheckProfile.js_6_s')
        })
        this.$set(this.fileList, index, item)
      }
    },
    getFileList(checkData) {
      // 看是否存在备份
      const list = _.get(this.checkData, 'CheckResult.CheckType.list')
      if (list) {
        list.forEach(item => {
          item.hasFixed = this.checkData.hasFixed
        })
        this.fileList = list
        return
      }
      let mustInstall = _.get(checkData, 'CheckResult.CheckType.Profiles')
      let policy = _.get(checkData, 'CheckType.Option.Item')
      if (!mustInstall || !policy) {
        return
      }
      mustInstall = mustInstall.split('|')
      if (!_.isArray(policy)) {
        policy = [policy]
      }
      const tmpObj = []
      mustInstall.forEach((item, index) => {
        for (let p = 0; p < policy.length; p++) {
          if (item === policy[p].ProfileUUID) {
            tmpObj.push({
              ProfileUUID: policy[p].ProfileUUID,
              remark: policy[p].Remark,
              RepairType: policy[p].RepairType,
              Url: policy[p].Url,
              Path: policy[p].Path,
              Param: policy[p].Param
            })
            tmpObj[index].hasFixed = this.checkData.hasFixed
          }
        }
      })
      this.checkData.CheckResult.CheckType.list = tmpObj // 存备份下次进来使用备份
      this.fileList = tmpObj
    }
  }
}
</script>
<style lang="scss" scoped>
.check-profile .model-content .pc-info .pc-info-rit {
  padding-left: 0;
}
</style>
