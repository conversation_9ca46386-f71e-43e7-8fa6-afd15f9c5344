<!-- 管理员权限 -->
<template>
  <div id="f-guest-receive">
    <div class="u-content">
      <g-tab v-model="activeTab" />
      <component :is="activeTab" class="active-menu-wrapper" />
    </div>
  </div>
</template>

<script>
import state from '@/render/components/layout/state'
import { mapState } from 'vuex'
import gTab from './components/gTab.vue'
import receive from './components/receive'
import audit from './components/audit'
import manage from './components/manage'
const empty = require('@/render/assets/stateIllustration/empty.png')
export default {
  components: {
    layoutState: state,
    gTab,
    receive,
    audit,
    manage
  },
  data() {
    return {
      stateImg: empty,
      activeTab: 'receive'
    }
  },
  computed: {
    ...mapState(['serveEntiretyConfig', 'clientInfo'])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const activeType = this.$route.query.activeType
      const { NetCode, IsNeedAppoint, AllowGuestTeam } = _.get(this.clientInfo, 'accessStatus', {})
      const canReceive = parseInt(NetCode) === 1 || parseInt(IsNeedAppoint) === 1 || parseInt(AllowGuestTeam) === 1
      const defaultType = canReceive ? 'receive' : 'audit'
      this.activeTab = activeType || defaultType
    }
  }
}
</script>
<style lang="scss">
#f-guest-receive {
  height: 100%;
  width: 100%;
  padding: 24px 24px 16px 24px;
  .u-forbid{
    height: 100%;
    display: flex;
    align-items: center;
  }
  .u-content{
    height: 100%;
    .active-menu-wrapper{
      height: calc(100% - 94px);
    }
  }
}
</style>
