import Auth from './auth'
import store from '@/render/store'
import common from './common'
import proxyApi from '@/service/api/proxyApi'
import authTypes from './authTypes'
import { Message } from 'element-ui'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { i18n } from '@/render/lang'

class Finger extends Auth {
  constructor() {
    super()
    this.type = authTypes.Finger
  }

  /**
   * 普通
   * @returns {Promise<boolean>}
   */
  async common(params) {
    // 免认证默认开启自动认证安检
    const apiParam = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID', 0),
      hintOver: G_VARIABLE.g_hintOver,
      type: this.type
    }

    _.assign(apiParam, params)

    const res = await proxyApi.authIndex(apiParam, { showError: false })

    const errorCode = parseInt(_.get(res, 'errcode', -1))
    if (errorCode === 0) {
      store.commit('setAuthInfo', { ...store.state.authInfo, ...{ basic: res.data }})
      await common.authEnd({
        type: this.type
      })
      Message.success(res.errmsg)
      return true
    }
    switch (errorCode) {
      case 21120030:
        // 超出最大可登录设备数
        return { revoke: true, data: {
          msg: res.errmsg,
          ...res.data
        }}
      default:
        Message.error(res.errmsg, i18n.t('auth.dot1xAuthFail'))
        return false
    }
  }

  isDot1xAuth() {
    return false
  }
}

export default Finger
