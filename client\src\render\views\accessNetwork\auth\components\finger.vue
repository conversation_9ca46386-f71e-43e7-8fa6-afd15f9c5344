<template>
  <!-- 指纹登录 -->
  <div class="finger-auth-page">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="0px"
      class="rule-form"
    >
      <div
        v-show="show.needFingerUserDevice"
        class="error-tip"
      >
        {{ $t('auth.insertFingerDeviceTip') }}
      </div>
      <el-form-item
        label=""
        prop="finger_id"
      >
        <el-input
          id="ui-accessNetwork-finger-input-finger_userid"
          v-model="ruleForm.fingerID"
          maxlength="50"
          :placeholder="$t('auth.userIdTip')"
        >
          <i
            slot="prefix"
            class="iconfont icon-ID"
          />
        </el-input>
      </el-form-item>
      <div
        v-show="!show.needFingerUserDevice"
        class="finger-wrapper"
      >
        <img src="../../../../assets/finger.png" alt="">
        <p>{{ $t('auth.enterFinger') }}</p>
      </div>
    </el-form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Finger from '@/render/utils/auth/finger'
import jas from '@/render/utils/auth/common/JAS'
import os_browser_info from '@/render/utils/os_browser_info'
import { EventBus } from '@/render/eventBus'

export default {
  name: 'FingerAuth',
  data() {
    return {
      show: {
        needFingerUserDevice: true
      },
      ruleForm: {
        fingerID: ''
      },
      jasFingerData: '', // 读取到的指纹特征
      jasAble: true,
      rules: {},
      finger: new Finger()
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig'
    ])
  },
  mounted() {
    if (!this.checkOsType()) {
      return
    }
    this.checkDevice()

    EventBus.$on('revoke:refresh', this.checkDevice)
  },
  beforeDestroy() {
    EventBus.$off('JAS:Version')
    EventBus.$off('JAS:Finger')
    EventBus.$off('revoke:refresh', this.checkDevice)
  },
  methods: {
    /**
     * 检查系统是否支持UKey
     */
    checkOsType() {
      const osType = os_browser_info.os_type || ''
      if (osType === 'mac') {
        this.$message.warning(this.$t('auth.fingerNoUKey'))
        return false
      }
      return true
    },
    checkDevice() {
      if (typeof (fpDevObj) === 'undefined') {
      // 加载控件
        try {
          // eslint-disable-next-line
           new ActiveXObject('MXUSBOCX.MxUsbOcxCtrl.1')
          return true
        } catch (e) {
        // 优先检测中正指纹仪 未安装 执行久安世
          try {
            this.getJASVersion()
          } catch (e) {
            this.$message.warning(this.$t('auth.needInstallControl'))
          }
          return false
        }
      // 中正指纹认证
      // this.ZZFinger.AuthByZZFinger()
      }
    },
    getJASVersion() {
      this.jasAble = false

      if (!jas.initVersion()) {
        this.jasAble = true
        jas.closeVersion()

        this.AuthByJASFinger()
      }

      EventBus.$off('JAS:Version')
      EventBus.$on('JAS:Version', (e) => {
        const data = e && e.data
        if (!_.isString(data) || data === '') {
          return
        }
        const res = JSON.parse(data)
        if (!_.isObject(res)) {
          return
        }

        // 检测是否安装控件 getVersion的返回结果
        switch (res['Code']) {
          case '200':
            this.jasAble = true
            jas.closeVersion()

            this.AuthByJASFinger()
            break
          default:
          case '300':
            this.$message.warning(this.$t('auth.noFingerDetected'))
            break
          case '301':
            this.$message.warning(this.$t('auth.fingerTurnOnFail'))
            break
          case '302':
            this.$message.warning(this.$t('auth.fingerReadFail'))
            break
          case '303':
            this.$message.warning(this.$t('auth.fingerCheckFail'))
            break
          case '304':
            this.$message.warning(this.$t('auth.fingerTimeOut'))
            break
          case '305':
            this.$message.warning(this.$t('auth.fingerFeatureFail'))
            break
          case '401':
            this.$message.warning(this.$t('auth.fingerDeviceOccupied'))
            break
        }
      })
    },
    AuthByJASFinger() {
      jas.initFinger()

      EventBus.$off('JAS:Finger')
      EventBus.$on('JAS:Finger', async(e) => {
        const data = e && e.data

        if (data.type && data.type === 'error') {
          return false
        }
        const res = JSON.parse(data)
        if (!_.isObject(res)) {
          return
        }

        if (res['Code'] === '200') {
          this.jasFingerData = res['FData']
          return await this.submitForm()
        }

        switch (res['Code']) {
          case '300':
            this.$message.warning(this.$t('auth.noFingerDetected'))
            break
          case '301':
            this.$message.warning(this.$t('auth.fingerTurnOnFail'))
            break
          case '302':
            this.$message.warning(this.$t('auth.fingerReadFail'))
            break
          case '303':
            this.$message.warning(this.$t('auth.fingerCheckFail'))
            break
          case '304':
            this.$message.warning(this.$t('auth.fingerTimeOut'))
            break
          case '305':
            this.$message.warning(this.$t('auth.fingerFeatureFail'))
            break
          default:
          case '400':
            this.show.needFingerUserDevice = false
            break
        }
      })
    },
    /**
     * 增加防抖处理，防止重复认证
     */
    submitForm: _.debounce(async function() {
      const fingerID = this.ruleForm.fingerID
      if (_.isEmpty(fingerID)) {
        this.$message.warning(this.$t('auth.needFingerID'))
        return false
      }
      EventBus.$off('JAS:Finger')

      const apiParam = {
        nID: fingerID,
        jasFingerData: this.jasFingerData,
        factory: 'JAS'
      }
      const res = await this.finger.auth(apiParam)

      if (res === false) {
        // 失败重新初始化监听
        this.AuthByJASFinger()
        return false
      } else if (_.isObject(res) && _.get(res, 'revoke', false) === true) {
        this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
        return false
      }
      jas.closeFinger()
      this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      return true
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style lang="scss" scoped>
.rule-form {
  position: relative;
  .error-tip {
    position: absolute;
    font-size: 12px;
    line-height: 17px;
    color:$error;
    z-index: 10;
    top:-20px
  }
  .finger-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 8px;
    padding-bottom: 24px;
    img {
      width: 72px;
      height: 72px;
    }
    p {
      font-size: 12px;
      font-weight: 400;
      color: $disabled-color;
      line-height: 17px;
      margin-top: 10px;
    }
  }
}
::v-deep .el-input .el-input__prefix .icon-ID{
  font-size: 16px;
}
</style>
