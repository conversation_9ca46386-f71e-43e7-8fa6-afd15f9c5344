<template>
  <div class="drawer-content">
    <el-form ref="ruleForm" class="proxy-set-form" :model="ruleForm" label-position="top">
      <el-form-item prop="tactics" class="radio-form-item" label="DNS代理模式">
        <span slot="label" class="flex-item-center">
          <label>DNS代理模式</label>
          <el-tooltip placement="right" popper-class="proxy-set-tool-tip">
            <div slot="content">虚拟网卡模式：使用虚拟网卡方式设置<br>127.0.0.100代理DNS请求<br>驱动模式：使用WinDivert驱动方式代<br>理DNS请求</div>
            <i class="iconfont icon-tishi" />
          </el-tooltip>
        </span>
        <el-radio-group v-model="ruleForm.VPNInterceptMode" :disabled="radioDisable">
          <el-radio id="ui-ztp-resource-radio-cardMode" :label="1">{{ $t('sourcePlatform.virCard') }}</el-radio>
          <el-radio id="ui-ztp-resource-radio-driveMode" :label="2">{{ $t('sourcePlatform.driver') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="otp-wrapper">
      <button id="ui-ztp-res_application-div-cancel" class="public-line-medium-btn" @click="cancelHandle">{{ $t('applyRes.cancel') }}</button>
      <div id="ui-ztp-res_application-div-submit" :class="['public-medium-btn', radioDisable?'btn-disabled':'']" @click="submitHandle"><i v-if="submiting" class="el-icon-loading" />{{ $t('applyRes.submit') }}</div>
    </div>
  </div>

</template>
<script>
import agentApi from '@/service/api/agentApi'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      ruleForm: {
        VPNInterceptMode: 1
      },
      submiting: false,
      radioDisable: false
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig'])
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const InterceptMode = parseInt(_.get(this.serveEntiretyConfig, 'server.InterceptMode', 0))
      if (InterceptMode === 5 || InterceptMode === 6) {
        this.$set(this.ruleForm, 'VPNInterceptMode', InterceptMode === 5 ? 1 : 2)
        this.radioDisable = true
        return
      }
      const ret = await agentApi.queryVPNInfo()
      const VPNInterceptMode = parseInt(_.get(ret, 'ASM.VPNInterceptMode', 0))
      const mode = VPNInterceptMode !== 0 ? VPNInterceptMode : InterceptMode
      this.$set(this.ruleForm, 'VPNInterceptMode', mode)
    },
    cancelHandle() {
      this.$emit('changeVisible', false)
    },
    async submitHandle() {
      if (this.radioDisable) {
        return
      }
      if (!this.submiting) {
        this.submiting = true
        const ret = await agentApi.setVPNInterceptMode({ VPNInterceptMode: this.ruleForm.VPNInterceptMode + '' })
        this.submiting = false
        if (parseInt(_.get(ret, 'ASM.ErrCode', -1)) === 0) {
          this.$message.success(this.$t('sourcePlatform.setSuccess'))
          this.$emit('changeVisible', false)
        } else {
          this.$message({
            message: _.get(ret, 'ASM.ErrorMsg') || this.$t('sourcePlatform.setFail'),
            type: 'error',
            offset: 15,
            customClass: 'apl-self-msg'
          })
        }
      } else {
        this.$emit('changeVisible', false)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-content{
  padding: 10px 32px;
  position: relative;
  height: 100%;
  .otp-wrapper{
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      padding: 24px 32px;
      justify-content: flex-end;
      div{
          width: 88px;
      }
      .public-medium-btn{
          margin-left: 10px;
          .el-icon-loading{
            margin-right: 4px;
          }
      }
      .btn-disabled{
        opacity: 0.8;
        cursor: not-allowed;
      }
      .public-line-medium-btn{
        width: 88px;
      }
  }
  .icon-tishi{
    color: $gray-7;
    font-size: 16px;
    cursor: pointer;
    &:hover{
      color:$--color-primary;
    }
  }
}
.apply-res-form ::v-deep .el-form-item__label{
    line-height: 20px;
    color: $title-color;
}

</style>
<style lang="scss">
.proxy-set-tool-tip{
  padding: 8px;
  font-size: 12px;
  line-height: 17px;
}
.proxy-set-form{
  .radio-form-item{
    .el-form-item__label{
      display: flex;
      align-items: center;
      height: 40px;
    }
    .el-form-item__content{
      line-height: 20px;
    }
  }
}
</style>
