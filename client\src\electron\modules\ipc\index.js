const { ipcMain, shell } = require('electron')
const { addTask } = require('./task')
const { moveTopOnce } = require('../window/handle')
const isSafeInteger = require('lodash/isSafeInteger')

const ipcEvent = {
  close: ({ winInstance }) => {
    winInstance.close()
  },
  hide: ({ winInstance }) => {
    winInstance.hide()
  },
  max: ({ winInstance }) => {
    winInstance.maximize()
  },
  noraml: ({ winInstance }) => {
    winInstance.unmaximize()
  },
  mini: ({ winInstance }) => {
    winInstance.minimize()
  },
  openDevTools: ({ winInstance }) => {
    winInstance.webContents.openDevTools()
  },
  // 把electron唤起到前台
  evokeAndFocus: ({ winInstance }) => {
    if (winInstance.isMinimized()) {
      winInstance.show()
    } else if (!winInstance.isVisible()) {
      winInstance.show()
    } else {
      moveTopOnce(winInstance)
    }
  },
  // 调用写日志
  log: ({ params }, log) => {
    log.info(params)
  },
  // 调用默认浏览器打开指定网站
  openUrl({ params }) {
    const { File, NeedWait } = params
    const open = (url) => {
      shell.openExternal(url)
    }

    if (isSafeInteger(NeedWait) && NeedWait > 0) {
      setTimeout(() => {
        open(File)
      }, NeedWait)
    } else {
      open(File)
    }
  }
}

const ipc = {
  listEvnet(winInstance, log) {
    ipcMain.on('request', (event, { eventName, params }) => {
      if (ipcEvent[eventName]) {
        ipcEvent[eventName]({ winInstance, params }, log)
      } else {
        console.warn(`该eventName${eventName}不存在`)
      }
    })

    ipcMain.on('requestAsync', (event, { serial, eventName, params }) => {
      addTask({ serial, event, params })
    })
  }
}

module.exports = ipc
