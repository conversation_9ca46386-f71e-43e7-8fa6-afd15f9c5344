const { nativeImage, BrowserWindow } = require('electron')
const ipc = require('../../ipc/index')
const registerEvent = require('../event')
const { getElectronVersion } = require('../../../utils/common')
const path = require('path')
const { parseInt } = require('lodash')

const afterCreate = ({ mainWindow, log, argv, rootDir }) => {
  // 为了尽可能的捕获日志,这个放在最前面
  oepnDevTools(argv, mainWindow)
  // 建立主进程和渲染进程的通信
  ipc.listEvnet(mainWindow, log)

  // 监听窗口,或者electron的事件
  registerEvent({ log, mainWindow, argv })

  setWindowProps(mainWindow, rootDir)
}

// 是否打开调试工具
const oepnDevTools = (argv, mainWindow) => {
  const debug = argv['devTools'] || false
  const version = getElectronVersion()
  if (debug) {
    // electron6.0版本的devTools会有问题,这里用hack的办法打开
    if (parseInt(version) === 6) {
      const devtools = new BrowserWindow()
      mainWindow.webContents.setDevToolsWebContents(devtools.webContents)
      mainWindow.webContents.openDevTools({ mode: 'detach' })
    } else {
      mainWindow.webContents.openDevTools()
    }
  }
}

// 设置窗口的一些属性
const setWindowProps = (mainWindow, rootDir) => {
  const appIcon = nativeImage.createFromPath(path.join(rootDir, '/assets/APP.png'))
  console.log(path.join(rootDir, '/assets/checkPass.png'))
  console.log(appIcon)
  console.log(appIcon.getSize())
  mainWindow.setIcon(appIcon)
}

module.exports = afterCreate
