module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module'
  },
  env: {
    browser: true,
    node: true,
    es6: true
  },
  globals: {
    _: true,
    lodash: true
  },
  // 预定义的配置：这里使用强烈推荐
  extends: ['plugin:vue/recommended', 'eslint:recommended'],

  rules: {
    // 强制每行允许的属性个数
    'vue/max-attributes-per-line': [2, {
      'singleline': 10,
      'multiline': {
        'max': 1,
        'allowFirstLine': false
      }
    }],
    // html元素或者组件元素前后必须换行
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    // 组件名按照PascalCase风格命名（单词以-或者_连接是不允许的）
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/no-v-html': 'off',
    // 规则在箭头函数的箭头（=>）之前/之后。使用两个空格
    'arrow-spacing': [2, {
      'before': true,
      'after': true
    }],
    // 在if或者else等代码块前后两个空格
    'block-spacing': [2, 'always'],
    // 花括号的格式。(不允许单行的花括号，花括号和if语句放同行，前后隔一个空格)
    'brace-style': [2, '1tbs', {
      'allowSingleLine': false
    }],
    // 强制所有的js变量使用驼峰法，不允许使用_或者-分割
    'camelcase': [0, {
      'properties': 'always'
    }],
    // 对象最后一个属性不允许使用逗号
    'comma-dangle': [2, 'never'],
    // 逗号分割变量的时候，逗号前两个空格，逗号后不能有空格（like:var a, b, c）
    'comma-spacing': [2, {
      'before': false,
      'after': true
    }],
    // 限定逗号放在变量后面，主要是防止IE浏览器在寻找一行的分割的时候报错
    'comma-style': [2, 'last'],
    // es6中class 查是否有一个有效的super()调用。
    'constructor-super': 2,
    // 无论单行还是多行情况下，if块或者其他块状语句后的花括号都不能省略（not recommad:if(foo) foo++ ）
    'curly': [2, 'all'],
    // JavaScript允许在成员表达式的点之前或之后放置换行符。
    'dot-location': [2, 'property'],
    // 禁止在文件或函数末尾出现换行符大于2
    'eol-last': 2,
    // 不允许使用==或者!==来判断
    'eqeqeq': ['error', 'always', { 'null': 'ignore' }],
    // 迭代函数通过在function关键字后面放置*来表示。
    'generator-star-spacing': [2, {
      'before': true,
      'after': true
    }],
    // 此模式期望一个Error对象或null作为回调的第一个参数，因为不处理这些错误会有些奇怪的现象
    'handle-callback-err': [2, '^(err|error)$'],
    // 处理嵌套语句的缩进，可以被eslint自动格式化
    'indent': [2, 2, {
      'SwitchCase': 1
    }],
    // JSX属性值可以包含用单引号或双引号分隔的字符串字面值。
    'jsx-quotes': [2, 'prefer-single'],
    // 强制在对象文字属性中的键和值之间保持两空格的间距
    'key-spacing': [2, {
      'beforeColon': false,
      'afterColon': true
    }],
    // 在关键字前后强制使用两个空格（var,const,let,async都是关键字）
    'keyword-spacing': [2, {
      'before': true,
      'after': true
    }],
    // 要求构造函数名称以大写字母开头
    'new-cap': [2, {
      'newIsCap': true,
      'capIsNew': false
    }],
    // 调用不带参数的构造函数时要求使用圆括号
    'new-parens': 2,
    // 不允许数组构造函数
    'no-array-constructor': 2,
    // 禁止使用caller/callee
    'no-caller': 2,
    // 允许使用console
    'no-console': 'off',
    // 禁止修改类声明的变量
    'no-class-assign': 2,
    // 在条件语句中禁止赋值操作符
    'no-cond-assign': 2,
    // 不能修改使用const关键字声明的变量
    'no-const-assign': 2,
    // 允许正则表达式中，出现一些特殊的字符
    'no-control-regex': 0,
    // 此规则不允许在变量上使用delete操作符。
    'no-delete-var': 2,
    // 此规则不允许在函数声明或表达式中使用重复的参数名称。它不适用于箭头函数或类方法，因为解析器报告错误。
    'no-dupe-args': 2,
    // 此规则不允许class有声明相同的成员函数等
    'no-dupe-class-members': 2,
    // 此规则不允许在对象文字中使用重复键。
    'no-dupe-keys': 2,
    // 此规则不允许在switch语句的case子句中使用重复的测试表达式。
    'no-duplicate-case': 2,
    // 此规则不允许在正则表达式中使用空字符类。
    'no-empty-character-class': 2,
    // 解构结构对象和数组时候不允许使用空模式
    'no-empty-pattern': 2,
    // 不允许使用eval
    'no-eval': 2,
    // 此规则不允许在catch子句中重新对错误err或者e赋值
    'no-ex-assign': 2,
    // 不允许直接修改内建对象的原型。
    'no-extend-native': 2,
    // 该规则禁止不必要的布尔转换。
    'no-extra-boolean-cast': 2,
    // 主要是规避switch中，如果不是最后一个case，其他case必须有break或者return
    'no-fallthrough': 2,
    // 避免浮点数的错误，必须加0在前面(0.7)而不是(.7)
    'no-floating-decimal': 2,
    // function不能重新声明
    'no-func-assign': 2,
    // 禁止影藏的eval声明，主要是setTimeout()和setInterval()使用字符串参数（setInterval("alert('Hi!');", 100);）
    'no-implied-eval': 2,
    // 这条规则要求函数声明位于程序的根节点或函数的主体中
    'no-inner-declarations': [2, 'functions'],
    // 此规则不允许RegExp构造函数中的无效正则表达式字符串。
    'no-invalid-regexp': 2,
    // 不允许使用非常规制表符和空格的无效空格。这些字符中的一些可能会在现代浏览器中引起问题
    'no-irregular-whitespace': 2,

    'no-label-var': 2,
    // 每次break或continue后接变量或者其他标签，它都会发出警告。例如（break 22;）
    'no-labels': [2, {
      'allowLoop': false,
      'allowSwitch': false
    }],
    // 不允许创建无效的代码块（由花括号包围）
    'no-lone-blocks': 2,
    // 此规则不允许使用混合空格和制表符进行缩进。
    'no-mixed-spaces-and-tabs': 2,
    // 规则旨在禁止在逻辑表达式，条件表达式，声明，数组元素，对象属性，序列和函数参数周围使用多个空格。
    'no-multi-spaces': 2,
    // 规则旨在防止使用多行字符串。（如果多行字符串必须使用+连接）
    'no-multi-str': 2,
    // 连续空行的最大数量控制
    'no-multiple-empty-lines': [2, {
      'max': 1
    }],
    // 规则不允许修改只读全局变量。
    'no-native-reassign': 2,
    // 规则不允许否定in表达式中的左操作数:以下不允许（ for(!item in items) ）
    'no-negated-in-lhs': 2,
    // 规则不允许使用Object构造函数。
    'no-new-object': 2,
    // 不允许使用new require表达。
    'no-new-require': 2,
    // 不允许使用new symbol
    'no-new-symbol': 2,
    'no-new-wrappers': 2,
    'no-prototype-builtins': 0,

    'no-octal': 2,
    'no-octal-escape': 2,
    'no-path-concat': 2,
    'no-proto': 2,
    'no-redeclare': 2,
    'no-regex-spaces': 2,
    'no-return-assign': [2, 'except-parens'],
    'no-self-assign': 2,
    'no-self-compare': 2,
    'no-sequences': 2,
    'no-shadow-restricted-names': 2,
    'no-spaced-func': 2,
    'no-sparse-arrays': 2,
    'no-this-before-super': 2,
    'no-throw-literal': 2,
    'no-trailing-spaces': 2,
    'no-undef': 2,
    'no-undef-init': 2,
    'no-unexpected-multiline': 2,
    'no-unmodified-loop-condition': 2,
    'no-unneeded-ternary': [2, {
      'defaultAssignment': false
    }],
    'no-unreachable': 2,
    'no-unsafe-finally': 2,
    'no-unused-vars': [2, {
      'vars': 'all',
      'args': 'none'
    }],
    'no-useless-call': 2,
    'no-useless-computed-key': 2,
    'no-useless-constructor': 2,
    'no-useless-escape': 0,
    'no-whitespace-before-property': 2,
    'no-with': 2,
    // 字符串连接符+应该放置的位置。应该放在行尾，而不是行首
    'operator-linebreak': [2, 'after', {
      'overrides': {
        '?': 'before',
        ':': 'before'
      }
    }],
    // 函数内的前后换行数量限制
    'padded-blocks': [2, 'never'],
    // 此规则强制一致使用反引号，或单引号(不允许使用双引号)
    'quotes': [2, 'single', {
      'avoidEscape': true,
      'allowTemplateLiterals': true
    }],
    // 关于结尾分号全部省略（vue里面全部省略）
    'semi': [2, 'never'],
    // 此规则旨在强制分号间隔,分号后间隔两个空格（like: var a; var b）
    'semi-spacing': [2, {
      'before': false,
      'after': true
    }],
    // 强制代码块之间的空格
    'space-before-blocks': [2, 'always'],
    'space-before-function-paren': [2, 'never'],
    'space-in-parens': [2, 'never'],
    'space-infix-ops': 2,
    'space-unary-ops': [2, {
      'words': true,
      'nonwords': false
    }],
    'spaced-comment': [2, 'always', {
      'markers': ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ',']
    }],
    // 模板字符串之间的空格格式化
    'template-curly-spacing': [2, 'never'],
    // 这条规则不允许比较'NaN'。
    'use-isnan': 2,
    // 此规则强制将typeof表达式与有效的字符串文字进行比较。
    'valid-typeof': 2,
    // 所有立即调用的函数表达式都包含在圆括号中
    'wrap-iife': [2, 'any'],
    'yield-star-spacing': [2, 'both'],
    // 变量与给定的字符串或者bool或者number等比较适合，变量放在前面
    'yoda': [2, 'never'],
    // 如果使用let,但是let后的值从来没有改变，会转变成const
    'prefer-const': 2,
    // 在正式环境下不允许使用debugger声明
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
    // 不允许在大括号之间留出空格
    'object-curly-spacing': [2, 'always', {
      objectsInObjects: false
    }],
    // 该规则在数组括号内强制实现一致的间距。
    'array-bracket-spacing': [2, 'never']
  }
}
