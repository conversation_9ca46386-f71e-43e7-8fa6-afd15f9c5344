<!-- 安检项组件 -->
<template>
  <div ref="checkingWrapper" class="checking-wrap">
    <div class="checking-main">
      <el-row v-for="item in checkList" :key="item.ItemID" class="item-box">
        <el-col
          class="item"
          :class="{ underway: item.checkStep === 1, end: item.checkStep === 2 }"
        >
          <p>
            <i :class="[ 'iconfont', item.CheckType&& item.CheckType.InsideName && item.CheckType.InsideName.indexOf('CheckCustom_')===-1 ? 'icon-' + calcIcon(item.CheckType.InsideName): 'icon-CheckCustom']" />
            {{ $i18n.locale === 'zh' ? item.CheckType.OutsideName :item.OutsideName_en }}
            <span>
              <b
                v-if="item.checkStep === 1"
                :class="{ 'el-icon-loading': item.checkStep === 1 }"
              />
              <b
                v-if="item.checkStep === 2"
                class="iconfont"
                :class="{
                  'icon-anjianheguixiang success': item.itemResultType === 0,
                  'icon-guanjianxiangbuhegui warn': item.itemResultType === 1,
                  'icon-guanjianxiangbuhegui err': item.itemResultType === 2,
                }"
              />
            </span>
          </p>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import checkMixins from '../../mixins/mixins'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { checkItemAlias } from '@/render/utils/bussiness/localDic'
import { sleep } from '@/render/utils/global'
import securityUtil from '@/render/utils/bussiness/securityUtil'
export default {
  mixins: [checkMixins],
  props: {
    checkList: {
      type: Array,
      default: () => []
    },
    checking: { // 正在安检第几项
      type: Number,
      default: 0
    },
    proccess: { // 进度条进度
      type: Number,
      default: 0
    },
    showProccess: { // 是否显示进度条
      type: Boolean,
      default: false
    },
    isAgin: { // 是否再次安检
      type: Boolean,
      default: false
    },
    isCheckReport: { // 是否正在安检上报
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null, // 定时器
      progressStep: 0, // 单项所占进度条长度
      stopLen: 0, // 自动滚动停止点
      STEP: 0.5, // 步长
      dataIndex: 0 // 当前安检项下标
    }
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
  },
  mounted() {
    this.startCheck()
  },
  methods: {
    async startCheck() {
      console.log('正在安检列表', this.checkList)
      this.restProccessData()
      const canCheck = await securityUtil.checkPrecondition()
      if (canCheck) {
        this.checkItem()
      }
    },
    // 初始进度条数据
    restProccessData() {
      this.progressStep = 100 / this.checkList.length
      this.stopLen = this.progressStep * 0.92
      if (this.progressStep > 10) {
        this.STEP = this.progressStep / 10
      }
      if (this.STEP > this.stopLen) {
        this.STEP = this.stopLen
      }
      this.$emit('update:proccess', 0)
      this.$emit('update:showProccess', true) // 显示进度条
    },
    // 安检前置条件
    // 获取安检结果
    async checkItem() {
      if (this.dataIndex < this.checkList.length) {
        this.$emit('update:checking', this.dataIndex + 1)
        const dataIndex = this.dataIndex
        this.checkList[dataIndex].checkStep = 1
        this.startProccess()

        const res = await securityUtil.requestCheckResult(this.checkList[dataIndex])
        // 设置滚动条位置
        this.setScrollPostion(this.$refs.checkingWrapper, dataIndex * 41)
        // 设置进度条到目标位置
        this.endProccess(dataIndex)
        this.checkResultHandle(res.Result, this.checkList[dataIndex])
        // 进行下一个安检
        this.dataIndex++
        if (res.Result) {
          this.checkItem()
        } else {
          this.$emit('checkError')
        }
      } else {
        this.$emit('update:isCheckReport', true)
        const checkRet = this.dataFormat(this.checkList)
        await commonUtil.securityCheckRePort({ IsRecheck: this.isAgin ? 1 : 0 })
        if (checkRet.resultType === 2) {
          await sleep(5000)
        }
        this.$emit('update:isCheckReport', false)
        console.log('安检上报结果')
        this.$emit('changeCheckTypeFn', checkRet)
        this.$emit('update:showProccess', false)
      }
    },
    // 模拟进度
    startProccess() {
      let currentItemProcess = 0
      this.timer = setInterval(() => {
        if (currentItemProcess + this.STEP < this.stopLen) {
          currentItemProcess += this.STEP
          const current = this.proccess + this.STEP
          this.$emit('update:proccess', current)
        } else {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 150)
    },
    // 结束进度
    endProccess(dataIndex) {
      this.timer && clearInterval(this.timer)
      this.$emit('update:proccess', this.progressStep * (dataIndex + 1))
      if (dataIndex === this.checkList.length) {
        this.$emit('update:proccess', 100)
      }
    },
    calcIcon(InsideName) {
      const deviceType = _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
      if (deviceType === 'linux' && InsideName) {
        return InsideName.replace(/_linux/, '')
      } else if (deviceType === 'mac' && InsideName) {
        return checkItemAlias[InsideName] || InsideName
      } else {
        return InsideName
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.checking-wrap {
  height: 100%;
  overflow: auto;
  padding-right: 24px;
  .checking-main {
    height: 100%;
    border-top: 1px solid $line-color;
    .el-col {
      border-bottom: 1px solid $line-color;
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      background: $row-bg;
      padding: 0 24px;
      &.end {
        p {
          color: $title-color;
          i {
            color: $disabled-color;
          }
        }
      }
      &.underway {
        background: $list-hover-bg;
        color: $--color-primary;
        p {
          color: $--color-primary;
        }
        b {
          font-size: 18px;
        }
      }
      p {
        text-align: left;
        color: $disabled-color;
        i {
          margin-right: 10px;
          font-size: 16px;
        }
        span {
          float: right;
          height: 39px;
          line-height: 39px;
          b {
            &.err {
              color: $error;
            }
            &.warn {
              color: $waring;
            }
            &.success {
              color: $success;
            }
          }
        }
      }
    }
  }
}
</style>
