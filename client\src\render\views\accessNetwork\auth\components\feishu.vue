<template>
  <div id="feishuPage">
    <div id="feishu_qrcode_login" :class="['u-feishu-qr-img', authData.authFrom === 'addition' ? 'addition-style': '' ]" />
    <iframe :src="qrCodeSrc" style="display: none" />
    <p v-if="authData.isTwoFactors || authData.AddAuthType === 'FeiShu'" class="towfactory-tip">{{ $t('auth.useFieshu') }}</p>
  </div>
</template>
<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import qs from 'qs'
import qrCodeCommon from '@/render/utils/auth/qrCodeCommon'
import proxyAjax from '@/service/utils/proxyAjax'
import proxyApi from '@/service/api/proxyApi'
import { EventBus } from '@/render/eventBus'
import urlUtils from '@/render/utils/url'
require('@/render/utils/auth/feishu')

export default {
  props: {
    isQrcode: {
      type: Boolean,
      default: false
    },
    authData: { // 双因子认证时有值
      type: Object,
      default: function() {
        return {}
      }
    },
    bindData: { // 主账号绑定时有值
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      feishuUrl: 'https://passport.feishu.cn/suite/passport/oauth/authorize',
      qrCodeSrc: '',
      taskId: '',
      username: '',
      password: '',
      qrLoginObj: null,
      goto: '',
      time: '',
      redirectUrl: '',
      qrCheckCount: 0,
      ServerProxy: false
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'authInfo']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID', 0)
    }
  },
  watch: {
    isQrcode(value) {
      if (value) {
        this.drawQrCode()
      } else {
        this.clearQrInterval()
      }
    }
  },
  mounted() {
    this.drawQrCode()
    EventBus.$on('revoke:refresh', this.drawQrCode)
  },
  beforeDestroy() {
    this.clearQrInterval()
    EventBus.$off('revoke:refresh', this.drawQrCode)
    if (typeof window.addEventListener !== 'undefined') {
      window.removeEventListener('message', this.handleMessage, false)
    } else if (typeof window.attachEvent !== 'undefined') {
      window.detachEvent('onmessage', this.handleMessage)
    }
  },
  methods: {
    // 绘制二维码
    drawQrCode() {
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      this.clearQrInterval()
      this.time = new Date().getTime()
      const appid = this.authData.AppID || _.get(this.serveEntiretyConfig, 'server.FeiShuConfig.appid')
      const urlParam = {
        deviceid: this.deviceid,
        appid,
        time: this.time
      }
      const ServerProxy = _.get(this.serveEntiretyConfig, 'server.FeiShuConfig.ServerProxy')
      this.ServerProxy = ServerProxy
      const redHost = qrCodeCommon.serverHost() || this.deDefaultPort(_.get(this.serveEntiretyConfig, 'server.ControlUrl'))

      // 避免回调域名需要配置几个在飞书的管理后台，这里formatUrl的平台参数，全部传为windows
      let redirect_uri = redHost +
          proxyAjax.formatUrl('feishu/user?' + qs.stringify(urlParam, { encode: false }), 'access', 'json', 'windows')
      this.redirectUrl = redirect_uri
      redirect_uri = encodeURIComponent(redirect_uri + '&redirect_uri=' + encodeURIComponent(redirect_uri))
      const indexParam = {
        client_id: appid,
        state: 'STATE',
        redirect_uri,
        response_type: 'code'
      }
      this.goto = this.feishuUrl + '?' + qs.stringify(indexParam, { encode: false })
      document.getElementById('feishu_qrcode_login').innerHTML = ''
      this.qrCheckCount = 0
      // eslint-disable-next-line no-undef
      this.qrLoginObj = QRLogin({
        id: 'feishu_qrcode_login',
        goto: this.goto,
        width: '260',
        height: '300',
        style: 'border:none;background-color:#FFFFFF;',
        mode: this.ServerProxy,
        manageIp: urlUtils.getBaseIPPort()
      })

      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', this.handleMessage, false)
      } else if (typeof window.attachEvent !== 'undefined') {
        window.attachEvent('onmessage', this.handleMessage)
      }
    },
    /**
    * 处理返回
    */
    handleMessage(event) {
      const origin = event.origin
      if (this.qrLoginObj.matchOrigin(origin) || origin === urlUtils.getBaseIPPort()) {
      // 在授权页面地址上拼接上参数 tmp_code，并跳转
        const loginTmpCode = event.data
        const url = this.ServerProxy ? this.goto.replace('https://passport.feishu.cn', urlUtils.getBaseIPPort()) : this.goto
        this.qrCodeSrc = url + '&tmp_code=' + loginTmpCode
        console.log(this.qrCodeSrc)
        this.queryScanRes()
      }
    },
    // 查询扫码结果
    queryScanRes() {
      this.clearQrInterval()
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      this.taskId = setInterval(() => {
        if (this.qrCheckCount > 30) {
          this.clearQrInterval()
          return
        }
        this.qrCheck()
      }, 2000)
    },
    /**
   * 清除定时
   */
    clearQrInterval() {
      const taskId = this.taskId
      this.qrCheckCount = 0
      if (taskId) {
        clearInterval(taskId)
        this.taskId = null
      }
    },
    /**
   * 查询扫码结果
   */
    async qrCheck() {
      this.qrCheckCount++
      const apiParam = {
        deviceid: this.deviceid,
        action: 'check',
        time: this.time
      }
      const result = await proxyApi.getFeiShuUser(apiParam, qrCodeCommon.serverHost())
      if (_.get(result, 'data.state') === false && _.get(result, 'data.type') === 'Prescaned') {
        this.$message.warning(_.get(result, 'data.message', result.errmsg))
        this.clearQrInterval()
        this.drawQrCode()
      } else if (_.get(result, 'data.state') === true) {
        // 扫描成功
        this.clearQrInterval()

        this.username = result.data.username
        this.password = result.data.token
        this.submitForm()
      } else {
        if (this.qrCheckCount > 30) {
          this.$message.warning(_.get(result, 'data.message', result.errmsg) || this.$t('interfaceErr'))
          this.clearQrInterval()
          this.drawQrCode()
        }
      }
    },
    async confirmBind(type) {
      return new Promise((resolve, reject) => {
        this.$emit('bind', { resolve, type })
      })
    },
    /**
     * 提交（支持撤销登录）
     */
    async submitForm() {
      const params = {
        guestType: 'feishuqrlogin',
        username: this.username,
        password: this.password,
        isQrcode: this.isQrcode,
        appid: this.authData.AppID || _.get(this.serveEntiretyConfig, 'server.FeiShuConfig.appid'),
        redirect_uri: this.redirectUrl
      }
      if (this.authData.isTwoFactors) { // 双因子
        params.factorType = 'FeiShu'
        const notBind = _.get(this.authInfo, 'basic.User.FactorAuthRelation.FeiShu') === false
        let autoBind = false
        if (notBind) {
          autoBind = await this.confirmBind('feishu')
        }
        if (autoBind !== false) {
          params.autoBind = autoBind
        }
      }
      const res = await qrCodeCommon.auth({ ...params, ...this.authData, ...this.bindData })
      console.log(res)
      if (res) {
        if (_.isObject(res) && _.get(res, 'revoke', false) === true) {
          this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
        } else {
          if (this.authData.isTwoFactors) { // 双因子认证
            this.$emit('towFactorSuccess')
            return
          }
          this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
        }
      } else {
        this.drawQrCode()
      }
    },
    // 删除默认端口-回调地址中带默认端口飞书没配默认端口会报错
    deDefaultPort(url) {
      if (!url) {
        return url
      }
      const arr = url.split(':')
      const port = arr[arr.length - 1]
      if (!port) {
        return url
      }
      if ((parseInt(port) === 80 && url.indexOf('https') === -1) || (parseInt(port) === 443 && url.indexOf('https') > -1)) {
        return arr.slice(0, arr.length - 1).join(':')
      }
      return url
    }
  }
}
</script>
<style scoped lang='scss'>
#feishu {
  width:260px;
  height:420px;
  border:0;
}
.addition-style{
  text-align: center;
  padding: 7px 0;
}
#feishuPage{
  position: relative;
  .towfactory-tip{
    position: absolute;
    width: 100%;
    text-align: center;
    left: 0;
    bottom: 5px;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: $default-color;
  }
}
</style>
