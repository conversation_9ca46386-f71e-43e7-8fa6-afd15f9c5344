<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.SoftLicense.h_7_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <div v-for="item in softList" :key="item.SoftwareName" class="pc-info">
            <img :src="forImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="optional-item margin-style">
                {{ $t("check.SoftLicense.h_5_rd") }}
                <span>{{ item.SoftwareName }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.SoftLicense.h_6_rd") }}
                <span>{{ item.InstallPath }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.SoftLicense.h_7_rd") }}
                <span>{{ item.CurrentKey }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
export default {
  name: 'CheckSoftOnlyInstallStat',
  components: {
    checkResult
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      forImgSrc: require('@/render/assets/forbidSoft.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    softList() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return []
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      const newList = list.filter(item => item.IsRegulate === 'Yes')
      return newList
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  }
}
</script>

