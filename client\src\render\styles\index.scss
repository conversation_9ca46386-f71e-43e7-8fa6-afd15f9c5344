
/**/

//和主题皮肤相关的
@import "./theme/default/index.scss";

//element ui的通用样式修改(和皮肤组件无关的样式)
@import "./elementUI/elementCustome.scss";

//和element ui组件无关，但是和整个界面相关的一些通用样式修改
@import "./normal.scss";



//关于element的全部基础样式和字体引入
$--font-path: '~element-ui/lib/theme-chalk/fonts';

//这里重写elemen-ui的官方样式，为了3746的换皮肤功能


@import "./elementUI/sourceCode/packages/theme-chalk/src/index.scss";

//阿里巴巴图标库字体图标
@import "./font/iconfont.css";
