<template>
  <div id="f-guest-auth" v-loading="initLoading||submitLoading">
    <template v-if="!initLoading">
      <div class="u-current-tag">
        <span>
          <i class="iconfont icon-renzheng" />{{ $t('guestAuth.guest.info_1') }}
        </span>
        <span v-if="isGuestReq && !isNoAuth" class="go-back-btn" @click="isGuestReq=false">
          <i class="iconfont icon-fanhui" />{{ $t('guestAuth.guest.info_82') }}
        </span>
        <span v-if="!isGuestReq && showSwitchStaff" id="ui-guest-auth-span-cut_staff_auth" :class="['change-tag', switching?'disabled-change-tag':'']" @click="toAuth">
          {{ $t('auth.switchStaff') }} <i class="iconfont icon-qiehuan " />
        </span>
      </div>
      <div v-show="!isGuestReq" class="authContent">
        <!--普通来宾认证/扫码来宾认证的切换-->
        <div class="tab-pane-nev">
          <span
            v-for="tabs in computeAuth['generalAuthWay']"
            :id="'ui-guest-auth-span-guest_'+tabs.value"
            :key="tabs.label"
            :class="{ 'is-active': activeType === tabs.value }"
            :style="computeAuth['generalAuthWay'].length === 1 ? 'text-align: center' : ''"
            @click="changeGeneralAuthWay(tabs.value)"
          >
            {{ generalAuthLabel(tabs.value) }}
          </span>
        </div>

        <div v-if="!noAuthorization" class="tab-pane-content">
          <div class="form-content">
            <!--普通来宾认证方式的切换-->
            <el-form v-if="activeType==='normal' ">
              <el-form-item class="auth-type-box" label-width="20px">
                <i class="iconfont icon-leixing slect-icon" />
                <el-select id="ui-guest-auth-tab-guest_authtype" v-model="curentAuthType" popper-class="papper-body">
                  <el-option
                    v-for="option in computeAuth['normalAuthWay']"
                    :id="'ui-guest-auth-option-'+option.value"
                    :key="option.value"
                    :label="normalAuthLabel(option.value)"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            <!-------放置动态的认证方式------>
            <template>
              <component :is="curentAuthType" ref="guestAuth" @enterSubmit="submit" />
            </template>
          </div>
          <div v-if="curentAuthType !=='guestQrCode' && curentAuthType!=='' " class="public-btn-box" style="margin-left:20px">
            <el-button id="ui-guest-auth-button-guest_auth_submit" :loading="submitLoading" type="primary" class="public-btn" @click="submit">{{ $t('guestAuth.guest.info_9') }}</el-button>
          </div>
        </div>
        <layout-state
          v-else
          :state-img="noPass"
          :state-msg="$t('reg.authorization')"
        />
        <ConfirmDialog id="guest-confirm-dialog" :show.sync="showConfirmDialog" :show-cancel-button="false" width="285px" @ok="errorHandle">
          <p class="guest-error-confirm-tips">{{ $t('guestAuth.forbid') }}</p>
        </ConfirmDialog>
      </div>

    </template>
  </div>
</template>
<script>
import guestMobile from './guestMobile.vue'
import guestCode from './guestCode.vue'
import guestApplySelf from './guestApplySelf.vue'
import guestQrCode from './guestQrCode.vue'
import { mapMutations, mapState } from 'vuex'
import { EventBus } from '@/render/eventBus'
import proxyApi from '@/service/api/proxyApi'
import authIndex from '@/render/utils/auth/index'
import localStorage from '@/render/utils/cache/localStorage'
import common from '@/render/utils/auth/common/index'
import scene from '@/render/utils/bussiness/scene'
import processController from '@/render/utils/processController'
import ztpUtils from '@/render/utils/auth/ztp/index'
import state from '@/render/components/layout/state'
const noPass = require('@/render/assets/stateIllustration/refuse.png') // 已入网的图片

export default {
  name: 'GuestMobile',
  components: {
    guestMobile, guestCode, guestApplySelf, guestQrCode, 'layoutState': state
  },
  data() {
    return {
      submitLoading: false,
      initLoading: true,
      activeType: '',
      generalAuthWay: [], // 认证方式[来宾认证|扫码认证]
      normalAuthWay: [], // [来宾码，自助申请，短信]
      curentAuthType: '', // 当前选择的认证方式
      isGuestReq: false,
      computeAuth: {},
      noAuthorization: false, // 无授权点数
      noPass,
      switching: false,
      showConfirmDialog: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig', 'authInfo']),
    isNoAuth() {
      return parseInt(_.get(this.authInfo, 'basic.UserID')) === 1
    },
    generalAuthLabel(key) {
      return (key) => {
        return key === 'normal' ? this.$t('guestAuth.guest.info_2') : this.$t('guestAuth.guest.info_3')
      }
    },
    normalAuthLabel(key) {
      return (key) => {
        const dic = {
          guestApplySelf: this.$t('guestAuth.guest.info_6'),
          guestCode: this.$t('guestAuth.guest.info_4'),
          guestMobile: this.$t('guestAuth.guest.info_5'),
          guestQrCode: this.$t('guestAuth.guest.info_3')
        }
        return dic[key]
      }
    },
    // 是否展示切换到来宾按钮
    showSwitchStaff() {
      const sceneinfo = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo', {})
      const { UserType, UserEntry } = sceneinfo
      if (parseInt(UserType) === 1) { // 应用对象为员工
        return true
      }
      if (parseInt(UserType) === 2 && parseInt(UserEntry) === 1) { // 应用对象为来宾
        return true
      }
      return false
    }
  },
  mounted() {
    this.computeAuth = this.computeAuthWay()
    this.init()
    this.listenGuestQrCode()
  },
  beforeDestroy() {
    EventBus.$off('guestQrCodeScan', this.handleBus)
  },
  methods: {
    ...mapMutations(['setServeEntiretyConfig', 'setClientInfo', 'setAuthInfo']),
    async init() {
      const forbidGuest = parseInt(_.get(this.clientInfo, 'detail.ForbidGuest', 0))
      if (forbidGuest === 1) {
        this.initLoading = false
        this.showConfirmDialog = true
        return true
      }
      this.initLoading = false
    },

    // 计算认证方式【普通认证，来宾认证等】
    computeAuthWay() {
      // 从场景里面获取来宾的认证方式配置
      const guestAuthTypeFromScene = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.AccessType', '').split(',')

      const authInfo = {
        normalAuthWay: [],
        generalAuthWay: []
      }

      // 是否开启自助申请
      if (guestAuthTypeFromScene.includes('GuestApplySelf')) {
        authInfo.normalAuthWay.push({ value: 'guestApplySelf', label: this.$t('guestAuth.guest.info_6') })
      }

      // 是否开启来宾码
      if (guestAuthTypeFromScene.includes('NetCode')) {
        authInfo.normalAuthWay.push({ value: 'guestCode', label: this.$t('guestAuth.guest.info_4') })
      }

      // 是否开启短信认证
      if (guestAuthTypeFromScene.includes('SmsState')) {
        authInfo.normalAuthWay.push({ value: 'guestMobile', label: this.$t('guestAuth.guest.info_5') })
      }

      if (!_.isEmpty(authInfo.normalAuthWay)) {
        authInfo.generalAuthWay.push({ value: 'normal', label: this.$t('guestAuth.guest.info_2') })
      }

      // 是否开启扫码
      if (guestAuthTypeFromScene.includes('QrCode')) {
        authInfo.generalAuthWay.push({ value: 'guestQrCode', label: this.$t('guestAuth.guest.info_3') })
      }

      // 开启了来宾认证，但是没有配任何认证方式，自动认证
      if (_.isEmpty(authInfo.generalAuthWay) || guestAuthTypeFromScene.includes('NoAuth')) {
        if (!ztpUtils.whetherZTPMode()) {
          this.noAuth()
        } else {
          EventBus.$emit('ztpNoAuth', true)
        }
      } else {
        this.activeType = this.activeType || authInfo.generalAuthWay[0]['value']
        this.curentAuthType = this.curentAuthType || (this.activeType === 'normal' ? authInfo.normalAuthWay[0]['value'] : 'guestQrCode')
      }

      return authInfo
    },

    // 切换总认证方式，来宾认证或者扫码
    changeGeneralAuthWay(value) {
      this.activeType = value
      if (value === 'guestQrCode') {
        this.curentAuthType = value
      } else {
        this.curentAuthType = this.computeAuth['normalAuthWay'][0]['value']
      }
    },
    // 监听QrCode扫描的信号
    listenGuestQrCode() {
      EventBus.$on('guestQrCodeScan', this.handleBus)
    },
    async handleBus(ret) {
      if (parseInt(_.get(ret, 'errcode')) === 0 && _.get(ret, 'data')) {
        await this.handleAuthNext(ret)
      }
    },
    async submit() {
      this.submitLoading = true
      try {
        const ret = await this.$refs['guestAuth'].submit()
        if (parseInt(_.get(ret, 'errcode')) === 0 && _.get(ret, 'data')) {
          await this.handleAuthNext(ret)
        } else if (parseInt(_.get(ret, 'errcode')) === ********) {
          processController.set('/access/message')
        }
        this.submitLoading = false
      } catch (error) {
        this.$msg({ message: this.$t('auth.authFail'), type: 'error' })
        this.submitLoading = false
      }
    },
    // 无认证方式认证通过。实际上就是来宾码认证方式，net_code等于空
    async noAuth() {
      if (parseInt(_.get(this.clientInfo, 'detail.ForbidGuest', 0)) === 1) {
        return
      }
      this.submitLoading = true
      const apiParam = {
        type: 'Guest',
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        net_code: '',
        guestType: 'NoAuth'
      }
      // 提交认证
      const ret = await proxyApi.authIndex(apiParam, { showError: false })
      if (parseInt(_.get(ret, 'errcode')) === 0) {
        await this.handleAuthNext(ret, true)
      } else if (_.get(ret, 'errcode') === '21139006') {
        this.noAuthorization = true
      } else {
        this.$msg({ message: ret.errmsg, type: 'error' })
        if (parseInt(_.get(ret, 'errcode')) === ********) {
          processController.set('/access/message')
        }
      }
      this.submitLoading = false
    },
    // 认证以后的后续事情处理
    async handleAuthNext(ret, isNoAuth) {
      this.setAuthInfo({ ...this.authInfo, ...{ basic: _.get(ret, 'data') }})
      await common.authEnd({
        type: this.curentAuthType,
        autoAuth: isNoAuth ? '1' : '0'
      })
      await authIndex.authSuccess({ authType: 'Guest', isAccountAuthType: false })
    },
    nextAudit(guestselfid) {
      const params = {
        type: this.curentAuthType,
        guestselfid,
        deviceid: _.get(this.clientInfo, 'detail.DeviceID')
      }
      localStorage.setItem('guestAuditStatus', JSON.stringify(params), 3600)
      EventBus.$emit('setApplyAudit', true)
    },
    async reqComplete(data) {
      if (parseInt(_.get(data, 'IsNeedAudit')) !== 1) {
        common.authEnd({
          type: this.curentAuthType
        })
        await authIndex.authSuccess({ authType: 'Guest', isAccountAuthType: false })
      } else {
        this.nextAudit(data.guestselfid)
      }
      this.submitLoading = false
      this.isGuestReq = false
    },
    setLoading(flag) {
      this.submitLoading = flag
    },
    async errorHandle() {
      if (!this.isGuestReq && this.showSwitchStaff) {
        await scene.getDeviceScene(1)
        processController.set('/access/auth')
      }
    },
    async toAuth() {
      if (this.switching) {
        return
      }
      this.switching = true
      const ret = await scene.getDeviceScene(1)
      this.switching = false
      if (parseInt(_.get(ret, 'IsAccess', 1)) === 0) { // 禁止接入
        processController.set('/access/message')
        return
      }
      processController.set({ path: '/access/auth', query: { fromGuest: 1 }})
    }
  }
}
</script>
<style lang="scss">
#f-guest-auth {
  padding: 24px;
  box-sizing: border-box;
  text-align: center;
  height: 100%;

  .u-loading{
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    text-align: left;
  }
  .u-current-tag{
    display: flex;
    justify-content: space-between;
    align-content: center;
    .go-back-btn{
      background: #f5f6f8;
      border: 1px solid #ededf1;
      border-radius: 15px;
      color: $default-color;
      cursor: pointer;
      &:hover{
        background: $--color-primary;
        border-color: $--color-primary;
        color: $light-color;
      }
      &:hover i{
        color: $light-color;
      }
      i{
        color: $disabled-color;
        margin-right: 8px;
      }
    }
    .change-tag{
      color: $default-color;
      cursor: pointer;
      display: flex;
      background: white;
      padding: 0;
      font-size: 14px;
      i{
        color: $disabled-color;
        font-size: 12px;
        margin-left: 8px;
      }
    }
    .change-tag:hover{
      color: $--color-primary;
      i{
        color: $--color-primary;
      }
    }
    .disabled-change-tag{
      color: $disabled-color;
      cursor: not-allowed;
    }

    .disabled-change-tag:hover{
      color: $disabled-color;
      i{
        color: $disabled-color;
      }
    }
  }

  .authContent {
    // min-width: 388px;
    display: flex;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    text-align: left;
    .tab-pane-nev {
      text-align: center;
      height: 40px;
      span {
        display: inline-block;
        font-size: 16px;
        line-height: 40px;
        color: $disabled-color;
        cursor: pointer;
        &.is-active,
        &:hover {
          font-size: 18px;
          font-weight: 500;
          color: $--color-primary;
        }
        &:nth-of-type(1) {
          padding-right: 16px;
          width: 206px;
          text-align: right;
        }
        &:nth-of-type(2) {
          padding-left: 16px;
          width: 245px;
          text-align: left;
        }
      }
    }
    .tab-pane-content {
      margin-top: 20px;
       .form-content {
          padding-right: 16px;
          width: 408px;

        }
      .el-form {
        display: flex;
        flex-direction: column;
        height: 100%;
        .el-form-item {
          text-align: left;
          margin-bottom: 24px;
          width: 380px;
          .el-form-item__content {
            position: relative;
            .el-select {
              width: 100%;
            }
            .el-input__inner {
              width: 100%;
              padding-left: 48px;
            }
            .el-input__suffix {
              right: 10px;
            }
            i {
              color: $disabled-color;
              font-size: 16px;
              &.iconfont {
                position: absolute;
                left: 16px;
                z-index: 2;
                font-size: 14px;
                &.input-icon {
                  font-size: 14px;
                }
              }
            }
          }
          &.submit-box {
            width: 388px;
            margin-bottom: 0;
          }
        }
      }
    }

  }
}
.guest-error-confirm-tips{
  padding: 24px;
}
</style>
