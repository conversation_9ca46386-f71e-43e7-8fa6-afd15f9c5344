{"name": "asm_client_project", "version": "0.1.0", "private": true, "scripts": {"serveOld": "vue-cli-service serve", "buildOld": "vue-cli-service build --no-module", "build": "webpack --config build/webpack.prod.config.js", "buildAsar": "webpack --config build/webpack.electron.prod.config.js", "serve": "webpack serve --progress --config build/webpack.dev.config.js"}, "dependencies": {"animate.css": "^4.1.1", "asar": "^3.2.0", "dexie": "^3.2.2", "electron-log": "^5.1.0", "electron-window-state": "^5.0.0", "element-ui": "^2.15.2", "jquery": "^3.3.1", "js-base64": "^3.7.0", "js-md5": "^0.7.3", "jsencrypt": "^3.2.0", "lodash": "^4.17.21", "qrcodejs2": "0.0.2", "qs": "^6.10.1", "reconnecting-websocket": "^4.4.0", "socket.io-client": "2.2.0", "stacktracey": "^2.1.7", "vue": "2.5.17", "vue-clipboard2": "^0.3.1", "vue-i18n": "^8.24.4", "vue-json-viewer": "^2.2.19", "vue-router": "^3.2.0", "vuex": "^3.4.0", "dotenv": "^16.0.3"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/plugin-transform-runtime": "^7.19.1", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.19.3", "@babel/runtime": "^7.19.0", "async-catch-loader": "^2.0.5", "autoprefix": "^1.0.1", "babel-eslint": "^7.2.3", "babel-loader": "^8.2.5", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.1", "css-vars-ponyfill": "^2.4.8", "eruda": "^2.11.2", "eslint": "^7.0.0", "eslint-plugin-vue": "^6.2.2", "eslint-webpack-plugin": "^3.2.0", "filemanager-webpack-plugin": "^7.0.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.6.1", "postcss-loader": "^7.0.1", "postcss-scss": "^4.0.5", "sass": "~1.32.6", "sass-loader": "10.1.1", "stacktracey": "^2.1.7", "vconsole": "^3.9.0", "vue-loader": "^15.0.0", "vue-template-compiler": "2.5.17", "webpack": "^5.0.8", "webpack-cli": "^4.10.0", "webpack-dev-server": "4.11.1", "webpack-merge": "^5.8.0"}, "browserslist": ["ie 8", "Safari >= 5"]}