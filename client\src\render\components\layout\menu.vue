<template>
  <div class="layout-aside">
    <div v-if="!clientInfo.online" class="u-offlineTips">
      <div class="off-tip-content">
        <i class="iconfont icon-guanjianxiangbuhegui" /> {{ $t('header.offline') }}
      </div>
    </div>
    <ul class="menu-wrapper">
      <li v-for="item in computedMenu" :key="item.title" class="menu-item">
        <div class="menu-item-title">
          {{ $t(item.title) }}
        </div>
        <ul class="sub-menu-wraper">
          <li
            v-for="i in item.subList"
            :id="i.uiId"
            :key="i.code"
            :class="['sub-menu-item', cutOut(i.code) === currentRouteCode ? 'active-sub-item' : '']"
            @click="changeMenu(i.url, i.params)"
          >
            <i :class="['iconfont', i.icon]" />
            <span :title="$t(i.name)"> {{ $t(i.name) }} </span>
          </li>
        </ul>
      </li>
    </ul>
    <!--底部的版本号-->
    <div class="version-wrapper" v-html="appVsersion" />
  </div>
</template>
<script>
import routes from '@/render/router/menu'
import { mapGetters } from 'vuex'
import authUtils from '@/render/utils/auth/index'
import menuUtil from '@/render/utils/bussiness/menuUtil'

export default {
  name: 'Menu',
  props: {
    linkageMenu: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentRouteCode: '101',
      limitRoute: ['access/guestAuth', 'access/auth', 'source/list'],
      taskId: null
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    computedMenu() {
      return this.computedMenuFun()
    },
    appVsersion() {
      const v = typeof (this.clientInfo.basic) !== 'undefined' ? this.clientInfo.basic.AgentVersion : ''
      if (v.indexOf('(') > -1) {
        const arr = v.split('(')
        return arr.join('<br/>(')
      }
      return v
    }
  },
  watch: {
    '$route': {
      handler(to, from) {
        if (to.meta && to.meta.code) {
          if (!_.get(to.meta, 'code')) {
            return
          }
          if (to.meta.code === this.currentRouteCode) {
            return
          }
          this.currentRouteCode = this.cutOut(to.meta.code)
        }
      },
      immediate: true
    }
  },

  methods: {
    // 计算菜单
    computedMenuFun() {
      let routesData = JSON.parse(JSON.stringify(routes))
      // 判断零信任授权
      if (!authUtils.isOpenZeroTrust()) {
        routesData = routesData.filter(v => v.path !== '/source/list')
      }
      if (!authUtils.isOpenSDCLinkAge()) {
        routesData = routesData.filter(v => v.path !== '/message/secuitybox')
      }
      // 判断是否已经入网成功
      if (parseInt(_.get(this.clientInfo, 'accessStatus.deviceStatus')) === 1) {
        // 过滤掉需要判断入网权限，且在非入网时显示的菜单(例如来宾接待)
        routesData = routesData.filter(item => {
          if (_.get(item, 'meta.menu.access') !== undefined && _.get(item, 'meta.menu.access') === false) {
            return false
          } else {
            return true
          }
        })
        // 来宾账号不显示来宾接待
        if (!authUtils.isShowGuestRecive()) {
          routesData = routesData.filter(v => v.path !== '/access/guestReceive')
        }
      } else {
        // 过滤掉需要判断入网权限，且在已入网时显示的菜单(例如来宾认证)
        routesData = routesData.filter(item => {
          if (_.get(item, 'meta.menu.access') !== undefined && _.get(item, 'meta.menu.access') === true) {
            return false
          } else {
            return true
          }
        })
      }

      // 按场景过滤自定义菜单
      routesData = menuUtil.mapDiyMenu(routesData)
      // 资源平台过滤
      routesData = menuUtil.mapResourceMenu(routesData)

      // 补丁管理过滤
      routesData = menuUtil.mapPatchMenu(routesData)

      // 对需要授权的菜单(远程管理授权等)进行检验
      routesData = menuUtil.mapAuthorizeMenu(routesData)

      // 按平台过滤掉不支持的菜单(例如mac,linux不支持补丁菜单等)
      routesData = menuUtil.mapPlatformMenu(routesData)

      // 便利联动菜单(msep联动和第三方联动菜单是否显示)
      const linkageRoute = menuUtil.mapLinkAgeMenu(this.linkageMenu)
      return this.routeInMenu(_.concat(routesData, linkageRoute))
    },
    routeInMenu(routes) {
      const list = []
      if (routes && _.isObject(routes)) {
        routes.forEach(item => {
          if (item.meta && item.meta.menu) {
            const idx = list.findIndex(i => i.title === item.meta.menu.moduleName)
            const { name, icon, uiId } = item.meta.menu
            const sublistItem = { name, icon, code: item.meta.code, requiresAuth: item.meta.requiresAuth, url: item.path, params: item.params || [], uiId }
            if (idx > -1) {
              list[idx].subList.push(sublistItem)
            } else {
              list.push({
                title: item.meta.menu.moduleName,
                subList: [sublistItem]
              })
            }
          }
        })
      }

      return list
    },

    // 切换菜单前做逻辑判断
    changeMenu(path, query = {}) {
      // 第三方客户端联动的处理
      const state = this.routerInterceptor(path)
      if (state.next) {
        const _query = { ...query, ...{ menuClick: true }}
        this.$router.push({ path, query: _query })
      } else {
        this.$router.push('/access/message')

        if (this.messageInstance && this.messageInstance.status()) {
          this.messageInstance.close()
          this.messageInstance = null
        }

        this.messageInstance = this.$msg({
          message: state['stateMsg'],
          type: 'warning'
        })
      }
    },
    routerInterceptor(path) {
      // 入网状态页点击进入认证、来宾认证提示
      const state = {
        next: false,
        stateMsg: this.$t('netExamine')
      }
      const idx = this.limitRoute.findIndex(item => path.indexOf(item) > -1)
      if (idx > -1) {
        const status = this.computeNetAccessStatus()
        if (_.isNull(_.get(status, 'isAccess')) && this.clientInfo.online) {
          return state
        }
        // 未入网
        if (!_.get(status, 'isAccess')) {
          // 设备被隔离
          if (_.get(status, 'cutOff')) {
            state['stateMsg'] = this.$t('header.cutoff')
            return state
          }
          // IP,MAC绑定违规/指纹违规
          if (_.get(status, 'ipMacBindIllegal')) {
            state['stateMsg'] = this.$t('header.outLineIP')
            return state
          }
          // 指纹违规
          if (_.get(status, 'fingerIllegal')) {
            state['stateMsg'] = this.$t('header.outLineFinger')
            return state
          }
          // 已注册待审核状态或者审核过期
          if (_.get(status, 'registeredStatus.needAduit') || _.get(status, 'registeredStatus.auditEnd')) {
            state['stateMsg'] = this.$t('header.auditing')
            return state
          }
        }
      }
      // 特殊情况,重注册待审核网络未断开不能跳到来宾接待
      if (path.indexOf('access/guestReceive') > -1) {
        const status = this.computeNetAccessStatus()
        if (_.get(status, 'registeredStatus.needAduit')) {
          state['stateMsg'] = this.$t('header.auditing')
          return state
        }
      }
      state['next'] = true
      return state
    },
    cutOut(str) {
      if (str && str.length) {
        return str.substr(0, 3)
      }
      return str
    }
  }
}
</script>
<style lang="scss" scoped>
.layout-aside {
  width: 200px;
  height:100%;
  background: $menu-bg;
  overflow: auto;
  z-index: 10;

  .u-offlineTips {
    width: 100%;
    padding: 10px;
    background: #fceded;
    display: flex;
    justify-content: center;

    .off-tip-content {
      display: flex;
      line-height: 20px;
      font-size: 14px;
      color: rgba(230, 83, 83, 1);

      i {
        padding-right: 10px;
        font-size: 14px;
      }
    }
  }

  .menu-wrapper {
    padding-bottom: 60px;
    margin: 0 16px;

    li {
      line-height: 40px;
    }

    .menu-item {
      font-size: 13px;
      color: $disabled-color;
      font-weight: 400;

      .menu-item-title {
        padding-left: 8px;
      }
    }

    .sub-menu-item {
      font-size: 14px;
      color: $default-color;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-left: 24px;
      white-space: nowrap;

      .iconfont {
        margin-right: 10px;
        color: $default-color
      }

      span{
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .sub-menu-item:hover {
      background: $default-bg;
      color: $--color-primary;
      border-radius: 4px;

      .iconfont {
        color: $--color-primary;
      }
    }

    .active-sub-item {
      background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
      border-radius: 4px;
      color: $light-color;

      .iconfont {
        color: $light-color
      }
    }

    .active-sub-item:hover {
      background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
      color: $light-color;
      border-radius: 4px;

      .iconfont {
        color: $light-color
      }
    }
  }

  .version-wrapper {
    position: fixed;
    bottom: 1px;
    left: 1px;
    width: 200px;
    background: $menu-bg;
    font-size: 12px;
    line-height: 33px;
    text-align: center;
    color: #B3B6C1;
    z-index: 11;
  }
}</style>
