import G_VARIABLE from '@/render/utils/G_VARIABLE'
import agentApi from '@/service/api/agentApi'
import { i18n } from '@/render/lang'
import Vue from 'vue'
import { sleep } from '@/render/utils/global'
const securityUtil = {
  getPolicysKey() { // 系统不同接口返回安检策略字段不同
    const pathDic = {
      'windows': 'Item',
      'linux': 'LinuxCheckItems.Item',
      'mac': 'MacCheckItems.Item'
    }
    const deviceType = _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    return pathDic[deviceType] || pathDic.windows
  },

  async checkPrecondition() {
    /*
       *如果没有配置违规外联，默认不检查
       *存在这样的情况，一开始配置了违规外联规范，这是小助手的配置文件中已经修改为NO或者YES
       *NO，这种情况，但如果不再这里重新发一遍报文给小助手，则当重新配置规范后安检后默认不发送报文给小助手，则小助手并不会将配置文件修改，
       *于是造成虽然安检通过，仍然小助手过段时间还是会安检不通过，因为小助手仍然按照上次发报文后记录的配置文件去进行安检
       */
    const param = {
      CheckType: {
        InsideName: 'CheckUnlawfulConnectOut',
        OutsideName: i18n.t('safecheck_js_langObj.js_1_s'),
        Desc: i18n.t('safecheck_js_langObj.js_4_s'),
        DLL: 'MsacAssRuntimeCheck.dll',
        DLLFunc: 'CheckUnlawfulConnectOut',
        Option: { ProcessInfo: '' }
      }
    }
    const res = await agentApi.callAgentOneFunc({
      WhatToDo: 'CallOneFunc',
      WhereIsModule: 'MsacAssRuntimeCheck.dll',
      WhatFuncToCall: 'CheckUnlawfulConnectOut',
      RequestParam: JSON.stringify(param)
    })
    console.log('违规外联接口完成', res)
    if (!res) {
      Vue.prototype.$msg({
        message: i18n.t('safecheck_js_langObj.js_1_s') + i18n.t('check.fail'),
        type: 'error'
      })
      return false
    }

    // 如果没有配置禁止运行进程，默认不检查
    const processParam = {
      CheckType: {
        InsideName: 'CheckSystemProcess',
        OutsideName: i18n.t('safecheck_js_langObj.js_5_s'),
        Desc: i18n.t('safecheck_js_langObj.js_6_s'),
        DLL: 'MsacCheckSoftInfo.dll',
        DLLFunc: 'CheckSystemProcess',
        Option: { ProcessInfo: '' }
      }
    }
    const processRes = await agentApi.callAgentOneFunc({
      WhatToDo: 'CallOneFunc',
      WhereIsModule: 'MsacCheckSoftInfo.dll',
      WhatFuncToCall: 'CheckSystemProcess',
      RequestParam: JSON.stringify(processParam)
    })
    console.log('系统进程检查完成', processRes)
    if (processRes) {
      return true
    } else {
      Vue.prototype.$msg({
        message: i18n.t('safecheck_js_langObj.js_5_s') + i18n.t('check.fail'),
        type: 'error'
      })
      return false
    }
  },
  async requestCheckResult(item) {
    let result = null
    const request = async() => {
      const ret = await agentApi.getSecurityCheckItem({
        ItemID: item.ItemID,
        InsideName: item.InsideName
      })
      if (ret && _.isObject(ret.Result)) {
        result = ret
      }
    }

    for (let i = 0; i < 3; i++) {
      try {
        // 暂停1s
        await request()
        if (result !== null) {
          break
        }
        await sleep(1000)
      } catch (error) {
        console.log(error)
        break
      }
    }
    return result || {}
  }
}

export default securityUtil
