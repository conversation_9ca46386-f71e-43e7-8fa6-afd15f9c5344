<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.ProcessMustRun.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <div v-for="(item, index) in processList" :key="item.SoftName" class="pc-info">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.ProcessMustRun.h_3_rd") }}
                  <span>{{ item.SoftName }}</span>
                </div>
                <span v-if="item.RepairType === 'url' && item.btnText" :class="[checkData.hasFixed ? 'disable-link-btn': 'link-btn']" @click="fixHandle(item, index)">{{ item.btnText }}</span>
                <button v-if="item.RepairType === 'path' && item.btnText" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ item.btnText }}
                </button>
              </div>
              <div class="optional-item">
                {{ $t("check.ProcessMustRun.js_3_rd") }}
                {{ item.Remark }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckProcessMustRun',

  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      processList: []
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getProcessList()
  },
  methods: {
    async fixHandle(item, index) {
      if (item.RepairType === 'url') {
        this.openUrl(item.url)
      } else if (item.RepairType === 'path') {
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            Path: item.path,
            Param: item.param,
            CheckProcess: item.softName
          },
          RepairType: 0,
          CreateProgress: 1
        }
        await this.submitHandle({
          params,
          CheckItem: item,
          needProccess: true,
          tip: this.$t('check.ProcessMustRun.js_7_s')
        })
        this.$set(this.processList, index, item)
      }
    },
    getProcessList() {
      let mustProcess = _.get(this.checkData, 'CheckResult.CheckType.Info.ProcessInfo')
      let policy = _.get(this.checkData, 'CheckType.Option.Item')
      if (!mustProcess || !policy) {
        return
      }
      mustProcess = mustProcess.split('|')
      if (!_.isArray(policy)) {
        policy = [policy]
      }
      const tmpObj = []
      mustProcess.forEach((item, index) => {
        for (let p = 0; p < policy.length; p++) {
          if (item === policy[p].SoftName) {
            tmpObj.push({
              SoftName: policy[p].SoftName,
              Remark: policy[p].Remark,
              RepairType: policy[p].RepairType
            })
            tmpObj[index].hasFixed = this.checkData.hasFixed
            if (policy[p].RepairType === 'url' && policy[p].Url) {
              tmpObj[index].btnText = this.$t('check.ProcessMustRun.js_5_s')
              tmpObj[index].url = policy[p].Url
            }
            if (policy[p].RepairType === 'path' && policy[p].Path) {
              tmpObj[index].btnText = this.$t('check.ProcessMustRun.js_7_s')
              tmpObj[index].path = policy[p].Path
              tmpObj[index].param = policy[p].Param
              tmpObj[index].softName = policy[p].SoftName
            }
          }
        }
      })
      this.processList = tmpObj
    }
  }
}
</script>

