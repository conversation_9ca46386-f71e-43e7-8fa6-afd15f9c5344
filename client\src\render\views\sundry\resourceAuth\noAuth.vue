
<template>
  <div id="f-resource-noauth" v-loading="loading" :element-loading-text="$t('resourceAuth.authLading')" />
</template>

<script>
import loading from '@/render/components/globalComponents/loading/index'
import proxyApi from '@/service/api/proxyApi'
import qs from 'qs'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      loading: true
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }
  },
  created() {
    this.init()
  },
  methods: {
    async    init() {
      loading.start({ msg: this.$t('dialogFoot.loading') })

      try {
        await this.autoAuth()
      } catch (error) {
        console.log(error)
      }

      loading.destory()
    },

    async autoAuth() {
      const firstUrl = _.get(qs.parse(location.search.substring(1)), 'firsturl', '')
      const apiParams = {
        deviceid: this.deviceid,
        resourceAddr: firstUrl,
        type: 'NoAuth' // 认证方式'NoAuth','User','Qrcode'
      }

      // 请求认证接口
      const ret = await proxyApi.resourceAuth(apiParams)
      this.loading = false
      if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data', '')) || !_.isObject(_.get(ret, 'data', ''))) {
        // 认证失败
        const err = _.get(ret, 'errmsg') || this.$t('resourceAuth.authFaild')
        this.$emit('authFaild', { type: 'noAuth', msg: err })
        return
      }

      this.$emit('authSuccess', { type: 'NoAuth' })
    }
  }
}
</script>

<style lang="scss">
#f-resource-noauth{
  width: 100%;
  height:500px;
}
</style>
