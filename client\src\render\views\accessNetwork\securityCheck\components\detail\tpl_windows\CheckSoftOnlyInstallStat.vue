<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.SoftOnlyInstallStat.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.SoftOnlyInstallStat.h_3_rs") }}
          </p>
          <div v-for="(item, index) in onlyInstallInfo" :key="item.SoftName + index" class="pc-info">
            <img :src="forImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.SoftOnlyInstallStat.h_5_rd") }}
                  <span>{{ item.SoftName }}</span>
                </div>
                <button v-if="item.SoftUninstall" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.SoftOnlyInstallStat.js_3_d") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.SoftOnlyInstallStat.h_11_rd") }}
                <span>{{ item.SoftVersion }}</span>
              </div>
              <div class="optional-item margin-style ">
                {{ $t("check.SoftOnlyInstallStat.h_7_rd") }}
                <span>{{ item.SoftInstallPath }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.SoftOnlyInstallStat.h_9_rd") }}
                <span>{{ item.Publisher }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckSoftOnlyInstallStat',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {},
      collapseFlag: true,
      onlyInstallInfo: [],
      forImgSrc: require('@/render/assets/forbidSoft.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.initFixTip()
    this.getOnlyInstallInfo()
  },
  methods: {
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.SoftOnlyInstallStat.h_11_rs'),
        fixSteps: [this.$t('check.SoftOnlyInstallStat.h_13_rs'), this.$t('check.SoftOnlyInstallStat.h_15_rs')]
      }
    },
    getOnlyInstallInfo() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.onlyInstallInfo = list
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          ApplicationName: '',
          CommandLine: item.SoftUninstall,
          WaitApplicationExit: 'No'
        },
        RepairType: 0,
        CreateProgress: 0
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.SoftOnlyInstallStat.js_3_d')
      })
      this.$set(this.onlyInstallInfo, index, item)
    }
  }
}
</script>

