/* eslint-disable */
import os_browser_info from '@/render/utils/os_browser_info'

var JSXML = function() {
  /** * ע��                : javascript����xml
   *** self                : xml������
   *** loadStrXml            : ����xml�ַ���
   *** loadFileXml        : ����xml�ļ�
   ***/

  this.self = null // xml����
  this.Browser = ''
  this.isIE = (os_browser_info.browser_version.indexOf('IE') != -1)
  /** * ����xml�ַ��� ***/
  this.loadStrXml = function(strxml) {
    var strxml = this._replace(strxml)
    if (window.ActiveXObject || this.isIE) {
      var xmlDomVersions = ['Msxml2.DOMDocument.6.0', 'Msxml2.DOMDocument.5.0', 'Msxml2.DOMDocument.4.0', 'Msxml2.DOMDocument.3.0', 'MSXML2.DOMDocument', 'MSXML.DOMDocument', 'Microsoft.XMLDOM']

      for (var i = 0; i < xmlDomVersions.length; i++) {
        try {
          this.self = new ActiveXObject(xmlDomVersions[i])
          this.self.async = false
          this.self.loadXML(strxml) // loadXML��������xml�ַ���
          break
        } catch (e) {
        }
      }
    } else if (window.DOMParser && document.implementation && document.implementation.createDocument) {
      var oParser = new DOMParser() // Firefox�����������
      this.self = oParser.parseFromString(strxml, 'text/xml')
    } else {
      return false
    }
    return true
  }
  this._replace = function(str) {
    str = str.replace(/\n/g, '')
    str = str.replace(/\r/g, '')
    str = str.replace(/\\'/g, '\'')// �޸�ʱ������ ��Ӧsafecheckdetail.php
    str = str.replace(/\t/g, '')
    str = str.replace(/>[ ]+</g, '><')
    return str
  }
  /** * ����xml�ļ�,filepathΪ�ļ�·�� ***/
  this.loadFileXml = function(filepath) {
    try { // IE���������
      this.self = new ActiveXObject('Microsoft.XMLDOM')
    } catch (e) {
      try { // Firefox, Mozilla, Opera, etc.
        this.self = document.implementation.createDocument('', '', null)
      } catch (e) {
        alert(e.message)
      }
    }
    try {
      this.self.async = false // �ر��첽
      this.self.load(filepath)
    } catch (e) {
      alert(e.message)
    }
    return true
  }

  // ȡԪ������ֵ
  this.selectAttrValue = function(path, AttrName) {
    var nodeObj = this.self.selectNodes(path)[0]

    var attrNodes = nodeObj.attributes
    //      alert(attrNodes.length);
    for (var i = 0; i < attrNodes.length; i++) {
      //          alert(attrNodes[i].nodeName);
      if (aRes[attrNodes[i].nodeName] = AttrName) {
        return attrNodes[i].nodeValue
      }
    }
    return null
  }

  // ȡԪ�ؽڵ�ֵ����:selectValue("Msac/Option[1]/Item")
  this.selectValue = function(path) {
    return this.ResolveXML(path).text
  }
  // ȡԪ���ֵܽڵ�����ֵ����:selectListValue("Msac/Option[1]/Item",'nodeName')
  this.selectListValue = function(path, avName) {
    if (window.ActiveXObject || this.isIE) {
      this.self.setProperty('SelectionLanguage', 'XPath')
      var res = this.self.selectNodes(path)
      for (var i = 0; i < res.length; i++) {
        if (res[i].childNodes[2].firstChild != null && res[i].childNodes[2].firstChild.nodeValue == avName) {
          var resw = res[i].childNodes
          if (resw[9]) {
            return resw
          } else {
            return 0
          }
        }
      }
    } else if (document.implementation && document.implementation.createDocument) {
      // code for Mozilla, Firefox, Opera, etc.
      var nodes = this.self.evaluate(path, this.self, null, XPathResult.ANY_TYPE, null)
      var result = nodes.iterateNext()
      if (result != null) {
        while (result) {
          if (result.childNodes[2].firstChild != null && result.childNodes[2].firstChild.nodeValue == avName) {
            var resw = result.childNodes
            if (resw[9]) {
              return resw
            } else {
              return 0
            }
          }
          result = nodes.iterateNext()
        }
      }
    }

    //      alert(res.length);
    return false
  }
  // ȡͬ���ֵܽڵ����
  this.getNodeNum = function(path, returntype) {
    return this.ResolveXML(path).leng
  }
  /*
* ��ͬ���������xml 2012-07-09 yancheng
* return object
*/
  this.ResolveXML = function(path) {
    var nodetext = ''
    var itemleng = 0
    if (window.ActiveXObject || this.isIE) {
      this.self.setProperty('SelectionLanguage', 'XPath')
      var nodes = this.self.selectNodes(path)
      itemleng = nodes.length
      if (itemleng > 0) {
        nodetext = nodes[0].childNodes.length > 0 ? nodes[0].childNodes[0].nodeValue : ''
      } else {
        nodetext = ''
      }
    } else if (document.implementation && document.implementation.createDocument) {
      // code for Mozilla, Firefox, Opera, etc.
      var nodes = this.self.evaluate(path, this.self, null, XPathResult.ANY_TYPE, null)
      var result = nodes.iterateNext()
      if (result != null) {
        nodetext = result.childNodes.length > 0 ? result.childNodes[0].nodeValue : ''
        while (result) {
          itemleng++
          result = nodes.iterateNext()
        }
      }
    }
    return {
      text: nodetext,
      leng: itemleng
    }
  }
  this.getxmlDoc = function() {
    return this.self
  }
}

export default JSXML

