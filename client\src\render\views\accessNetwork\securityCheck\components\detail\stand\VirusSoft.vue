<template>
  <div class="stand-detail-modle">
    <p class="model-title">
      {{ $t('accessNetwork.securityCheck.info_18')
      }}<i
        :class="
          modelIsOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
        "
        @click="modelIsOpen = !modelIsOpen"
      />
    </p>
    <el-collapse-transition>
      <div v-show="modelIsOpen" class="stand-model-content">
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          class="public-no-boder-table"
        >
          <el-table-column prop="name" :show-overflow-tooltip="true" :label="$t('accessNetwork.securityCheck.info_22')" />
          <el-table-column
            prop="versions"
            width="140"
            :label="$t('accessNetwork.securityCheck.info_23')"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="info"
            width="172"
            :label="$t('accessNetwork.securityCheck.info_24')"
            :show-overflow-tooltip="true"
          />
        </el-table>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
export default {
  props: {
    standData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      modelIsOpen: true,
      tabHeight: '240'
    }
  },
  computed: {
    tableData() {
      const _standData = []
      let compare = { 'MoreThan': '大于', 'Equal': '等于', 'LessThan': '小于' }
      let cpday = { 'Date': '更新日期', 'Day': '未更新天数', 'Version': '版本号' }
      if (this.$i18n.locale === 'en') {
        compare = { 'MoreThan': ' MoreThan ', 'Equal': ' equale ', 'LessThan': ' LessThan ' }
        cpday = { 'Date': 'Update Date', 'Day': 'Number of days not updated' }
      }
      this.standData.forEach(item => {
        if (item.IsCheck === 'Yes') {
          let dday = item.DBCompareType === 'Day' ? ' Days' : ''
          if (dday === ' Days' && this.$i18n.locale === 'zh') {
            dday = '天'
          }
          let _versions = compare[item.SoftCompare] + item.SoftVersion
          if (_versions.toLowerCase() === 'undefined') {
            _versions = '' // 针对通用杀毒软件，默认不显示版本信息
          }
          const obj = {
            name: this.$i18n.locale === 'en' ? item.AntiVirusEnName : item.AntiVirusName,
            versions: _versions,
            info: item.DBVersion ? (cpday[item.DBCompareType] + compare[item.DBCompare] + item.DBVersion + dday) : ''
          }
          _standData.push(obj)
        }
      })
      console.log('_standData', _standData)
      return _standData
    }
  }
}
</script>

