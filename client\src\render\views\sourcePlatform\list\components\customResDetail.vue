<!-- 自定义应用资源详情 -->
<template>
  <div class="custom-res-detail">
    <div class="basic-info">
      <p class="sub-heading">{{ $t('sourcePlatform.baseInfo') }}</p>
      <div class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.appName') }}</div>
        <div class="info-value text-clamp" :title="resDetail.ResName">{{ resDetail.ResName }}</div>
      </div>
      <div class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.desc') }}</div>
        <div class="info-value text-clamp" :title="resDetail.Remark">{{ resDetail.Remark || '--' }}</div>
      </div>
    </div>
    <div class="open-type">
      <p class="sub-heading">{{ $t('sourcePlatform.openTypeTitle') }}</p>
      <div class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.setOrigin') }}</div>
        <div class="info-value opt-wrapper">
          <span class="tag  active-tag">{{ isCustomModel ? $t('sourcePlatform.customSet'): $t('sourcePlatform.adminSet') }}</span>
          <span v-if="isCustomModel || isOpenCustomModel" class="tag" @click="changeMode">{{ customPath ? $t('sourcePlatform.restoreAdminSet') : $t('sourcePlatform.customSet') }}</span>
        </div>
      </div>
      <div class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.openType') }}</div>
        <div class="info-value">{{ openType }}</div>
      </div>
      <div v-if="!isCustomModel && !isMac" class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.softName') }}</div>
        <div class="info-value text-clamp" :title="resDetail.ProcessByName">{{ resDetail.ProcessByName || '--' }}</div>
      </div>
      <div v-if="!isMac || isCustomModel" class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.softAddr') }}</div>
        <div :class="['info-value', 'text-clamp', 'path-wrapper', customPath? 'pr-30': '' ]" :title="calcPath">{{ calcPath || '--' }} <span v-if="isCustomModel" class="edit-btn" @click="selectApp">{{ $t('sourcePlatform.edit') }}</span></div>
      </div>
      <div v-if="isMac && !isCustomModel" class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.bundle') }}</div>
        <div class="info-value text-clamp" :title="resDetail.ProcessByName">{{ resDetail.ProcessByName || '--' }}</div>
      </div>
      <div v-if="isCustomModel" class="row-item">
        <div class="info-label">{{ $t('sourcePlatform.setTime') }}</div>
        <div class="info-value">{{ customTime }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import agentApi from '@/service/api/agentApi'
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import os_browser_info from '@/render/utils/os_browser_info'
import dateHelper from '@/render/utils/date/dateUtil'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      resDetail: {},
      customPath: '',
      customTime: ''
    }
  },
  computed: {
    ...mapGetters(['clientInfo', 'authInfo']),
    openType() {
      const mode = this.resDetail.AccessMode
      if (mode) {
        return mode === 'path' ? this.$t('sourcePlatform.pathMode') : mode === 'browser' ? this.$t('sourcePlatform.browserMode') : this.$t('sourcePlatform.scriptMode')
      }
      return ''
    },
    userId() {
      return _.get(this.clientInfo, 'accessStatus.UserID') || _.get(this.authInfo, 'basic.ID')
    },
    isOpenCustomModel() {
      return parseInt(this.info.AllowSelfCfg) === 1
    },
    calcPath() {
      return this.customPath || this.resDetail.ProcessByPath
    },
    isCustomModel() {
      return this.customPath
    },
    isMac() {
      return os_browser_info.os_type === 'mac'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.resDetail = { ...this.info }
      const { path, time } = await this.getLocalConf(_.get(this.info, 'ResID'))
      if (path) {
        this.customPath = path
        this.customTime = time
      }
    },

    async getLocalConf(resId) {
      const pathKey = `customConf.${resId}-${this.userId}`
      const customInfo = await sourceListUtil.readResConfFromClientLocal(pathKey)
      return typeof customInfo === 'object' ? customInfo : {}
    },
    async changeMode() {
      if (this.customPath) {
        this.customPath = ''
        this.customTime = ''
        sourceListUtil.delResConfClientLocal(`customConf.${_.get(this.info, 'ResID')}-${this.userId}`)
      } else {
        this.selectApp()
      }
    },
    async selectApp() {
      const params = {
        FileName: '',
        File: '',
        Params: '',
        ResType: 2
      }
      const res = await agentApi.windowCustomAppOpenUrl(params)
      if (parseInt(_.get(res, 'ASM.ErrCode')) === 0 && _.get(res, 'ASM.OpenedFile')) {
        this.customPath = _.get(res, 'ASM.OpenedFile')
        const time = dateHelper.timeStampToDate(new Date().getTime())
        this.customTime = time
        const data = {}
        data[_.get(this.info, 'ResID') + '-' + this.userId] = { path: this.customPath, time }
        sourceListUtil.saveResConfToClientLocal('customConf', data)
      }
    }
  }
}
</script>
  <style lang="scss" scoped>
  .custom-res-detail{
    padding: 10px 32px;
    position: relative;
    height: 100%;
    color: $default-color;
    .sub-heading{
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 22px;
      color: $title-color;
    }
    .row-item{
      display: flex;
      align-items: center;
      margin-bottom: 22px;
    }
    .info-label{
      line-height: 22px;
      width: 115px;
    }
    .info-value{
      color: $title-color;
      width: 191px;
      .tag{
        font-size: 12px;
        line-height: 22px;
        color: $--color-primary;
        cursor: pointer;
        margin-right: 6px;
      }
      .active-tag{
        height: 20px;
        line-height: 18px;
        border: 1px solid $success;
        border-radius: 4px;
        color: $success;
        padding-left: 4px;
        padding-right: 4px;
        cursor: default;
      }
    }
    .path-wrapper{
      position: relative;
      .edit-btn{
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        color: $--color-primary;
      }
    }
    .pr-30{
      padding-right: 30px;
    }
    .opt-wrapper{
      display: flex;
      align-items: center;
    }
  }
  </style>
