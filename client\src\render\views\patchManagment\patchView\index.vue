<!-- 补丁查看 -->
<template>
  <div id="pathSeeWrap" v-loading="loading" class="patch-see-wrap">
    <div
      v-if="tableData.length"
      class="patch-see-content"
    >
      <p class="title">{{ $t('patchView.info_1') }}</p>
      <div class="table-wrap">
        <!-- border -->
        <el-table
          :data="tableData"
          stripe
          height="100%"
          style="width: 100%"
          class="public-no-boder-table"
        >
          <el-table-column prop="Description" show-overflow-tooltip :label="$t('patchView.info_3')" />
          <el-table-column
            prop="KB"
            width="112"
            :label="$t('patchView.info_5')"
          />
          <el-table-column
            prop="InstallTime"
            width="142"
            :label="$t('patchView.info_6')"
          />
        </el-table>
      </div>
      <el-pagination
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size.sync="pageSize"
        class="public-pag-bag"
        layout="prev, pager, next, sizes, jumper"
        :total="total"
        background
        @size-change="handleSizeChange"
      >
        <!-- hide-on-single-page -->
      </el-pagination>
    </div>
    <div v-if="!tableData.length && inited" class="empty-wrap">
      <img src="../../../assets/stateIllustration/empty.png" alt="">
      <p class="empty-info">{{ $t('patchView.info_2') }}</p>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import agentApi from '@/service/api/agentApi'

export default {
  name: 'PatchSee',
  components: {},
  data() {
    return {
      allPatchData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10,
      loading: true,
      inited: false
    }
  },

  computed: {
    tableData() {
      return _.get(this.pagePatchData, '[' + (this.currentPage - 1) + ']', [])
    },
    pagePatchData() {
      return _.chunk(this.allPatchData, this.pageSize)
    }
  },
  created() {
    this.getPatch()
  },
  methods: {
    async getPatch() {
      const res = await agentApi.getInstalledPatchList()
      this.inited = true
      let patchArr = _.get(res, 'ASM.UpdateList.Update', [])
      if (_.isObject(patchArr) && !_.isArray(patchArr)) {
        patchArr = [patchArr]
      }
      if (patchArr.length > 0) {
        this.total = patchArr.length
        this.allPatchData = patchArr
      }
      this.loading = false
    },
    handleSizeChange(val) {
      this.currentPage = 1 // 当前页码
      this.pageSize = val
    }
  }
}
</script>
<style lang="scss">
.patch-see-wrap {
  padding: 0 24px;
  box-sizing: border-box;
  position: relative;
  height: 100%;
  text-align: center;
  overflow: auto;
  .patch-see-content{
    height: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 500;
    color: $title-color;
    line-height: 22px;
    padding-top: 24px;
    padding-bottom: 16px;
    text-align: left;
  }
  .table-wrap {
    height: calc(100% - 136px);
    .el-table {
      .cell {
        .patch-title {
          cursor: pointer;
          display: inline-block;
          width: auto;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .el-table__body {
        tr {
          .waring {
            color: $waring;
          }
          .error {
            color: $error;
          }
        }
      }
    }
  }
  .el-pagination{
    margin-top: 24px;
  }
  .empty-wrap {
    text-align: center;
    display: inline-block;
    margin-top: 100px;
    img {
      display: inline-block;
      width: 246px;
      height: 236px;
    }
    .empty-info {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: $default-color;
      line-height: 22px;
      margin-top: 40px;
      font-weight: 500;
    }
  }
}
#pathSeeWrap{
  .el-table th{
    background: $gray-1;
    border-left: 1px solid $line-color;
    &:first-child{
      border-left: none;
    }
  }
  .el-table .gutter{
    border-left: none
  }
}
</style>
