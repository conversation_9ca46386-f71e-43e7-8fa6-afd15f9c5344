const VueLoaderPlugin = require('vue-loader/lib/plugin')
const path = require('path')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MineCssExtractPlugin = require('mini-css-extract-plugin')
const { DefinePlugin } = require('webpack')
const CopyPlugin = require('copy-webpack-plugin')
const FileManagerPlugin = require('filemanager-webpack-plugin')
const EslintPlugin = require('eslint-webpack-plugin')
const fs = require('fs')
const { exec } = require('child_process')

// 生成webpack的基本配置代码
exports.bootstrap = function(dev = 'production') {
  return {
    entry: {
      main: './src/main.js' // 入口
    },
    devtool: dev === 'production' ? 'cheap-module-source-map' : 'eval',
    resolve: {
      extensions: ['.json', '.js', '.jsx', '.vue'],
      alias: {
        '@': path.resolve(path.join('.', 'src'))
      }
    },
    // 当前webpack的cache，只对正式环境开放，因为eslint有个bug和cache冲突，导致eslint无效
    cache: dev === 'production' ? {
      type: 'filesystem',
      buildDependencies: {
        // 更改配置文件时，重新缓存
        config: [__filename]
      }
    } : { type: 'memory' },
    output: {
      // 输出目录 这里使用path.resolve方法是为了消除跨平台的差异因为mac和window的绝对路径表示方式不一样
      path: path.resolve(__dirname, '../dist'),
      clean: true, // 在生成文件之前清空 output 目录
      filename: 'js/[name].js?t=[chunkhash]',
      chunkFilename: 'js/[name].js?t=[chunkhash]'
    },
    optimization: {
      chunkIds: 'named',
      minimize: false // 这里不压缩以换取时间
    }
  }
}

exports.setMode = function name(dev = 'production') {
  return {
    mode: dev
  }
}

// 设置插件相关的
exports.setPlugins = function() {
  return {
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html', // 我们要使用的 html 模板地址
        filename: 'index.html', // 打包后输出的文件名
        title: '' // index.html 模板内，通过 <%= htmlWebpackPlugin.options.title %> 拿到的变量
      }),

      new VueLoaderPlugin(),

      // 提取css
      new MineCssExtractPlugin({
        filename: 'css/[name].css?t=[chunkhash]',
        chunkFilename: 'css/[name].css?t=[chunkhash]',
        ignoreOrder: true
      }),

      // 设置环境变量
      new DefinePlugin(
        {
          BASE_URL: '"./"'
        }
      ),

      new CopyPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, '../public'),
            to: path.resolve(__dirname, '../dist'),
            toType: 'dir',
            noErrorOnMissing: true,
            globOptions: {
              ignore: [
                '**/.DS_Store',
                '**/index.html'
              ]
            },
            info: {
              minimized: true
            }
          }
        ]
      }
      )
    ]
  }
}

// 进行eslint检查.webpack5中用eslint-webpack-plugin的plugin代替eslint-loader
// @todo 截止2022/12/29 webpack-eslint-plugin官方插件有bug，当webpack5设置cache:filesystem，只会在第一次的时候进行eslint检查。后续cache存在后则不检查
// bug:https://github.com/webpack-contrib/eslint-webpack-plugin/issues/130 [如果bug修复，development的cache也需要开启]
exports.setEslints = function() {
  return {
    plugins: [new EslintPlugin({
      failOnWarning: true,
      extensions: ['js', 'vue', 'ts']
    })]

  }
}

// 检查electron的资源app.asar是否存在,如果不存在,就先编译一下
class CheckAsarExist {
  apply(compiler) {
    compiler.hooks.afterEmit.tapAsync('CheckAsarExist', async(stats, callback) => {
      const filePath = path.resolve(__dirname, '../distAsar/app.asar')

      fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
          // File does not exist, run the command
          console.log(`${filePath} does not exist. Running webpack with the specified config...`)
          exec('webpack --env from=client --config build/webpack.electron.prod.config.js', (error, stdout, stderr) => {
            if (error) {
              console.error(`Error executing command: ${error.message}`)
              return callback(error)
            }
            if (stderr) {
              console.error(`stderr: ${stderr}`)
            }
            console.log(`stdout: ${stdout}`)
            callback()
          })
        } else {
          // File exists, proceed with the build
          console.log(`${filePath} exists. Proceeding with the build...`)
          callback()
        }
      })
    })
  }
}

/**
 *
 *  from ="electron",来自webpack.electron.prod.config.js
 *
 * */
exports.makeQrcZip = function(from = '') {
  const plugins = []

  if (from !== 'electron') {
    plugins.push(new CheckAsarExist())
  }

  plugins.push(
    new FileManagerPlugin({
      events: {
        onEnd: {
          delete: [path.join(__dirname, '../dist/pack/Qrc')],
          copy: [
            {
              source: path.join(__dirname, '../dist/**'),
              destination: path.join(__dirname, '../dist/pack/Qrc/dist'),
              globOptions: {
                ignore: ['**/Qrc', '**/Qrc.zip']
              }
            },
            { source: path.join(__dirname, '../src/render/assets/qrcSource'), destination: path.join(__dirname, '../dist/pack/Qrc') },
            { source: path.join(__dirname, '../distAsar/app.asar'), destination: path.join(__dirname, '../dist/pack/Qrc/app.asar') }
          ],
          archive: [{
            source: path.join(__dirname, '../dist/pack'),
            destination: path.join(__dirname, '../dist/Qrc.zip')
          }]
        }
      },
      runTasksInSeries: true
    })
  )

  return {
    plugins: plugins
  }
}

// 设置loaders
exports.setLoaders = function() {
  return {
    module: {
      rules: [
        // handle vue sfc
        {
          test: /\.vue$/,
          exclude: /(node_modules)/,
          use: ['vue-loader']
        },
        // handle scss filetype
        {
          test: /\.css|sass|scss$/,
          exclude: /(bower_components)/,
          use: [
            MineCssExtractPlugin.loader,
            {
              loader: 'css-loader',
              options: {
                sourceMap: false
              }
            },
            {
              loader: 'postcss-loader'
            },
            {
              loader: 'sass-loader',
              options: {
                additionalData: `@import "~@/render/styles/theme/default/index.scss";`
              }
            }

          ]
        },
        // handle assert(such as .png,.jpg ,font filetype and so on) filetypes
        {
          test: /\.(png|jpe?g|gif|svg|eot|ttf|woff|woff2|ico)$/,
          type: 'asset/resource',
          generator: {
            filename: 'staic/[name].[hash:6][ext]'
          }
        }
      ]
    }
  }
}

function excludeNodeModulesExcept(modules) {
  var pathSep = path.sep
  if (pathSep === '\\') {
    pathSep = '\\\\'
  }
  var moduleRegExps = modules.map(function(modName) {
    return new RegExp('node_modules' + pathSep + modName)
  })

  return function(modulePath) {
    if (/node_modules/.test(modulePath)) {
      for (var i = 0; i < moduleRegExps.length; i++) {
        if (moduleRegExps[i].test(modulePath)) {
          return false
        }
      }
      return true
    }
    return false
  }
}

exports.setBabelLoader = function() {
  // 这些在node_modules里面的包也需要转成es5的语法。
  const modulesToTranspile = ['as-table', 'stacktracey', 'get-source', 'printable-characters', 'js-base64', 'vue-json-viewer']
  return {
    module: {
      rules: [
        {
          test: /(.js|.mjs)$/,
          // 正则匹配需要注意window和linux路径分割符不一致，需要同时兼容
          exclude: excludeNodeModulesExcept(modulesToTranspile),
          use: [
            { loader: 'babel-loader' } // 转换成es5
          ]
        }
      ]
    }
  }
}
