<template>
  <div v-loading="!inited && loading" class="single-content">
    <empty v-if="inited && !tableData.length" />
    <el-table
      v-if="inited && tableData.length"
      v-loading="inited && loading"
      :data="tableData"
      border
      stripe
      :max-height="maxHeight"
      style="width: 100%"
      class="guest-formate-style-table"
    >
      <el-table-column
        prop="Name"
        fixed
        show-overflow-tooltip
        width="105"
        :label="$t('guestAuth.guest.info_58')"
      />
      <el-table-column
        prop="Tel"
        width="120"
        :label="$t('guestAuth.guest.info_59')"
      />
      <el-table-column
        prop="Unit"
        show-overflow-tooltip
        :label="$t('guestAuth.guest.info_61')"
        min-width="180"
      />
      <el-table-column
        prop="InsertTime"
        width="180"
        :label="$t('guestAuth.guest.info_63')"
      />
      <el-table-column
        prop="lifeTime"
        :label="$t('guestAuth.guest.info_69')"
        width="130"
        show-overflow-tooltip
      />

      <el-table-column
        fixed="right"
        :label="$t('guestAuth.guest.info_64')"
        width="100"
      >
        <template slot-scope="scope">
          <i class="iconfont icon-xiaxian" :title="$t('guestAuth.guest.info_83')" @click="outLine(scope.row)" />
        </template>
      </el-table-column>
    </el-table>

    <verticalDialog
      :show="showOutLine"
      :show-close="false"
      :show-foot="true"
      width="384px"
      :loading="submiting"
      class="guest-m-confirm-dialog"
      @cancel="showOutLine = false"
      @confirm="confirmHandle"
    >
      <div slot="header" class="v-header">
        <i class="iconfont icon-putongxiangbuhegui" />
        {{ $t('guestAuth.guest.info_76') }}
      </div>
      <div class="g-s-diaolog-content">
        <div class="form-content">
          <p class="outline-tips">{{ $t('guestAuth.guest.info_74') }}</p>
        </div>
      </div>
    </verticalDialog>
  </div>

</template>
<script>
import '@/render/styles/guestTable.scss'
import empty from '../../audit/components/empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapState } from 'vuex'
export default {
  components: {
    empty
  },
  props: {
    maxHeight: {
      default: '',
      type: [String, Number]
    }
  },
  data() {
    return {
      inited: false,
      showOutLine: false,
      loading: false,
      tableData: [],
      submiting: false,
      currentRow: {}
    }
  },
  computed: {
    ...mapState(['clientInfo'])
  },
  mounted() {
    console.log('heightssss', this.maxHeight)
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const ret = await proxyApi.reqSingleInfoList({
        GreetUserID: _.get(this.clientInfo, 'accessStatus.UserID'),
        DeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0)
      })
      this.tableData = _.get(ret, 'data', [])
      this.loading = false
      this.inited = true
      console.log(ret)
    },
    outLine(row) {
      console.log(row)
      this.currentRow = row
      this.showOutLine = true
    },
    async confirmHandle() {
      this.submiting = true
      const params = {
        device_id: this.currentRow.DeviceID,
        msg: this.$t('guestAuth.guest.info_78', {
          userName: _.get(this.clientInfo, 'accessStatus.userName', ''),
          guestName: this.currentRow.Name
        }),
        isSendCutOffMsg: '1',
        currDeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0),
        remark: 'GuestDeviceCutOff'
      }
      const ret = await proxyApi.netCutoff(params)
      this.showOutLine = false
      this.submiting = false
      if (parseInt(ret.errcode) === 0) {
        this.$message({
          message: this.$t('guestAuth.guest.info_79'),
          type: 'success'
        })
        this.getList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.single-content{
  height: 100%;
}
.icon-xiaxian{
    color: $--color-primary;
    padding: 0 6px;
    cursor: pointer;
}
.guest-m-confirm-dialog{
    .v-header{
        line-height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 600;
        color: $title-color;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 6px;
            font-weight: 400;
        }
    }
    .outline-tips{
        padding: 34px 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
}
</style>
