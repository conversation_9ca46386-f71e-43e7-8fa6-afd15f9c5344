<template>
  <div id="f-reg" v-loading="initLoad || loading" :class="edit ? 'f-edit' : ''">
    <template>
      <div v-if="!edit" class="u-current-tag">
        <span>
          <i class="iconfont icon-zhuce" />
          {{ $t('reg.reg') }}
        </span>
      </div>
      <!--修改设备的情况下，展示设备的已注册信息情况-->
      <div class="g-reg-wrap" :style="{ height: edit ? '100%' : 'calc(100% - 28px)' }">
        <h1 v-if="!edit" class="title">
          <span>{{ $t('reg.pageTitle') }}</span>
        </h1>
        <div
          id="regFormWrapper"
          ref="regFormWrapper"
          :style="{paddingLeft: paddingLeft}"
          :class="['wrapper', edit ? 'edit-wrapper' : '']"
        >
          <el-form
            ref="regForm"
            :model="form"
            label-width="16px"
            class="reg-ruleForm"
          >
            <template v-for="item in formFields">
              <!--树形组件-->
              <el-form-item
                v-if="item.InputType === 'tree'"
                :key="item.Name"
                :rules="item.Rule"
                :prop="item.Name"
                label=" "
                class="select-form-box"
              >
                <i :class="item.Icon" />
                <TreeSelect
                  v-if="item.Name == 'depart_id'"
                  ref="departTree"
                  v-model="form[item.Name]"
                  :pop-id-name="`ui-accessNetwork-register-div-${item.Name}`"
                  :placeholder="item.Placeholder"
                  :options="item.Options"
                  :load="loadNode"
                  :lazy="lazyTree"
                  :search="loadSearch"
                  :default-value="defaultValue"
                  :disabled-value="disabledItems"
                  :popper-append-to-body="true"
                  :class="{'offset-option-tree' : !edit }"
                  :tree-name="item.Name"
                  @select-disabled="selectDisableItem"
                />
                <TreeSelect
                  v-if="item.Name == 'positions'"
                  ref="locationTree"
                  v-model="form[item.Name]"
                  :pop-id-name="`ui-accessNetwork-register-div-${item.Name}`"
                  :placeholder="item.Placeholder"
                  :options="item.Options"
                  :load="locationLoadNode"
                  :lazy="lazyTree"
                  :popper-append-to-body="true"
                  :search="locationLoadSearch"
                  :default-value="locationValue"
                  :class="{'offset-option-tree' : !edit }"
                  :tree-name="item.Name"
                />
              </el-form-item>

              <!--input输入框-->
              <el-form-item
                v-if="item.InputType === 'text'"
                :key="item.Name"
                :rules="item.Rule"
                :prop="item.Name"
                label=" "
              >
                <el-input
                  :id="`ui-accessNetwork-register-input-${item.Name}`"
                  v-model="form[item.Name]"
                  :placeholder="item.Placeholder"
                >
                  <i slot="prefix" :class="item.Icon" />
                </el-input>
              </el-form-item>

              <!--textare输入框-->
              <el-form-item
                v-if="item.InputType === 'textarea'"
                :key="item.Name"
                :rules="item.Rule"
                :prop="item.Name"
                label=" "
              >
                <el-input
                  :id="`ui-accessNetwork-register-input-${item.Name}`"
                  v-model="form[item.Name]"
                  class="mark-input"
                  type="textarea"
                  :placeholder="item.Placeholder"
                />
              </el-form-item>

              <!--select表单-->
              <el-form-item
                v-if="item.InputType === 'select'"

                :key="item.Name"
                :rules="item.Rule"
                :prop="item.Name"
                class="select-form-box"
                label=" "
              >
                <i :class="item.Icon" />
                <el-select
                  :id="`ui-accessNetwork-register-input-${item.Name}`"
                  v-model="form[item.Name]"
                  clearable
                  :placeholder="item.Placeholder"
                >
                  <el-option
                    v-for="(single, key) in item.Options"
                    :key="key"
                    :label="single.label"
                    :value="single.value"
                  />
                </el-select>
              </el-form-item>

              <!--checkbox表单-->
              <el-form-item
                v-if="item.InputType === 'checkbox'"

                :key="item.Name"
                class="check-item-form"
                label-width="80px"
                :rules="item.Rule"
                :prop="item.Name"
              >
                <el-tooltip
                  slot="label"
                  effect="dark"
                  :content="item.Title"
                  placement="left"
                >
                  <div slot="content" style="max-width: 180px">{{ item.Title }}</div>
                  <span>{{ item.Title }}</span>
                </el-tooltip>
                <el-checkbox-group :id="`ui-accessNetwork-register-input-${item.Name}`" v-model="form[item.Name]">
                  <el-checkbox
                    v-for="(single, key) in item.Options"
                    :key="key"
                    :label="single"
                  />
                </el-checkbox-group>
              </el-form-item>

              <!--radio表单-->
              <el-form-item
                v-if="item.InputType === 'radio'"
                :key="item.Name"
                :rules="item.Rule"
                :prop="item.Name"
                class="check-item-form"
                label-width="80px"
              >
                <el-tooltip
                  slot="label"
                  effect="dark"
                  :content="item.Title"
                  placement="left"
                > <div slot="content" style="max-width: 180px">{{ item.Title }}</div>
                  <span>{{ item.Title }}</span>
                </el-tooltip>
                <el-radio-group :id="`ui-accessNetwork-register-input-${item.Name}`" v-model="form[item.Name]" :rules="item.rule">
                  <el-radio
                    v-for="(label, key) in item.Options"
                    :key="key"
                    :label="label"
                  >
                    {{ label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-form>
        </div>
        <!--提交注册表单-->
        <div v-if="!edit" v-show="!initLoad" class="u-submit-btn-wrapper">
          <el-button
            id="ui-accessNetwork-register-button-registe_submit"
            :disabled="!authorizationAble"
            type="primary"
            @click="submitForm()"
          >
            {{ $t('reg.submit') }}
          </el-button>
        </div>
        <div v-else v-show="!initLoad" class="u-edit-btn-wrapper">
          <button id="ui-accessNetwork-register-button-submit_change" class="public-medium-btn" @click="submitForm()">
            {{ $t('changeRegInfo.submit') }}
          </button>
        </div>
      </div>
    </template>
    <!--修改成功的吐司-->
    <News-hint
      :visible.sync="toastVisible"
      info-type="success"
      :message="message"
    />
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
import regular from '@/render/utils/regular'
import regUtil from '@/render/utils/bussiness/regUtil'
import arrayExtend from '@/render/utils/array/arrayExtend'
import processController from '@/render/utils/processController'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import { EventBus } from '@/render/eventBus'

export default {
  props: {
    edit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      initLoad: false,
      toastVisible: false,
      message: this.$t('changepassword.successTip'),
      form: {},
      formFields: [],
      apiRet: [],
      loading: false,
      authorizationAble: true,
      regSuccess: false,
      lazyTree: false,
      defaultValue: '',
      locationValue: '',
      disabledItems: ['0'],
      paddingLeft: 0
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo', 'serveEntiretyConfig']),
    // 是否重新注册用户
    reRegDev() {
      return parseInt(_.get(this.clientInfo, 'detail.Registered')) === -1
    },
    // 自动注册
    autoReg() {
      return (
        !this.edit && parseInt(_.get(this.serveEntiretyConfig, 'scene.IsRegistered', 1)) === 0
      )
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.$refs.regForm.resetFields()
      this.init()
    }
  },
  mounted() {
    this.init()
    this.addResize()
  },
  beforeDestroy() {
    this.removeResize()
  },

  methods: {
    ...mapMutations(['setClientInfo']),
    init() {
      this.initLoad = true
      // 是否开启部门树懒加载
      const lazyTree =
        _.get(this.serveEntiretyConfig, 'server.ShowAllDevice.lazyLoading') ===
        '1'
      this.lazyTree = lazyTree
      // promise.all的顺序不能变
      Promise.all([
        this.getRegInfo(),
        this.getDepartInfo(),
        this.getLocationInfo(),
        this.getDepartDefaultValue(),
        this.getAuth() // 首先判断授权
      ])
        .then(ret => {
          this.initLoad = false
          this.apiRet = ret
          this.setAuthHandle(ret[4])
          this.createFormItemHandle()
          this.$nextTick(() => {
            this.fixPaddingLeft()
          })
        })
        .catch(err => {
          this.initLoad = false
          console.log(err)
        })
        .finally(() => {
          this.reRegWarning()
        })
    },
    // 生成提供渲染的动态表单数据项
    createFormItemHandle() {
      const data = this.apiRet
      this.createFormModel(data[0])
      this.createFormField(data[0], data[1], data[2])
      if (!this.edit && !this.reRegDev) {
        // 新注册填充信息
        this.setFormDefaultVlue()
      }
      // 设备类型默认设置为101个人电脑
      if (this.form.device_type === '') {
        this.form.device_type = '101'
      }
      // 如果是自动注册用户，自动提交
      if (this.autoReg && this.authorizationAble) {
        this.postForm()
      } else {
        // 手动注册，需要展示
        EventBus.$emit('client:show')
      }
    },
    // 生成表单双向绑定相关的数组:对应v-mode="form['column']"
    createFormModel(data) {
      try {
        const regConf = _.get(data, 'regConfig')[
          this.$i18n.locale === 'zh' ? 'zh' : 'en'
        ]
        if (!_.isArray(regConf) || _.isEmpty(regConf)) {
          this.noFieldReg()
        }
        const needDefaultValue = this.edit || this.reRegDev
        regConf.forEach(item => {
          if (_.get(item, 'InputType') === 'checkbox') {
            let value = needDefaultValue
              ? this.handleIconOrKey(item, 'editValue')
              : ''
            value = value ? value.split(',') : []
            this.$set(this.form, this.handleIconOrKey(item, 'key'), value)
          } else {
            const value = needDefaultValue
              ? this.handleIconOrKey(item, 'editValue')
              : ''
            this.$set(this.form, this.handleIconOrKey(item, 'key'), value)
            if (item.Column === 'RequireDep' && this.lazyTree) {
              this.defaultValue = {
                id: value,
                label:
                  _.get(data, 'deviceInfo.AllDepartName') ||
                  _.get(data, 'deviceInfo.DepartName')
              }
            }
          }
        })
      } catch (error) {
        console.error(error)
        throw new Error('生成表单双向绑定相关的数组失败', error)
      }
    },
    // 生成动态表单的字段项
    // 每个表单项由这几个元素组成{Rule:'校验规则',Title:'标题',Remark:'描述',Column:'后台唯一表单标识',InputType:'表单类型'}
    createFormField(data, departTree, locationsTree) {
      try {
        const regConf = _.get(data, 'regConfig')[
          this.$i18n.locale === 'zh' ? 'zh' : 'en'
        ]
        if (!_.isArray(regConf) || _.isEmpty(regConf)) {
          return true
        }
        this.formFields = regConf.map(item => {
          item['Name'] = this.handleIconOrKey(item, 'key')
          item['Placeholder'] = this.handlePlaceHolder(item)
          item['Rule'] = this.handleRule(item)
          item['Icon'] = this.handleIconOrKey(item)
          item['Options'] = this.handleOptions(item)
          item['editValue'] = this.handleIconOrKey(item, 'editValue')
          return item
        })
      } catch (error) {
        throw new Error('生成动态表单项失败', error)
      }
    },
    // 根据title和remark生成表单的placeHolder
    handlePlaceHolder(item) {
      if (!_.isEmpty(_.get(item, 'Remark'))) {
        return item.Remark
      }
      if (
        _.get(item, 'InputType') === 'tree' ||
        _.get(item, 'InputType') === 'select'
      ) {
        return this.$t('reg.commonSelectPlacholder') + _.get(item, 'Title')
      }
      return this.$t('reg.commonTextPlacholder') + _.get(item, 'Title')
    },
    // 为部门，位置等树形组件，或者select,radios和checkbox生成选择项，用于element的表单组件渲染
    handleOptions(item) {
      // 生成设备列表的options
      if (_.get(item, 'Column') === 'RequireDevType') {
        const deviceType = _.get(this.apiRet[0], 'deviceType')
        if (deviceType) {
          const turnInfo = this.$i18n.locale === 'zh' ? {
            TypeName: 'label',
            TypeID: 'value'
          } : {
            AliasName: 'label',
            TypeID: 'value'
          }
          return arrayExtend.replaceArrObjKey(deviceType, turnInfo)
        } else {
          return []
        }
      }
      // 生成部门树的options
      if (_.get(item, 'Column') === 'RequireDep') {
        return _.get(this.apiRet, '[1].tree') || []
      }
      // 生成位置树的opitons
      if (_.get(item, 'Column') === 'RequirePos') {
        return _.get(this.apiRet, '[2].tree') || []
      }

      // 如果是扩展字段的select，还需要进行转换以兼容element的select组件
      if (_.get(item, 'InputType') === 'select') {
        return arrayExtend.transformArrToArrObj(_.get(item, 'InputValue'), [
          'label',
          'value'
        ])
      }

      if (
        _.get(item, 'InputType') === 'radio' ||
        _.get(item, 'InputType') === 'checkbox'
      ) {
        return _.get(item, 'InputValue')
      }
    },
    // 生成验证的表单项
    handleRule(item) {
      const itemCopy = _.clone(item)
      const rules = []
      // 是否必填字段
      if (parseInt(_.get(itemCopy, 'Select')) === 1) {
        rules.push({
          required: true,
          message: `[${_.get(item, 'Title')}] ${this.$t('auth.emptyErr')}`,
          trigger: 'change'
        })
      }
      // 添加其他校验规则:Default,Email，Phone
      if (
        !_.isEmpty(_.get(itemCopy, 'Rule')) &&
        parseInt(_.get(itemCopy, 'Rule')) !== 0
      ) {
        const regex = _.get(itemCopy, 'Rule')
        const validateFun = (rule, value, callback) => {
          if (value) {
            const reg = regular.rules[regex]
            if (reg && !reg.test(value.trim())) {
              if (!value.trim()) {
                callback(
                  new Error(
                    `[${_.get(item, 'Title')}] ${this.$t(
                      'changeRegInfo.styleErro'
                    )}${regular.errorTipMap()['onlySpace']}`
                  )
                )
                return
              }
              callback(
                new Error(
                  `[${_.get(item, 'Title')}] ${this.$t(
                    'changeRegInfo.styleErro'
                  )}${regular.errorTipMap()[regex]}`
                )
              )
            }
          }
          callback()
        }
        rules.push({ validator: validateFun, trigger: 'blur' })
      }
      return rules
    },
    // 获取注册信息相关的表单项
    async getRegInfo() {
      if (this.apiRet[0] && !_.isEmpty(this.apiRet[0])) {
        return this.apiRet[0]
      }
      const apiParams = {
        DeviceID: _.get(this.clientInfo, 'detail.DeviceID'),
        IsGuest: 0
      }
      const ret = await proxyApi.getRegInfo(apiParams)
      if (
        parseInt(_.get(ret, 'errcode')) !== 0 &&
        !_.get(ret, 'data.regConfig')
      ) {
        this.$msg({ message: '获取表单接口失败', type: 'error' })
        throw new Error('获取表单接口失败')
      }
      return _.get(ret, 'data')
    },
    // 获取默认部门
    async getDepartDefaultValue() {
      if (this.apiRet[3]) {
        return this.apiRet[3]
      }
      return await regUtil.getDefaultDepart()
    },
    // 获取部门树信息
    async getDepartInfo() {
      if (this.apiRet[1] && !_.isEmpty(this.apiRet[1])) {
        return this.apiRet[1]
      }
      if (
        !this.isDisplayItem('RequireDep', 'RequireDep_en') ||
        this.lazyTree ||
        this.autoReg
      ) {
        // 隐藏项或懒加载直接返回空对象
        return {}
      }
      // 这里的console千万不能去掉，去掉后不知道为啥就变成阻塞了，估计是浏览器的BUG
      return await regUtil.getDepartTree()
    },
    // 部门树异步加载
    async loadNode(node) {
      if (this.autoReg) {
        return
      }
      const res = await regUtil.getDepartTree(
        { parentDepartId: node.id || '0' },
        node.level === 0
      )
      if (node.level === 0) {
        node.resolve([
          {
            fullLabel: res.CompanyName,
            id: '0',
            label: res.CompanyName,
            pid: '-1',
            AllDepartID: '-1/0'
          }
        ])
        this.$nextTick(() => {
          if (this.$refs['departTree'][0]) {
            this.$refs['departTree'][0].clickHandle('tr-0-1')
          }
        })
      } else {
        node.resolve(res.arr)
      }
    },
    // 部门树异步搜索
    async loadSearch(node) {
      const res = await regUtil.getDepartTree(node.params)
      node.resolve(res.arr)
    },
    selectDisableItem(id) {
      this.$msg({ message: this.$t('reg.seTopDepartTip'), type: 'warning' })
    },
    // 设备所在位置异步加载
    async locationLoadNode(node) {
      if (this.autoReg) {
        return
      }
      const res = await regUtil.getLocationTree(
        { parentLocationId: node.id || '0' },
        node.level === 0
      )
      if (node.level === 0) {
        if (res.arr.length === 0) {
          node.resolve([])
          return
        }
        node.resolve([
          {
            fullLabel: res.CompanyName,
            id: '0',
            label: res.CompanyName,
            pid: '-1',
            AllDepartID: '-1/0'
          }
        ])
        this.$nextTick(() => {
          if (this.$refs['locationTree'][0]) {
            this.$refs['locationTree'][0].clickHandle('tr-0-1')
          }
        })
      } else {
        node.resolve(res.arr)
      }
    },
    // 设备所在位置异步搜索
    async locationLoadSearch(node) {
      const res = await regUtil.getLocationTree({ keyword: node.keyword })
      node.resolve(res.arr)
    },
    // 获取位置树信息
    async getLocationInfo() {
      if (this.apiRet[2] && !_.isEmpty(this.apiRet[2])) {
        return this.apiRet[2]
      }
      if (!this.isDisplayItem('RequirePos', 'RequirePos_en') || this.autoReg) {
        // 隐藏项直接返回空对象
        return {}
      }
      return await regUtil.getLocationTree()
    },
    // 计算icon或者表单的键(和请求接口统一)@吐槽下因为接口字段名称不统一，不得不进行这种转换
    handleIconOrKey(item, type = 'icon') {
      const column = _.get(item, 'Column')
      if (column === 'RequireDep') {
        if (type === 'editValue') {
          return this.getDefaultVal('DepartID')
        } else {
          return type === 'icon' ? 'iconfont icon-bumen' : 'depart_id'
        }
      }
      if (column === 'RequireDevType') {
        if (type === 'editValue') {
          return this.getDefaultVal('Type')
        } else {
          return type === 'icon' ? 'iconfont icon-shebei' : 'device_type'
        }
      }
      if (column === 'RequireMail') {
        if (type === 'editValue') {
          return this.getDefaultVal('EMail')
        } else {
          return type === 'icon' ? 'iconfont icon-youxiang' : 'email'
        }
      }
      if (column === 'RequireUser') {
        if (type === 'editValue') {
          return this.getDefaultVal('UserName')
        } else {
          return type === 'icon' ? 'iconfont icon-zhanghu' : 'username'
        }
      }
      if (column === 'RequirePos') {
        if (type === 'editValue') {
          return this.getDefaultVal('LocationId')
        } else {
          return type === 'icon' ? 'iconfont icon-dizhi' : 'positions'
        }
      }
      if (column === 'RequireTel') {
        if (type === 'editValue') {
          return this.getDefaultVal('Tel')
        } else {
          return type === 'icon' ? 'iconfont icon-shoujihao' : 'tel'
        }
      }
      if (column === 'RequireRem') {
        if (type === 'editValue') {
          return this.getDefaultVal('Remark')
        } else {
          return type === 'icon' ? '' : 'remark'
        }
      }
      // 扩展项
      if (type === 'editValue') {
        return this.getDefaultVal(column.replace('RequireExpand_', 'Expand_'))
      } else {
        return type === 'icon'
          ? 'iconfont icon-tongyong'
          : column.replace('RequireExpand_', 'requireexpand')
      }
    },
    // 获取到默认的设置值
    getDefaultVal(column = '') {
      // 扩展字段的修改
      if (column.indexOf('Expand_') !== -1) {
        if (_.get(this.apiRet, '[0].deviceExpand')) {
          const expandColumn = _.get(this.apiRet, '[0].deviceExpand')
          return _.get(expandColumn, column) || ''
        }
      }
      // 其他字段的默认值的返回
      if (_.get(this.apiRet, '[0].deviceInfo')) {
        const regInfo = _.get(this.apiRet, '[0].deviceInfo')
        return _.get(regInfo, column) || ''
      }
    },
    // 系统授权判断
    async getAuth() {
      if (this.apiRet[4] && !_.isEmpty(this.apiRet[4])) {
        return this.apiRet[4]
      }
      return await proxyApi.getAuthorization({ cache: new Date().getTime() })
    },
    // 设置系统授权参数
    setAuthHandle(res) {
      if (parseInt(res.errcode) === 0) {
        if (!res.data.val) {
          this.$msg({
            message: this.$t('reg.authorization'),
            type: 'error',
            duration: 4000
          })
          this.authorizationAble = false
        }
      }
    },
    // 重注册用户，进行提示
    reRegWarning() {
      if (this.edit || !this.reRegDev) {
        return false
      }
      // 这里去除掉难看的 |:::|，以后直接从数据中去除
      let msg = this.$t('oWelcome.ReRegistered')
      if (!_.isEmpty(_.get(this.clientInfo, 'detail.ReRegReason', ''))) {
        msg +=
          '[' +
          _.get(this.clientInfo, 'detail.ReRegReason', '').replace(
            /\|:::\|/g,
            ''
          ) +
          ']'
      } else {
        msg += '--'
      }
      this.$msg({
        message: msg,
        type: 'warning',
        duration: 5000,
        showIcon: false,
        showClose: true
      })
    },
    isDisplayItem(zhKey, enKey) {
      const currentLang = this.$i18n.locale
      let path = 'server.CLIENTCHECK.' + zhKey
      if (currentLang === 'en') {
        path = 'server.CLIENTCHECK.' + enKey
      }
      const conf = _.get(this.serveEntiretyConfig, path, '')
      const arr = conf.split('|')
      if (arr[0] === '2') {
        // 是否是隐藏项
        return false
      }
      // 员工页面是否显示
      if (arr[4] && !arr[4].split(',')[0]) {
        return false
      }
      return true
    },
    // 新发现设备注册
    async setFormDefaultVlue() {
      // 默认部门id，自动填充的部门id
      var initData = {
        depart_id: _.get(this.apiRet, '[3].id', '') + '',
        username:
          parseInt(
            _.get(this.serveEntiretyConfig, 'server.UserDevBind.IsUpDevName')
          ) === 1
            ? parseInt(
              _.get(
                this.serveEntiretyConfig,
                'server.UserDevBind.IsUpdateUserToDev'
              )
            ) === 1
              ? _.get(this.authInfo, 'basic.UserName', '')
              : _.get(this.authInfo, 'basic.TrueNames', '')
            : '',
        tel:
          _.get(this.clientInfo, 'detail.Tel') ||
          _.get(this.authInfo, 'basic.Tel') ||
          '',
        remark: _.get(this.clientInfo, 'detail.Remark') || '',
        email:
          _.get(this.clientInfo, 'detail.EMail') ||
          _.get(this.authInfo, 'basic.EMail') ||
          ''
      }

      if (this.lazyTree && this.isDisplayItem('RequireDep', 'RequireDep_en')) {
        this.defaultValue = _.get(this.apiRet, '[3]', '')
      }
      // 拓展项填充默认值
      this.formFields.forEach(item => {
        const inputType = item.InputType
        if (
          item.Name.indexOf('requireexpand') > -1 &&
          (inputType === 'textarea' || inputType === 'text')
        ) {
          initData[item.Name] = item.InputValue
        }
      })
      this.form = Object.assign({}, this.form, initData)
    },
    // 没有勾选任何注册项的情况下进行提交
    noFieldReg() {
      if (
        parseInt(_.get(this.authInfo, 'basic.IsNeedReg', 1)) !== 0 &&
        !this.edit &&
        this.authorizationAble
      ) {
        // 不是自动注册且不是编辑页面
        this.postForm()
      }
    },
    async submitForm() {
      this.$refs['regForm'].validate(async(valid, obj) => {
        if (valid) {
          await this.postForm()
        } else {
          // 滚动到校验不通过项位置
          this.scrollToErr(obj)
        }
      })
    },
    async postForm() {
      if (this.loading) {
        return
      }
      const apiParam = {}
      for (const key in this.form) {
        if (this.form[key] && typeof this.form[key] === 'string') {
          apiParam[key] = this.form[key].trim()
        } else {
          apiParam[key] = this.form[key]
        }
      }
      apiParam['roleid'] = _.get(this.authInfo, 'basic.RoleID')
      apiParam['device_id'] = _.get(this.clientInfo, 'detail.DeviceID')
      apiParam['from'] = 'IsAgent'
      apiParam['sceneId'] = _.get(this.serveEntiretyConfig, 'scene.SceneID', '')
      this.loading = true // 禁用按钮防止重复点击
      const ret = await proxyApi.regSubmit(apiParam)
      if (
        parseInt(_.get(ret, 'errcode')) === 0 &&
        _.get(ret, 'data.Registered')
      ) {
        this.regSuccess = true
        await this.regNext(_.get(ret, 'data'))
      } else {
        this.$msg({ message: this.$t('reg.checkFail'), type: 'error' })
        EventBus.$emit('client:show')
      }
      this.loading = false
    },
    scrollToErr(errObj) {
      // 获取第一个错误项
      const keys = Object.keys(errObj)
      let idx = this.formFields.length - 1
      keys.forEach(item => {
        const index = this.formFields.findIndex(o => o.Name === item)
        if (index !== -1 && index < idx) {
          idx = index
        }
      })
      try {
        const parent = document.getElementsByClassName('reg-ruleForm')[0]
        const current = parent.getElementsByClassName('el-form-item')[idx]
        const scrollWrapper = document.getElementById('regFormWrapper')
        const top = current.offsetTop - scrollWrapper.offsetTop
        if (top >= 0) {
          scrollWrapper.scrollTop = top
        }
      } catch (e) {
        console.log(e)
      }
    },

    // 注册或者修改注册信息的下一步
    async regNext(ret) {
      // 更新设备详情信息，然后通过流程控制器跳转
      if (this.edit) {
        this.toastVisible = true
        // 这里修改注册信息完后重新去拿一次设备信息。因为可能会变成待审核，那么欢迎页要做出改变
        commonUtil.detail()
        // 关闭drawer窗口
        this.$emit('changeVisible', false)
      } else {
        const clientInfo = _.set(
          this.clientInfo,
          'detail.Registered',
          _.get(ret, 'Registered')
        )
        this.setClientInfo({ ...clientInfo })
        await processController.next()
      }
    },
    addResize() {
      if (!this.edit) {
        window.addEventListener('resize', this.fixPaddingLeft)
      }
    },
    removeResize() {
      if (!this.edit) {
        window.removeEventListener('resize', this.fixPaddingLeft)
      }
    },
    // 计算paddingLeft让form表单不管是否有滚动条都居中
    fixPaddingLeft: _.debounce(function() {
      try {
        const element = this.$refs.regFormWrapper
        if (this.hasScrollbar(element)) {
          this.paddingLeft = this.getScrollbarWidth(element) + 'px'
        } else {
          this.paddingLeft = 0
        }
      } catch (error) {
        console.log('计算滚动条出错了')
      }
      console.log('触发', this.paddingLeft)
    }, 300),
    // 是否有滚动条
    hasScrollbar(element) {
      return (element.scrollHeight > element.clientHeight || element.offsetHeight > element.clientHeight)
    },
    // 滚动条宽度
    getScrollbarWidth(element) {
      return element.offsetWidth - element.clientWidth
    }
  },
  beforeRouteLeave(to, from, next) {
    // 如果是注销的，直接跳转，不做重定向前的提示
    if (_.get(to, 'params.forceTo', false) || to.path.indexOf('access/audit') > -1) {
      next()
      return true
    }

    if (!this.regSuccess) {
      this.$dialogTip({
        content: this.$t('reg.leaveHalfway'),
        popName: 'ui-accessNetwork-regist',
        success: () => {
          next()
        },
        cancel: () => {
          next(false)
        }
      })
    } else {
      next()
    }
  }
}
</script>
<style lang="scss">
#f-reg {
  padding: 24px;
  padding-right: 0;
  height: 100%;
  width: 100%;
  // overflow: hidden;
  .u-loading {
    height: 100%;
    width: 360px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
  .u-edit-info {
    font-size: 13px;
    color: #686e84;
    margin-bottom: 32px;
    .u-item {
      margin-bottom: 16px;
      width: 100%;
    }
    .u-title {
      float: left;
      width: 70px;
      text-align: right;
      overflow: hidden;
      -o-text-overflow: ellipsis;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: right;
      vertical-align: middle;
    }
    .u-desc {
      margin-left: 80px;
    }
  }
  .g-reg-wrap {
    height: 100%;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .title {
      text-align: center;
      margin-bottom: 32px;
      span {
        font-size: 18px;
        font-weight: 500;
        color: $--color-primary;
        line-height: 25px;
        padding-right: 28px;
      }
    }
    .wrapper {
      width: 100%;
      max-height: calc(100% - 163px);
      overflow-y: auto;
      margin: 0 auto;
      .rule-form {
        width: 360px;
      }
    }
    .edit-wrapper {
      max-height: calc(100% - 56px);
    }
    .u-submit-btn-wrapper {
      width: 376px;
      margin: 32px auto;
      button {
        width: 360px;
        margin-left: 16px;
      }
    }
    .u-edit-btn-wrapper {
      padding-top: 24px;
      padding-left: 16px;
      width: 100%;
    }
  }
  .reg-ruleForm {
    width: 376px;
    min-height: 40px;
    margin: 0 auto;
    .el-form-item__label {
      overflow: hidden;
      -o-text-overflow: ellipsis;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 40px;
      color: #b3b6c1;
      position: relative;
      height: 40px;
      padding-right: 8px;
      &::before {
        position: absolute;
        left: 0;
      }
    }
    .el-radio-group,
    .el-checkbox-group {
      line-height: 22px;
      .el-form-item__label {
        line-height: 22px;
      }
    }
    .check-item-form {
      .el-form-item__label {
        line-height: 22px;
        height: 22px;
        padding-left: 16px;
        text-align: left;
      }
      .el-radio {
        line-height: 22px;
      }
      .el-form-item__content {
        line-height: 22px;
        .el-checkbox-group,
        .el-radio-group {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
  }
  .offset-option-tree{
    .yg-tree-select{
      transform: translateX(-5px);
    }
  }
}
.f-edit {
  position: absolute;
  top: 54px;
  left: 0;
  bottom: 0;
  height: initial !important;
  .g-reg-wrap {
    justify-content: flex-start !important;
  }
}
</style>
