<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-15 10:15:08
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-12-09 15:10:32
 * @Description: 来宾自助申请
-->
<template>
  <div id="f-guest-apply-self" @keydown="keyDown" />
</template>
<script>

import { mapState } from 'vuex'
import proxyApi from '@/service/api/proxyApi'

export default {
  data() {
    return {
      form: {},
      formFields: []
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig'])
  },
  methods: {
    // 提交来宾认证
    async submit() {
      const apiParam = {
        type: 'Guest',
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        net_code: '',
        guestType: 'self'
      }
      // 提交认证
      const ret = await proxyApi.authIndex(apiParam)
      return ret
    },

    /**
     * 回车提交
     */
    keyDown(event) {
      if (parseInt(event.keyCode) !== 13) {
        return
      }
      this.$emit('enterSubmit')
    }
  }
}
</script>

<style lang="scss">
#f-guest-apply-self {
  height: 8px;
}

</style>
