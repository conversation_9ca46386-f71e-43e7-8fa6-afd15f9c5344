<template>
  <div id="f-update">
    <layout-state
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-disabled="state['stateBtnDisabled']"
      :state-btn-fun="state['stateBtnFun']"
      state-id="ui-accessNetwork-update-div-check_msg"
      state-btn-id="ui-accessNetwork-update-button-restart"
    />
    <ConfirmDialog id="guest-confirm-dialog" :show.sync="showConfirmDialog" :show-cancel-button="false" width="285px" @ok="errorHandle">
      <p class="guest-error-confirm-tips">{{ deviceType === 'mac' ? $t('macUpdateTip'): $t('updateTip') }}</p>  </ConfirmDialog>
  </div>
</template>

<script>
import state from '@/render/components/layout/state'
import { mapState } from 'vuex'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
const update = require('@/render/assets/stateIllustration/update.png')
export default {
  components: {
    'layoutState': state
  },
  data() {
    return {
      showConfirmDialog: false,
      deviceType: _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    }
  },
  computed: {
    ...mapState(['clientInfo']),
    state() {
      return {
        stateImg: update,
        stateMsg: this.deviceType === 'mac' ? this.$t('macUpdateTip') : this.$t('updateTip'),
        stateBtnDisabled: false
      }
    }
  },
  methods: {
    errorHandle() {
    }
  },
  beforeRouteLeave(to, from, next) {
    this.showConfirmDialog = true
    next(false)
  }
}
</script>
<style>
#f-update {
  position: relative;
  height: 100%;
  .u-state-msg{
    margin-top: 32px;
    line-height: 25px;
    .u-state-msg-html{
      font-size: 18px;
    }
  }
}
.guest-error-confirm-tips{
  padding: 24px;
}
</style>
