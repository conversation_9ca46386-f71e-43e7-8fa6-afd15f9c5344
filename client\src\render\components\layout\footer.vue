<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-11 14:47:32
 * @LastEditors: gening <EMAIL>
 * @LastEditTime: 2024-06-05 17:21:18
 * @Description: Layout中的公共底部组件
-->
<template>
  <div class="layout-foot">
    <div class="local-info">
      <div class="left">
        <span v-if="isShowServerAddress">{{ $t('serverIp') }}：{{ clientInfo.basic.ServerIP }}</span>
      </div>
      <div class="right">
        <span
          v-if="isZtpUser && !isElectronSys()"
          id="ui-ztp-footer-span-troubleshooting"
          class="link"
          @click="faultDiagnosisFn"
        >
          <img :src="sCheckImg" alt="">{{ $t('sourceCheck') }}
        </span>
        <span v-if="showConnect" id="ui-ztp-footer-span-conn_status" class="link link-status" @click="connectionStatusFn">
          <img :src="linkImg" alt="">{{ $t('connectionStatus') }}
          <i v-if="isProxy && gateWayState !== 2" :class="['point', gateWayState === 1 ? 'normal-point' : '']" />
          <img v-if="isProxy && gateWayState === 2" class="link-point" :src="pointLink" alt="">
        </span>
      </div>

    </div>
    <!-- vpn -->
    <ConfirmDialog
      id="gateway-dialog"
      :cancel-text="$t('header.close')"
      :show.sync="showIpDialog"
      :show-confirm-button="false"
      width="285px"
    >
      <p class="gateway-dialog-title">{{ $t('connectionStatus') }}</p>
      <div class="gateway-dialog-body">
        <ul v-if="netCardList.length" class="netcard-list">
          <li v-for="(item, ind) in netCardList" :key="ind">
            {{ item.Name }}
            <i
              :title="item.Status === 'true' ? $t('header.linkSuccess') : $t('header.linkFail')"
              :class="['iconfont', item.Status === 'true' ? 'icon-lianjie' : 'icon-duankailianjie']"
            />
          </li>
        </ul>
        <p v-else class="info">
          <span v-if="GatewayIsEmpty === 'true'">{{ $t('header.footInfo') }}</span>
          <span v-else>{{ $t('header.footInfo2') }}</span>
        </p>
      </div>
    </ConfirmDialog>

    <!-- 侧边抽屉 -->
    <el-drawer
      :title="$t('connectionStatus')"
      :visible.sync="showDrawer"
      :append-to-body="true"
      :wrapper-closable="true"
      :destroy-on-close="true"
      size="450px"
      @close="closeDrawer"
    >
      <div v-loading="!inited" :class="['footer-drawer-content', 'empty-content']">
        <el-collapse v-if="gateWayList.length && inited" v-model="activeNames">
          <el-collapse-item
            v-for="item in gateWayList"
            :id="`ui-ztp-gateway-div-gw_${item.ID}`"
            :key="item.ID"
            :name="item.ID"
          >
            <template slot="title">
              {{ item.Name }}
              <i
                v-if="parseInt(item.usable) !== 2"
                :id="`ui-ztp-gateway-i-gw_${item.ID}_conn_status`"
                :class="['iconfont', parseInt(item.usable) === 0 ? 'icon-duankailianjie' : 'icon-lianjie', 'link-status']"
              />
              <img v-else class="linking-img" :src="linkingImg" alt="">
            </template>
            <ul class="item-info">
              <li v-if="parseInt(item.usable) === 0" :id="`ui-ztp-gateway-li-gw_${item.ID}_icon`" class="error-info">
                <i class="iconfont icon-yichang" />
                {{ item.errMsg }}
              </li>
              <li class="link-speed">
                <span class="color-d">{{ $t('footer.netSpeed') }}：</span>
                <div class="right">
                  <span class="color-t"><i class="iconfont icon-shanghang" />{{ parseInt(item.usable) === 1 ?
                    formateSize(item.upspeed).size : '--' }}<span v-if="parseInt(item.usable) === 1" class="color-d">{{
                    formateSize(item.upspeed).unit }}/s</span></span>
                  <span class="color-t"><i class="iconfont icon-xiahang" />{{ parseInt(item.usable) === 1 ?
                    formateSize(item.downspeed).size : '--' }}<span v-if="parseInt(item.usable) === 1" class="color-d">{{
                    formateSize(item.downspeed).unit }}/s</span></span>
                </div>
              </li>
              <li class="net-status">
                <span class="color-d">{{ $t('footer.netStatus') }}：</span>
                <span v-if="parseInt(item.usable) === 1" class="color-t">{{ $t('footer.delayed') }} {{
                  formateLatency(item.latency) }}ms</span>
                <span v-else class="color-t">--</span>
              </li>
              <li class="flow-line">
                <span class="color-d">{{ $t('footer.flowCount') }}：</span>
                <div class="right">
                  <span class="color-t"><i class="iconfont icon-shanghang" />{{ parseInt(item.usable) === 1 ?
                    formateSize(item.uptraffic).size : '--' }}<span v-if="parseInt(item.usable) === 1" class="color-d">{{
                    formateSize(item.uptraffic).unit }}</span></span>
                  <span class="color-t"><i class="iconfont icon-xiahang" />{{ parseInt(item.usable) === 1 ?
                    formateSize(item.downtraffic).size : '--' }}<span
                    v-if="parseInt(item.usable) === 1"
                    class="color-d"
                  >{{ formateSize(item.downtraffic).unit }}</span></span>
                </div>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
        <empty v-if="inited && !gateWayList.length" :msg="$t('reg.noData')" />
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import agentApi from '@/service/api/agentApi'
import empty from '@/render/views/sourcePlatform/list/components/empty.vue'
import authUtils from '@/render/utils/auth/index'
import { isElectron } from '@/service/utils/ipcPlugin/utils/index'
const linkImg = require('@/render/assets/link.png')
const sCheckImg = require('@/render/assets/s-check.png')
const pointLink = require('@/render/assets/qrcSource/Image/point-link.gif')
const linkingImg = require('@/render/assets/qrcSource/Image/link.gif')
export default {
  components: {
    empty
  },
  data() {
    return {
      showDrawer: false,
      linkImg,
      sCheckImg,
      pointLink,
      linkingImg,
      showIpDialog: false,
      netCardList: [],
      GatewayIsEmpty: 'true',
      webGateWayList: [],
      inited: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'gateWayInfos']),
    ...mapGetters([
      'serveEntiretyConfig'
    ]),
    isZtpUser() {
      return authUtils.isOpenZeroTrust()
    },
    showConnect() {
      if (this.isProxy) {
        return this.isZtpUser && this.gateWayInfos.total > 0 && this.clientInfo.online && parseInt(this.gateWayInfos.VPNStatus) === 1
      } else {
        return this.isZtpUser
      }
    },
    isProxy() {
      return _.get(this.serveEntiretyConfig, 'server.ZTP.VpnType') === 'proxy'
    },
    gateWayState() {
      return parseInt(_.get(this.gateWayInfos, 'state', 2))
    },
    gateWayList() {
      const newList = []
      const socketGwMap = _.get(this.gateWayInfos, 'gateWayMap', {})
      this.webGateWayList.forEach(item => {
        const socket = _.isEmpty(socketGwMap) ? { usable: 2 } : socketGwMap[String(item.ID)] || { usable: 0 }
        let errMsg = parseInt(socket.errcode) === 1 || (parseInt(item.ErrCode) === 21148059 && socketGwMap[String(item.ID)]) ? this.$t('footer.ipFail') : this.$t('footer.error')
        if (parseInt(socket.errcode) === 3) {
          errMsg = this.$t('footer.gwFail')
        }
        if (parseInt(socket.errcode) === 4) {
          errMsg = this.$t('footer.authFail')
        }
        newList.push({ ...item, ...socket, ...{ errMsg }})
      })
      return newList
    },
    activeNames() {
      const ids = []
      this.webGateWayList.forEach(item => {
        ids.push(item.ID)
      })
      return ids
    },
    isShowServerAddress() {
      return _.get(this.serveEntiretyConfig, 'server.EnvSwitch.HideServerAddress') !== 'on'
    }
  },
  watch: {
    // 入网状态发生变化
    'clientInfo.accessStatus.deviceStatus': {
      handler(newVal, oldVal) {
        if (parseInt(oldVal) !== 1 && parseInt(newVal) === 1 && this.isZtpUser && this.isProxy) {
          console.log('触发', newVal, oldVal)
          commonUtil.getWebGateWay() // 更新网关状态
        }
      },
      deep: true
    }
  },
  methods: {
    // 故障诊断
    faultDiagnosisFn() {
      this.$ipcSend('AssUIPluginTools', 'WebCall_OpenAnalyzeTools')
    },
    // 连接状态
    async connectionStatusFn() {
      if (this.isProxy) {
        this.showDrawer = true
        agentApi.getGatewayInfos({ Start: 1 })
        this.webGateWayList = await commonUtil.getWebGateWay()
        this.inited = true
      } else {
        this.VpnConnectionStatus()
      }
    },
    // vnp连接状态
    async VpnConnectionStatus() {
      this.showIpDialog = true
      const netCardDetail = await commonUtil.getNetCardInfo()
      this.netCardList = netCardDetail.Gateway || []
      this.GatewayIsEmpty = netCardDetail.GatewayIsEmpty
    },
    closeDrawer() {
      agentApi.getGatewayInfos({ Start: '0' })
    },
    formateSize(bye) {
      if (bye === undefined || bye === '--') {
        return { size: '--', unit: 'KB' }
      }
      const kb = (bye / 1024).toFixed(1)
      if (kb < 1024) {
        return { size: kb, unit: 'KB' }
      }
      const mb = (bye / 1024 / 1024).toFixed(1)
      if (mb < 1024) {
        return { size: mb, unit: 'MB' }
      }
      return { size: (bye / 1024 / 1024 / 1024).toFixed(1), unit: 'GB' }
    },
    formateLatency(lc) {
      if (lc === undefined || lc === '--') {
        return '--'
      } else {
        return lc < 1 ? '<1' : lc
      }
    },
    isElectronSys() {
      return isElectron()
    }
  }
}
</script>
<style lang="scss" scoped>
.local-info {
  display: flex;
  justify-content: space-between;
  padding-left: 24px;
  padding-right: 8px;
  font-size: 12px;
  color: $title-color;
  position: fixed;
  width: calc(100% - 200px);
  bottom: 0;
  line-height: 33px;
  height: 33px;
  box-shadow: 0px 2px 10px 0px rgba(16, 36, 66, 0.20);

  .right {
    display: flex;
  }

  .link {
    padding: 0 16px;
    line-height: 33px;
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    img {
      width: 13px;
      height: 13px;
      margin-right: 6px;
    }

    .link-point {
      margin-right: 0;
      margin-left: 6px;
      width: 8px;
      height: 8px;
    }

    &:hover {
      background: $gray-3;
    }

    .point {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: $error-1;
      margin-left: 4px;
      box-shadow: 0px 2px 2px 0px rgba(16, 36, 66, 0.10);
    }

    .normal-point {
      background: $green-3;
    }
  }

  .link-status::before {
    content: '';
    display: block;
    position: absolute;
    width: 1px;
    height: 12px;
    background: $line-color;
    top: 10px;
    left: 0;
  }
}

.footer-drawer-content {
  padding: 14px 32px;

  .linking-img {
    width: 16px;
    margin-left: 16px;
  }

  .item-info {
    li {
      padding: 16px;
      padding-top: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        line-height: 20px;
      }

      .color-t {
        color: $title-color;
      }

      .color-d {
        color: $default-color;
      }

      .icon-shanghang {
        color: $green-3;
        font-size: 10px;
        margin-right: 6px;
      }

      .icon-xiahang {
        font-size: 10px;
        margin-right: 6px;
        color: $blue-3;
      }

      .right {
        display: flex;

        &>span {
          display: flex;
          margin-left: 16px;
          align-items: center;
        }
      }
    }

    .link-speed {
      margin-top: 16px;
    }

    .error-info {
      padding: 8px 16px;
      background: $yellow-4;
      border: 1px solid $yellow-3;
      border-radius: 4px;
      margin-top: 8px;
      justify-content: center;
      font-size: 13px;
      line-height: 18px;

      i {
        margin-right: 8px;
        font-size: 14px;
        color: $yellow-1;
      }
    }

    .flow-line {
      padding-bottom: 0;
    }
  }

  ::v-deep .link-status {
    font-size: 16px;
    margin-left: 16px;
    color: $green-3;
  }

  ::v-deep .icon-duankailianjie {
    color: $error-1;
  }

  ::v-deep .el-collapse-item__content {
    padding-bottom: 10px;
  }
}

.empty-content {
  height: calc(100% - 54px);
}

.footer-drawer-content ::v-deep .el-collapse {
  border: none;

  .el-collapse-item {
    margin-bottom: 6px;
  }

  .el-collapse-item__header {
    background: $gray-1;
    border: none;
    padding-left: 16px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }

  .el-collapse-item__wrap {
    border: none;
  }

  .el-collapse-item__arrow {
    margin: 0 16px 0 auto;
    color: $disabled-color;
  }
}

#gateway-dialog {
  .gateway-dialog-title {
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: #3c404d;
    border-bottom: 1px solid $line-color;
    height: 46px;
    line-height: 46px;
    padding: 0 24px;
  }

  .gateway-dialog-body {
    max-height: 150px;

    .info {
      padding: 24px;
      height: 100%;
      display: flex;
      align-items: center;
      font-size: 14px;
    }
  }

  .netcard-list {
    height: 100%;
    max-height: 150px;
    padding: 24px;
    padding-bottom: 0;
    overflow-y: auto;

    li {
      display: flex;
      align-items: center;
      line-height: 20px;
      font-size: 14px;
      color: $title-color;
      margin-bottom: 10px;
      justify-content: space-between;

      &:last-child {
        margin-bottom: 24px;
      }

      i {
        font-size: 16px;
      }

      .icon-lianjie {
        color: $success;
      }

      .icon-duankailianjie {
        color: $error-1;
      }
    }
  }

  .el-dialog__footer button {
    height: 40px;
    line-height: 40px;
    border-bottom-right-radius: 4px;
  }
}
</style>
