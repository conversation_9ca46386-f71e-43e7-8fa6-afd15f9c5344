<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :width="width"
      :show-close="false"
      :destroy-on-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      top="0"
      custom-class="yg-vertical-dialog"
      @closed="closeAfter"
    >
      <template v-slot:title>
        <div class="v-header">
          <i class="iconfont icon-putongxiangbuhegui" />
          <span>{{ title }}</span>
        </div>
      </template>
      <div class="g-s-diaolog-content">
        <div class="form-content">
          <p id="ui-check-index-p-handle_fix_tip" class="outline-tips" v-html="content" />
        </div>
      </div>

      <div class="d-dialog-foot-wrapper">
        <div v-if="showCancel" :id="`ui-${popName}-div-cancel`" class="cancel btn" @click="onCancel">{{ cancelText }}</div>
        <div :id="`ui-${popName}-div-contain`" :class="['btn', !showCancel?'full-btn': '']" @click="onConfirm">{{ confirmText }}</div>
      </div>
    </el-dialog>
  </div>

</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    success: {
      type: Function,
      default: () => {}
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    width: {
      type: String,
      default: '384px'
    },
    popName: {
      type: String,
      default: 'dialog-tip'
    },
    cancelText: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: ''
    },
    showCancel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  mounted() {
    this.dialogVisible = true
  },
  methods: {
    onConfirm() {
      this.dialogVisible = false
      _.isFunction(this.success) && this.success()
    },
    onCancel() {
      this.dialogVisible = false
      _.isFunction(this.cancel) && this.cancel()
    },
    closeAfter() {
      this.$el.parentNode.removeChild(this.$el)
    }
  }
}
</script>

<style lang="scss">
.yg-vertical-dialog {
  margin: 0;
  border-radius: 5px;
  .el-dialog__body {
    padding: 0;
    .v-header{
        height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        color: $title-color;
        display: flex;
        align-items: center;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 10px;
            margin-top: 2px;
            font-weight: 400;
        }
        span{
          font-weight: 500;
        }
    }
    .outline-tips{
        padding: 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
  }
  .d-dialog-foot-wrapper{
    display: flex;
    .btn{
      width: 50%;
      line-height: 38px;
      border-top: 1px solid $line-color;
      text-align: center;
      font-size: 14px;
      color: $--color-primary;
      cursor: pointer;
      border-bottom-right-radius: 5px;
      &:hover {
        background: $--color-primary;
        color: white;
        border-left-color: $--color-primary;
        border-top-color: $--color-primary;
      }
      .iconfont{
        line-height: 38px;
        margin-right: 5px;
      }
    }
    .full-btn{
      width: 100%;
      border-bottom-left-radius: 5px;
    }
    .cancel{
      color: $default-color;
      border-right: 1px solid $line-color;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 5px;
    }
  }
}
</style>
