
import store from '@/render/store'
import proxyApi from '@/service/api/proxyApi'
import common from './common'
import _ from 'lodash'
import proxyAjax from '@/service/utils/proxyAjax'
import { Base64Encode } from '@/render/utils/global'
import urlUtils from '@/render/utils/url'
import { Message } from 'element-ui'
import { i18n } from '@/render/lang'
import processController from '../processController'
import md5 from 'js-md5'

export async function auth(params = {}) {
  const apiParam = {
    type: 'Sso',
    deviceid: _.get(store.getters.clientInfo, 'detail.DeviceID', 0),
    label: params.label,
    uniqueId: params.uniqueId,
    user_name: Base64Encode(params.loginState)
  }
  if (!_.isEmpty(params.bindData)) {
    apiParam.bindAuthServer = _.get(params, 'bindData.bindAuthServer', '')
    apiParam.userid = _.get(params, 'bindData.userid')
    apiParam.authFrom = 'bind'
  }
  const res = await proxyApi.authIndex(apiParam)
  if (parseInt(res.errcode) !== 0) {
    processController.set('/access/message')
    return false
  }
  store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: res.data }))
  await common.authEnd({
    type: params.label,
    autoAuth: '0'
  })
  return true
}

export function getThirdPageUrl(label, uniqueId) {
  const deviceid = _.get(store.getters.clientInfo, 'detail.DeviceID', 0)
  return `${_.get(store.getters.serveEntiretyConfig, 'server.ControlUrl')}${proxyAjax.formatUrl('sso/auth', 'passport')}?label=${label}&deviceId=${deviceid}&local_lguage_set=${i18n.locale}&uniqueId=${uniqueId}&resId=&resurl=`
}

export async function reportLoginState(label, data) {
  if (_.isObject(data)) {
    data.forceBrowser = 0
    data.label = label
  }
  const ret = await proxyApi.ssoAuthCall(data)
  const errcode = parseInt(ret.errcode)
  return errcode === 0
}

export const checkLoginState = async(params = {}) => {
  const res = await proxyApi.ssoCheckLoginState({
    deviceId: _.get(store.getters.clientInfo, 'detail.DeviceID', 0),
    label: params.label,
    uniqueId: params.uniqueId
  })
  const errcode = parseInt(res.errcode)
  if (errcode === 0 && _.get(res, 'data.username', '') !== '') {
    return _.get(res, 'data.username')
  } else if (errcode === 21147009) {
    Message.error(res.errmsg)
    return
  }
  return false
}

export function createUniquId() {
  const str = String(new Date().getTime() + parseInt(Math.random() * 100000))
  return md5(str)
}

/**
   * 跳转退出登录
   */
export async function ssoLogout(label) {
  const userName = _.get(store.state, 'authInfo.basic.UserName', _.get(store.state, 'clientInfo.accessStatus.userName', ''))
  const lastAuthType = _.get(store.state, 'authInfo.basic.AuthType', _.get(store.state, 'clientInfo.accessStatus.lastAuthType', ''))
  if (_.isEmpty(userName) || _.isEmpty(lastAuthType)) {
    return false
  }

  const res = await proxyApi.ssoLogout({
    deviceId: _.get(store.state.clientInfo, 'basic.AgentID', 0),
    label,
    isHttps: urlUtils.isServerHttps() ? '1' : '0'
  })

  const errcode = parseInt(_.get(res, 'errcode', -1))
  if (errcode !== 0) {
    return false
  }

  const isAuth = parseInt(_.get(res, 'data.isAuth', 0))
  if (isAuth !== 1) {
    return false
  }

  const url = _.get(res, 'data.url', '')
  if (!_.isString(url) || url === '') {
    return false
  }

  return url
}

