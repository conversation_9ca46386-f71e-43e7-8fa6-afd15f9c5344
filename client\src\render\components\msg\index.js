import Message from './msg.vue'

const MESSAGE = {
  duration: 3000, // 显示的时间 ms
  install(Vue) {
    if (typeof window !== 'undefined' && window.Vue) {
      Vue = window.Vue
    }
    this.createRootElement()
    Vue.component('Message', Message)

    function msg(options) {
      const duration = options.duration || MESSAGE.duration
      const VueMessage = Vue.extend({
        render(h) {
          const props = {
            showIcon: options.showIcon,
            type: options.type || 'info',
            text: options.message,
            showClose: options.showClose
          }
          return h('message', { props })
        }
      })
      const newMessage = new VueMessage()

      let vm = newMessage.$mount()
      const el = vm.$el
      const root = document.getElementById('globalMsgBox')
      let showStatus = true
      root.appendChild(el) // 把生成的提示的dom插入body中

      const t1 = setTimeout(() => {
        clearTimeout(t1)
        root.removeChild(el) // 从body中移除dom
        newMessage.$destroy()
        showStatus = false
        vm = null // 设置为null，好让js垃圾回收算法回收，释放内存
      }, duration)

      return {
        close: () => {
          clearTimeout(t1)
          root.removeChild(el) // 从body中移除dom
          newMessage.$destroy()
          false
          vm = null // 设置为null，好让js垃圾回收算法回收，释放内存
        },
        status: () => {
          return showStatus
        }
      }
    }
    /*
      options 参数
      message: '提示内容(必传)'
      type: '提示类型可取值，error, success, waring,info(默认)'
      showClose: '显示关闭按钮'
      duration: '显示时长'
    */
    Vue.prototype.$msg = (options) => {
      if (!options.message) {
        return
      }
      return msg(options)
    }
  },
  createRootElement() {
    const box = document.createElement('div')
    box.style.position = 'fixed'
    box.style.top = '50px'
    box.style.left = '200px'
    box.style.right = '0'
    box.style.zIndex = '1000'
    box.setAttribute('id', 'globalMsgBox')
    document.body.appendChild(box)
  }
}
export default MESSAGE
