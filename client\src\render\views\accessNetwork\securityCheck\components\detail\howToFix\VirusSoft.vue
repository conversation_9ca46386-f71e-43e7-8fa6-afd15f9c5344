<template>
  <div class="how-to-fix">
    <p class="model-title">
      {{ $t('accessNetwork.securityCheck.info_19')
      }}<i
        :class="
          modelIsOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
        "
        @click="modelIsOpen = !modelIsOpen"
      />
    </p>
    <el-collapse-transition>
      <div v-show="modelIsOpen" class="fix-model-content">
        <el-row>
          <el-col class="question">
            {{ $t('check.antivirussoft.h_15_rd') }}
          </el-col>
          <el-col class="way">
            {{ $t('check.antivirussoft.h_17_rs') }}<br>
            <span v-if="checkData.itemResultType !==0">{{ $t('check.antivirussoft.h_19_rs') }}<span :class="[checkData.hasFixed ? 'disable-link-btn': 'link-btn']" @click="fixHandle">{{ $t('check.antivirussoft.js_1_d') }}</span>{{ $t('check.antivirussoft.h_20_rs') }}
            </span>
          </el-col>
        </el-row>
        <el-row v-for="repairWay in getFixTip" :key="repairWay.question">
          <el-col class="question">
            {{ repairWay.question }}
          </el-col>
          <el-col v-for="(way, ind) in repairWay.wayList" :key="way" class="way">
            {{ ind + 1 }}) {{ way }}
          </el-col>
        </el-row>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
import tplMixins from '../mixins/tpl_windows'
export default {
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      modelIsOpen: true,
      fixOptions: {}
    }
  },
  computed: {
    getFixTip() {
      return [
        {
          question: this.$t('check.antivirussoft.h_22_rd'),
          wayList: [this.$t('check.antivirussoft.h_24_rs'), this.$t('check.antivirussoft.h_26_rs'), this.$t('check.antivirussoft.h_28_rs')]
        },
        {
          question: this.$t('check.antivirussoft.h_30_rd'),
          wayList: [this.$t('check.antivirussoft.h_32_rs'), this.$t('check.antivirussoft.h_34_rs'), this.$t('check.antivirussoft.h_28_rs')]
        },
        {
          question: this.$t('check.antivirussoft.h_40_rd'),
          wayList: [this.$t('check.antivirussoft.h_41_rs'), this.$t('check.antivirussoft.h_42_rs')]
        }
      ]
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const avName = _.get(this.checkData, 'CheckResult.CheckType.Info.AntiVirusName')
      const checkTypeData = this.checkData.CheckType
      const RepairType = this.getRepairType()
      console.log(avName, RepairType)
      if (!RepairType || !checkTypeData.Repair[RepairType]) {
        return
      }
      // 全局指定修复参数
      const res = {
        aWay: checkTypeData.Repair[RepairType].Way,
        aUrl: checkTypeData.Repair[RepairType].Url,
        aSoftPath: checkTypeData.Repair[RepairType].SoftPath,
        aSoftArg: checkTypeData.Repair[RepairType].SoftArg
      }
      if (res.aWay === '' && RepairType === 'IsRuning') {
        res.aWay = checkTypeData.Repair.NoInstall.Way
      }
      this.fixOptions = res
      // 杀毒软件独有修复参数
      if (avName && RepairType !== 'NoInstall') {
        this.fixOptions = this.dealData(res, checkTypeData, RepairType, avName)
      }
    },
    getRepairType() {
      const message = _.get(this.checkData, 'CheckResult.CheckType.Message')
      let RepairType = ''
      switch (message) {
        case this.$t('check.antivirussoft.lang_js_Obj.js_8_s') :
          RepairType = 'NoInstall'
          break
        case this.$t('check.antivirussoft.lang_js_Obj.js_9_s') :
          RepairType = 'NoInstall'
          break
        case this.$t('check.antivirussoft.lang_js_Obj.js_10_s') :
          RepairType = 'AntVersion'
          break
        case this.$t('check.antivirussoft.lang_js_Obj.js_13_s') :
          RepairType = 'DBVersion'
          break
        case this.$t('check.antivirussoft.lang_js_Obj.js_17_s') :
          RepairType = 'IsRuning'
          break
      }
      return RepairType
    },
    dealData(res, checkTypeData, RepairType, avName) {
      let antItem
      const BaseCheckItem = _.get(checkTypeData, ('Option.BaseCheckItem'))
      if (BaseCheckItem) {
        antItem = BaseCheckItem.find(item => item.AntiVirusName === avName)
      }
      if (!antItem || !antItem.Repair) {
        return res
      }
      const Repair = antItem.Repair
      const Way = Repair[RepairType].Way
      const Url = Repair[RepairType].Url
      const SoftArg = Repair[RepairType].SoftArg
      const SoftPath = Repair[RepairType].SoftPath
      if (Way === 'Url' && Url) {
        res.aUrl = Url
        res.aWay = Way
        return res
      }
      if (Way === 'Soft' && SoftPath) {
        res.aSoftPath = SoftPath
        if (SoftArg) {
          res.aSoftArg = SoftArg
        } else {
          res.aSoftArg = ''
        }
        res.aWay = Way
        return res
      }
      return res
    },
    fixHandle() {
      if (this.fixOptions.aWay === 'Url') {
        if (!this.fixOptions.aUrl) {
          this.$message({
            message: this.$t('check.antivirussoft.lang_js_Obj.js_19_s'),
            type: 'error'
          })
          return
        }
        this.openUrl(this.fixOptions.aUrl)
      } else if (this.fixOptions.aWay === 'Soft') {
        if (!this.fixOptions.aSoftPath) {
          this.$message({
            message: this.$t('check.antivirussoft.lang_js_Obj.js_19_s'),
            type: 'error'
          })
          return
        }
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            SoftPath: this.fixOptions.aSoftPath,
            SoftArg: this.fixOptions.aSoftArg
          },
          RepairType: 0,
          CreateProgress: 1
        }
        this.submitHandle({
          params,
          CheckItem: this.checkData,
          needProccess: true,
          tip: this.$t('check.install')
        })
      }
    }
  }
}
</script>
