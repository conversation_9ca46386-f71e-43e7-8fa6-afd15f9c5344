/* eslint-disable no-undef */
/**
 * description:qt和javascript通信的接口类。
 * author:liubw
 */
import QWebChannel from './vendor/qwebchannel'
import { __DEV__, dispathSender, addListener, removeListener, imitateQTEnv } from './utils/index'

class QtWebChannelIpcInterface {
  // 这个callback可以用来做判断，是否和QT通讯连接已经完成。
  constructor(callback = _ => { }) {
    imitateQTEnv()
    // 初始化未完成之前先用来暂存发起的通讯事件和回调事件
    this.sendQueue = []
    this.eventQueue = []

    // 这种情况是预防没有初始化QWebChannel成功，就有通讯事件，先暂存在sendQueue
    this.send = ({ module, action, strSerial, data = '' }) => {
      return new Promise((resolve, reject) => {
        this.sendQueue.push({
          module: module,
          action: action,
          strSerial,
          data: data,
          promise: {
            resolve: resolve,
            reject: reject
          }
        })
      })
    }

    this.on = (module, event, callback) => {
      this.eventQueue.push({
        module: module,
        event: event,
        callback: callback
      })
    }

    this.off = (module, event, callback) => {
      console.log('尚未初始化！')
    }

    new QWebChannel(window.qt.webChannelTransport, (channel) => {
      if (!Object.keys(channel).includes('objects')) {
        if (!__DEV__) {
          throw new Error('js与qt初始化失败')
        }
      }

      const QtServer = channel.objects

      this.send = dispathSender(QtServer)
      this.on = addListener(QtServer)
      this.off = removeListener(QtServer)

      // 把暂存的数据都send了。
      if (this.sendQueue.length > 0) {
        this.sendQueue.forEach(e => {
          this.send({ module: e.module, action: e.action, strSerial: e.strSerial, data: e.data, promise: e.promise })
        })

        this.sendQueue = []
      }

      // 把暂存的send通知都处理了
      if (this.eventQueue.length > 0) {
        this.eventQueue.forEach(e => {
          this.on(e.module, e.event, e.callback)
        })

        this.eventQueue = []
      }

      callback(QtServer)
    })
  }
}

export default QtWebChannelIpcInterface
