<template>
  <div class="drawer-content">
    <div class="content-style" v-html="msg" />
  </div>

</template>
<script>
export default {
  props: {
    msg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  mounted() {
    console.log(this.info)
  },
  methods: {

  }
}
</script>
<style lang="scss" scoped>
.drawer-content{
  padding: 10px 32px;
  position: relative;
  height: 100%;
  .content-style{
    font-size: 14px;
    color: $title-color;
    line-height: 24px;
    word-wrap: break-word;
  }
}
</style>

