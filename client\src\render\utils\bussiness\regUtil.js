/*
 * @Author: <EMAIL>
 * @Date: 2021-08-17 08:40:39
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-08-23 19:19:13
 * @Description: 注册页的辅助函数
 */
import proxyApi from '@/service/api/proxyApi'
import store from '@/render/store'
import treeTransform from '@/render/utils/array/treeTransform'

const regUtil = {

  serveEntiretyConfig: _.get(store, 'state.serveEntiretyConfig'), // 全局状态的服务器配置信息

  // 获取部门树和默认的部门id
  async getDepartTree(params = {}) {
    let departTree = {}
    let departArr = []
    let CompanyName = ''
    const clientInfo = store.getters.clientInfo
    let _params = { devip: _.get(clientInfo, 'detail.IP') || _.get(clientInfo, 'basic.Ip'), parentDepartId: '-1', start: 0,
      limit: 200000 }
    _params = { ..._params, ...params }
    const resTree = await proxyApi.getDepart(_params)
    if (parseInt(_.get(resTree, 'errcode')) === 0) {
      const list = _.get(resTree, 'data.list', [])
      list.forEach(item => {
        item.pid = item.UpID
        item.id = item.DepartID
        item.label = item.DepartName
        item.fullLabel = item.AllDepartName
      })
      CompanyName = _.get(resTree, 'data.CompanyName')
      if (_params.parentDepartId < 0 && !_params.keyword) { // 同步查找全部-返回树
        const rootDepart = { fullLabel: CompanyName, id: '0',
          label: CompanyName, pid: '-1', AllDepartID: '-1/0'
        }
        list.push(rootDepart)
        departTree = treeTransform.arrayTotree(list)
        if (departTree.length > 1) {
          /* 设置了ip段部门,后端接口会过滤掉但是不会过滤子级数据，
           arrayTotree方法会将找不到父节点的数据都放到最外层
          */
          departTree = departTree.filter(item => item.id === '0')
        }
      } else {
        departArr = list
      }
    }
    return {
      tree: departTree,
      arr: departArr,
      CompanyName
    }
  },
  // 获取位置树信息
  async getLocationTree(params = {}) {
    let locationTree = []
    let locationArr = []
    const CompanyName = _.get(store, 'state.serveEntiretyConfig.server.ADMINISTRATORINFO.CompanyName')
    let _params = { parentLocationId: '-1', start: 0, limit: 200000 }
    _params = { ..._params, ...params }
    const resTree = await proxyApi.getLocation(_params)
    if (_.get(resTree, 'data.list') && !_.isEmpty(_.get(resTree, 'data.list')) && _.isArray(_.get(resTree, 'data.list'))) {
      const list = _.get(resTree, 'data.list', [])
      list.forEach(item => {
        item.pid = item.UpID
        item.id = item.LocationID
        item.label = item.Location
        item.fullLabel = item.Location
      })
      if (_params.parentLocationId < 0 && !_params.keyword) { // 同步查找全部-返回树
        list.push({ id: '0', label: CompanyName, pid: '-1', fullLabel: CompanyName })
        locationTree = treeTransform.arrayTotree(list)
      } else {
        locationArr = list
      }
    }
    return {
      tree: locationTree,
      arr: locationArr,
      CompanyName
    }
  },
  // 获取部门设置的默认值
  async getDefaultDepart() {
    let defaultDepartId = ''
    const clientInfo = store.getters.clientInfo
    const params = {
      devip: _.get(clientInfo, 'basic.Ip') || _.get(clientInfo, 'detail.IP')
    }
    let departName = ''
    const ret = await proxyApi.getDefaultDepart(params)
    if (parseInt(_.get(ret, 'errcode')) === 0 && _.get(ret, 'data.selDepartId')) {
      defaultDepartId = parseInt(_.get(ret, 'data.selDepartId'))
      departName = _.get(ret, 'data.selDepartName')
    }
    // 如果默认的部门id为0,从服务器返回的设备信息接口里面获取
    if ((parseInt(defaultDepartId) === 0 || !defaultDepartId) &&
        parseInt(_.get(clientInfo, 'detail.DepartID')) !== 0
    ) {
      defaultDepartId = parseInt(_.get(clientInfo, 'detail.DepartID'))
      departName = _.get(clientInfo, 'detail.AllDepartName')
    }
    if (!defaultDepartId || !departName) {
      return ''
    }
    return {
      id: defaultDepartId,
      label: departName
    }
  }

}

export default regUtil
