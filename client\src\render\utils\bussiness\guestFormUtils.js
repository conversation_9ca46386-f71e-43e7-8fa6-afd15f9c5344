import GuestForm from './guestFormBase'
import { i18n } from '@/render/lang'
import regular from '@/render/utils/regular'
import store from '@/render/store'
import scene from './scene'
const guestFormUtils = {
  createForm(parmas, config = {}) {
    const defaultConfig = {
      formatePlaceholder: guestFormUtils.formatePlaceholder,
      formateRule: guestFormUtils.formateRule,
      formateFiledsIcon: guestFormUtils.formateFiledsIcon,
      lang: i18n.locale
    }
    return new GuestForm(parmas, { ...defaultConfig, ...config })
  },
  // 对图标的处理
  formateFiledsIcon(item, type = 'icon') {
    const column = _.get(item, 'Column')
    if (column === 'GuestRequireUser' || column === 'AuditRequireUser') {
      return 'iconfont icon-laibinrenzheng'
    }
    if (column === 'GuestRequireUnit') {
      return 'iconfont icon-danwei'
    }
    if (column === 'GuestRequireTel' || column === 'AuditRequireTel') {
      return 'iconfont icon-shoujihao'
    }
    if (column === 'AllowRegionIDs') {
      return 'iconfont icon-wangluoyu'
    }
    if (column === 'AllowTime') {
      return 'iconfont icon-shangwangshichang'
    }
    return 'iconfont icon-tongyong'
  },
  formatePlaceholder(item) {
    if (!_.isEmpty(_.get(item, 'Remark'))) {
      return item.Remark
    }
    if (_.get(item, 'InputType') === 'tree' || _.get(item, 'InputType') === 'select') {
      return i18n.t('reg.commonSelectPlacholder') + _.get(item, 'Title')
    }
    return i18n.t('reg.commonTextPlacholder') + _.get(item, 'Title')
  },
  // 生成验证的表单项
  formateRule(item) {
    const rules = []
    // 是否必填字段
    if (parseInt(_.get(item, 'Select')) === 1) {
      const trigger = _.get(item, 'InputType') === 'hour' ? 'blur' : 'change'
      rules.push({ required: true, message: `[${_.get(item, 'Title')}] ${i18n.t('auth.emptyErr')}`, trigger })
    }
    // 添加其他校验规则:Default,Email，Phone
    if (!_.isEmpty(_.get(item, 'Rule')) && parseInt(_.get(item, 'Rule')) !== 0) {
      const regex = _.get(item, 'Rule')
      const validateFun = (rule, value, callback) => {
        if (value) {
          const reg = regular.rules[regex]
          if (reg && !reg.test(value)) {
            callback(new Error(`[${_.get(item, 'Title')}] ${i18n.t('changeRegInfo.styleErro')} ${regular.errorTipMap()[regex]}`))
          }
        }
        callback()
      }
      rules.push({ validator: validateFun, trigger: 'blur' })
    }
    return rules
  },
  // 获取可访问域
  async getNetcode() {
    if (!_.get(store.state.serveEntiretyConfig, 'scene.SceneID', false)) {
      await scene.getDeviceScene(false)
    }
    const list = _.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo.allocationRegionData', [])
    if (!list || !list.length) {
      return []
    }
    list.forEach(item => {
      item.id = item.RID
      item.label = item.RegionName
    })
    return list
  },
  formateFormParams(params) {
    const _params = {}
    for (const k in params) { // 格式化日期，拼接数组类型值
      _params[k] = params[k]
      if (Array.isArray(params[k])) {
        if (k === 'AllowRegionIDs') {
          const arr = [...params[k]]
          arr.sort()
          _params[k] = arr.join(',')
        } else {
          _params[k] = params[k].join(',')
        }
      }
      if (k === 'GuestStartTime') {
        console.log(params.GuestStartTime)
        if (params.GuestStartTime) {
          _params.GuestStartTime = params.GuestStartTime[0] + ' 00:00:00'
          _params.GuestEndTime = params.GuestStartTime[1] + ' 23:59:59'
        }
      }
    }
    console.log('_params', _params)
    return _params
  }
}

export default guestFormUtils
