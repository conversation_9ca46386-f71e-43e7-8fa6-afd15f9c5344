<template>
  <div v-show="show" class="global-message-box" :class="type">
    <i v-if="showIcon" :class="['iconfont','typeIcon',iconClass]" />
    <span class="text" v-html="text" />
    <i v-if="showClose" class="iconfont icon-guanbi" @click="closeHandle" />
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: 'Message',
  props: {
    type: {
      type: String,
      default: 'info',
      validator: val => ['info', 'success', 'warning', 'error'].includes(val)
    },
    text: {
      type: String,
      default: ''
    },
    showClose: {
      type: Boolean,
      default: false
    },
    showIcon: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      show: true
    }
  },
  computed: {
    iconClass() {
      let icon = ''
      switch (this.type) {
        case 'success' :
          icon = 'icon-anjianheguixiang'
          break
        case 'warning' :
          icon = 'icon-putongxiangbuhegui'
          break
        case 'error' :
          icon = 'icon-guanjianxiangbuhegui'
          break
      }
      return icon
    }
  },
  methods: {
    closeHandle() {
      this.show = false
    }
  }
}
</script>
<style lang="scss" scoped>
.global-message-box{
    text-align: center;
    font-size: 14px;
    background: #edf2fc;
    font-weight: 400;
    color: #909399;
    margin-bottom: 10px;
    position: relative;
    box-sizing: border-box;
    padding: 10px 30px;
    .iconfont{
      font-size: 16px;
      margin-right: 10px;
    }
    .icon-guanbi{
      position: absolute;
      top: 10px;
      right: 2px;
      cursor: pointer;
    }
    .text{
      line-height: 20px;
    }
    .typeIcon{
      line-height: 20px;
    }
}
.global-message-box.success{
  background: #f2f9ec;
  color: #24B377;
  .iconfont{
    color: #29CC88;
  }
}
.global-message-box.warning{
  background: #f2f0eb;
  color: #E6A017;
  .iconfont{
    color:#F2A918
  }
}
.global-message-box.error{
  background: #fceded;
  color: #e65353;
  .iconfont{
    color: #e65353;
  }
}

</style>

