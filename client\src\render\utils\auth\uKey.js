
import Auth from './auth'
import { Message } from 'element-ui'
import store from '@/render/store'
import proxyApi from '@/service/api/proxyApi'
import common from './common'
import _ from 'lodash'
import dot1xCommon from '@/render/utils/auth/dot1x'
import authTypes from './authTypes'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { i18n } from '@/render/lang'
import authIndex from '@/render/utils/auth/index'
import Vue from 'vue'
import agentApi from '@/service/api/agentApi'
import { Base64Encode, stringIsEmpty } from '@/render/utils/global'

class UKey extends Auth {
  constructor() {
    super()
    this.type = authTypes.UKey
  }

  // 获取证书
  async getCert() {
    const certList = []
    // 软证书
    const uKeyOnSoftAuth = _.get(store.getters.computeServeEntiretyConfig, 'UKey.Onsoftauth', 0)
    const apiParam = {
      Onsoftauth: uKeyOnSoftAuth
    }
    const RunResult = await agentApi.getAllUsbKeyCert(apiParam)

    let certInfo = _.get(RunResult, 'ASM.CertInfo', [])

    if (_.isObject(certInfo) && !_.isArray(certInfo)) {
      certInfo = [certInfo]
    }

    if (_.isArray(certInfo) && !_.isEmpty(certInfo)) {
      _.map(certInfo, (value) => {
        certList.push(this.handleCert(value))
      })
    }

    // 没有拿到则再次去拿
    return certList
  }

  // 处理证书的格式
  handleCert(certInfo) {
    const singerCert = {
      IssueTo: _.get(certInfo, 'IssueTo', ''),
      Subject: _.get(certInfo, 'Subject', ''),
      CertSerial: _.get(certInfo, 'CertSerial', ''),
      CertBase64: _.get(certInfo, 'CertBase64', ''),
      IsSoftCert: _.get(certInfo, 'IsSoftCert', '')
    }
    singerCert['label'] = singerCert['IssueTo']
    singerCert['value'] = singerCert['CertSerial']
    return singerCert
  }

  /**
   *认证前参数的格式化
   * @params
   * @return {Object} { result:true|false,[可选]msg:string,[可选]params:object }
  */
  async authParamsPrepare(authData, accessNetwork, isWireLess, certInfo) {
    const uKeyType = _.get(store.getters.computeServeEntiretyConfig, 'UKey.UKeyType', -1)

    if (authIndex.isDot1xMode()) {
      if (!_.isString(accessNetwork) || accessNetwork === '') {
        Vue.prototype.$msg({ type: 'error', message: i18n.t('auth.selectNetworkList') })
        return false
      }
    }

    let res
    switch (uKeyType) {
      case '3':
        // 将证书序列号作为用户名进行认证
        res = await this.serialUKeyTypeStart(authData, accessNetwork, isWireLess, certInfo)
        break
      case '4':
        // 选择其他Ukey类型认证
        res = await this.OtherUKeyTypeStart(authData, accessNetwork, isWireLess, certInfo)
        break
      default:
        // ukey认证默认流程 （UKeyType=1,2 也就是ldap和根证书认证，以及802.1x认证则默认是单证书认证）
        res = await this.defaultUkeyTypeStart(authData, accessNetwork, isWireLess, certInfo)
        break
    }

    return res
  }

  async defaultUkeyTypeStart(authData, accessNetwork, isWireLess, certInfo) {
    if (_.isEmpty(_.get(certInfo, 'CertSerial'))) {
      return { result: false, msg: i18n.t('auth.needRightfulUKey') }
    }

    const IssueTo = certInfo['IssueTo']
    const isSoftCert = parseInt(_.get(certInfo, 'IsSoftCert', 0))
    const UkeyOnsoftauth = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'UKey.Onsoftauth', 0))
    const ukeyPin = parseInt(_.get(store.getters.computeServeEntiretyConfig, 'UKey.PIN', 0))
    // 增加判断PIN码 xiaoxj 20200925
    // 不是软件证书，并且开启了校验PIN码就校验PIN码
    if (isSoftCert !== 1 && ukeyPin === 1) {
      try {
        const runResult = await agentApi.callAgentOneFunc({
          WhereIsModule: 'MsacCheckCert.dll',
          WhatFuncToCall: 'CheckUsbKeyCertPin',
          RequestParam: JSON.stringify({
            ASM: {
              IssueTo: IssueTo,
              CertSerial: _.get(certInfo, 'CertSerial'),
              Onsoftauth: UkeyOnsoftauth + ''
            }
          })
        })
        console.log('CheckUsbKeyCertPin', runResult)
        const res = parseInt(_.get(runResult, 'ASM.VerifyResult', 0))
        if (res !== 1) {
          return { result: false, msg: i18n.t('auth.checkPinFail') }
        }
      } catch (error) {
        console.error('证书pin校验请求出错==')
        console.error(error)
        return { result: false, msg: i18n.t('auth.checkPinFail') }
      }
    }
    return this.defaultUkeyTypeTimeOutSuccess(authData, certInfo, accessNetwork, isWireLess)
  }

  defaultUkeyTypeTimeOutSuccess(authData, certInfo, accessNetwork, isWireLess) {
    const user_name = certInfo['IssueTo']

    // 证书颁发实体不能为空
    if (stringIsEmpty(user_name)) {
      return { result: false, msg: i18n.t('auth.emptyIssueTo') }
    }
    const subject = certInfo['Subject']
    const caserial = certInfo['CertSerial']
    const certserial = certInfo['CertSerial']
    const certbase64 = certInfo['CertBase64']
    const apiParams = {
      'user_name': Base64Encode(user_name),
      'subject': Base64Encode(subject),
      'caserial': caserial,
      'certserial': certserial,
      'certbase64': certbase64,
      autoAuth: authData.uKeyAutoAuth,
      autoLogin: authData.uKeyAutoLogin,
      AuthParam: {
        AccessNetwork: accessNetwork,
        IsWireLess: isWireLess,
        CertInfo: {
          ...certInfo
        }
      }
    }
    return { result: true, params: apiParams }
  }

  async OtherUKeyTypeStart(authData, accessNetwork, isWireLess, certInfo) {
    const uKeyPin = _.get(authData, 'ukeyPin', '')
    const res = await agentApi.callAgentOneFunc({
      WhereIsModule: 'MsacAssAutoCheck.dll',
      WhatFuncToCall: 'UKeyAuth_Jit',
      RequestParam: JSON.stringify({
        ASM: {
          GatewayIp: _.get(store.getters.computeServeEntiretyConfig, 'UKey.UkeyOtherIP', ''),
          Port: _.get(store.getters.computeServeEntiretyConfig, 'UKey.UkeyOtherPort', ''),
          AppFlag: _.get(store.getters.computeServeEntiretyConfig, 'UKey.UkeyOtherAppFlag', ''),
          Pin: uKeyPin || ''
        }
      })
    })

    localStorage.setItem('Pincode', uKeyPin, 24)

    const UserProperties = _.get(res, 'Result.UserProperties')
    const AuthResult = parseInt(_.get(res, 'Result.AuthResult'))
    if (AuthResult === -1) {
      // 下载控件 todo
      return { result: false, msg: i18n.t('auth.failedToLoadControl') }
    }
    if (AuthResult === 'FALSE') {
      return { result: false, msg: UserProperties }
    }
    if (!stringIsEmpty(UserProperties)) {
      return { result: false, msg: i18n.t('auth.needRightfulUKey') }
    }

    const apiParams = {
      user_name: Base64Encode(UserProperties),
      clientip: _.get(store.getters.computeServeEntiretyConfig, 'HTTP_CLIENT_IP', ''),
      autoAuth: authData.uKeyAutoAuth,
      autoLogin: authData.uKeyAutoLogin,
      AuthParam: {
        AccessNetwork: accessNetwork,
        IsWireLess: isWireLess,
        CertInfo: {
          ...certInfo
        }
      }
    }

    return { result: true, params: apiParams }
  }

  async serialUKeyTypeStart(authData, accessNetwork, isWireLess, certInfo) {
    const apiParam = {
      WhereIsModule: 'MsacCheckCert.dll',
      WhatFuncToCall: 'GetUsbKeySerialNum',
      RequestParam: '<ASM><UkeyType>ePass1000ND</UkeyType></ASM>'
    }
    const RunResult = await agentApi.callAgentOneFunc(apiParam)
    const user_name = _.get(RunResult, 'ASM.SerialNum')

    if (!_.isString(user_name) || user_name === '') {
      return { result: false, msg: i18n.t('auth.emptySerialNum') }
    }

    const uKeyPassword = authData.uKeyPassword
    const apiParams = {
      'user_name': Base64Encode(user_name),
      'password': Base64Encode(uKeyPassword),
      autoAuth: authData.uKeyAutoAuth || 0,
      autoLogin: authData.uKeyAutoLogin || 0,
      AuthParam: {
        AccessNetwork: accessNetwork,
        IsWireLess: isWireLess,
        CertInfo: {
          ...certInfo
        }
      }
    }
    return { result: true, params: apiParams }
  }

  /**
   * 普通
   * @returns {Promise<boolean>}
   */
  async common(params) {
    const apiParam = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID', 0),
      hintOver: G_VARIABLE.g_hintOver,
      type: this.type
    }
    // 去除802.1x用参数
    _.omit(params, 'AuthParam')
    _.assign(apiParam, params)

    const res = await proxyApi.authIndex(apiParam, { showError: false })
    const errorCode = parseInt(_.get(res, 'errcode', -1))
    if (errorCode === 0) {
      store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: res.data }))

      await common.authEnd({
        type: this.type,
        password: apiParam.password,
        certbase64: params.certbase64,
        autoAuth: this.autoAuth(params) ? '1' : '0'
      })

      Message.success(res.errmsg)
      return res.data
    }
    switch (errorCode) {
      case 21120030:
        // 超出最大可登录设备数
        return { revoke: true, data: {
          msg: res.errmsg,
          ...res.data
        }}
      default:
        Message.error(res.errmsg, i18n.t('auth.dot1xAuthFail'))
        return false
    }
  }

  /**
   * 802.1x
   * @returns {Promise<boolean>}
   */
  async dot1x(params) {
    const apiParam = {
      AuthParam: {
        AuthType: this.type
      }
    }
    _.merge(apiParam, params)
    const res = await dot1xCommon.auth(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', '失败'))
      return false
    }

    const authResponse = _.get(res, 'ASM.AuthResponse', '')
    const authResponseJson = dot1xCommon.handleAuthResponse(authResponse)
    store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: authResponseJson }))

    await dot1xCommon.authEnd({
      type: this.type,
      authResponse: false,
      autoAuth: this.autoAuth(params) ? '1' : '0'
    })
    return authResponseJson
  }

  /**
   * 是否自动认证
   * 1.勾选记住账户、自动登录
   * 2.允许记住账户
   * @returns
   */
  autoAuth({ autoAuth }) {
    return autoAuth &&
        parseInt(_.get(store.getters.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0)) === 1
  }
}

export default UKey
