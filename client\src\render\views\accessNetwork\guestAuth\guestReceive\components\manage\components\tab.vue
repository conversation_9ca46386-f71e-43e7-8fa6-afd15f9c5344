<template>
  <ul class="tab-wrapper">
    <li v-for="item in list" v-show="item.isAuth" :id="'ui-guest-receive-manage-li-'+item.id" :key="item.id" :class="['tab-item', activeTab === item.id?'active-item': '']" @click="change(item)">
      {{ item.name }}
    </li>
  </ul>
</template>
<script>
import { mapState } from 'vuex'
export default {
  props: {
    value: {
      default: 'single',
      type: String
    }
  },
  data() {
    return {

    }
  },
  computed: {
    ...mapState(['serveEntiretyConfig']),
    list() {
      return [
        { name: this.$t('guestAuth.guest.info_39'), id: 'single', isAuth: true },
        { name: this.$t('guestAuth.guest.info_40'), id: 'team', isAuth: parseInt(_.get(this.serveEntiretyConfig, 'server.GUESTAUTH.authority', 0)) === 1 },
        { name: this.$t('guestAuth.guest.info_89'), id: 'access', isAuth: true }
      ]
    },
    activeTab: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    change(item) {
      console.log(item)
      if (item.id === this.value) {
        return
      }
      this.activeTab = item.id
    }
  }
}
</script>
<style lang="scss" scoped>
    .tab-wrapper{
        width: 100%;
        display: flex;
        border-bottom: 1px solid $line-color;
        .tab-item{
            padding: 6px 16px;
            line-height: 20px;
            font-size: 14px;
            color: $title-color;
            margin-right: 4px;
            border-radius: 4px 4px 0px 0px;
            border: 1px solid $line-color;
            border-bottom: none;
            background: $gray-5;
            cursor: pointer;
            &:hover{
                background: $line-color;
                border-color: $gray-2;
            }
        }
        .active-item{
            background: $green;
            color: $light-color;
            border-color: $green;
            &:hover{
                background: $green;
                border-color: $green;
            }
        }
    }
</style>
