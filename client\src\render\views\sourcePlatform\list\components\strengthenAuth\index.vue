<!-- 强化认证 -->
<template>
  <VerticalDialog
    :show.sync="show"
    :title="calcTitle"
    width="440px"
    @closed="closeHandle"
  >
    <div :class="['dialog-content', isQrcode? 'qr-code-content':'']" @keydown="keyDown">
      <component
        :is="authType"
        v-if="showCom"
        ref="authComponent"
        :id-pre="idPre"
        :is-qrcode="isQrcode"
        :auth-data="authData"
        @emitHandle="emitHandle"
        @loading="loadingHandle"
      />
      <div v-if="!isQrcode && showCom" class="content-footer">
        <div :id="`${idPre}-div-cancel`" class="clear" @click="cancleHandle">{{ $t('sourcePlatform.cancel') }}</div>
        <div :id="`${idPre}-div-submit`" :class="[loading? 'disable-btn': '', 'submit']" @click="submitForm()"><i v-if="loading" class="el-icon-loading" />{{ $t('sourcePlatform.ok') }}</div>
      </div>
    </div>
  </VerticalDialog>
</template>

<script>
import accountAuth from './account.vue'
import mobileAuth from '@/render/views/accessNetwork/auth/components/mobile.vue'
import emailAuth from './email.vue'
import dingTalkAuth from '@/render/views/accessNetwork/auth/components/dingtalk.vue'
import feishuAuth from '@/render/views/accessNetwork/auth/components/feishu.vue'
import weWorkAuth from '@/render/views/accessNetwork/auth/components/wework.vue'
import otpAuth from '@/render/views/accessNetwork/auth/components/otp.vue'
export default {
  name: 'StrengthenAuth',
  components: {
    'as-account-auth': accountAuth,
    'as-mobile-auth': mobileAuth,
    'as-email-auth': emailAuth,
    'as-dingding-auth': dingTalkAuth,
    'as-feishu-auth': feishuAuth,
    'as-weWork-auth': weWorkAuth,
    'as-otp-auth': otpAuth
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: function() {
    return {
      showCom: true,
      loading: false
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 认证组件类型
    authType() {
      const authData = this.authData
      let comType = ''
      switch (authData.AddAuthType) {
        case 'Mobile' :
          comType = 'as-mobile-auth'
          break
        case 'Mailbox' :
          comType = 'as-email-auth'
          break
        case 'DingTalk' :
          comType = 'as-dingding-auth'
          break
        case 'FeiShu' :
          comType = 'as-feishu-auth'
          break
        case 'Finger' :
          comType = 'as-finger-auth'
          break
        case 'WeWork' :
          comType = 'as-weWork-auth'
          break
        case 'OTP' :
          comType = 'as-otp-auth'
          break
        default:
          comType = 'as-account-auth'
      }

      return comType
    },
    idPre() {
      const pre = this.authType.replace('as', 'ui-ztp')
      return pre.replace('-auth', '')
    },
    isQrcode() {
      const dic = ['FeiShu', 'DingTalk', 'WeWork', 'OTP']
      const currentType = this.authData.AddAuthType
      return dic.indexOf(currentType) > -1
    },
    calcTitle() {
      const currentType = this.authData.AddAuthType
      return this.$t('strenthAuth.title') + ' - ' + this.$t(`strenthAuth.${currentType}`)
    }
  },
  watch: {
    show(flag) {
      console.log(flag)
      if (flag) {
        this.showCom = true
      }
    }
  },
  created() {
  },
  methods: {
    cancleHandle() {
      this.show = false
    },
    emitHandle(data) {
      if (data.type === 'AuthSuccess') {
        this.$emit('success')
      }
    },
    submitForm() {
      if (this.loading) {
        return
      }
      this.$refs.authComponent.submitForm()
    },
    closeHandle() {
      this.showCom = false
    },
    loadingHandle(flag) {
      this.loading = flag
    },
    /**
     * 回车提交
     */
    keyDown(event) {
      console.log('keyDown', event.keyCode)
      // 非加载中
      if (!this.loading && parseInt(event.keyCode) === 13) {
        this.$refs.authComponent.submitForm()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 24px;
  .content-footer{
    border-top: 1px solid $line-color;
    display: flex;
    &>div{
      width: 50%;
      line-height: 40px;
      text-align: center;
      font-size: 14px;
      color: $--color-primary;
      cursor: pointer;
      &:hover{
        background: $gray-3;
      }
    }
    .clear{
      border-right: 1px solid $line-color;
      color: $default-color;
      border-bottom-left-radius: 5px;
    }
    .submit{
      border-bottom-right-radius: 5px;
      .el-icon-loading{
          margin-right: 5px;
      }
    }
    .disable-btn{
        cursor: not-allowed;
        &:hover{
            background: white;
        }
    }
  }
}
.qr-code-content{
    padding-top: 0;
}
</style>
