<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-26 09:30:55
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-08-26 10:34:03
 * @Description: 注册账号
 * 当前客户端注册账号是唤起浏览器注册，不过先保留在这里，说不定产品又会变
-->
<template>
  <div id="f-reg-account" class="auth-wrap">
    <div class="sign-wrapper">
      <span />
      <span class="mode-wrapper" @click="returnAuth">
        <i class="iconfont icon-fanhui mr12" />
        {{ $t('header.goBack') }}
      </span>
    </div>
    <div class="u-main">
      <h1 class="u-title">认证注册账号</h1>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    returnAuth() {
      this
        .$router
        .push('/access/auth')
    }
  }
}
</script>
<style lang="scss" scoped="scoped">
    #f-reg-account {
        padding: 24px;
        box-sizing: border-box;
        text-align: center;
        height: 100%;
        .u-main {
            display: flex;
            height: 100%;
            width: 100%;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            text-align: left;
        }

    }
</style>
