<template>
  <div id="f-resource-user-auth">
    <el-form ref="ruleForm" class="u-auth-main" :model="ruleForm" :rules="rules" label-width="20px">
      <el-form-item prop="userName">
        <el-input
          v-model="userName"
          :placeholder="$t('resourceAuth.ClientUser')"
          size="medium"
          readonly
          disabled
        />
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          v-model="ruleForm.password"
          type="password"
          size="medium"
          :placeholder="$t('resourceAuth.ClientPassword')"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          class="public-medium-btn"
          type="primary"
          size="medium"
          @click="submitForm()"
        >
          {{ $t('reg.submit') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { Message } from 'element-ui'
import authIndex from '@/render/utils/auth/index'
import { Base64Encode, GetUrlParam } from '@/render/utils/global'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { mapGetters } from 'vuex'
import proxyApi from '@/service/api/proxyApi'

export default {
  props: {
    userName: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      ruleForm: {
        password: ''
      },
      rules: {

      }
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeNetAccessStatus']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }
  },
  methods: {
    // 点击按钮进行高级动态身份认证提交
    async submitForm() {
      if (_.isEmpty(this.ruleForm.password)) {
        const err = this.$t('resourceAuth.emptyPwdErr')
        Message({ message: err, type: 'error', duration: 6000, offset: 60 })
        return
      }

      const apiParams = {
        user_name: Base64Encode(this.userName),
        deviceid: this.deviceid,
        idp: Base64Encode(GetUrlParam('idp')),
        tokenId: Base64Encode(GetUrlParam('tokenId')),
        remoteIp: GetUrlParam('remoteIp'),
        password: authIndex.passwordEncrypt(this.ruleForm.password),
        hintOver: G_VARIABLE.g_hintOver,
        autoAuth: 0,
        resourceAddr: GetUrlParam('firsturl'),
        type: 'User' // 认证方式'NoAuth','User','Qrcode'
      }

      const ret = await proxyApi.resourceAuth(apiParams)
      if (parseInt(_.get(ret, 'errcode', 200)) !== 0 || _.isEmpty(_.get(ret, 'data', '')) || !_.isObject(_.get(ret, 'data', ''))) {
        // 认证失败
        const err = _.get(ret, 'errmsg') || this.$t('resourceAuth.authFaild')
        this.$emit('authFaild', { type: 'user', msg: err })
        return
      }

      this.$emit('authSuccess', { type: 'Pap' })
    }
  }
}
</script>

<style lang="scss">
#f-resource-user-auth{

  .u-auth-main{
    width: 520px;

    .public-medium-btn{
      width:100%;
    }
  }
}
</style>
