<!-- 用户权限 -->
<template>
  <div
    v-loading="loading"
    class="auth-wrap"
    @keydown="keyDown"
  >
    <!-- 认证 -->
    <template v-if="!isAccountBind">
      <div class="sign-wrapper">
        <span class="auth-tag"> <i class="iconfont icon-renzheng " />{{ $t('auth.auth') }}</span>
        <span v-if="showSwitchGuest" id="ui-access-auth-span-to_guest" :class="['change-tag', switching?'disabled-change-tag':'']" @click="toGuest">{{ $t('auth.switchGuest') }} <i class="iconfont icon-qiehuan " /></span>
      </div>

      <layout-state
        v-if="!clientInfo.online && !isDot1xMode && !isKnockPort"
        :state-img="serverNotWorking"
        :state-msg="$t('auth.unableConnectToNetwork')"
        :state-btn-txt="$t('refresh')"
        @stateBtnHandle="refresh"
      />

      <div v-else class="content">
        <p class="tab-pane-nev">
          <span
            v-for="(tab, index) in tabs"
            :key="index"
            class="tab-wrapper"
          >
            <span
              :id="`ui-accessNetwork-auth-span-${tab.uId}`"
              :class="{ 'is-active': activeType === index }"
              @click="switchActiveType(index)"
            >{{ tab.name }}</span>
          </span>
        </p>
        <div v-if="activeType === 0" class="tab-pane-content ">
          <!--普通认证-->
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            class="rule-form"
          >
            <el-form-item class="select-form-box">
              <i class="iconfont icon-leixing slect-icon" />
              <el-select
                id="ui-accessNetwork-auth-div-authtype"
                v-model="ruleForm.authType"
                popper-class="papper-body"
                @change="changeAuthType"
              >
                <el-option
                  v-for="item in authTypeOptions"
                  :id="`ui-accessNetwork-auth-li-${item.value}_auth`"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <component
              :is="currentCom"
              v-if="ruleForm.authType"
              ref="auth"
              :key="ruleForm.authType"
              :server-info="serverInfo"
              :auth-data.sync="authData"
              :auth-type="ruleForm.authType"
              @loading="changeLoading"
              @emitHandle="emitHandle"
            />

          </el-form>
        </div>
        <!-- 扫码认证 -->
        <div
          v-show="activeType === 1"
          v-if="qrcodeOptions.length > 0"
          class="tab-pane-content scan-code-wrap"
        >
          <el-select id="ui-accessNetwork-auth-input-authtype" v-model="qrcodeType" popper-class="papper-body" class="scan-select">
            <el-option v-for="item in qrcodeOptions" :id="`ui-accessNetwork-auth-li-${item.value}_auth`" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <component
            :is="qrcodeType"
            v-if="activeType === 1"
            ref="qrcode"
            :is-qrcode="activeType === 1"
            @emitHandle="emitHandle"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <accountBind ref="bindCom" @emitHandle="emitHandle" />
    </template>
    <revokeDevice
      ref="revoke"
      :revoke-data="revokeData"
      @emitHandle="emitHandle"
    />

    <!--自动登录提示dialog-->
    <ConfirmDialog
      :show.sync="shouwAutoLoginTip"
      :show-confirm-button="false"
      pop-name="auto-login-pop"
      @no="cancelAutoLogin"
    >
      <div id="ui-auth-auth-div-auto_login_tip" style="text-align: center;margin:15px 0;">{{ $t('auth.autoLogining') }}</div>
    </ConfirmDialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import AccountAuth from './components/account.vue'
import UkeyAuth from './components/ukey.vue'
import MobileAuth from './components/mobile.vue'
import FingerAuth from './components/finger.vue'
import NAC from './components/nac'
import WeWork from './components/wework'
import DingTalk from './components/dingtalk'
import FeiShu from './components/feishu'
import revokeDevice from './components/revokeDevice'
import SsoComponent from './components/sso.vue'
import state from '@/render/components/layout/state'
import _ from 'lodash'
import os_browser_info from '@/render/utils/os_browser_info'
import agentApi from '@/service/api/agentApi'
import NoAuth from '@/render/utils/auth/noAuth'
import authIndex from '@/render/utils/auth/index'
import authTypes from '@/render/utils/auth/authTypes'
import AdAuto from '@/render/utils/auth/adAuto'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import accessNetwork from '@/render/utils/accessNetwork'
import { EventBus } from '@/render/eventBus'
import dot1xCommon from '@/render/utils/auth/dot1x'
import processController from '@/render/utils/processController'
import { Base64Decode } from '@/render/utils/global'
import accountBind from './components/accountBind'
import ztpUtils from '@/render/utils/auth/ztp/index'
import scene from '@/render/utils/bussiness/scene'
import authUtils from '@/render/utils/auth/index'
const serverNotWorking = require('@/render/assets/serverNotWorking.png')

export default {
  components: {
    User: AccountAuth,
    UKey: UkeyAuth,
    Mobile: MobileAuth,
    Finger: FingerAuth,
    NAC,
    DingTalk,
    WeWork,
    FeiShu,
    Sso: SsoComponent,
    revokeDevice,
    'layoutState': state,
    accountBind
  },
  data() {
    return {
      isAdAuto: false,
      activeType: 0, // 选中的tabs 0 普通认证 1扫码认证
      ruleForm: {
        authType: ''
      },
      qrcode: null,
      qrcodeType: '',
      loading: false,
      isSwitchUser: false, // 是否切换账户
      shouwAutoLoginTip: false, // 自动认证提示
      autoLoginTimeout: null, // 自动认证定时器
      authData: { // 认证数据（用户名、密码等）
        userName: '',
        password: '',
        verifyCode: '',
        userAutoLogin: false,
        userAutoAuth: false,
        cert: '',
        uKeyName: '',
        uKeyPassword: '',
        uKeyPin: '',
        uKeyAutoLogin: false,
        uKeyAutoAuth: false,
        uKeyCertDefault: '' // 证书的默认值
      },
      revokeData: {
        msg: '',
        Username: '',
        AuthType: ''
      },
      serverNotWorking,
      isAccountBind: false,
      switching: false
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'clientInfo',
      'authInfo'
    ]),
    isShowNotFuond() { // 是否会展示404页面
      if (this.isAccountBind || this.clientInfo.online || this.isDot1xMode || this.isKnockPort) {
        return false
      }
      return true
    },
    tabs() {
      const tabs = []
      if (!_.isEmpty(this.authTypeOptions)) {
        tabs.push({
          name: this.$t('auth.accountAuth'),
          uId: 'account_auth'
        })
      }
      if (!_.isEmpty(this.qrcodeOptions)) {
        tabs.push({
          name: this.$t('auth.scanAuth'),
          uId: 'scan_auth'
        })
      }
      return tabs
    },
    /**
     * 是否用户名密码认证
     */
    isAccountAuthType() {
      return authTypes.isAccountAuth(this.ruleForm.authType)
    },
    currentCom() {
      if (this.isAccountAuthType) {
        return authTypes.User
      }
      const ssoConfig = _.get(this.computeServeEntiretyConfig, 'SSOConfig', {})
      if (ssoConfig[this.ruleForm.authType]) {
        return 'Sso'
      }
      return this.ruleForm.authType
    },
    /**
     * 是否强制SSO
     */
    isForceSso() {
      const { ForceSSO } = _.get(this.computeServeEntiretyConfig, 'SSO', {})
      const defaultAuthType = _.get(this.computeServeEntiretyConfig, 'AUTHPARAM.DefaultAuthType')
      const ssoConfig = _.get(this.computeServeEntiretyConfig, 'SSOConfig', {})
      return !this.isDot1xMode && parseInt(ForceSSO) === 1 && ssoConfig[defaultAuthType]
    },
    // 获取账号密码别名认证方式
    userAliasAuth() {
      let serverAlias = _.get(this.computeServeEntiretyConfig, 'User.AuthServerAlias') // 别名
      if (!serverAlias) {
        return []
      }
      try {
        serverAlias = JSON.parse(serverAlias)
      } catch (e) {
        serverAlias = []
      }
      let authServer = _.get(this.computeServeEntiretyConfig, 'User.AuthServer') // 认证服务器
      if (!authServer) {
        return []
      }
      authServer = authServer.split('|')
      const aliasMap = serverAlias.reduce((acc, cur) => {
        const key = cur['name']
        acc[key] = cur
        return acc
      }, {})
      return authServer.map((item) => {
        const alias = aliasMap[item]
        return {
          value: item,
          label: alias ? alias['value'] : this.$t('auth.' + item)
        }
      })
    },
    // 普通可选认证方式
    authTypeOptions() {
      let authType = []
      const allowAuthType = _.get(this.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType')
      if (!_.isArray(allowAuthType) || _.isEmpty(allowAuthType)) {
        return authType
      }

      const userAliasAuthTypes = this.userAliasAuth // 账号密码别名配置

      const SSOConfig = _.get(this.computeServeEntiretyConfig, 'SSOConfig', {})
      const dot1xExclude = [authTypes.Finger]

      allowAuthType.forEach((item) => {
        if (this.isDot1xMode && dot1xExclude.indexOf(item) !== -1) {
          return
        }
        if (item === authTypes.User && userAliasAuthTypes.length > 0) { // 账号密码有别名用别名
          authType = authType.concat(userAliasAuthTypes)
        } else if (authTypes.getQrcode().indexOf(item) === -1 && item !== authTypes.Guest) { // 去除扫码认证和来宾认证
          if (SSOConfig[item]) { // sso认证方式
            if (!this.isDot1xMode) {
              authType.push({
                value: item,
                label: SSOConfig[item]['Nickname']
              })
            }
          } else {
            authType.push({ value: item, label: this.$t('auth.' + item) })
          }
        }
      })
      return authType
    },

    // 二维码可选认证方式
    qrcodeOptions() {
      const options = []

      if (this.isDot1xMode ||
        _.isNil(this.serveEntiretyConfig.server) ||
        _.isEmpty(this.serveEntiretyConfig.server)
      ) {
        return options
      }

      const allowAuthType = _.get(this.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType', [])

      const type = {
        NAC: 'UserAuthControl.UserAuthEnableQR',
        WeWork: 'WeWorkConfig.QRCode',
        DingTalk: 'DingTalkConfig.QRCode',
        FeiShu: 'FeiShuConfig.QRCode'
      }

      _.forEach(type, (item, index, arr) => {
        if (index === authTypes.NAC) {
          if (ztpUtils.whetherZTPMode()) {
            return
          }
          // NAC需要开启用户名密码认证
          if (allowAuthType.indexOf(authTypes.User) === -1) {
            return
          }
          const qrCode = parseInt(_.get(this.serveEntiretyConfig, 'server.' + item, 0))
          if (qrCode !== 1) {
            return
          }
        } else if (allowAuthType.indexOf(index) === -1) {
          return
        }

        options.push({
          value: index,
          label: this.$t('auth.' + index)
        })
      })
      console.log(options, 22222)
      return options
    },
    // 服务器配置
    serverInfo() {
      return {
        // 是否允许保存用户名
        clientSaveName: parseInt(_.get(this.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0)) === 1,
        // 是否允许保存密码
        clientSavePass: parseInt(_.get(this.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', 0)) === 1,
        verifyCode: parseInt(_.get(this.computeServeEntiretyConfig, 'AUTHPARAM.verifyCode', 0)) === 1,
        // 用户名密码邮箱辅助输入 802.1x后续再支持
        address: _.get(this.serveEntiretyConfig.server, 'Address', []) || []
      }
    },
    /**
     * 是否802.1x认证
     */
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    /**
     * 是否802.1x自动认证
     */
    dot1xAutoAuth() {
      return _.get(this.$route, 'query.ToDo') === 'dot1xAutoAuth' && this.isDot1xMode
    },
    /**
     * 是否强制802.1x认证
     */
    IsForceDot1xAuth() {
      return parseInt(_.get(this.$route, 'query.IsForceAuth')) === 1 && this.isDot1xMode
    },
    // 是否是敲端口模式
    isKnockPort() {
      return _.get(this.clientInfo, 'webSlot.isKnockPort', false)
    },
    // 是否展示切换到来宾按钮
    showSwitchGuest() {
      if (!authUtils.isOpenGuestAuth()) {
        return false
      }
      const sceneinfo = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo', {})
      const { UserType, GuestEntry } = sceneinfo
      if (parseInt(UserType) === 1 && parseInt(GuestEntry) === 1) { // 应用对象为员工开了展示来宾入口
        return true
      }
      if (parseInt(UserType) === 2) { // 应用对象为来宾
        return true
      }
      return false
    },
    forbidGuest() {
      return parseInt(_.get(this.clientInfo, 'detail.ForbidGuest', 0)) === 1
    }
  },
  watch: {
    'clientInfo.online': function(newVal, oldVal) { // 认证过程中推送离线组件变成404，loading可能未关闭导致无法点击刷新
      if (newVal !== oldVal && this.isShowNotFuond) {
        this.changeLoading(false)
      }
    }
  },
  created() {
    this.changeLoading(true)
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.authCancelEventBus()
  },
  methods: {
    ...mapMutations(['setClientInfo']),
    async init() {
      await commonUtil.checkDefaultPort()
      await this.updateSceen()
      this.authEventBus()
      this.AuthStart(true).then((res) => {
      // true代表认证成功,不取消
        if (!res) {
          this.changeLoading(false)
        }
      }).catch(() => {
        this.changeLoading(false)
        EventBus.$emit('client:show')
      })
    },
    async updateSceen() {
      const guestAuthBack = parseInt(_.get(this.$route, 'query.fromGuest', 0)) === 1
      const isFromSwitchAccount = parseInt(_.get(this.$route, 'query.switchUser', 0)) === 1
      const isStaffScene = parseInt(_.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.UserType', 2)) === 1 // 员工场景
      if (!isFromSwitchAccount && (guestAuthBack || (isStaffScene && _.get(this.serveEntiretyConfig, 'scene.SceneID', false)))) {
        return
      }
      await scene.getDeviceScene(1)
    },
    emitHandle(data) {
      console.log('emitHandle', data)
      switch (data.type) {
        case 'AuthSuccess':
          this.AuthSuccess()
          break
        case 'AuthFailed':
          this.AuthFailed()
          break
        case 'revoke:success':
          // 继续认证需要自动认证，手动撤销需要手动认证(如果是扫码的话需要刷新二维码)
          if (!data.value) {
            if (this.isAccountBind) {
              this.$refs.bindCom.submit()
            } else {
              if (this.isAdAuto) {
                this.AdDomainAutoLogin()
              } else if (this.activeType === 0) {
                this.$refs.auth.submitForm('ruleForm')
              } else {
                this.$refs.qrcode.submitForm('ruleForm')
              }
            }
          } else {
            if (this.isAdAuto) {
              this.AdDomainAutoLogin()
            } else {
              EventBus.$emit('revoke:refresh')
              this.changeLoading(false)
            }
          }
          break
        case 'revoke:show':
          this.revokeData = {
            ...this.revokeData,
            ...data.value
          }
          this.$refs.revoke.showRevoke(true)
          EventBus.$emit('client:show')
          break
        case 'revoke:cancel':
          EventBus.$emit('revoke:refresh')
          this.changeLoading(false)
          break
        case 'loading':
          this.changeLoading(data.value)
          break
        default:
          return false
      }
    },
    /**
     * 认证成功
     */
    async AuthSuccess() {
      this.changeLoading(true)
      const authType = this.activeType === 0 ? this.ruleForm.authType : this.qrcodeType
      const isAccountAuthType = this.activeType === 0 ? this.isAccountAuthType : false
      const res = await authIndex.authSuccess({
        ...this.authData,
        authType,
        isAccountAuthType
      })
      this.changeLoading(!!authIndex.config.IsServerIPChanged)
      console.log('认证成功')
      if (res && _.isObject(res) && res.BindAuth) {
        this.isAccountBind = true
      }
    },
    /**
     * 认证失败
     */
    AuthFailed() {
      // 切换账户失败、认证方式是用户名密码且启用别名
      // 则默认选择第一个认证方式
      const serverAlias = _.get(this.computeServeEntiretyConfig, 'User.AuthServerAlias')
      if (this.isSwitchUser && this.ruleForm.authType === authTypes.User && _.isString(serverAlias) && serverAlias !== '') {
        this.ruleForm.authType = _.get(this.authTypeOptions, '[0].value', authTypes.User)
      }
      EventBus.$emit('client:show')
      console.log('认证失败')
    },
    /**
     * 自动认证开始
     */
    async AuthStart(isFirst = false) {
      // 802.1x模式则获取802.1x配置
      console.log('isDot1xMode', this.isDot1xMode)
      if (this.isDot1xMode) {
        // 认证前安检结果
        const checkSuccess = _.get(this.$route, 'query.checkSuccess', false)
        // 802.1x配置是否为空
        const isEmptyclient = _.isEmpty(_.get(this.serveEntiretyConfig, 'client'))

        if (checkSuccess) {
          if (isEmptyclient) {
            await commonUtil.getDot1xInfo()
          }
        } else {
          await commonUtil.getDot1xInfo()
        }

        if (dot1xCommon.preSecCheck.is() && !checkSuccess) {
          console.log('需要802.1x认证前安检')
          processController.set({
            path: '/access/check',
            query: {
              CheckSource: 1
            }
          })
          return false
        }
        // 加载网卡列表
        await accessNetwork.init()
        if (!isFirst) {
          EventBus.$emit('Dot1x:networkList:refresh')
        }
      } else if (this.clientInfo.online) {
        // 802.1x从离线变成在线，然后切换到普通，防止设备信息不存在
        if (_.isEmpty(_.get(this.clientInfo, 'detail.DeviceID'))) {
          await commonUtil.detail()
        }
        // 在线才重新获取服务器配置
        await commonUtil.server()
      } else {
        if (!this.isKnockPort) {
          return false
        }
      }

      await authIndex.authReady()

      // 免认证
      if (this.isNoAuth()) {
        const noAuth = new NoAuth()
        const res = await noAuth.auth()
        if (res) {
          this.emitHandle({ type: 'AuthSuccess', value: '' })
          return true
        }
      }

      // 开启了AD域自动单点登录先判断能否登陆
      // 如果linux则不进行ad域单点登录
      if (!os_browser_info.os_Linux &&
          authIndex.config.adDomainAutoLoginFlag &&
          await this.AdDomainAutoLogin()) {
        return true
      }

      if (_.get(this.$route.query, 'switchUser', '0') === '1') {
        return this.switchUser({
          userName: this.$route.query.userName,
          password: this.$route.query.password
        })
      }
      if (this.isForceSso) {
        this.setAuthServerDefault(_.get(this.computeServeEntiretyConfig, 'AUTHPARAM.DefaultAuthType'))
        return true
      }
      return await this.AuthDefault()
    },
    /**
     * ad域单点登录
     */
    async AdDomainAutoLogin() {
      const adAuto = new AdAuto()
      const authRes = await adAuto.auth()
      if (parseInt(_.get(authRes, 'errcode', -1)) !== 0 && parseInt(_.get(authRes, 'errcode', -1)) === 21120030) {
        this.isAdAuto = true
        this.emitHandle({ type: 'revoke:show', value: { msg: authRes.errmsg, ...authRes.data }})
        return false
      }
      if (authRes === false) {
        return false
      }
      this.emitHandle({ type: 'AuthSuccess', value: '' })
      return true
    },
    /**
     * 认证默认值
     */
    async AuthDefault() {
      // 获取本地关于上次认证方式，本地保存的用户名密码相关的缓存
      const { authConfigFromWebAuthConfigTmp, authData, authTypeFromWebAuthTypeTmp } = await authIndex.lastAuthCache()
      _.assign(this.authData, authData)

      // 查询当前应该设置的默认认证方式为哪种默认认证[根据上次的认证以及服务器配置的认证信息]
      const defaultAuthType = authIndex.queryDefaultAuthType(authConfigFromWebAuthConfigTmp, authTypeFromWebAuthTypeTmp)

      this.setAuthServerDefault(defaultAuthType, authConfigFromWebAuthConfigTmp)

      // 查询是否符合自动认证的条件
      const allowAutoAuth = authIndex.whetherAllowAutoAuth(authConfigFromWebAuthConfigTmp, defaultAuthType, this.authData)
      console.log('【auth,是否符号自动认证的条件】=', allowAutoAuth)
      if (allowAutoAuth) {
        this.autoLogin()
      } else if (this.IsForceDot1xAuth) {
        EventBus.$emit('client:show')
      }
    },
    /**
     * 读取上次认证方式，并设置默认值
     * @param authType 上次认证方式
     * @param mode 小助手存储的数据
     */
    setAuthServerDefault(authType, authConfigFromWebAuthConfigTmp) {
      if (authType === authTypes.User) {
        if (_.get(this.computeServeEntiretyConfig, 'User.AuthServerAlias')) {
          // 如果缺省认证方式选的用户名密码验证，并且指定的认证服务器则查出首选认证服务器设为选中
          const firstAuthServer = _.get(this.computeServeEntiretyConfig, 'User.FirstAuthServer')

          // 如果记录了认证服务器则优先选中认证服务器
          const authServer = _.get(this.computeServeEntiretyConfig, 'User.AuthServer')
          let authServerList = []
          if (_.isString(authServer) && authServer !== '') {
            authServerList = authServer.split('|')
          }

          // 判断上一次认证的用户名密码类型是否还存在允许的用户名密码认证类型中,若不存在则使用首选用户名密码认证服务器
          if (!_.isEmpty(_.get(authConfigFromWebAuthConfigTmp, 'AuthServer')) && authServerList.indexOf(_.get(authConfigFromWebAuthConfigTmp, 'AuthServer')) !== -1) {
            authType = _.get(authConfigFromWebAuthConfigTmp, 'AuthServer')
          } else if (firstAuthServer) {
            authType = firstAuthServer

            // 如果不允许自动认证，则代表上次认证信息无效，清空
            console.log('不允许自动认证')
            _.assign(this.authData, {
              userName: '',
              password: '',
              verifyCode: '',
              userAutoLogin: false,
              userAutoAuth: false
            })
          }
        }
      }
      // 如果是来宾则选中第一个
      if (authType === authTypes.Guest) {
        authType = _.get(this.authTypeOptions, '[0].value')
      }
      // 如果是802.1x模式且认证方式是扫码则选中第一个
      if (authTypes.isQrCode(authType) && authIndex.isDot1xMode()) {
        authType = _.get(this.authTypeOptions, '[0].value')
      }

      if (authTypes.isQrCode(authType)) {
        this.activeType = 1
        this.qrcodeType = authType
        this.ruleForm.authType = _.get(this.authTypeOptions, '[0].value')
      } else {
        this.activeType = 0
        this.ruleForm.authType = authType
        this.qrcodeType = _.get(this.qrcodeOptions, '[0].value')
      }
    },
    /**
     * 自动登录
     */
    autoLogin(authType) {
      if (this.IsForceDot1xAuth) {
        EventBus.$once('Dot1x:networkList:inited', () => {
          console.info('802.1x强制自动登录：' + authType)
          this.$refs.auth.submitForm('ruleForm')
        })
        return true
      }

      this.shouwAutoLoginTip = true
      this.autoLoginTimeout = setTimeout(() => {
        this.shouwAutoLoginTip = false
        if (authType === authTypes.User) {
          console.info('确定可以执行User自动登录')
          this.$refs.auth.submitForm('ruleForm')
        } else {
          console.info('确定可以执行Ukey自动登录')
          this.$refs.auth.submitForm('ruleForm', true)
        }
      }, 2000)
    },
    /**
     * 取消自动登录
     */
    cancelAutoLogin() {
      this.shouwAutoLoginTip = false
      if (!_.isNil(this.autoLoginTimeout)) {
        clearTimeout(this.autoLoginTimeout)
        this.autoLoginTimeout = null
      }
    },
    async changeMode(mode = -1) {
      this.changeLoading(true)
      if (mode === -1) {
        const param = { AgentMode: this.isDot1xMode ? '0' : '1' }
        const switchRes = await agentApi.switchAgentMode(param)
        if (parseInt(_.get(switchRes, 'ASM.Result', 0)) === 0) {
          this.setClientInfo(_.merge({}, this.clientInfo, { basic: { AgentMode: param.AgentMode }}))
        } else {
          this.$message.error(this.$t('auth.switchModeErr'))
          this.changeLoading(false)
          return
        }
      }

      try {
        await this.AuthStart()
      } catch (e) {
        console.error(e)
      }
      this.$message.success(this.$t('auth.switchModeSuccess'))
      this.changeLoading(false)
    },
    changeLoading(value) {
      this.loading = value
    },
    /**
     * 用户名密码切换别名时清空对应参数
     */
    changeAuthType() {
      _.assign(this.authData, {
        userName: '',
        password: '',
        verifyCode: '',
        cert: '',
        uKeyName: '',
        uKeyPassword: '',
        uKeyPin: '',
        userAutoLogin: false,
        userAutoAuth: false,
        uKeyAutoLogin: false,
        uKeyAutoAuth: false
      })
    },
    /**
     * 切换账户
     */
    switchUser({ userName, password }) {
      const allowAuthType = _.get(this.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType', [])
      if (allowAuthType.indexOf(authTypes.User) === -1) {
        this.$message.error(this.$t('auth.noOpenUserAuth'))
        return false
      }

      this.isSwitchUser = true
      this.authData = {
        ...this.authData,
        ... {
          userName: Base64Decode(userName),
          password: Base64Decode(password)
        }
      }
      this.ruleForm.authType = authTypes.User
      if (!this.serverInfo.verifyCode) {
        this.$nextTick(() => {
          this.$refs.auth.submitForm('ruleForm')
        })
      } else {
        this.$message.error(this.$t('auth.switchCodeFail'))
        return false
      }
      return true
    },
    authEventBus() {
      this.authCancelEventBus()
      EventBus.$on('switchUser', (userInfo) => {
        console.log('EventBus:switchUser')
        this.switchUser(userInfo)
      })
      EventBus.$on('Dot1x:NewAgentMode', (mode) => {
        console.log('EventBus:NewAgentMode:' + mode)
        this.changeMode(mode)
      })
    },
    authCancelEventBus() {
      EventBus.$off('switchUser')
      EventBus.$off('Dot1x:NewAgentMode')
    },
    /**
     * 回车提交
     */
    keyDown(event) {
      if (parseInt(event.keyCode) !== 13) {
        return
      }
      // 非加载中
      if (!this.loading) {
        // 非802.1x离线则不处理
        if (!this.clientInfo.online && !this.isDot1xMode) {
          return
        }
        if (this.isAccountBind) {
          this.$refs.bindCom.submit()
        } else {
          this.$refs.auth.submitForm('ruleForm')
        }
      }
    },
    /**
     * 刷新整个页面，依旧在认证页
     */
    refresh() {
      window.location.reload()
    },
    async toGuest() {
      if (this.switching) {
        return
      }
      if (this.forbidGuest) {
        this.$message.error(this.$t('guestAuth.forbid'))
        return
      }
      this.switching = true
      const ret = await scene.getDeviceScene(2)
      this.switching = false
      if (!_.get(ret, 'SceneID', '')) { // 获取不到来宾场景
        return
      }
      if (parseInt(_.get(ret, 'IsAccess', 1)) === 0) { // 禁止接入
        processController.set('/access/message')
        return
      }
      processController.set('/access/guestAuth')
    },
    isNoAuth() {
      const isGuestScene = parseInt(_.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.UserType', 1)) === 2 // 来宾场景
      const sceneIsNoAuth = parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAuth', 1)) !== 1
      if (!sceneIsNoAuth) {
        return false
      }
      if (isGuestScene && this.isDot1xMode) {
        return false
      }
      if (isGuestScene && _.get(this.clientInfo, 'webSlot.isKnockPort', false) && !_.get(this.clientInfo, 'online', true)) { // 离线敲端口不走来宾免认证
        return false
      }
      return true
    },
    /**
     * 切换认证类型tab页
     * @param index
     * @returns {boolean}
     */
    switchActiveType(index) {
      // 如果激活类型activeType=0，并且authTypeOptions认证类型为空，说明只有扫码认证类型，那么就不进行切换
      if (index === 0 && _.isEmpty(this.authTypeOptions)) {
        return false
      }
      this.activeType = index
    }
  }
}
</script>
<style lang="scss">
.auth-wrap {
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  .f-state{
    height: calc(100% - 28px);
    min-height: calc(100% - 28px);
  }
  .sign-wrapper {
    color: $--color-primary;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .auth-tag {
      height: 28px;
      line-height: 28px;
      background: linear-gradient(360deg, $light-color 47%, $list-hover-bg);
      border-radius: 15px;
      padding: 0 16px;

      i {
        margin-right: 3px;
        font-size: 14px;
      }
    }
    .change-tag{
      color: $default-color;
      cursor: pointer;
      display: flex;
      font-size: 14px;
      line-height: 28px;
      i{
        color: $disabled-color;
        font-size: 12px;
        margin-left: 8px;
      }
    }
    .change-tag:hover{
      color: $--color-primary;
      i{
        color: $--color-primary;
      }
    }
    .disabled-change-tag{
      color: $disabled-color;
      cursor: not-allowed;
    }

    .disabled-change-tag:hover{
      color: $disabled-color;
      i{
        color: $disabled-color;
      }
    }

    .mode-wrapper {
      height: 28px;
      border: 1px solid $line-color;
      border-radius: 15px;
      background: $menu-bg;
      padding: 0 16px;
      line-height: 26px;
      color: $default-color;
      font-size: 14px;
      cursor: pointer;

      i {
        font-size: 12px;
      }
      .ml12{
        margin-left: 5px;
      }
      .mr12{
        margin-right: 5px;
      }
    }
  }

  .content {
    display: flex;
    // height: calc(100% - 28px);
    width: 100%;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    text-align: left;

    .tab-pane-nev {
      display: flex;
      width: 100%;
      align-items: center;
      height: 40px;
      .tab-wrapper {
        display: inline-block;
        font-size: 16px;
        line-height: 40px;
        color: $disabled-color;
        width: 50%;
        &:nth-of-type(1) {
          padding-right: 16px;
          text-align: right;
        }
        &:nth-of-type(2) {
          padding-left: 16px;
          text-align: left;
        }
        span{
          cursor: pointer;
          &.is-active,
          &:hover {
            font-size: 18px;
            font-weight: 500;
            color: $--color-primary;
          }
        }
      }
    }

    .tab-pane-content {
      margin-top: 20px;

      .rule-form {
        width: 360px;
        margin: 0 auto;

        .icon-leixing {
          font-size: 16px;
        }
      }

      &.scan-code-wrap {
        text-align: center;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .scan-select {
          width: 240px;
        }

        .ewm-box {
          margin: 20px auto;
          width: 240px;
          height: 240px;
          background: url('../../../assets/frame.png') no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            border-radius: 10px;
            opacity: 0.7;
          }
          .nac-ewm{
            width: 200px;
            height: 200px;
          }
        }

        .hint {
          color: $default-color;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
