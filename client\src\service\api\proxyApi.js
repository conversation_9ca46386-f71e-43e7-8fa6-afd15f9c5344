/*
 * @Author: <EMAIL>
 * @Date: 2021-08-04 15:35:59
 * @LastEditors: gening <EMAIL>
 * @LastEditTime: 2024-05-11 09:46:35
 * @Description: 所有通过代理转发的请求都在这个文件夹下
 */

import proxyAjax from '@/service/utils/proxyAjax'
import urlUtils from '@/render/utils/url'

const proxyApi = {
  getDevToolsProtocol() {
    return proxyAjax.get('http://localhost:9999/json/list')
  },
  // 获取后台设置的语言
  getLangSet() {
    return proxyAjax.get(proxyAjax.formatUrl('server/lang'), { showError: false, timeout: { time: 100, retry: 1 }})
  },

  // 获取远程的语言包
  getRemoteLang() {
    return proxyAjax.get(proxyAjax.formatUrl('server/parameter'))
  },

  // 获取设备信息接口
  getDeviceinfoProcess(params, config = { showError: true }) {
    return proxyAjax.post(proxyAjax.formatUrl('device/info'), params, config)
  },
  // 获取场景化信息
  getDeviceScene(params) {
    return proxyAjax.post(proxyAjax.formatUrl('scene/getDeviceScene'), params, { showError: false })
  },

  // 获取入网状态
  getDeviceNetStatus(params, config = { showError: true }) {
    return proxyAjax.post(proxyAjax.formatUrl('device/netStatus'), params, config)
  },
  // 获取全局服务器配置
  getGlobalServerDispose(params = {}, config) {
    const url = urlUtils.urlParamsStringfy('server/info', params)
    return proxyAjax.get(proxyAjax.formatUrl(url), config)
  },
  // 获取服务器状态
  getEnvStatus(params = {}, config = { showError: false }) {
    const baseURL = params.baseURL || proxyAjax.formatUrlPrefix()
    return proxyAjax.axiosGet(baseURL, proxyAjax.formatUrl('device/envStatus'), 100)
  },
  // 获取服务器环境
  getServerConf(data, config = { showError: true }) {
    const baseURL = proxyAjax.formatUrlPrefix()
    return proxyAjax.axiosGet(baseURL, proxyAjax.formatUrl('server/config'), data, config, 100)
  },
  // 获取二维码
  getScanInfo(data, config = { showError: true }) {
    return proxyAjax.post(proxyAjax.formatUrl('scan/info'), data, config)
  },
  // 获取是否已扫码
  getScanStatus(data) {
    return proxyAjax.post(proxyAjax.formatUrl('scan/status'), data)
  },
  // 认证
  authIndex(data, config = { showError: true }) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/index'), data, config)
  },
  // 获取在线、快速入网设备
  deviceOnline(data) {
    return proxyAjax.post(proxyAjax.formatUrl('device/online'), data)
  },
  // 强制下线
  netCutoff(data) {
    return proxyAjax.post(proxyAjax.formatUrl('net/cutoff'), data)
  },
  // 来宾审批列表
  guestInfoList(data) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/infoList'), data)
  },
  // 安检详情
  getCheckDetail(data) {
    return proxyAjax.post(proxyAjax.formatUrl('check/detail'), data)
  },
  // 发送短信
  smsSend(params) {
    return proxyAjax.post(proxyAjax.formatUrl('sms/send'), params)
  },
  authFlag() {
    return proxyAjax.post(proxyAjax.formatUrl('auth/flag'))
  },
  // 来宾自助申请(提交申请，查询申请状态，获取申请后的角色信息)
  guestSelfApply(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/self'), params)
  },
  // 生成来宾二维码
  drawQrCode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('scan/guestInfo'), params)
  },
  // 查询来宾二维码扫描状态
  getScanGuestRes(params) {
    return proxyAjax.post(proxyAjax.formatUrl('scan/guest'), params)
  },
  // 获取授权信息
  getAuthorization(params) {
    return proxyAjax.post(proxyAjax.formatUrl('server/authorization'), params)
  },

  getParameter() {
    return proxyAjax.get(proxyAjax.formatUrl('server/parameter'))
  },
  // 获取部门列表支持根据id查询直接子集和查所有
  getDepart(params) {
    return proxyAjax.post(proxyAjax.formatUrl('depart/list'), params)
  },
  // 获取部门列表
  getDepartTree(params) {
    return proxyAjax.post(proxyAjax.formatUrl('depart/tree'), params)
  },
  // 获取位置
  getLocation(params) {
    return proxyAjax.post(proxyAjax.formatUrl('user/locationList'), params)
  },
  // 获取设备类型
  getDeviceType(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/reginfo'), params)
  },

  getRegInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/reginfo'), params)
  },
  // 注册提交
  regSubmit(params) {
    return proxyAjax.post(proxyAjax.formatUrl('register/submit'), params, { showError: false })
  },
  getDefaultDepart(params) {
    return proxyAjax.post(proxyAjax.formatUrl('depart/default'), params)
  },
  scanIndex(params) {
    return proxyAjax.post(proxyAjax.formatUrl('scan/index'), params)
  },
  // 企业微信扫描是否成功接口
  getWeWorkUser(params, host = '') {
    return proxyAjax.post(host + proxyAjax.formatUrl('wework/user'), params)
  },
  // 钉钉扫描是否成功接口
  getDingTalkUser(params, host = '') {
    return proxyAjax.post(host + proxyAjax.formatUrl('dingtalk/user'), params)
  },
  // 飞书扫描是否成功接口
  getFeiShuUser(params, host = '') {
    return proxyAjax.post(host + proxyAjax.formatUrl('feishu/user'), params)
  },
  // 修改密码
  changePass(params) {
    return proxyAjax.post(proxyAjax.formatUrl('user/changePass'), params)
  },
  // 获取补丁详细信息
  getPatchDetailInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('patch/detail'), params)
  },
  // 断网
  cutoffDevice(params) {
    return proxyAjax.post(proxyAjax.formatUrl('net/cutoff'), params)
  },
  getRoleInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/roleinfo'), params)
  },
  getSceneInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/sceneinfo'), params)
  },
  // 获取ad域配置
  getDomainConfig(params) {
    return proxyAjax.post(proxyAjax.formatUrl('user/domainConfig'), params)
  },
  // 是否需要修改密码
  getUserFlag(params) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/config'), params)
  },
  // 设备注册状态
  getDeviceIsReg(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/isReg'), params)
  },
  // sso检查登录状态
  ssoCheckLoginState(params) {
    return proxyAjax.post(proxyAjax.formatUrl('sso/checkLoginState', 'passport'), params, { showError: false })
  },
  // ztp获取用户信息
  getZtpUser() {
    return proxyAjax.post(proxyAjax.formatUrl('ztpUser/userinfo'))
  },
  // 获取用户的资源列表
  getZtpResource(params, config = {}) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/list'), params, config)
  },
  // 取消收藏
  cancelCollect(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/cancelCollect'), params)
  },
  // 收藏
  sourceCollect(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/collect'), params)
  },
  // 修改密码获取账号
  getAtpAccount(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/getAccount'), params)
  },
  // 保存账号
  saveAtpAccount(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/saveAccount'), params)
  },
  // 检查资源
  checkSource(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/checkStatus'), params, { showError: false })
  },
  // 获取跳转地址
  getAccessUrl(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/accessUrl'), params)
  },
  // 添加日志
  sourceRecordLog(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/recordLog'), params)
  },
  // 获取日志列表
  getWorkLog(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpUser/loglist'), params)
  },
  // 校验账户是否激活了令牌
  checkActivationKey(params) {
    return proxyAjax.post(proxyAjax.formatUrl('OTP/checkActivationKey'), params)
  },
  // 通过设备ID获取在线表的用户ID
  getNacOnlineUserID(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/userId'), params)
  },
  policyCheck(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/policyCheck'), params)
  },
  // 获取来宾可访问域和来宾码
  reqGeustNetcode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/netcode'), params)
  },
  // 扫码接待来宾
  reqReceptionGuest(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/scansubmit'), params)
  },
  // 来宾必填信息提交
  reqGuestRequired(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/self'), params)
  },
  // 来宾放开网络
  reqGuestSubmit(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/submit'), params)
  },
  // 团队申请
  reqBatchNetcode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/batchNetcode'), params)
  },
  // 来宾待审批列表
  reqApproveInfoList(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/approveInfoList'), params)
  },
  // 审批来宾
  reqAuditGuest(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/self'), params)
  },
  // 单个来宾在线管理
  reqSingleInfoList(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/singleInfoList'), params)
  },
  // 团队来宾在线管理
  reqBatchInfoList(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/batchInfoList'), params)
  },
  // 团队下线
  reqTeamCutoff(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/teamCutoff'), params)
  },
  getZtpResourceInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/accessUrl'), params, { showError: false })
  },
  // 忘记密码
  forgetPass(params) {
    return proxyAjax.post(proxyAjax.formatUrl('user/forgetpass'), params)
  },
  // 给指定账户发送验证码
  sendUserCode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('sms/sendusercode'), params)
  },
  // 访客预约
  guestReserve(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/reserve'), params)
  },
  // 获取预约信息
  reqReserveList(params) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/unusedList'), params)
  },
  // 取消预约
  reqCancelReserve(params, config = { showError: false }) {
    return proxyAjax.post(proxyAjax.formatUrl('guest/undo'), params, config)
  },
  // 是否能访问资源列表
  getNetInfo(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/netInfo'), params)
  },
  // 获取高级动态身份认证的相关配置信息
  getResourceAuthConf(config = { showError: false }) {
    return proxyAjax.post(proxyAjax.formatUrl('resource/config'), {}, config)
  },

  // 高级动态身份认证里面获取当前认证的用户名
  getResourceAuthUser(params) {
    return proxyAjax.post(proxyAjax.formatUrl('resource/user'), params)
  },
  resourceAuth(params) {
    return proxyAjax.post(proxyAjax.formatUrl('resource/auth'), params)
  },
  getResourceAuthQrcode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('resource/qrcode'), params)
  },

  queryScanResult(params) {
    return proxyAjax.post(proxyAjax.formatUrl('resource/qrcode'), params)
  },

  // sso退出登录
  ssoLogout(params) {
    return proxyAjax.post(proxyAjax.formatUrl('sso/logout', 'passport'), params, { showError: false })
  },

  // 资源申请记录
  getResApplyLog(params) {
    const url = urlUtils.urlParamsStringfy('ztpResource/getApplyResLogList', params)
    return proxyAjax.get(proxyAjax.formatUrl(url))
  },

  // 获取消息列表
  getResMsg(params, config = { showError: true }) {
    const url = urlUtils.urlParamsStringfy('userNotice/getNoticeList', params)
    return proxyAjax.get(proxyAjax.formatUrl(url), config)
  },
  // 判断零信任点数是否充足
  getZtpUserExceed(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/ztpUserExceed'), params)
  },
  // 可申请的资源列表
  getApplyList(params) {
    const url = urlUtils.urlParamsStringfy('ztpResource/getApplyResList', params)
    return proxyAjax.get(proxyAjax.formatUrl(url))
  },
  reqApply(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpResource/apply'), params, { showError: false })
  },
  getQrCode(params) {
    return proxyAjax.post(proxyAjax.formatUrl('server/getQrCode'), params)
  },
  getGateway(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ZtpGateway/getGateway'), params)
  },
  writelog(params) {
    return proxyAjax.post(proxyAjax.formatUrl('server/writelog'), params)
  },
  getResGateway(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpGateway/getResGateway'), params)
  },
  authFail(params) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/fail'), params)
  },
  // 记住密码
  rememberFSARPassword(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpGateway/rememberFSARPassword'), params)
  },
  // 获取钉钉qrcode
  getDingTalkQrCode(params) {
    const url = urlUtils.urlParamsStringfy('user/qrcode/generate', params)
    return proxyAjax.get(proxyAjax.formatUrl(url))
  },
  // 获取钉钉扫码状态和结果
  getDingTalkScanRes(params) {
    return proxyAjax.post(proxyAjax.formatUrl('login/login_with_qr'), params)
  },
  // 重启客户端
  reStartApp(params) {
    return proxyAjax.post(proxyAjax.formatUrl('device/restartAgent'), params)
  },
  ztpDivideList() {
    return proxyAjax.post(proxyAjax.formatUrl('ztpDivide/list'), {})
  },
  ztpDivideChange(params, showError = false) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpDivide/change'), params, { showError })
  },
  getAuthResult(params, showError = false) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/result'), params, { showError })
  },
  ssoAuthCall(params) {
    const url = urlUtils.urlParamsStringfy(`sso/auth`, params)
    return proxyAjax.get(proxyAjax.formatUrl(url, 'passport'), { showError: false })
  },
  skipBindApi(params) {
    return proxyAjax.post(proxyAjax.formatUrl('auth/skipBind'), params)
  },
  ztpDivideCheckStatus(params) {
    return proxyAjax.post(proxyAjax.formatUrl('ztpDivide/checkStatus'), params, { showError: false })
  }
}

export default proxyApi
