/* eslint-disable new-cap */
/* eslint-disable no-global-assign */
/* eslint-disable no-native-reassign */
import Vue from 'vue'
import { initI18n, i18n } from '@/render/lang'
import Element from 'element-ui'
import ipcApi from '@/service/utils/ipcPlugin'
import msg from '@/render/components/msg'
import VueClipboard from 'vue-clipboard2'
import errorHandler from '@/render/utils/error'
import eruda from '@/render/utils/debugger/eruda'
import { initThemeColor } from './theme'
import { isQtWebChannel } from '@/service/utils/ipcPlugin/utils/index'
import { dialogTip } from '@/render/components/dialogTip'
import { isElectron } from '@/service/utils/ipcPlugin/utils/index'

import qs from 'qs'
import agentApi from '@/service/api/agentApi'

class Bootstrap {
  constructor(callback = _ => { }) {
    this.initAssuiDebug()
    this.initErrHandler()
    this.initEruda()
    // this.initQtPlugin()
    this.initI18n()
    this.initElement()
    this.initVuePlugIn()
    this.initFunComponent()
    callback(i18n)

    return this
  }

  // 加载qt通讯插件。绑定到vue的原型上(this.$ipcSend和this.$ipcOn）
  initQtPlugin() {
    Vue.use(ipcApi)
  }

  // 捕获错误
  initErrHandler() {
    if (process.env.NODE_ENV !== 'development') {
      errorHandler.init()
    }
  }

  // 初始Element(ui库初始化，并且和i18n国际化绑定)
  initElement() {
    Vue.use(Element, {
      i18n: (key, value) => i18n.t(key, value)
    })
  }

  // 初始化在线的代码调试工具
  initEruda() {
    // xp下的qtWebkit不需要这个客户端，因为可以直接按f12打开调试工具
    if (!isQtWebChannel()) {
      return false
    }
    eruda.init()
    document.getElementById('eruda').style = 'display:none'
  }

  // 初始化多语言包插件(vue插件)
  async initI18n() {
    initI18n()
  }

  // 初始化其他vue的组件
  initVuePlugIn() {
    Vue.use(msg)
    // 剪贴板组件
    Vue.use(VueClipboard)
  }

  // 重写console.debug 记录日志到小助手文件内，只支持一个参数（小助手）WLog:为标记
  initAssuiDebug() {
    console.debug = (function(oriInfoFunc) {
      return function(str) {
        // electron环境记录到日志文件里面
        if (isElectron()) {
          try {
            agentApi.elelWriteLog(`(fromeRender-debug):${str}`)
          } catch (error) {
            console.error('electron日志记录错误:')
            console.error(error)
          }
        }
        oriInfoFunc.call(console, `WLog:${str} `)
      }
    })(console.info)

    if (isElectron()) {
      console.error = (function(oriInfoFunc) {
        return function(str) {
          // electron环境记录到日志文件里面
          try {
            agentApi.elelWriteLog(`(fromeRender-error):${str}`)
          } catch (error) {
            console.error('electron日志记录错误:')
            console.error(error)
          }
          oriInfoFunc.call(console, `WLog:${str} `)
        }
      })(console.error)
    }
  }
  // 挂载全局函数组件
  initFunComponent() {
    Vue.prototype.$dialogTip = dialogTip
  }
  // afterCallback
  bindAfter() {
    initThemeColor()
  }

  initPlafrom() {
    const query = qs.parse(location.search.substring(1))
    let platform = _.get(query, 'platform', '')

    alert(platform)

    if (platform) {
      if (platform === 'mac') {
        platform = 'mac'
      } else if (platform === 'linux') {
        platform = 'linux i686'
      } else {
        platform = 'win64'
      }
      navigator.__defineGetter__('platform', function() {
        return platform
      })
    }
  }
}

export default Bootstrap
