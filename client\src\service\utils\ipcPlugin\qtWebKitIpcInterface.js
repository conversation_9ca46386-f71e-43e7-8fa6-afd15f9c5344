/*
 * @Author: <EMAIL>
 * @Date: 2022-11-15 15:32:27
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-12 09:31:04
 * @Description: file content
 */

class IpcInterface {
  // 这个callback可以用来做判断，是否和QT通讯连接已经完成。
  constructor(cb = () => { }) {
    this.send = this.bindSend()
    this.on = this.bindOn()
    cb()
  }

  bindSend() {
    return ({ module, action, strSerial, data = '' }) => {
      return new Promise((_, reject) => {
        // QTwebkit的方法是直接注入到JS的window里面
        const QtServer = window

        if (typeof QtServer[module] === 'undefined') {
          console.error('[SENDER]: 该module ' + module + ' 不存在 !')
          return reject(new Error('[SENDER]: 该module' + module + ' 不存在 !'))
        }

        if (typeof QtServer[module][action] === 'undefined') {
          console.error('[SENDER]: 该action ' + action + ' 不存在 !')
          return reject(new Error('[SENDER]: 该action ' + action + ' 不存在 !'))
        }

        if (typeof QtServer[module][action] !== 'function') {
          return reject(
            new Error(
              typeof QtServer[module][action].connect === 'function'
                ? `[SENDER]: ${action} 不是一个QT信号或者QT方法`
                : `[SENDER]:  action : ${action} 不是一个QT函数 !`
            )
          )
        }

        try {
          if (strSerial === -1) {
            QtServer[module][action](data)
          } else {
            QtServer[module][action](strSerial, data)
          }
        } catch (error) {
          return reject(new Error('[SENDER]: 该action' + action + ' 执行错误 !'))
        }
      })
    }
  }

  bindOn() {
    // 挂载在window上
    // ToRequestWeb是客户端主动通知我们的消息;
    // ResponseToWeb是客户端被动响应我们的请求消息
    return (module, event, callback) => {
      module = window
      module[event] = callback
    }
  }
  // QTwebkit监听信号的方式，是QT可以直接调用JS的方法

  off() {
    console.log('尚未初始化！')
  }
}

export default IpcInterface
