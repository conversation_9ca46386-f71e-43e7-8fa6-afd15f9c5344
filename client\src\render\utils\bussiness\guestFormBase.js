
/*
  config:{
    formateRule: function 校验规则-必传
    formatePlaceholder: function  生成placeholder 必传
  }
*/
export default class GuestForm {
  constructor(params, config = {}) {
    if (!params || typeof params !== 'object' || !config || typeof config.formateRule !== 'function' || typeof config.formatePlaceholder !== 'function') {
      console.log('guestForm参数不合法')
    }
    const { formateRule, formatePlaceholder, formateFiledsIcon } = config
    this.formateRule = formateRule
    this.formatePlaceholder = formatePlaceholder
    this.formateFiledsIcon = formateFiledsIcon
    this.form = {}
    this.formFields = []
    this.config = config
    this.sortFields = ['AllowRegionIDs', 'AllowTime', 'GuestStartTime', 'GuestRequireUser', 'GuestRequireUnit', 'GuestRequireTel', 'GuestRequireReason', 'GuestRequireExpand', 'IsNeedAudit']
    if (config.sortFields) {
      this.sortFields = config.sortFields
    }
    this.init(params)
  }
  init(params) {
    const formParms = this.format(params)
    this.createFormModel(formParms) // 生成form 建立表单项key和value的关系
    this.createFormField(formParms) // 用于遍历生成表单
  }
  // 对接口返回的和来宾认证的参数进行格式化
  format(formFieldsParam) {
    const formParams = []
    Object.keys(formFieldsParam).forEach(key => {
      if (this.isNeedItem(key)) {
        // 扩展字段
        const arr = formFieldsParam[key].split('|')
        const item = {}
        const _key = key && key.replace('_en', '')
        if (key.indexOf('GuestRequireExpand') !== -1) {
        // 如果第一个元素等于2表示隐藏,等于0表示选填,1表示必填
          if (parseInt(arr[0]) !== 2) {
            item['Column'] = _key
            item['Select'] = this.formateSelect(_key, arr[0] || 0)
            item['Title'] = arr[1] || ''
            item['InputType'] = arr[2] || 'text'
            item['Options'] = !_.isEmpty(arr[3]) ? arr[3].split('#') : ''
            item['Remark'] = arr[4] || ''
            item['Rule'] = arr[5] || ''
          }
        } else {
        // const arr = formFields[key].split('|')
        // 如果第一个元素等于2表示隐藏,等于0表示选填,1表示必填
          if (parseInt(arr[0]) !== 2) {
            item['Column'] = _key
            item['Select'] = this.formateSelect(_key, arr[0] || 0)
            item['Title'] = arr[1] || ''
            item['Remark'] = arr[2] || ''
            item['Rule'] = arr[3] || ''
            item['InputType'] = arr[4] ? arr[4] : key.indexOf('GuestRequireReason') > -1 ? 'textarea' : 'text'
            item['Options'] = !_.isEmpty(arr[5]) ? arr[5].split('#') : ''
          }
        }
        if (_.get(item, 'Column')) {
          formParams.push(item)
        }
      }
    })
    return formParams
  }
  // 生成表单项
  createFormModel(formParms) {
    formParms.forEach(item => {
      if (_.get(item, 'InputType') === 'checkbox') {
        this.form[this.formateFiledsKey(_.get(item, 'Column'))] = []
      } else {
        this.form[this.formateFiledsKey(_.get(item, 'Column'))] = ''
      }
    })
  }
  // 生成cloumn和接口对应的表单id(@完全是提交请求接口和提交接口字段不统一造成的)
  formateFiledsKey(column) {
    if (column === 'GuestRequireReason') {
      return 'content'
    }
    if (column === 'GuestRequireTel') {
      return 'guestmobile'
    }
    if (column === 'GuestRequireUnit') {
      return 'guestcompany'
    }
    if (column === 'GuestRequireUser') {
      return 'guestname'
    }
    if (column === 'AuditRequireUser') {
      return 'username'
    }
    if (column === 'AuditRequireTel') {
      return 'usermobile'
    }
    return column
  }
  // 对需要循环的表单的icon,placeholder,rule。校验规则等进行处理
  createFormField(formParms) {
    this.formFields = formParms.map(item => {
      item['icon'] = this.formateFiledsIcon && this.formateFiledsIcon(item)
      item['Placeholder'] = this.formatePlaceholder(item)
      item['Rule'] = this.formateRule(item, this.config.isApply)
      item['Options'] = this.formateOptions(item)
      item['Name'] = this.formateFiledsKey(_.get(item, 'Column'))
      return item
    })
    this.sortFormFields()
  }
  formateOptions(item) {
    // 如果是扩展字段的select，还需要进行转换以兼容element的select组件
    if (_.get(item, 'InputType') === 'select' && _.get(item, 'Options')) {
      return this.transformArrToArrObj(_.get(item, 'Options'), ['label', 'id'])
    } else {
      return _.get(item, 'Options')
    }
  }
  formateSelect(key, val) {
    const isApply = this.config.isApply
    const requiredKey = ['AllowRegionIDs', 'AllowTime', 'GuestStartTime']
    if (requiredKey.indexOf(key) > -1) {
      return '1'
    }
    if (isApply) {
      return '0'
    }
    return val
  }
  transformArrToArrObj(array, keys) {
    if (!_.isArray(array)) {
      throw new Error(`转换对象${array}必须为数组`)
    }
    let newArr = []
    newArr = array.map(item => {
      const newItem = {}
      keys.forEach(keys => {
        newItem[keys] = item
      })
      return newItem
    })
    return newArr
  }
  // 对动态form表单进行排序(@因为后台接口返回没有排序，只能在这里排序)
  sortFormFields() {
    const formFields = _.cloneDeep(this.formFields)
    this.formFields = []
    this.sortFields.forEach(key => {
      let item
      if (key !== 'GuestRequireExpand') {
        item = _.filter(formFields, function(item) {
          return item.Column === key
        })
      } else {
        item = _.filter(formFields, function(item) {
          return item.Column.indexOf('GuestRequireExpand') !== -1
        })
      }
      this.formFields.push(...item)
    })
  }
  isNeedItem(key) {
    if (this.config.lang === 'zh' && key.indexOf('_en') > -1) {
      return false
    }
    if (this.config.lang === 'en' && key.indexOf('_en') === -1) {
      return false
    }
    if (key.indexOf('GuestRequireExpand') !== -1) {
      return true
    }
    const unitKey = key && key.replace('_en', '')
    if (this.sortFields.indexOf(unitKey) > -1) {
      return true
    }
    return false
  }
}
