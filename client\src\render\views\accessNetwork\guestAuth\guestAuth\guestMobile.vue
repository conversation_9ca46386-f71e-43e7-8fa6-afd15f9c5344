<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-14 15:19:02
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-01-06 14:18:07
 * @Description: 短信认证==>
-->
<template>
  <div id="f-moblie-auth">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="20px">
      <el-form-item prop="smsLogin.authPhone">
        <i class="iconfont icon-shoujihao input-icon" />
        <el-input
          id="ui-guest-auth-input-iphone"
          v-model="ruleForm.smsLogin.authPhone"
          :placeholder="$t('guestAuth.guest.info_10')"
        />
      </el-form-item>
      <div class="specal-form-item">
        <div class="form-item-wrapper">
          <el-form-item prop="smsLogin.code">
            <i class="iconfont icon-yanzhengma" />
            <el-input
              id="ui-guest-auth-input-check_code"
              v-model="ruleForm.smsLogin.code"
              class="code-inp"
              :placeholder="$t('guestAuth.guest.info_11')"
            />
          </el-form-item>
        </div>

        <!--发送验证码按钮，包括倒计时-->
        <template>
          <div
            v-if="!smsCodeIsDisabled"
            id="ui-guest-auth-div-get_check_code"
            class="sms-wrapper public-line-medium-btn"
            @click="sendPhoneCode(ruleForm.smsLogin.authPhone)"
          >
            {{ $t("guestAuth.guest.info_12") }}
          </div>
          <div v-else class="sms-wrapper sms-disabled public-line-medium-btn">
            {{ getSmsCodeButtonMsg }}
          </div>
        </template>
      </div>
    </el-form>
  </div>
</template>

<script>
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'
import { mapState, mapMutations } from 'vuex'
import Mobile from '@/render/utils/auth/mobile'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'

export default {
  data() {
    return {
      getSmsCodeLoading: false,
      nowTime: 0, // 倒计时
      maxGetSmsCodeInterval: 30,
      submitLoading: false,
      ruleForm: {
        smsLogin: {
          authPhone: '',
          code: ''
        }
      },
      rules: {
        smsLogin: {
          authPhone: [{ validator: this.checkPhone, trigger: 'blur' }],
          code: [
            { required: true, validator: this.codeValidator, trigger: 'blur' }
          ]
        }
      }
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo']),
    // 验证码按钮文字
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    },
    // 是否禁用获取验证码按钮
    smsCodeIsDisabled() {
      if (this.getSmsCodeLoading) {
        return true
      }
      return this.nowTime > 0
    },
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }
  },
  watch: {
    // 中英文切换时重新校验失败条目
    '$i18n.locale': function() {
      this.$refs['ruleForm'].fields.forEach(item => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    }
  },
  methods: {
    ...mapMutations(['setClientInfo', 'setAuthInfo']),
    checkPhone(rule, value, callback) {
      if (!regular.phone(value)) {
        callback(new Error(this.$t('auth.phoneValidateErr')))
      }
      callback()
    },
    codeValidator(rule, value, callback) {
      if (!regular.phone(value) || value.length < 6) {
        return callback(
          new Error(this.$t('guestAuth.guest.phoneCodeValidateErr'))
        )
      } else {
        callback()
      }
    },
    // 获取验证码
    async sendPhoneCode(phone) {
      this.$refs.ruleForm.validateField('smsLogin.authPhone', async(res) => {
        if (_.isEmpty(res)) {
          this.getSmsCodeLoading = true
          const mobile = new Mobile()
          const res = await mobile.smsSend({ phone, isGuestAuth: 1 })
          this.getSmsCodeLoading = false
          if (res) {
            this.countDown(true)
          }
        }
      })
    },
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    // 提交认证
    submit() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(async(valid) => {
          if (valid) {
            // 敲门-无需敲门或者敲门成功返回true
            const knockParam = ['Guest', '', this.ruleForm.smsLogin.authPhone, this.ruleForm.smsLogin.code]
            const knockIsSuccess = await commonUtil.knockPort(knockParam)
            if (!knockIsSuccess) {
              resolve()
              return
            }
            const apiParam = {
              type: 'Mobile',
              mobile_phone: this.ruleForm.smsLogin.authPhone,
              deviceid: this.deviceid,
              check_code: this.ruleForm.smsLogin.code,
              isGuestAuth: 1
            }
            // 提交认证
            const ret = await proxyApi.authIndex(apiParam)
            resolve(ret)
          } else {
            resolve()
          }
        })
      })
    }
  }
}
</script>
<style lang="scss">
#f-moblie-auth {
    .specal-form-item {
      width: 380px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .form-item-wrapper {
        width: 246px;
        .el-form-item{
          width: 100%!important;
        }
      }
      .sms-wrapper {
        width: 120px;
        height: 40px;
        line-height: 38px;
      }
      .sms-disabled {
        border-color: $line-color;
        background: $line-color;
        color: $disabled-color;
        cursor: not-allowed;
      }
    }
}
</style>
