<template>
  <div class="result-detail-modle">
    <p class="model-title">
      {{ $t('accessNetwork.securityCheck.info_16')
      }}<i
        :class="
          modelIsOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
        "
        @click="modelIsOpen = !modelIsOpen"
      />
    </p>
    <el-collapse-transition>
      <div v-show="modelIsOpen" class="result-model-content">
        <div class="result-content">
          <div class="left-wrapper">
            <i
              class="iconfont"
              :class="{
                'icon-anjianheguixiang success': resultData.checkType === 0,
                'icon-guanjianxiangbuhegui warn': resultData.checkType === 1,
                'icon-guanjianxiangbuhegui err': resultData.checkType === 2
              }"
            />
            <span :id="`ui-check-${checkName}-span-tip`">{{ resultData.Message }}</span>
          </div>
          <button v-if="resultData.checkType!==0 && showBtn" :class="['btn-small', hasFixed ? 'btn-disabled': 'public-medium-btn', loading ? 'loading-disable': '']" @click="fixHandle">
            <i v-show="loading" class="el-icon-loading" />
            {{ fixBtn }}
          </button>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>

export default {
  props: {
    resultData: {
      type: Object,
      default: function() {
        return {
          checkType: 1,
          Message: ''
        }
      }
    },
    btnText: {
      type: String,
      default: ''
    },
    hasFixed: {
      type: Boolean,
      default: false
    },
    showBtn: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    checkName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      modelIsOpen: true
    }
  },
  computed: {
    fixBtn() {
      return this.btnText || this.$t('check.DomainUser.js_1_d')
    }
  },
  methods: {
    fixHandle() {
      this.$emit('fix')
    }
  }
}

</script>

