/* eslint-disable new-cap */
/*
 * @Author: <EMAIL>
 * @Date: 2022-11-23 10:51:31
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-11-29 19:39:24
 * @Description: 用indexedDB去存储发送的请求
 */

import { EventBus } from '@/render/eventBus'
const networkListUtils = {
  list: [],
  // 新增
  add(item) {
    const onOff = localStorage.getItem('debugg_network_on_off')
    if (parseInt(onOff) !== 1) {
      return
    }
    // 查过5000w条的话，进行清除
    if (this.list.length >= 5000) {
      this.clearAll()
    }

    this.list.push(item)
    EventBus.$emit('refreshDebuggerNetwork', { type: 'add', item })
  },

  // 更新
  update(item) {
    // 找到对应的索引，然后更新了
    const index = this.list.findIndex(single => {
      return single.key === item.key
    })

    if (index !== -1) {
      this.list[index] = item
    }

    EventBus.$emit('refreshDebuggerNetwork', { type: 'update', item })
  },
  clearAll() {
    this.list = []
  }

}

export default networkListUtils
