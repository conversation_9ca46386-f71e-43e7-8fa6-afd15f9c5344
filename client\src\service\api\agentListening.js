// 全局监听客户端消息
import agentApi from '@/service/api/agentApi'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import store from '@/render/store'
import router from '@/render/router'
import authIndex from '@/render/utils/auth'
import { EventBus } from '@/render/eventBus'
import menuUtils from '@/render/utils/bussiness/menuUtil'
import { asyncFileToken } from '@/render/utils/token'
import gateWayUtils from '@/render/utils/bussiness/gateWayUtils'
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'

/**
  * 刷新回到首页
  */
const goToIndex = () => {
  location.href = _.replace(_.replace(location.href, location.search, ''), location.hash, '')
}

// ip发生变动的时候调用
const ipRenew = async() => {
  await commonUtil.basic()
  commonUtil.detail()
}

// token发生变动的时候调用
const tokenReNew = () => {
  asyncFileToken()
  commonUtil.getWebGateWay(3000)
}
/**
 * 管理员要求立即进行认证安检
 * 如果当前是入网状态页面，则重新获取入网状态
 */
const securityCheck = () => {
  if (router.currentRoute.path === '/access/message') {
    commonUtil.netRet()
  }
}

// 入网状态发生变化的时候进行通知
const netStateChanged = () => {
  commonUtil.detail().then(async() => {
    await commonUtil.netRet()
  })
}

// 第三方客户端联动策略变更通知
const refreshThirdLinkageMenu = async() => {
  const linkageMenus = await menuUtils.getLinkageMenu()
  EventBus.$emit('refreshThirdLinkageMenu', linkageMenus || [])
}

/**
 * 在线离线处理
 * @param {*} online
 * @returns
 */
const onlineStatusChange = (qMsgRequest) => {
  const online = _.get(qMsgRequest, 'result.ASM.IsOnline')
  const oldOnline = _.get(store, 'state.clientInfo.basic.IsOnline', 0)
  console.log('新旧值', parseInt(online), parseInt(oldOnline), new Date().toLocaleString())
  const oldPortConnect = _.get(store, 'state.clientInfo.webSlot.portConncet')
  const isOnline = parseInt(online) === 1
  if (!authIndex.isDot1xMode() && oldPortConnect !== isOnline) { // 多个地方会更新isOnline这里以推送的为准
    commonUtil.setWebSlot({ portConncet: isOnline })
  }
  if (parseInt(online) !== parseInt(oldOnline)) {
    if (parseInt(online)) {
      store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { basic: { IsOnline: 1 }}))
      commonUtil.getWebGateWay()
      return onlineProcessing()
    } else {
      store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { basic: { IsOnline: 0 }}))
      const oldVpnStatus = _.get(store, 'state.gateWayInfos.VPNStatus', 0)
      store.commit('setGateInfos', { state: 2, gateWayMap: {}, total: 0, VPNStatus: oldVpnStatus })
      return offlineProcessing()
    }
  }
}

//  802.1x模式下认证页面在线变化不处理，由其处理
const onlineProcessing = () => {
  if (!(router.currentRoute.path === '/access/message' || (router.currentRoute.path === '/access/auth' && authIndex.isDot1xMode()))) {
    Promise.all([commonUtil.server(), commonUtil.detail()]).then(() => {
      commonUtil.netRet()
    })
  }
  return true
}

// 网关状态处理
const gateWayhandle = (qMsgRequest) => {
  gateWayUtils.gateWayhandle(qMsgRequest)
  EventBus.$emit('receiveGateway', gateWayUtils.analyzeMsg(_.get(qMsgRequest, 'result.ASM.Data')))
}

const ChangeResGroup = async(qMsgRequest) => {
  if (!_.get(store.state.clientInfo, 'online', false)) {
    console.debug(`离线不执行ChangeResGroup回调`)
    return false
  }
  const CurGroupID = _.get(qMsgRequest, 'result.ASM.CurGroupID')
  if (!CurGroupID && parseInt(CurGroupID) !== 0) {
    return
  }
  const curIsSourcePage = router.currentRoute.path === '/source/list'
  await sourceListUtil.clientChangeDivide(_.get(qMsgRequest, 'result.ASM.CurGroupID'), curIsSourcePage)
  if (curIsSourcePage) { // 当前不在资源列表页面就不要通知资源页面刷新数据了
    EventBus.$emit('ChangeResGroup', _.get(qMsgRequest, 'result.ASM.CurGroupID'))
  }
}

/**
 * 离线处理
 * @returns
 */
const offlineProcessing = () => {
  return true
}

// 智能802.1x模式切换
const agentModeChanged = (qMsgRequest) => {
  const mode = parseInt(_.get(qMsgRequest, 'result.ASM.NewAgentMode'))
  console.log('agentModeChanged', mode)
  if (mode < 0 || mode > 1) {
    return
  }
  // 如果是认证界面，则需要重新渲染，否则修改当前模式值即可
  store.commit('setClientInfo', _.merge({}, store.state.clientInfo, { basic: { AgentMode: _.toString(mode) }}))
  if (router.currentRoute.path === '/access/auth') {
    EventBus.$emit('Dot1x:NewAgentMode', mode)
  }
}

const reopen = (params) => {
  EventBus.$emit('reopen', params)
}

// electron独有的信号通知。一般是唤醒客户端的时候通知的
const OpenNewUrl = (qMsgRequest) => {
  let url = _.get(qMsgRequest, 'result.ASM.Url')
  if (!_.isEmpty(url) && url.includes('/index.html')) {
    url = url.replace('/index.html', 'index.html')
    window.location.href = url
    agentApi.evokeAndFocus()
  }
}

const resourceNotice = (qMsgRequest) => {
  const Msg = parseInt(_.get(qMsgRequest, 'result.ASM.Msg', 0))
  store.commit('setMsgCenter', { ...store.state.msgCenter, ...{ unRead: Msg }})
}

const agentMsgListening = {
  // WhatToDo与对应处理函数处理. ==>归属SrvMsgNotice的大类
  msgMap: {
    // 断网
    ShutdownNetwork: goToIndex,
    OfflineNotice: goToIndex,
    // 查询获取IP
    IpRenew: ipRenew,
    // 自动认证更新了token
    TokenReNew: tokenReNew,
    // 修改计算机名
    ModifyDevName: ipRenew,
    // 修改计算机IP等信息
    ModifyNetInfo: ipRenew,
    // 管理员要求立即进行认证安检
    DoSecurityCheck: securityCheck,

    NoticeNetStateChanged: netStateChanged,
    // 第三方客户端联动菜单刷新
    RefreshThirdLinkageMenu: refreshThirdLinkageMenu,
    // 消息中心消息推送
    ResourceNotice: resourceNotice
  },
  strWhatToDoExtend: {},
  // 初始化监听
  init() {
    agentApi.toRequestWeb(null, (strWhatToDo, qMsgRequest) => {
      console.log('【Listen Request】接受到客户端消息通知,参数=' + JSON.stringify({ strWhatToDo, qMsgRequest }))
      switch (strWhatToDo) {
        // 消息通知监听
        case 'SrvMsgNotice': {
          const strWhatToDo = _.get(qMsgRequest, 'result.ASM.WhatToDo')
          if (_.isString(strWhatToDo)) {
            const callback = _.get(this.msgMap, strWhatToDo)
            if (_.isFunction(callback)) {
              callback(qMsgRequest)
            }
          }
          break
        }
        // 监听客户端在线状态变化
        case 'AgentState': {
          onlineStatusChange(qMsgRequest)
          break
        }
        // 802.1x 双因子
        case 'InvokeUI': {
          const module = _.get(qMsgRequest, 'result.ASM.Module', '')
          const command = _.get(qMsgRequest, 'result.ASM.Command', '')
          const type = _.get(qMsgRequest, 'result.ASM.Type', '')
          if (module === 'Dot1x' && command === 'GetCheckCode' && type === 'SMSFactor') {
            EventBus.$emit('Dot1x:GetCheckCode:SMSFactor', qMsgRequest)
          }
          break
        }
        case 'AgentModeChanged':
          agentModeChanged(qMsgRequest)
          break
        case 'ReOpen':
          reopen(qMsgRequest)
          break
        case 'SwitchNetwork':
          ipRenew()
          break
        case 'ReportGWStatus':
          gateWayhandle(qMsgRequest)
          break
        case 'OpenNewUrl':
          OpenNewUrl(qMsgRequest)
          break
        case 'ChangeResGroup':
          ChangeResGroup(qMsgRequest)
          break
        default:
          this.strWhatToDoExtendHand(strWhatToDo, qMsgRequest)
      }
    })
  },

  addStrWhatToDo(strWhatToDoName, callback) {
    this.strWhatToDoExtend[strWhatToDoName] = callback
  },

  strWhatToDoExtendHand(strWhatToDo, qMsgRequest) {
    // 扩展方法里面寻找callback
    const extendCallback = _.get(this.strWhatToDoExtend, strWhatToDo)
    if (extendCallback && _.isFunction(extendCallback)) {
      extendCallback(qMsgRequest)
    } else {
      console.warn('没有找到对应的消息类型')
      console.log(strWhatToDo, qMsgRequest)
    }
  }

}

// 监听是否需要展示
EventBus.$on('client:show', () => {
  agentApi.showWnd()
})

export default agentMsgListening
