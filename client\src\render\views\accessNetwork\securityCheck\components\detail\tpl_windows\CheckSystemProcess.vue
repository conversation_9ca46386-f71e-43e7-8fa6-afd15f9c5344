<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.SystemProces.h_3_rs") }}
          </p>
          <div v-for="(item, index) in proccessInfo" :key="item.ProcName + index" class="pc-info">
            <img :src="prImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.SystemProces.h_5_rd") }}
                  <span>{{ item.ProcName }}</span>
                </div>
                <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.SystemProces.h_7_s") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.SystemProces.h_9_rd") }}
                <span>{{ item.ImagePath }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckIEActiveX',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      proccessInfo: [],
      prImgSrc: require('@/render/assets/forbidProcess.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getProccessInfo()
  },
  methods: {
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          procid: item.ProcId
        },
        RepairType: 0,
        CreateProgress: 0
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.SystemProces.h_10_rd')
      })
      this.$set(this.proccessInfo, index, item)
    },
    getProccessInfo() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.proccessInfo = list
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
