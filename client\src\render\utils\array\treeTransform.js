/*
 * @Author: <EMAIL>
 * @Date: 2021-08-16 16:25:17
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-08-19 20:56:38
 * @Description: array和tree的装换
 */
const treeTransformer = {
  arrayTotree(arr, pid = 'pid') {
    const map = treeTransformer.toMap(arr)
    return arr.reduce((acc, current) => {
      const parentId = current[pid]
      if (map[parentId]) {
        map[parentId].children = map[parentId].children || []
        map[parentId].children.push(current)
      } else {
        acc.push(current)
      }

      return acc
    }, [])
  },
  toMap(arr) {
    const result = arr.reduce((acc, current) => {
      acc[current.id] = current
      return acc
    }, {})
    return result
  }
}

export default treeTransformer
