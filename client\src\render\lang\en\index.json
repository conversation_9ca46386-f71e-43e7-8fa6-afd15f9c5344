{"phone": "phone", "connectionStatus": "connection status", "local": "Native", "pcName": "Computer name", "getServerInfoTimeOut": "Get server info timeout configuration information, please reboot your app!", "getServerStatusTimeOut": "Get server status timeout,please reboot", "getDeviceFail": "Get device info error, please reboot your app", "getDetailDeviceFail": "Get device detail info error,please reboot your app!", "getNetStatusFail": "Get device net status error,please reboot your app", "g_ShowName": "Internet users", "devOutTimeTips": "Expiration date:", "letgoNetAccess": "Hello！<br />&nbsp;Your network access has been granted and you can use the network normally！", "ipMacBindIllegal": "Your computer is suspected of using an unauthorized IP address, please contact the administrator for assistance.", "ipMacBindIllegalInfo": "Current IP address and MAC address:", "fingerIllegal": "Your computer device feature information is abnormal, temporarily forbidden to access the network, please contact the administrator.", "netExamine": "Hello, the system is detecting your network environment, please wait...", "mustParamsErr": "Failed to obtain interface parameters. Please contact the administrator for assistance in modifying!", "refresh": "refresh", "tips": "Tips", "interfaceErr": "Interface data is abnormal", "netError": "The network is abnormal. Check the network and try again.", "getDeviceSceneFail": "Failed to get scene configuration!", "serverError": "The server is abnormal. Please try again!", "serverIp": "Connected server ip", "changeNet": "Switching network", "sourceCheck": "Resources diagnosis", "isZtpAbnormal": "Zero trust resource session expired, please click ", "serverChangeTip": "The network you select to switch is abnormal, which may cause normal use after the switchover. Are you sure to continue the switchover?", "logoutErr": "Logout failed! This could be because the network is unavailable or the server is busy.", "updateTip": "The minor assistant version is too early. Please manually restart the minor assistant and force the minor assistant version to upgrade.", "macUpdateTip": "The mini Assistant version is too low, please restart the Mini Assistant or restart the computer to upgrade the Mini Assistant version.", "updateBtn": "Reboot client", "apiUpdateTip": "Operation failed because the minor assistant version is low. Please restart the assistant or computer to upgrade.", "oWelcome": {"UnRegistered": "Welcome！<br/>Your network access may be temporarily restricted ! Please select your type of identity authentication !", "Registered": "   Welcome！<br/>Your network access may be temporarily restricted, first select the type of your identity !", "ReRegistered": "Your registration information does not meet the requirements of network management practices , the administrator does not allow you to access the network temporarily , please re-fill !<br/>Reason：", "AuditStop": "   Welcome！<br/>Your device has exceeded the period of use, the need to access the network administrator to re-examine !", "WaitAudit": "Welcome！<br/>Your device to access the network after administrator approval is required !", "CutOff": "  Welcome！<br/>Your device has been quarantined , please contact the administrator !", "Web_OnAccesss_default": "Hello, your device is prohibited from accessing the network!"}, "SDC_init": "Security is loading,please wait...", "SDC_init_complete": "Securitybox init complete，loading..", "changepassword": {"currentpassword": "Please enter the current password", "newpassword": "Please enter a new password", "submitchange": "Submit", "confirmpassword": "Please confirm new password", "defaultErrTip": "Cannot enter \", no spaces before and after and no more than 50 characters and less than 2 characters", "lengthTip": "At least {PassWordLength} characters!", "excludeNameTip": "Cannot include username!", "norepeateTip": "Do not repeat letters and numbers!", "noeqTip": "Password cannot be {value}", "numAndChat": "Must be a combination of numbers and letters!", "complexPassTip": "Must be a combination of uppercase letters, lowercase letters, numbers, and special characters!", "comfirPassErrTip": "The two new passwords entered are not the same", "successTip": "Successfully modified"}, "forgetpassword": {"userName": "Username", "sendCodeText": "Recevice", "varifycode": "Code", "Pass": "Password", "checkPass": "Confirm", "inputvarifycode": "Please enter verification code", "newPass": "Please enter a new password", "checkNewPass": "Please confirm new password", "inputUserName": "Please enter user name", "currentpassword": "Please enter the current password", "newpassword": "Please enter a new password", "confirmpassword": "Please confirm new password", "submitchange": "Submit", "cancelsub": "cancel", "submitsub": "Submit", "varifyCodeTip": "The verification code must be a six digit integer, please confirm!", "userNameTips": "The user account consists of letters, numbers, Chinese and -_@. Composition and length shall not exceed 50 digits", "defaultErrTip": "Cannot enter \", no spaces before and after and no more than 50 characters and less than 2 characters", "lengthTip": "At least {PassWordLength} characters!", "excludeNameTip": "Cannot include username!", "norepeateTip": "Do not repeat letters and numbers!", "noeqTip": "Password cannot be {value}", "numAndChat": "Must be a combination of numbers and letters!", "complexPassTip": "Must be a combination of uppercase letters, lowercase letters, numbers, and special characters!", "comfirPassErrTip": "The two new passwords entered are not the same", "successTip": "Successfully modified", "phone": "Phone", "email": "Email", "messageCodeText": "Verification code has been sent to"}, "header": {"changePassword": "Change Password", "changeRegInfo": "Modify registration information", "network": "Fast internet access", "myDevice": "My device", "changeCount": "Switch account", "logout": "Logout", "logouting": "Being logged out, please wait a moment...", "skin": "<PERSON>ing", "seeIp": "View server IP", "lang": "中文", "logoutTip": "The automatic identity authentication function will be cancelled after logout. Are you sure you want to logout?", "currentCount": "Current account", "currentIpTip": "Current server IP: {ip}", "goBack": "back", "logoutClient": "Are you sure to exit the client?", "switchNetwork": "Network switch", "extNetwork": "The external network", "intNetwork": "The internal network", "optionNetwork": "Optional network:", "switchSuccess": "Switch success!", "switchFail": "Switch failure!", "cutoff": "The current terminal has been isolated by the administrator", "outLineIP": "Your terminal has irregularities [IP/MAC binding of violation]", "outLineFinger": "Your terminal has irregularities [fingerprint violations]", "linking": "The current network is open, if you want to change the account, please click Enter the network now, enter the network process", "auditing": "The terminal is being audited. Please wait patiently", "haveNetwork": "The terminal has been connected to the network. If you want to change the account, click Re-Access to enter the network process ", "offline": "Not connected to the current server", "clickRefresh": "Click refresh", "close": "Close", "linkSuccess": "Connected", "linkFail": "Ununited", "getFail": "Fail to get", "reNet": "Your network has been switched over. Do you want to reconnect?", "netInner": "Inner net", "netOut": "Outer net", "reAuthLoginOut": "Re-access, the current authentication account will be offline. Are you sure to re-access the network?", "footInfo": "The encrypted tunnel resource is not enabled!", "footInfo2": "Direct Intranet connection mode", "applyLog": "Application record", "curLink": "Current use network", "outAddr": "External address", "innerAddr": "Intranet address", "connect": "Connected", "connectFail": "Cannot access", "switch": "Switch", "switchTip": "The network you select to switch is abnormal, which may cause normal use after the switchover. Are you sure to continue the switchover?", "mobileConf": "Code scanning configures mobile terminals"}, "networkAccess": {"tip1": "The current device has been connected to the network, and the mobile phone is authorized to connect to the network", "tip2": "It is recommended to use the mobile phone\"s own browser, or access the APP to scan the QR code", "tip3": "Number of devices simultaneously logged in to the current account", "success": "login successful! \nPlease feel free to go online"}, "myDevice": {"title": "My device", "tai": "", "compute": "Computer name", "currentDevice": "Current", "logout": "Are you sure to force this computer to be offline?", "outline": "Forced offline", "operate": "Operate", "regTime": "Certification time"}, "changeCount": {"add": "Add", "addTips": "Up to 10 accounts can be added", "enterName": "Please enter the account name", "nameErrorTip": "userName is composed of letters, numbers, Chinese and - @. With a length less than 50", "nameRepeatTip": "Do not add repeatedly", "enterPassword": "Please enter your account password", "mark": "Please enter a note (maximum 120 characters)", "markErrorTip": "The Note cannot be entered ', \", /, \\, <,>, ^, &, +,=,{,}, and can not exceed 120 characters", "cancel": "cancel", "submit": "submit", "countName": "account name", "tableMark": "Remarks", "operating": "operate", "isChangeCount": "Are you sure to switch to this account?", "changeCount": "Switch account", "isDeleteCount": "Are you sure to delete this account?", "deleteCount": "Delete account", "lengthLimit": "3 to 18 characters in length", "currentCount": "Current", "outlineOperate": "The number of accounts that can be added has reached the upper limit!", "editCount": "Edit account", "defaultErrTip": "Cannot enter \", no spaces before and after and no more than 50 characters and less than 1 characters"}, "changeRegInfo": {"deviceInfo": "Device Information", "IP": "Native", "computerName": "Computer name", "system": "operating system", "department": "Please enter or select department", "realname": "Please enter the real name of the user", "phone": "Please enter phone number", "selectDevice": "Please select the device type", "email": "Please enter your email", "local": "Please select the computer location", "mark": "Please enter a note (maximum 120 characters)", "submit": "Submit", "childPostion": "Subordinate position", "emptyTip": "Not allowed to be empty, please enter!", "selectEmptyTip": "{name} must not be empty, please select!", "styleErro": "Format error! ", "checkBoxTip": "Please select at least one"}, "guestAuth": {"administrator": {"info_1": "Apply for guest code", "info_2": "Please select the role of the guest", "info_4": "guest", "info_3": "Guest approval list", "info_20": "Co<PERSON> failed", "info_5": "Your guest code", "info_7": "Copy guest code", "info_6": "Guest approval list", "info_9": "Guest name", "info_10": "Guest unit", "info_11": "contact details", "info_12": "Online time", "info_19": "hour", "info_13": "operating", "info_16": "Are you sure to log off this guest?", "info_17": "cancel", "info_18": "determine", "info_14": "Offline guest", "info_15": "Configure online duration", "info_8": "Copy successfully", "info_21": "No permission", "info_22": "Please approve the request to access the network as a guest. If the request is not for you, ignore this prompt."}, "guest": {"info_14": "Please enter the guest name", "info_15": "Please enter the guest unit", "info_16": "Please enter the guest phone", "info_9": "submit", "info_20": "Please enter 11 digits mobile phone number", "info_7": "Please enter the guest code", "info_11": "please enter verification code", "info_17": "Please enter the name of the host", "info_2": "Guest auth", "info_3": "Scan code ", "info_18": "Please enter the host\"s phone number", "info_10": "Please enter a certified mobile phone number", "info_21": "Please scan the QR code with a certified mobile phone", "info_19": "Please enter the reason for the visit (maximum 120 characters)", "info_12": "send SMS", "info_13": "sending...", "info_8": "Please get the guest code access from the receptionist!", "info_6": "Apply for access", "info_5": "SMS authentication", "info_4": "Guest code authentication", "info_1": "<PERSON><PERSON>", "info_23": "Incorrect guest phone format", "info_22": "The receptionist\"s phone format is wrong", "info_24": "Audit", "info_25": "Your network access application has been submitted and is awaiting review by the receptionist.", "info_26": "During the waiting period, please do not close or leave the page.", "info_27": "Refresh review status", "info_28": "Your network access application has been rejected!", "info_29": "Denial Reason:", "info_30": "Your network access application has been passed. According to the network security requirements of your organization, you need to continue to perform the next steps to access the network.", "info_31": "carry on", "info_32": "Give up access", "phoneCodeValidateErr": "Verification code must be six integers, please confirm!", "noPermission": "Guest authentication is not enabled, please use another authentication method!", "info_33": "Guest Reception", "info_34": "Receptionists apply for guest codes for guests", "info_35": "The guest approval", "info_36": "After the guest accesses, the host approves the list", "info_37": "Guest Management", "info_38": "Manage applied guest codes or accessed guests", "info_39": "Single receive", "info_40": "Team receive", "info_41": "The number of reception", "info_42": "Access validity period", "info_43": "Available Online Duration", "info_44": "The network domain can be accessed", "info_45": "people", "info_46": "hour", "info_47": "Please select", "info_48": " to ", "info_49": "Access start date", "info_50": "Access end date", "info_51": "Please enter the number of guests", "info_52": "Select an access validity period", "info_53": "Please enter the online duration", "info_54": "Select an accessible network domain", "info_55": "The generated guest code", "info_56": "Generated two-dimensional code", "info_57": "Apply again", "info_58": "Guest name", "info_59": "Contact way", "info_60": "IP address", "info_61": "Guest unit", "info_62": "Access way", "info_63": "Turn-on time", "info_64": "Operations", "info_65": "The examination and approval set", "info_66": "Refused to access", "info_67": "Please enter a rejection reason", "info_68": "Please enter", "info_69": "Time remaining", "info_70": "Team name", "info_71": "Guest code", "info_72": "Expiry date", "info_73": "Current online", "info_74": "Are you sure to log off the guest?", "info_75": "Are you sure to take the team offline?", "info_76": "Tips", "info_77": "Please enter a team name", "info_78": "User {userName} offline guest {guestName}", "info_79": "Offline success!", "info_80": "The approval operation is successful!", "info_81": "Access denial succeeded!", "info_82": "Go back", "info_83": "Offline", "info_84": "Visitor reservation", "info_85": "Visi<PERSON>'s name", "info_86": "Visitor unit", "info_87": "Visitor's tel", "info_88": "Send message", "info_89": "To be added", "info_90": "Type", "info_91": "Visitor code", "info_92": "Name", "info_93": "Tel", "info_94": "Indate", "info_95": "Cancel", "info_96": "After canceling, the visitor will not be able to access! Are you sure to cancel?", "info_97": "The visitor has been added. Please go to the [Single Visitor] or [Team Visitor] TAB to manage it.", "info_98": "If you want to send a short message, please fill in [{name}]", "info_99": "General <PERSON>", "info_100": "Subscribe", "info_101": "successful appointment!", "info_102": "Cancel successfully!", "all_max_number": "max number", "max_number": "allow number"}, "hour": "hours", "allowRegion": "The network domain can be accessed", "allowTime": "The online duration", "allowRange": "The access validity period", "needAudit": "Does it need to be reviewed", "pEnter": "Please enter", "pSelect": "Please select", "yes": "yes", "no": "no", "yesAudit": "need to audit", "noAudit": "no need to audit", "emptyAuthType": "Guest authentication is not available yet", "notAuditPermision": "Without approval authority", "forbid": "The device prohibits guests from accessing the network"}, "networkStatus": {"info_7": "Re-enter", "info_6": "Certification time", "info_5": "Authentication account name", "info_4": "Your terminal is connected to the network, please feel free to surf the Internet", "info_3": "Get online now", "info_2": "Your terminal is not connected to the network, please go to the network connection process", "info_1": "This device is offline, please join the network immediately", "info_8": "Security details", "refreshStaus": "Refresh the network status", "info_33": "Security is loading,please wait..."}, "patchView": {"info_1": "The patch installed on the current system", "info_3": "Patch title", "info_4": "level", "info_5": "KB number", "info_6": "Installation date", "info_2": "No patch has been installed", "info_7": "primary", "info_8": "medium", "info_9": "important", "info_10": "serious"}, "patchRepair": {"tip": "Open the patch repair tool", "fail": "Open the failure!", "info_1": "Release date", "info_2": "State", "info_3": "Repair", "info_4": "The patch that needs to be fixed", "info_5": "After the patch is repaired, the machine automatically shuts down", "info_6": "No patches need to be repaired", "info_7": "Is being repaired", "info_8": "The little helper will automatically fix the patch at {time}", "info_9": "{total} patches need to be installed on the computer. {installed} patches have been installed and {install} patches are to be installed", "info_10": "The computer needs to install {total} patches", "info_11": "Downloading"}, "remoteAssistance": {"info_3": "The administrator has approved your remote assistance application, you can accept or reject it", "info_5": "You can accept or decline", "info_7": "Refuse", "info_6": "accept", "info_1": "Apply for remote assistance", "info_2": "Waiting for administrator review...", "info_4": "Your terminal has entered the remote assistance state!", "info_8": "Remote assistance tool has been invoked"}, "faultDiagnosis": {"tip": "Open the fault diagnosis tool"}, "nav": {"status": "Net in", "commonFun": "Common function", "accessNet": "The net", "guestAuth": "Guest auth", "guestReceive": "Guest receive", "commonTools": "Common tools", "diyMenu": "Custom menu", "logDownload": "Download the log", "assistance": "Remote help", "netCheck": "Network detection", "faultDiagnosis": "Fault diagnosis", "pathManagment": "Patch management", "pathView": "Patch to see", "patchRepair": "Patch to repair", "sourcePlatform": "Source platform", "thirdLinkAgeMenu": "Terminal security", "system": "System info", "security": "Security box"}, "confirmDialog": {"yes": "Yes", "no": "No"}, "accessNetwork": {"securityCheck": {"info_1": "Security check, please wait...", "info_2": "The security check is passed and the network has been successfully connected, please rest assured to surf the Internet!", "info_3": "Your terminal has a violation problem, and the security check fails, please solve it in time!", "info_4": "Basically comply with the inspection rules, but there are potential safety hazards, please fix it!", "info_5": "Repairing...", "info_6": "Re-security", "info_7": "One-click repair", "info_8": "Cancel repair", "info_11": "There are hidden danger security check items", "info_10": "Failed key security items", "info_12": "Security inspection compliance items", "info_9": "see details", "info_13": "Repairing", "info_14": "It has been fixed", "info_15": "Security check item result details", "info_16": "Test result", "info_17": "Computer current information", "info_18": "Compliance value", "info_19": "How to fix", "info_20": "Loading desperately", "info_21": "The following anti-virus software is currently installed on the computer:", "info_22": "Antivirus software name", "info_23": "Antivirus software version", "info_24": "Virus database version", "info_25": "name:", "info_26": "version:", "info_27": "Whether to run:", "info_28": "Repair failed", "info_29": "Basically comply with the inspection rules, but there are potential safety hazards!", "info_30": "Security check is completed, no key security check items are not passed, immediately enter the certification process?  ", "info_31": "Security check is completed, all the {total} security check items are in compliance. ", "info_32": "Security check is completed, all the {total} security check items are in compliance. "}}, "auth": {"auth": "<PERSON><PERSON>", "authFail": "<PERSON><PERSON> failed,please reboot your app and try again", "commonMode": "Normal mode", "8021xMode": "802.1x mode", "switchModeSuccess": "Switching mode succeeded", "switchModeErr": "Switching mode failed", "needAccessNetwork": "Please select a network card", "accountAuth": "Account auth", "scanAuth": "Scan code", "scanTip": "Please open the NAC client APP to complete the scan code authentication", "enterpassword": "Please enter your account password", "enterCountName": "Please enter the account name", "enterCode": "please enter verification code", "remenberCount": "Remember account", "autoLogin": "Auto login", "submit": "Log in", "notCount": "No account?", "reg": "Sign up now", "forgetpass": "Forget", "first": "first step:", "sysjh": "Please use the mobile phone number you filled in during registration to get the verification code.", "regTel": "Certified mobile phone number:", "telladmin": "Please contact the administrator to complete your personal information!", "phoneCode": "Send SMS", "insertFingerDeviceTip": "Please insert the fingerprint authentication USB device", "userIdTip": "Please enter user ID", "enterFinger": "Press the fingerprint device to verify", "select": "please choose", "User": "Username Password", "UKey": "UKey authentication", "Mobile": "SMS", "Finger": "Fingerprint authentication", "Guest": "Guest authentication", "Localhost": "Local server", "AdDomain": "AD domain server", "LDAP": "LDAP server", "Other": "Third party server", "Email": "Email server", "Radius": "Radius server", "NAC": "NAC", "DingTalk": "<PERSON><PERSON><PERSON>", "WeWork": "WeChat", "smsCode": "Two-factor authentication", "guestApplySelf": "Self-apply ", "guestCode": "Guest Code", "guestSms": "SMS authentication", "guestQrCode": "Guest scan", "ClientUser": "UserName", "ClientPassword": "Password", "RememberAccount": "Remember account", "AutoLogin": "Auto login", "verifyCode": "Verification code", "inputVerifyCode": "Please enter verification code", "inputUserName": "{user} does not allowed to be empty, please enter!", "noSpecialCharUserName": "{user} does not allow special characters, please re-enter!", "inputPassword": "{password} must not be empty, please enter!", "guestCodePlaceHolder": "Please enter the guest code", "switchVerifyCode": "Can\"t see clearly, click to change one!", "guestCodeTips": "Get code from your receptionist!", "needGuestCode": "Please enter the guest access code: (access code requires you to obtain access to the people)", "guestPhonePlaceHolder": "Please enter the authenticated mobile phone number", "guestPhoneCodePlaceHolder": "Please enter the verification code", "getPhoneCode": "Get code", "phoneEmptyErr": "Phone number cannot be empty", "phoneValidateErr": "Phone numbers are not legitimate, please re-enter!", "phoneCodeValidateErr": "Verification code must be six integers, please confirm!", "guestUsernamePlaceHolder": "Please enter the guest company ", "guestCompanyPlaceHolder": "Please enter the guest name ", "guestApplyPhonePlaceHolder": "Please input guest phone number ", "guestUnawaresPlaceHolder": "Please enter the name of the receptionist ", "guestUnPhonePlaceHolder": "Please enter the number of the receptionist ", "guestVisitingReason": "Please enter the reason for visiting ", "inputPlaceholder": "Please input ", "notAllowAuthType": "Not allowed authentication server!", "selfApplyLabel": {"username": "guest name", "company": "guest company", "phone": "guest phone number", "unawares": "name of the receptionist", "unawaresPhone": "phone of the receptionist", "reason": "reason for visiting"}, "emptyErr": "Can not be empty", "macNoUKey": "Mac does not support UKey authentication yet!", "linuNoUKey": "Linux does not support UKey authentication yet!", "fingerNoUKey": "Mac does not support fingerpint authentication yet!", "needFingerUserDevice": "Insert the USB device CKS fingerprint authentication.", "needFingerCheck": "Press the fingerprint device to verify.", "fingerDeviceOccupied": "The fingerprint device is occupied!", "noFingerDetected": "No fingerprint detected!", "fingerTurnOnFail": "The fingerprint device failed to open!", "fingerReadFail": "The fingerprint reader failed to read!", "fingerCheckFail": "The fingerprint verification failed!", "fingerTimeOut": "Set fingerprint waiting timeout, please refresh the page to re-acquisition!！", "fingerFeatureFail": "Failed to get the fingerprint feature, please make sure your fingers and device are clean!", "needFingerID!": "Please enter your user ID!", "needInstallControl": "Please install the controls!", "needRightfulUKey": "Please insert the legitimate <PERSON>ey", "needUKeyInstallAgent": "UKey certification need to install assistant, click download and install!", "needPassword": "Enter Password", "needNoEmptyPassword": "Password must not be empty, please enter!", "needPin": "Enter the pin code", "failedToLoadControl": "Failed to load control, install control", "getAuthInfoFail": "Failure to obtain authentication information", "uKeyAuthFail": "UKey authentication failed", "insertUKey": "Please insert Ukey and select the certificate！", "checkPinFail": "Verify PIN code failed, please use right certificate", "domainUserCheck": "Domain User", "noConfigChangePasswordUrl": "No configuration changes password link address!", "passwordHasExpired": "Your password has expired. Please change it and continue to use!", "changePass": "Change Password", "mustChangePass": "This certification must change the password!", "unboundMobile": "The current authenticated user is not yet bound phone number, please contact the administrator to improve the user information!", "illegalMobile": "The current authenticated user\"s phone number is not legally binding, please contact the administrator to modify user information!", "firstStep": "First:", "secondStep": "Second:", "inputMobileToGetCode": "Please fill out the registration using your mobile phone number to get the verification code.", "mobileNumber": "Certification phone number:", "noGetMobile": "Did not get to the phone number", "contactAdminToUserInfo": "Please log management platform to improve user information!", "smsVerification": "SMS verification code:", "getSmsCode": "Send SMS", "recapture": "{second} seconds ", "check": "verify", "downLoad": "Click here to download the control", "markFail": "Failed to load QR code image", "sendSMSOK": "Sending verification code succeeded", "emptySerialNum": "The certificate serial number is empty, please check", "emptyIssueTo": "The certificate issuing entity is empty, please check", "needFingerID": "Please enter your user ID!", "FeiShu": "<PERSON><PERSON><PERSON><PERSON>", "cancelLoginTip": "To choose the device to undo login, please click", "manageMyDevice": "Manage my equipment", "continueCertification": "Continue auth", "revokeDevice": "Revoke", "selectSpecifiedDevice": "Please select the specified device", "equipmentName": "Device name", "equipmentType": "Device type", "ipAddress": "IP address", "userNameOrPasswordError": "The user name and password are abnormal, and the account cannot be switched", "autoLogining": "Auto login...", "noOpenUserAuth": "The user name and password authentication is not turned on, and the account switching fails", "switchModeErrorByOnline": "Failed to obtain server configuration due to offline", "autoSwitchDot1xMode": "Not online, automatically switch to 802.1x mode!", "dot1xDisOnline": "If you are not online in 802.1x mode, you cannot continue to access the network. Please wait until you release the network and re authenticate!", "unableConnectToNetwork": "The current server is unavailable and unable to connect to the network, please check the network!", "selectCert": "Please select certificate", "pfix": "please modify！", "manualOffline": "User [{username}] has been disconnected from another computer [{ip}]!", "loginOffline": "User [{user}] has logged in on another computer [{ip}], and your computer will be disconnected from the Internet later!", "adAutoFail": "AD domain single sign-on failed, please authenticate manually!", "noEnterCode": "Note:The verification code has not been entered for a long time, please re authenticate! ", "dot1xAuthFail": "Authentication failed, please try again!", "selectNetworkList": "Please select a network", "twoFactorsOvertime": "Note:The verification code is not entered for a long time, the two-factor box will be closed after {second}s, please re authenticate!", "revokeSuccess": "The device is successfully revoked, please re-authenticate manually!", "serverIPChanged": "The server IP has changed and is being restarted, please wait!", "manualOfflineGuest": "User [{username}] has been disconnected from another computer [{ip}]!", "sso": {"goto": "Jumping to SSO", "openSsoAuthByDefaultBrowser": "Opening the authentication interface, please do not close it!!", "openedSsoAuthByDefaultBrowser": "The authentication interface has been opened, please do not close it!!"}, "qrdefault": "Please scan", "refresh": "Refresh", "scaned": "Scaned", "logining": "Logining,pleash wait", "expire": "Qrcode is exp , please refresh", "dot1xAdAutoFail": "Single sign-on failed, please select a network!", "accountBind": "Bind account", "needOTPPassword": "please input code", "otpActivationToken": "The system requires the password generated by the token to complete the two factor authentication. You do not have an activation token. Please use the NAC app to scan the QRcode to activate the token.", "otpDownloadClient": "Please use the mobile browser to scan the QR code and download the NAC app", "ok": "OK", "close": "Close", "scanned": "Scanned", "reScan": "Rescan Code", "noInstallNAC": "The mobile phone has not installed the NAC app?", "noActivatedAccount": "Check that the account number is not activated!", "downloadAPP": "Download APP", "useFieshu": "Please use feishu app to scan the code", "cancelOneDev": "To cancel device", "switchCodeFail": "<PERSON><PERSON> failed, please enter the verification code and try again!", "switchStaff": "Switch to employee access", "switchGuest": "Switch to guest access", "forbidGuest": "The administrator did not enable guest access!", "browserAuth": "<PERSON>lick <PERSON><PERSON> to begin the authentication process.", "pTel": "Please enter a tel", "validTelErr": "The contact number format is incorrect", "notTelTip": "Contact number is not entered during registration, please enter manually", "feishuAccount": "FeiShu:", "feishuBindTip": "This account is not bound to the Flybook account, please use \"FeiShu\" scan code for secondary authentication for the first time", "feishuBindConfirm": "This account is not bound to the Flybook account, do you want to bind this account?", "dingtalkBindConfirm": "This account is not bound to a Pin <PERSON> account. Do you want to bind this account?", "weworkBindConfirm": "This account is not bound to an enterprise wechat account. Do you want to bind this account?", "skipBind": "Skip binding", "bindAccount": "Bind {name} account"}, "reg": {"authorization": "system is not authorized or license has expired, or the maximum number of devices that can support model has exceeded the number of registered equipment, please contact your dealer!", "reason": "reason", "autoRemark": "the system automatically register the device", "pSelect": "please select", "noEmpty": " must not be empty, please enter!", "ruleErr": "format is incorrect!", "subLocal": "Please choose the lower position!", "checkFail": "Information submission failed, please check the data and submit again!", "regSuccess": "to access network equipment registered, please wait for administrator review later!", "filterTip": "Enter keywords to filter", "pageTitle": "Device registered", "reg": "registered", "submit": "Submit", "commonTextPlacholder": "Please click enter", "commonSelectPlacholder": "Please select", "searchPlaceHolder": "Please enter a keyword to search", "Default": "It cannot contain <, >,*,%,',\",& and cannot exceed 50 characters", "Tel": "Please enter telephone number", "Email": "Please enter email", "IdCard": "Please enter ID Card", "HKMIdCard": "Please enter Hong Kong ID Card", "TaiwanIdCard": "Please enter Taiwan ID Card", "Number": "Please enter number", "Letter": "Please enter letter", "Chinese": "Please enter Chinese", "leaveHalfway": "The device is in the network entry process - device registration process. If you enter other pages, the network entry process will be refreshed and whether to leave the device registration page?", "noData": "no data", "noRegTips": "The current device is not registered, please  register!", "seTopDepartTip": "Top-level department registration cannot be selected!", "1-999Num": "Please enter an integer between 1 and 999", "1-100Num": "Please enter an integer between 1 and 100", "search": "Search", "loadMore": "Loading...", "space": "You can't just fill in the blanks"}, "lang_function_Obj": {"js_1_s": "Please select specific entries!", "js_4_s": "Only select one entry!", "js_5_s": "Child window has not closed, leave this page at the same time will close the child window.", "js_6_s": "Help Help", "js_7_s": "Show", "js_8_s": "<PERSON>de", "js_9_s": "Enter the IP address is not legitimate!", "js_10_s": "IP address format errors, please re-enter!", "js_11_s": "MAC address format error!", "js_12_s": "Please select the entry you want to edit", "js_13_s": "Please select the entry to be deleted", "js_14_s": "OK to delete the selected entry?", "js_15_s": "Determined to clear all entry?", "js_16_s": "Error message:", "js_17_s": "Url address:", "js_18_s": "Line number:", "js_19_s": "Subnet address must not be empty or malformed!", "js_20_s": "The subnet mask must not be empty or malformed!", "js_21_s": "Subnet address error!", "js_22_s": "IP addresses can not be subnet address!", "js_23_s": "IP address can not be a broadcast address!", "js_24_s": "Address is not specified network range!", "js_25_s": "Select Sector", "js_26_s": "Configure the switch packet", "js_27_s": "Update data", "js_28_s": "Being", "js_29_s": "Please ...... Later", "js_30_s": "This configuration is not found!", "js_31_s": "Second", "js_32_s": "Minute", "js_33_s": "hour", "js_34_s": "Day", "js_35_s": "hide"}, "check": {"success": " success", "fail": " fail", "loading": "Obtaining security check items, please wait...", "fixFinish": "Check the item repair is complete! After {second} seconds the system will check again... ", "autoFix": "After {second} seconds the system will start automatically repair inspection items.", "downLoading": "Downloading resources:", "fixed": "Repair Complete", "operate": "operate", "handleFixTip": "Manually repair the faulty items!", "total": "A total of ", "currentCheck": " security check items, checking item ", "item": "", "checkCompleteErr": "The security check was completed, and ", "checkCompleteErrTow": " non-compliance items were detected", "autoFixTip1": "A total of ", "autoFixTip2": "non-compliance items have been detected and ", "autoFixTip3": "non-compliance items have been repaired", "install": "install", "Fixing": "Fixing", "fix": "fix", "js_8_s": "Searching download resources, please wait ...", "js_12_s": "Being installed, please wait ......", "js_16_s": "Has been downloaded:", "js_20_s": "Download speed:", "js_22_s": "Download Progress:", "js_23_s": "You have entered the network at full speed, and we will show you the security results", "js_24_s": "Your device can normally use the network, but there is a security risk. Repair it in time", "js_25_s": "If the repair is not complete within <span>{day}</span> days, the system will restrict your network access", "js_26_s": "It is in the process of network entry - security check. If entering other pages, the network entry process will be refreshed. Whether to leave the security check page?", "js_27_s": "Visit the original website", "js_28_s": "Access to specific websites", "js_29_s": "No security check item is configured on the current device. The security check process is about to end", "hiddenDanger": "Security check was completed and {num} non-compliance items were detected", "poLoadTitle": "Please wait while obtaining security entry...", "poLoadsubTitle": "Do not leave the current page to avoid interrupting the security check process", "checkReportTitle": "Security check completed, reporting results now...", "checkReportSubTitle": "Please wait patiently", "checkFault": "Your device can use the network normally now, but there are security risks, please <span>repair</span> in time!", "checkFalse": "Your computer key item does not pass, prohibit access to the network, need to first <span> repair!</span>", "antivirussoft": {"js_1_d": " Here ", "js_3_d": "Close", "js_5_rs": "Running", "js_6_rs": "Not running", "h_1_rs": "Computer current information", "h_3_rs": "The following computer antivirus software currently installed.", "h_5_rs": "Name:", "h_7_rs": "Version:", "h_8_rs": "Virus database version:", "h_9_rs": "Virus database version:", "h_11_rs": "Whether to run:", "h_13_rs": "How to Repair", "h_15_rd": "1. anti-virus software is not installed or the version undesirable:", "h_17_rs": "If you have not installed anti-virus software or the version currently installed anti-virus software or virus database version does not meet the requirements,", "h_19_rs": "Please click", "h_20_rs": "Installation repair.", "h_22_rd": "2. antivirus software virus database version does not meet the requirements:", "h_24_rs": "Enter the anti-virus software management center.", "h_26_rs": "Updated antivirus software virus database version to the latest version.", "h_28_rs": "Recheck after the repair is successful.", "h_30_rd": "3. anti-virus software is not running:", "h_32_rs": "Open the \"Start\" menu or antivirus software installed.", "h_34_rs": "Currently installed antivirus software is running.", "h_40_rd": "4. Active defense module did not open it：", "h_41_rs": "Double click on the screen at the bottom right antivirus software icon。", "h_42_rs": "Open active threat protection function。", "h_43_rs": "antivirus name", "h_44_rs": "antivirus version", "h_45_rs": "Virus version", "h_46_rs": "Of values", "lang_js_Obj": {"js_1_s": "You have not installed antivirus ", "js_4_s": "Please install the new version of antivirus software!", "js_6_s": "Please update your antivirus software virus database version to the latest version!", "js_8_s": "You have not installed antivirus", "js_9_s": "Not installed the assigned antivirus", "js_10_s": "Antivirus version doesn't meet the requirement", "js_11_s": "Anti-virus software", "js_12_s": "Run anti-virus software fixes success", "js_13_s": "Virus database doesn't meet the requirement", "js_15_s": "Antivirus software does not run, does not support automatic repair!", "js_17_s": "Antivirus doesn't operate", "js_19_s": "Administrators do not specify a path to repair, repair manually", "js_20_s": "You have not installed antivirus"}}, "agentInfo": {"js_1_d": "Close", "js_3_s": "Has been installed", "js_4_s": "Not Installed", "js_6_s": "Has been running", "js_7_s": "Not running", "h_1_rs": "Computer current information", "h_3_rs": "Computers currently has the following client is not installed or not running", "h_5_rd": "Client Name:", "h_9_rd": "Is installed:", "h_11_rd": "Whether to run:", "h_13_rd": "Install", "h_14_rd": "The current version:", "h_15_rd": "Required version:"}, "computerName": {"js_1_d": "Close", "js_3_d": "Modify", "h_1_rs": "Computer current information", "h_3_rs": "Computer name does not meet the requirements", "h_5_rd": "The current computer name:", "h_7_rd": "Rule Description:", "h_9_rs": "How to Repair", "h_11_rs": "Click the System \"Start\" menu, open the \"Settings - Control Panel - User Accounts - Change Account - point to take account you want to modify - change my password.\"", "h_13_rs": "Sequentially enter the current password, the new password and confirm password.", "h_15_rs": "Click the \"Save password.\"", "h_17_rs": "Click the above \"modify the computer name\" button.", "h_19_rd": "Required to enter the computer name.", "h_21_rs": "Click the \"Confirm\" button.", "h_25_rs": "Restart the computer after repaired successfully, and then recheck.", "h_26_rs": "Please enter a computer name", "h_27_rs": "Rule"}, "diskInfo": {"js_3_s": "(System tray)", "js_1_d": "Close", "js_5_s": "Disk Cleanup", "h_1_rs": "Computer current information", "h_3_rs": "Computer disk information below current", "h_5_rd": "Total capacity/Remaining capacity:", "h_8_rd": "File System:"}, "HUB": {"h_3_rs": "Ensure that your current computer port on the switch rather than on the HUB."}, "IEActiveX": {"js_2_rd": "Status:", "js_5_s": "Access control installation page", "js_7_s": "Install", "js_11_s": "Enable", "js_16_d": "Uninstall", "js_18_d": "Close", "h_1_rs": "Your computer will need to install the following controls", "h_3_rs": "Controls name:", "h_4_rs": "Description:", "h_6_rs": "Your computer will need to uninstall the following controls", "h_11_rs": "For the repair of failed controls, how to operate?", "h_13_rs": "For controls need to be installed manually download the setup file to install the controls.", "h_15_rs": "For controls you want to uninstall, find the corresponding control library file management add-in in IE, then an anti-registration operation in the command line.", "js_21_rd": "Disable"}, "IEHomePage": {"js_1_d": "Repair", "js_3_d": "Close", "h_1_rs": "Computer current information", "h_3_rs": "Administrators do not meet the requirements of IE Home", "h_5_rd": "You need to set the current IE home page:"}, "DomainUser": {"js_1_d": "Repair", "js_2_d": "Repair", "js_3_d": "Administrators do not specify a path to repair!"}, "SoftInstallStat": {"js_1_d": "Close", "js_3_rd": "Description: ", "js_5_s": "Access page", "js_7_s": "Install", "js_9_d": "After the installation, please close the page.", "h_1_rs": "The following software is not installed on your computer or the software is not compliant", "h_3_rd": "Software name: ", "js_11_rd": "Condtion: ", "js_12_rd": "Version Number: ", "js_13_rd": "Signature certificate: ", "h_32_rd": "No Compare", "h_33_rd": "More Than", "h_34_rd": "Equal", "h_35_rd": "Less Than"}, "ProcessMustRun": {"js_1_d": "Close", "js_3_rd": "Description:", "js_5_s": "Access Software page", "js_7_s": "Load", "js_9_d": "After the installation, please close the page.", "h_1_rs": "Your computer is not running the following processes", "h_3_rd": "Process name:"}, "SoftForbidInstallStat": {"js_1_d": "Close", "js_3_d": "Uninstall", "h_1_rs": "Computer current information", "h_3_rs": "Currently installed computer software installed following administrator ban", "h_5_rd": "Software name: ", "h_6_rd": "Version Number：", "h_7_rd": "Installation path:", "h_9_rd": "Posted by:", "h_11_rs": "For the uninstall feature does not provide the software, how to fix?", "h_13_rs": "Click the System &quot;Start&quot; menu, open the &quot;Settings - Control Panel - Add or Remove Programs.&quot;", "h_15_rs": "Choose the software you want to delete, click on \"Change / Remove\" button to uninstall the software.", "h_17_rs": "Recheck after the uninstall is complete.", "js_4_d": "First, check the results suggest", "js_5_d": "Prohibition exists on your computer to install the software, run the uninstall, as there are multiple prohibit the installation software, in order to uninstall one by one!", "js_6_d": "Please click on the button to check again"}, "VulnerablePassword": {"js_1_s": "Do you want to disable account ", "js_4_s": " ?", "js_3_rs": "Currently logged in user", "js_5_d": "Disable", "js_7_d": "Reset", "js_8_d": "Change", "js_9_d": "Repair", "h_1_rs": "Computer current information", "h_3_rs": "The following user computers currently exist weak passwords", "h_5_rs": "Username:", "h_7_rd": "Full Name:", "h_9_rd": "Description:", "h_11_rs": "How to Repair", "h_13_rs": "Click the System &quot;Start&quot; menu, open the &quot;Settings - Control Panel - User Accounts - Change Account - point to take account you want to modify - change my password.&quot;", "h_15_rs": "Sequentially enter the current password, the new password and confirm password.", "h_17_rs": "Click the \"Save password.\"", "h_19_rs": "Click the \" Reset Password\" or \"Change Password \" follow the prompts.", "h_21_rs": "Repair is successful , re- safety checks .", "h_23_rs": "Click the \"OK\" button.", "h_25_rs": "Continue to click on the pop-up prompt window \"OK\" button.", "h_27_rs": "Recheck after the repair is successful.", "h_29_rs": "Click the \" Reset Password\" follow the prompts.", "h_10_rd": "original password", "h_11_rd": "please enter new password", "h_12_rd": "Please confirm the new password", "h_13_rd": "Recommended password rules", "h_14_rd": "Cannot contain the user account name and cannot contain more than two consecutive characters in the user name;", "h_15_rd": "Must be at least six characters long;", "h_16_rd": "Contains three of the following four types of characters:", "h_17_rd": "capital letters(A-Z)<br/>lower-alpha(a-z)<br/>10 numbers(0-9)<br> Nonalphanumeric character（for example！、$、#、%）<br>and，The password must meet the minimum length, password complexity, and password history requirements.", "h_18_rd": "Cancel", "h_19_rd": "Confirm", "h_20_rd": "You do not need to enter a password because the password has not been set before!", "h_21_rd": "Dissatisfaction with requirements", "h_22_rd": "Tip", "h_23_rd": "Resetting this password might cause irreversible loss of user account information . For security reasons , windows by making the appropriate information in the user's password reset inaccessible for protection. The next time the user logs this data loss will occur .", "h_24_rd": "you sure you want to reset", "h_25_rd": " For a password?", "h_26_rd": "recommended only when the user forgets the password , and under no circumstances password reset disk before using this command. If the user creates a password reset disk , you should use this disk to reset your password.", "h_27_rd": "Failed to change the password of user {accountN<PERSON>}!  The specified original password is incorrect or does not meet the requirements of the password policy  ", "h_28_rd": "Input Chinese characters is not allowed", "h_29_rd": "Input of Chinese characters and Chinese symbols is not allowed."}, "SystemProces": {"h_1_rs": "Computer current information", "h_3_rs": "There are currently prohibited administrator computer running processes:", "h_5_rd": "Process Name:", "h_7_s": "End", "h_9_rd": "Process path:", "h_10_rd": "Process to shut down"}, "NetPort": {"js_1_s": "Process closes", "js_3_d": "End Process", "h_1_rs": "Computer current information", "h_3_rs": "The following process uses computer port administrators to view:", "h_5_rd": "Process Name:", "h_7_rd": "Agreement Name:", "h_9_rd": "Path:", "h_11_rd": "Memory usage:", "h_13_rd": "Began running time:", "h_15_rd": "Ports:"}, "IpMacBind": {"h_1_rs": "How to Repair", "h_3_rd": "1.does not use the administrator assigned IP:", "h_5_rs": "Right. Mouse click on \"My Network Places\" and select \"Properties\" to open the network connection interface.", "h_7_rs": "Then right-click on \"Local Area Connection\" and select \"Properties\", open the \"Local Area Connection Properties.\"", "h_9_rs": "Mouse double-click \"Internet Protocol (IP / mac) Properties\", will own IP address for the administrator to manually set the IP address assigned to you.", "h_11_rs": "After setting <PERSON>lick the \"OK\" button.", "h_13_rs": "Recheck after the repair is successful.", "h_15_rd": "2.administrator has not assigned you IP:", "h_17_rs": "Contact your administrator to obtain valid IP addresses.", "h_19_rs": "Repeating an operation method for setting the IP address of the computer as an administrator assigned IP.", "h_21_rs": "Recheck after the repair is successful."}, "OSVersion": {"js_1_rd": "Operating system version requirements for administrators", "js_3_rs": "Above.", "js_5_d": "Close", "h_1_rd": "The current version of your operating system your computer is:", "h_3_rs": "How to Repair", "h_5_rs": "Please update service pack version for your computer,or install the required operating system version;", "h_2_rs": "All prohibited."}, "P2PSoft": {"js_3_d": "Uninstall", "h_1_rs": "Computer current information", "h_3_rs": "Computer software is currently installed the following P2P", "h_5_rd": "Software name: ", "h_7_rs": "For non-uninstall feature of P2P software, how to fix?", "h_9_rs": "Click the System &quot;Start&quot; menu, open the &quot;Settings - Control Panel - Add or Remove Programs.&quot;", "h_11_rs": "Choose to delete P2P software, click on the \"Change / Remove\" button to uninstall the software.", "h_13_rs": "Recheck after the uninstall is complete.", "h_15_rs": "Hazard: P2P type applications software is a multi-point transfer tool, users rely on multiple file transfers, like KOOGLE, PP little pass are all kind of software."}, "RubbishFile": {"js_3_d": "Cleanup", "h_1_rs": "Computer current information", "h_3_rs": "System junk files exist", "h_5_rd": "Current computer junk files exist", "h_7": "Current computer junk files over", "h_9_rs": "How to Repair", "h_11_rs": "Clicking the button above rubbish.", "h_13_rs": "If your computer there are a lot of junk files, please wait for a few seconds.", "h_15_rs": "Prompt window pops up click \"OK\" button.", "h_17_rs": "Re-check when the clearing has finished.", "h_18": "Months or"}, "Shutdown": {"h_1_rs": "Computer current information", "h_3_rs": "Not shut down for a long time", "h_7": "Terminal continuous boot time:", "h_8": "Timed out:", "h_9": "The administrator requires that the continuous boot time cannot exceed:", "h_9_rs": "How to Repair", "h_11_rs": "Restart your computer for repair.", "h_18": "h"}, "SoftOnlyInstallStat": {"js_3_d": "Uninstall", "h_1_rs": "Computer current information", "h_3_rs": "Currently installed computer software or software version installed following administrator ban", "h_5_rd": "Software name: ", "h_7_rd": "Installation path:", "h_9_rd": "Posted by:", "h_11_rd": "Software version：", "h_11_rs": "For the uninstall feature does not provide the software, how to fix?", "h_13_rs": "Click the System &quot;Start&quot; menu, open the &quot;Settings - Control Panel - Add or Remove Programs.&quot;", "h_15_rs": "Choose the software you want to delete, click on \"Change / Remove\" button to uninstall the software.", "h_17_rs": "Recheck after the uninstall is complete.", "js_4_d": "First, check the results suggest", "js_5_d": "Prohibition exists on your computer to install the software or the wrong software version, run the uninstall, as there are multiple prohibit the installation software, in order to uninstall one by one!", "js_6_d": "Please click on the button  &quot;to check again  &quot;"}, "SoftLicense": {"h_5_rd": "SoftWare Name：", "h_6_rd": "Path：", "h_7_rd": "Serial Number：", "h_7_rs": "Problems items "}, "WSUS": {"js_1_s": "Unopened WSUS", "js_3_s": "Download and update", "js_5_s": "Download updates only", "js_7_s": "Only prompted to download", "js_9_s": "Hour", "js_11_s": "Automatic restart after installing updates", "js_12_s": "Do not automatically reboot after installing updates", "js_14_d": "Repair", "js_16_d": "Close", "h_1_rs": "Computer current information", "h_3_rs": "WSUS configuration item comparison:", "h_5_rd": "WSUS local configuration  ", "h_7_rd": "WSUS administrator configuration  ", "h_9_rd": "Automatic Update option:", "h_11_rd": "WSUS update address:", "h_13_rd": "Frequency of Inspection:", "h_15_rd": "Automatic restart:", "h_16_rd": "Every day", "h_17_rd": "Sunday", "h_18_rd": "Monday", "h_19_rd": "Tuesday", "h_20_rd": "Wednesday", "h_21_rd": "Thursday", "h_22_rd": "Friday", "h_23_rd": "Saturday"}, "WindowsShare": {"js_1_d": "Close Sharing", "js_3_d": "Close", "js_4_s": "Closed system of shared resources ", "h_1_rs": "Computer current information", "h_3_rs": "Your computer currently has the following shared", "h_5_s": "<PERSON><PERSON><PERSON> shared", "h_5_rd": "Sharing Name:", "h_6_rd": "Sharing Type:", "h_8_rs": "Permissions:", "h_10_rs": "Number of connections:", "h_12_rs": "Shared Address:"}, "UnlawfulConnectOut": {"js_3_d": "Close dialing", "js_4_d": "Close", "js_5_d": "Disable NIC", "h_1_rs": "Computer current information", "h_3_rd": "Computer connected to the external network behavior irregularities", "h_5_rs": "Please close violation dial-up connection:", "h_7_rd": "Computer irregularities dialing behavior", "h_9_rs": "Please disable redundant NIC:", "h_11_rd": "Card name:", "h_13_rd": "Physical Address:", "h_15_rd": "Ip Address:", "h_17_rs": "How to Repair", "h_19_rs": "Choose the card you want to disable or dial-up connection. Clicking the appropriate button to Disable NIC or Close dialing.", "h_21_rs": "Prompt window pops up click \"OK\" button.", "h_23_rs": "Recheck.", "h_25_rs": "If you have multiple dial-up connections or multiple network cards, you need to operate repeatedly.", "h_27_rs": "Note: Please disable your LAN or dial-up connections violation outreach.", "h_28_rs": "Please close the wireless connection:", "h_29_rs": "The computer has a wireless connection violation"}, "SystemServices": {"js_3_s": "Started", "js_5_s": "Stop", "js_7_s": "Disable", "js_9_d": "Disable", "h_1_rs": "Computer current information", "h_3_rs": "The presence of the following services are not permitted on the computer system", "h_5_rd": "Service Name:", "h_7_rd": "Service Status:", "h_9_rd": "Installation path:", "h_11_rd": "Service Description:"}, "Patch": {"js_1_d": "Serious", "js_2_d": "Important", "js_3_d": "Installation immediately", "js_5_d": "Close", "h_1_rs": "Computer current information", "h_3_rd": "Computers currently has ", "h_4_rd": " Patches need to be installed.", "h_6_rd": "Totaling installation ", "h_7_rd": "Months, the first being installed", "h_8_rd": " Months.", "h_10_rd": "Installing ...", "h_12_rd": "Patch has all the installation is completed (after the computer restarts in force).", "h_14_rd": "Level", "h_16_rd": "KB Number", "h_18_rd": "Patch title", "h_20_rd": "Published", "h_22_rs": "Status", "h_24_rd": "Not yet installed", "h_26_rd": "After installing automatic shutdown", "js_13_s": "Patch <PERSON>", "js_14_s": "Patch title:", "js_15_s": "Knowledge Base Article ID:", "js_16_s": "Patch Category:", "js_17_s": "Release date:", "js_18_s": "Description:", "js_19_s": "Official Download", "js_20_s": "Official description", "js_5_s": "Failed to get patch information!", "js_6_s": "Currently installing ", "js_7_s": "Reset computer", "js_8_s": "Installation successful", "js_11_s": "You sure you want to restart the computer?", "js_12_s": "System patch installation is complete, the system will reboot.", "js_1_s": "Your system is currently being installed the patch, please do not close this page!"}, "PasswordPolicy": {"js_1_d": "Change Password Settings policy "}, "GuestUser": {"js_1_s": "GUEST Guest account is disabled "}, "IsFirewallEnable": {"js_1_s": "Firewall turned on "}, "RemoteDesktopCon": {"js_1_s": "Remote Desktop feature to disable "}, "ScreenProtection": {"js_1_s": "Screen saver repair "}, "SystemTime": {"js_1_s": "Update the system time "}, "CDRom": {"js_1_s": "<PERSON> Rom is disabled "}, "NicBind": {"js_1_s": "Modify NIC teaming check "}, "AgentInfo": {"js_1_s": "Repair is complete", "js_2_s": "Administrators do not specify a path to repair!"}, "customCheck": {"js_1_s": "Repair"}, "CheckProfile": {"js_1_s": "Description:", "js_2_s": "Access page", "js_3_s": "Install", "js_4_s": "The following profile is not installed on your computer", "js_5_s": "ProfileUUID:", "js_6_s": "File installation"}}, "safecheck_js_langObj": {"js_1_s": "Outreach to check irregularities", "js_4_s": "Check the current existence of irregularities outreach computer access, or double card, dial-up, etc., if there are more cases, given the appropriate treatment.", "js_5_s": "System process checks", "js_6_s": "Check whether the current system administrator does not allow the process to run.", "js_7_s": "Check is complete, the data is being processed ...", "js_8_s": "Scanning Section ", "js_9_s": " Items of ", "js_10_s": "A checklist.", "js_11_s": "Start", "js_12_s": "Ok", "js_13_s": "The security check data is abnormal, please re-enter the network!"}, "rulesLangObj": {"Default": "Please enter Chinese, letters, numbers, ., @,!,！,(,)50 characters length!", "guestName": "Consist of letters, numbers, Chinese, and-_ @. Component and the length is not more than 50", "Tel": "Please enter your phone number!", "Email": "Please input the correct email address!", "IdCard": "Please enter 18 valid ID characters!", "HKMIdCard": "Please enter the correct Hong Kong and Macao ID characters!", "TaiwanIdCard": "Please enter the correct Taiwan ID card character!", "Number": "Please enter the number!", "Letter": "Please enter the letters!", "Chinese": "Please enter Chinese characters!", "commonErr": "Invalid format", "contentErr500": "Can not enter \\', \", /,  , <,>, ^, ~,&, + and other characters, and can not exceed 500 characters", "guestUserName": "Consist of letters, numbers, Chinese, and-_ composition and length of not more than 50", "usepassword": "Can not enter \" and no spaces before and after and not more than 50 characters and Less than 2 characters", "needGuestUserName": "Please enter the guest name！", "accountTitle": "Consist of letters, numbers, Chinese, and-_ @. Component and the length is not more than 50"}, "netExamineRefresh": "Failed to query network release status, please refresh and try again!", "errcode": {"********": "The request timed out. Please try again later"}, "thirdLinkAgeMenu": {"empty": "The business menu is not configured yet!"}, "sourcePlatform": {"recently": "Recently used", "collect": "Collect", "cancelCollect": "Cancel collection", "ipResources": "IP resources", "success": " success", "changePassTitle": "Edit account password", "pUser": "Please enter your account name", "pPass": "Please enter your account password", "clean": "Clear", "ok": "Ok", "notSource": "No resources are currently accessible", "appendAuthTitle": "Strengthen the certification", "send": "Send", "submit": "Submit", "notTel": "Mobile phone number does not exist, please contact the administrator to configure!", "notEmail": "Mailbox does not exist, please contact the administrator to configure!", "wxTip": "Tip: Please use the enterprise wechat to scan the QR code.", "pCode": "Verification code", "cancel": "Cancel", "addRange": "Address range:", "myLog": "My log", "search": "Search", "time": "Time", "logContent": "Log contents", "pcsPage": "/page", "page": "page", "jumpTo": "Go to", "myAccount": "My account", "myApp": "My application", "changPass": "Change password", "account": "Account:", "curPass": "Current password:", "newPass": "New password:", "confirmPass": "Confirm password:", "accountManager": "Account management", "edit": "Edit", "notLogin": "Your terminal is not connected to the network, please enter the network process!", "loginExpeir": "Your terminal login has expired, please log in again!", "pEmail": "Please input your email !", "limitedNet": "Limited", "slodOut": "Slod out", "ipTip": "Please use a browser or other tools to access.", "onlyWinClient": "Windows client only", "onlyWinClientTips": "This resource is only allowed to run on windows clients!", "remoteApiErr": "Failed to get remote application configuration", "searchTip": "Enter search terms", "searchNull": "No search result is available", "all": "All", "contact": "Contact：", "tel": "Tel：", "sourceType": "Source type：", "address": "address：", "accessDomain": "Access domain", "webApp": "Web application", "remoteApp": "Remote application resource", "micApp": "Microapplication", "customApp": "Custom applications", "fileServerApp": "File Server applications", "downloadTip": "The application requires a client to access it,", "down": "download client", "sourceSlodOut": "The resource has been removed", "noHeath": "The health status of the resource is abnormal. Contact the resource contact", "vpnFail": "Setting up an encrypted tunnel, please wait...", "vpnPass": "A tunnel has been established. You can use the address in Address Range to access the resource.", "sourceFail": "No access! You do not meet the access policy requirements for this resource.", "seeDetail": "view details", "ztpUserExceed": "The number of online user connections has reached the upper limit of zero trust and cannot access resources. Please try again later or contact the administrator。", "clickCopy": "Click to copy", "proxyLink": "Tunnel link：", "ZTPSet": "ZTP access configuration", "virCard": "Virtual network card mode", "driver": "Drive mode", "setSuccess": "Set success！", "setFail": "Set fail!", "proxyUnlink": "Unlink", "baseInfo": "Basics", "appName": "Name:", "desc": "Description:", "serverAddr": "Server address:", "openTypeTitle": "App Launch Method", "setOrigin": "Config Type:", "openType": "Launch Method:", "softAddr": "Program Path:", "softName": "Program Name:", "setTime": "Custom configuration time:", "customSet": "Custom", "adminSet": "Specified by <PERSON><PERSON>", "restoreAdminSet": "<PERSON><PERSON>", "browserMode": "Browser", "pathMode": "Specified Program", "scriptMode": "<PERSON><PERSON><PERSON>", "installed": "Try Again", "selectOther": "Select another program", "ikonw": "I see", "pEnter": "Please enter", "windwosAccountErr": "Cannot contain \\/\"[]:|<>+=;,?*@, The length does not exceed 50", "bundle": "Bundle ID", "coverFile": "The alias already exists. Can I overwrite it?", "listApiFail": "No data has been obtained, please click <span> to refresh </span> and try again!", "isolationChangeFail": "Resource failed to open, please try again!"}, "accountBind": {"User": "Username Password", "Localhost": "Local account", "AdDomain": "AD domain", "LDAP": "LDAP", "Email": "Email", "Radius": "<PERSON><PERSON>", "DingTalk": "DingTalk", "WeWork": "WeWork", "FeiShu": "<PERSON><PERSON><PERSON><PERSON>", "Other": "Web", "notAccount": "Unable to obtain account, Please contact the administrator to complete personal information!", "notEmail": "Unable to obtain email, Please contact the administrator to complete personal information!", "notPhone": "Unable to obtain phone number, Please contact the administrator to complete personal information!"}, "strenthAuth": {"title": "Strengthen the certification", "User": "Localhost", "AdDomain": "AD domain", "LDAP": "LDAP", "Email": "Email", "Radius": "<PERSON><PERSON>", "DingTalk": "DingTalk", "WeWork": "WeWork", "FeiShu": "<PERSON><PERSON><PERSON><PERSON>", "WebAuth": "WebAuth", "Mobile": "SMS Code", "Mailbox": "Email Code", "OTP": "Token password", "FeiShuCode": "Feishu Code"}, "dialogFoot": {"loading": "loading……", "yes": "Confirm", "no": "Cancel"}, "resourceAuth": {"user": "Enter password authentication", "app": "App code scan authentication", "ClientUser": "UserName", "ClientPassword": "Password", "resourceInitErr": "Error or timeout in getting advanced dynamic authentication information, please try again", "noAuth": "There is no authentication at the current terminal. Please use the client or web page for authentication before continuing.", "authFaild": "Authentication failed, please try again!", "drawQrCodeFail": "Failed to load QR code image", "emptyPwdErr": "Password cannot be empty", "sanQrcodeFail": "Failed to query the scan result of QR code. Please refresh the QR code and scan again!", "authLading": "Authenticating, please wait...", "success": "The authentication is successful and is about to be closed automatically!"}, "system": {"devInfo": "Device info", "sysInfo": "System info", "address": " address", "support": "IT Support: ", "notice": "Notice:", "copy": "Copy info", "scan": "Scan code", "copySuccess": "Copy success", "cardName": "Network card name", "currentUse": "Current", "gateIP": "Link IP"}, "applyLog": {"name": "Resource name", "reson": "<PERSON><PERSON>", "result": "Status", "pass": "Pass", "refuse": "Reject", "wait": "To be reviewed", "time": "Application Date"}, "applyRes": {"add": "Resource application", "name": "Resource name", "reson": "<PERSON><PERSON>", "time": "Service time", "day": "days", "selectTime": "Please select duration", "selectRes": "Please select a resource", "preson": "Please enter the reason for application", "submit": "Submit", "cancel": "Cancel"}, "msgCenter": {"title": "Message center", "read": "All read", "msgContent": "Message content", "send": "Addresser", "sendTime": "Notification time", "pass": "The resource [{name}] request has been approved", "refuse": "Resource [{name}] request rejected", "reTime": "Remaining time", "appRes": "Resources that are allowed to apply", "appOk": "Resource permission request submitted successfully!"}, "footer": {"netStatus": "Network state", "flowCount": "Flow total", "delayed": "Delayed", "netSpeed": "Speed", "error": "The gateway is abnormal!", "ipFail": "The virtual IP address pool is insufficient. Please contact the administrator.", "gwFail": "The gateway needs to be re-authenticated and enabled.", "authFail": "The number of authorization points of the terminal agent gateway is full."}, "fileRes": {"aliName": "Alias:", "pAliName": "Please enter alias", "account": "Account:", "pAccount": "Please enter account", "password": "password:", "pPassword": "Please enter password", "qd": "Driver", "pQd": "Please enter driver", "remPassword": "Remember the password"}}