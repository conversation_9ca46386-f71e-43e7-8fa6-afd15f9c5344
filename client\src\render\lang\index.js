/**
 * 多语言实现方案
 */
import Vue from 'vue'
import VueI18n from 'vue-i18n'
// element的语言方案
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import enLocale from './en/index.json'
import zhLocale from './zh/index.json'
import agentApi from '@/service/api/agentApi'
import proxyApi from '@/service/api/proxyApi'
import _ from 'lodash'
import store from '@/render/store'

export let i18n

export const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale
  }
}

// 初始化语言功能
export const initI18n = () => {
  Vue.use(VueI18n)
  i18n = new VueI18n({
    locale: 'zh',
    messages
  })

  renewSetLanguage()
}

// 设置语言函数（1：修改i18n的语言；2：修改小助手的语言缓存；3：修改全局状态管理里面的语言）
export const setLang = (lang, storage = true) => {
  i18n.locale = lang
  agentApi.setLocalLangue({
    Lang: lang
  })
  if (storage) {
    localStorage.setItem('lang', lang)
  }
}

// 重新设置语言(因为从服务器或者qt里面读取语言配置会有延迟，所以先new初始化VueI18n，然后重设)
// 从缓存里面获取本地的语言设置(如果缓存里面没有，就先设置为中文然后去请求后端获取)
const renewSetLanguage = async() => {
  let lang = await agentApi.getLocalLangue()
  console.log('lang Local==', lang)
  const storageLang = localStorage.getItem('lang')
  if (!lang || _.isEmpty(lang) || _.indexOf(['zh', 'en'], lang) === -1 || lang !== storageLang) {
    // 去后端获取接口,并且计算返回语言
    console.time('testLang')
    const res = await proxyApi.getLangSet()
    console.timeEnd('testLang')
    lang = computeLang(res)
    setLang(lang, false)
  } else {
    i18n.locale = lang
  }
}

// 获取服务器配置的语言包(入网流程>中英文界面配置信息)
export const getRemoteLangPack = async() => {
  if (!_.get(store.state.clientInfo, 'online', false)) {
    return
  }
  const res = await proxyApi.getRemoteLang()
  if (_.get(res, 'data')) {
    addMessage(_.get(res, 'data'))
  }
}

// 如果语言设置为自适应，则从浏览器设置里面拿
const computeLang = function(res) {
  try {
    let lang = _.get(res, 'data.PCLang', false)
    if (lang === 'auto' || !lang) {
      let navigatorLang = navigator.userLanguage
      if (navigator.appName.toString() === 'Netscape') {
        navigatorLang = navigator.language
      }
      // 取得浏览器语言的前两个字母
      lang = navigatorLang.substr(0, 2)
      if (_.indexOf(['zh', 'en'], lang) === -1) {
        lang = 'zh'
      }
    }
    return lang
  } catch (e) {
    return 'zh'
  }
}

export const addMessage = (obj) => {
  const en = _.merge(messages.en, obj.en)
  const zh = _.merge(messages.zh, obj.zh)
  i18n.setLocaleMessage('en', en)
  i18n.setLocaleMessage('zh', zh)
}

