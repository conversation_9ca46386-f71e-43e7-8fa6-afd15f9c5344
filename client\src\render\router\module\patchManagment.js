export default [
  {
    path: '/patchView',
    name: 'patchView',
    component: () => import('@/render/views/patchManagment/patchView'),
    platform: ['windows'],
    meta: {
      code: '301', // 保证唯一性，用于侧边栏判断是否显示激活状态（通过前3位判断）
      menu: { // 不需要显示到侧边导航栏不配
        name: 'nav.pathView',
        icon: 'icon-chakanxiangqing', // 侧边栏显示图标
        moduleName: 'nav.pathManagment', // 所属模块名称
        uiId: 'ui-menu-patchView-li-patch_view'
      }
    }
  },
  {
    path: '/patchRepair',
    name: 'patchRepair',
    component: () => import('@/render/views/patchManagment/patchRepair'),
    platform: ['windows'],
    meta: {
      code: '302', // 保证唯一性，用于侧边栏判断是否显示激活状态（通过前3位判断）
      menu: { // 不需要显示到侧边导航栏不配
        name: 'nav.patchRepair',
        icon: 'icon-budingxiufu', // 侧边栏显示图标
        moduleName: 'nav.pathManagment', // 所属模块名称
        uiId: 'ui-menu-patchRepair-li-patch_repair'
      }
    }
  }
]

