// 为electron的app.asar打包
const path = require('path')
const asar = require('asar')
const fs = require('fs')

const CopyPlugin = require('copy-webpack-plugin')

const baseWebpackConf = require('./webpack.base.config.js')
const { merge } = require('webpack-merge')

// 打包成asar文件
class AsarPlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tapAsync('AsarPlugin', async(stats, callback) => {
      const src = path.join(__dirname, '../distAsar')
      const dest = path.join(__dirname, '../distAsar/app.asar')

      await asar.createPackage(src, dest)
      callback()
    })
  }
}

// 判断client项目是否已经编译。client/dist下是否有文件
class CheckClientDistExist {
  apply(compiler) {
    compiler.hooks.afterEmit.tapAsync('CheckClientDistExist', async(compilation, callback) => {
      const filePath = path.resolve(__dirname, '../dist/js/main.js')

      fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
          const error = new Error('请先编译client项目')

          // 添加错误到 Compilation 对象的 errors 数组
          compilation.errors.push(error)

          // 调用 callback 函数，并传递 error 参数来终止编译
          return callback(error)
        } else {
          // File exists, proceed with the build
          console.log(`${filePath} exists. Proceeding with the build...`)
          callback()
        }
      })
    })
  }
}

module.exports = (env) => {
  const from = env.from || 'electron'
  const plugins = [

    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../src/electron/assets'),
          to: path.resolve(__dirname, '../distAsar/assets'),
          toType: 'dir',
          noErrorOnMissing: true,
          globOptions: {
            ignore: [
              '**/.DS_Store',
              '**/index.html'
            ]
          },
          info: {
            minimized: true
          }
        },
        {
          from: path.resolve(__dirname, '../src/electron/package.json'),
          to: path.resolve(__dirname, '../distAsar'),
          toType: 'dir',
          noErrorOnMissing: true,
          globOptions: {
            ignore: [
              '**/.DS_Store',
              '**/index.html'
            ]
          },
          info: {
            minimized: true
          }
        }
      ]
    }
    ),

    new AsarPlugin()
  ]

  if (from !== 'client') {
    plugins.push(new CheckClientDistExist())
  }

  return merge({
    mode: 'production',
    devtool: 'source-map',
    target: 'electron-renderer',
    entry: {
      index: path.join(__dirname, '../src/electron/index.js'),
      preload: path.join(__dirname, '../src/electron/modules/ipc/preload.js')
    },
    node: {
      __dirname: false,
      __filename: false
    },
    output: {
      filename: '[name].js',
      library: {
        type: 'commonjs2'
      },
      clean: true, // 在生成文件之前清空 output 目录
      path: path.join(__dirname, '../distAsar'),
      publicPath: ''
    },
    resolve: {
      alias: {
        '@electron': path.join(__dirname, '../../src/electron')
      },
      extensions: ['.tsx', '.ts', '.js', '.json', '.node']
    },
    module: {
      rules: [
        {
          test: /\.node$/,
          use: 'node-loader'
        },
        {
          test: /(.js|.mjs)$/,
          // 正则匹配需要注意window和linux路径分割符不一致，需要同时兼容
          // exclude: excludeNodeModulesExcept(modulesToTranspile),
          use: [
            {
              loader: 'babel-loader',
              options: {
                configFile: path.resolve(__dirname, '../babel.electron.config.js') // 指定 Babel 配置文件路径
              }
            }
          ]
        }
      ]
    },
    plugins: plugins,
    optimization: {
      minimize: false
    }
  }, baseWebpackConf.makeQrcZip('electron'))
}
