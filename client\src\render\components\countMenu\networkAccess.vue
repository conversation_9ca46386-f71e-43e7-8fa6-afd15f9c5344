<template>
  <div class="network-access-page">
    <div class="mark-wrapper">
      <div v-if="errMsg">
        <img :src="networkAccessPng" :alt="errMsg">
      </div>
      <div
        v-else
        class="ewm-box"
        @click="getMark"
      >
        <img :src="scanUrl" :title="$t('header.clickRefresh')" alt="">
      </div>
      <p
        v-if="errMsg"
        class="tips text-red"
      >
        {{ errMsg }}
      </p>
      <p
        v-else
        class="tips"
      >
        {{ $t('networkAccess.tip1') }}
      </p>
      <p class="tips">{{ $t('networkAccess.tip2') }}</p>
      <p class="device-number tips" @click="seeDevice">
        {{ $t('networkAccess.tip3') }}
      </p>
    </div>
  </div>
</template>

<script>
import proxyApi from '@/service/api/proxyApi'
import { mapGetters } from 'vuex'
const networkAccessPng = require('@/render/assets/networkAccess.png')

export default {
  data() {
    return {
      scanUrl: '',
      code: '',
      timer: null,
      errMsg: '',
      networkAccessPng,
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'authInfo',
      'serveEntiretyConfig'
    ])
  },
  mounted() {
    this.getMark()
  },
  beforeDestroy() {
    this.loading = true
    this.clearHandle()
  },
  methods: {
    async getMark() {
      if (this.loading) {
        return
      }
      this.clearHandle()
      const data = {
        DeviceID: _.get(this.clientInfo, 'detail.DeviceID', 0),
        roleID: _.get(this.authInfo, 'basic.RoleID', '') || _.get(this.clientInfo, 'accessStatus.roleID', 0),
        username: _.get(this.authInfo, 'basic.UserName', '') || _.get(this.clientInfo, 'accessStatus.userName', ''),
        userType: _.get(this.authInfo, 'basic.AuthType', '') || _.get(this.clientInfo, 'accessStatus.lastAuthType', 'User'),
        sceneID: _.get(this.serveEntiretyConfig, 'scene.SceneID', 0),
        clientType: 'A'
      }
      this.loading = true
      const result = await proxyApi.getScanInfo(data, { showError: false })
      this.loading = false
      switch (parseInt(result.errcode)) {
        case 0: {
          const scanUrl = _.get(result, 'data.qrcodeUrl', '')
          if (!_.isNil(scanUrl) && !_.isEmpty(scanUrl) && _.get(result, 'data.code', '')) {
            this.scanUrl = scanUrl
            this.code = result.data.code
            this.getScanStatus()
          }
          break
        }
        case 21139009: {
          this.errMsg = _.get(result, 'errmsg')
          break
        }
        default:
          this.$message.error(_.get(result, 'errmsg'))
      }
    },
    getScanStatus() {
      this.clearHandle()
      this.timer = setInterval(this.scanStatusServe, 3000)
    },
    async scanStatusServe() {
      const result = await proxyApi.getScanStatus({ code: this.code })
      if ((result.errcode === '0' && result.data.state) ||
       (_.get(result, 'data.state') === false && _.get(result, 'data.type') === 'overtime')) {
        // 成功情况下扫码端已给出提示，PC不用提示
        clearInterval(this.timer)
        this.timer = null
        this.getMark()
      }
    },
    seeDevice() {
      this.$emit('hdEvent', { type: 'router', val: 'myDevice' })
    },
    clearHandle() {
      this.timer && clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss" scoped>
.network-access-page {
  .mark-wrapper {
      padding: 0 32px;
    .ewm-box {
      margin: 16px auto;
      width: 240px;
      height: 240px;
      box-sizing: border-box;
      background: url("../../assets/frame.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 10px;
        opacity: 0.7;
      }
    }
    .tips {
      text-align: center;
      font-size: 14px;
      margin-top: 16px;
      color: $default-color;
      line-height: 20px;
    }
    .text-red{
      color: red;
    }
    .tips:nth-of-type(1){
          margin-top: 32px;
      }
    .device-number {
      color: $--color-primary;
      text-decoration: underline;
      margin-top: 40px;
      cursor: pointer;
    }
  }
  .success-img{
      margin: 55px 55px 35px 55px;
      background: url('../../assets/empty.png') no-repeat;
      background-size: cover;
      height: 250px;
  }
  .success-tips{
      font-size: 14px;
      text-align: center;
      line-height: 20px;
      color:$default-color;
  }
}
</style>
