<!--
 * @Author: <EMAIL>
 * @Date: 2021-09-19 19:16:52
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-11-23 10:34:15
 * @Description: debugger工具条里面的网络tab页
-->
<template>
  <div id="f-chrome-inspect">
    <div v-if="!isElectron">
      <el-alert
        title="请确保AssUIConfig.db文件存在指定的目录,并添加以下配置"
        type="info"
        description="xp(或其他webkit环境下)下不生效，且确保debug端口绑定为9999,下面为AssUIConfig.db文件"
        show-icon
        style="margin-bottom:10px"
      />
      <textarea style="width: 100%;" readonly rows="5" cols="50">
        [Function]
        DebugPort=9999
        EnableReload=1

      </textarea>
      <div class="u-button">
        <el-button slot="reference" size="small" type="primary" @click="openInspect">
          打开inspect
        </el-button>
      </div>
    </div>
    <div v-else>
      <el-button slot="reference" size="small" type="primary" @click="openDevTools">
        打开devTools
      </el-button>
    </div>
  </div>
</template>
<script>
import proxyApi from '@/service/api/proxyApi'
import { isElectron } from '@/service/utils/ipcPlugin/utils/index'
import agentApi from '@/service/api/agentApi'

export default {
  data() {
    return {
      isElectron: isElectron()
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    async openInspect() {
      // 请求chrome devTools获取协议地址
      let ret = []

      try {
        ret = await proxyApi.getDevToolsProtocol()
      } catch (error) {
        console.log(error)
        this.$msg({ type: 'error', message: '获取端口协议失败' })
        return false
      }

      if (_.isEmpty(ret)) {
        this.$msg({ type: 'error', message: '获取端口协议失败' })
        return false
      }

      const devtoolsFrontendUrl = _.get(ret, '[0].devtoolsFrontendUrl', false)
      let devtoolsPage = ''
      if (devtoolsFrontendUrl) {
        devtoolsPage = 'http://localhost:9999' + devtoolsFrontendUrl
      }

      if (!window.devtoolsPage) {
        window.devtoolsPage = devtoolsPage
        window.open(devtoolsPage, 'hasOpen')
      } else {
        alert('请关闭客户端重新打开inspect工具')
      }
    },
    async openDevTools() {
      agentApi.openDevTools()
    }
  }
}
</script>
<style lang="scss">
#f-chrome-inspect {
  padding: 10px
}
</style>
