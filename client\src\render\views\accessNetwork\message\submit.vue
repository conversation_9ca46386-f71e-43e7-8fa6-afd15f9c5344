<!-- 入网状态 -->
<template>
  <div id="f-submit">
    <layout-state
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-txt="state['stateBtnTxt']"
      :state-btn-disabled="state['stateBtnDisabled']"
      state-id="ui-submit-check-div-check_alter_msg"
      @stateBtnHandle="handleNext"
    >
      <div v-if="computeNetAccessStatus()['isAccess']" class="u-networe-status-extend">
        <p class="u-networe-status-user">
          <el-tooltip class="item" effect="dark" :content="userName" placement="top-end">
            <span v-if="userName.length > 0">
              {{ $t('networkStatus.info_5') }}：{{ userName }}
            </span>
          </el-tooltip>
          <span v-if="authTime.length > 0">
            {{ $t('networkStatus.info_6') }}：{{ authTime }}
          </span>
        </p>
        <div class="sub-btn-wrapper">
          <div v-if="count >= 0" class="visit-web cutoff-line" @click="redirectTo">{{ isSpecUrl ? $t('check.js_28_s') :
            $t('check.js_27_s') }} {{ count }}s</div>
          <div v-if="showCheckDetail" id="ui-submit-check-div-checklist" class="visit-web cutoff-line" @click="toSecurity('pass')">{{
            $t('networkStatus.info_8') }}</div>
          <div id="ui-accessNetwork-submit-div-re_enter_network" class="visit-web" @click="gotoAuth">{{ $t('networkStatus.info_7') }}</div>
        </div>
      </div>

      <div
        v-if="showCheckRepair"
        id="ui-submit-check-div-check_alter_msg"
        slot="stateMsg"
        class="slot-state-msg"
        @click="toSecurity"
        v-html="$t('check.checkFault')"
      />
      <div v-if="isShowStateMsg" slot="stateMsg">
        {{ ztpStateMsg }}<a href="javascript:void(0)" style="color: #6680ff;" @click="gotoAuth">{{ $t('refresh') }}</a>。
      </div>
    </layout-state>
  </div>
</template>
<script>
import state from '@/render/components/layout/state'
import { mapGetters, mapMutations, mapState } from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import agentApi from '@/service/api/agentApi'
import qs from 'qs'
import processController from '@/render/utils/processController'
import authIndex from '@/render/utils/auth/index'
import authUtils from '@/render/utils/auth/index'
import { sleep } from '@/render/utils/global'
const loading = require('@/render/assets/stateIllustration/loading.png') // loading 图片
const access = require('@/render/assets/stateIllustration/access.png') // 已入网的图片
const checkFault = require('@/render/assets/stateIllustration/checkFault.png')

export default {
  components: {
    'layoutState': state
  },
  data() {
    return {
      state: {
        stateImg: loading,
        stateMsg: this.$t('netExamine'),
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      },
      ztpStateMsg: '',
      isShowStateMsg: false,
      isSpecUrl: false, // 跳转指定网站地址
      redirectUrl: '',
      count: -1, // 倒计时
      cutTimer: null,
      auditAlarm: false,
      checkResult: '',
      IsDoSecurityCheck: 0 // 之前安检是否有安检项
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo', 'serveEntiretyConfig', 'redirectInfo']),
    ...mapGetters(['computeNetAccessStatus']),
    // 802.1x紧急模式下认证账户名
    emergentModeUserName() {
      return _.get(this.authInfo, 'dot1x.emergentModeUserName', '')
    },
    isEmergency() {
      return authIndex.isDot1xMode() &&
        (parseInt(_.get(this.clientInfo, 'accessStatus.isEmergency', 0)) === 1 ||
          parseInt(_.get(this.clientInfo, 'accessStatus.dot1xEscapeStatus', 0)) === 1)
    },
    userName() {
      if (this.isEmergency && !_.isEmpty(this.emergentModeUserName)) {
        return this.emergentModeUserName
      }

      const userName = _.get(this.authInfo, 'basic.UserName', '')
      if (!_.isEmpty(userName)) {
        return userName
      }

      return _.get(this.clientInfo, 'accessStatus.userName', '')
    },
    authTime() {
      if (this.isEmergency && !_.isEmpty(this.emergentModeUserName)) {
        return ''
      }
      return _.get(this.clientInfo, 'accessStatus.authTime', '')
    },
    showCheckRepair() {
      return this.checkResult === 'fault' && parseInt(_.get(this.clientInfo, 'accessStatus.roleID', 0)) !== 0
    },
    showCheckDetail() {
      return parseInt(_.get(this.clientInfo, 'accessStatus.roleID', 0)) !== 0 && this.checkResult === 'success' && this.IsDoSecurityCheck === 1
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.setNetStatusState()
    },
    // 设备注册状态发生变化,变为待审核的时候重新跳转
    'clientInfo.detail.Registered': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (_.get(this.$router, 'currentRoute.path') === '/access/submit') {
            processController.set('/access/message')
          }
        }
      },
      deep: true
    },
    // 入网状态发生变化，变成未入网的时候重新跳转
    'clientInfo.accessStatus.deviceStatus': {
      handler(newVal, oldVal) {
        if (parseInt(oldVal) === 1 && parseInt(newVal) !== 1) {
          if (_.get(this.$router, 'currentRoute.path') === '/access/submit') {
            processController.set('/access/message')
          }
        }
      },
      deep: true
    },
    // 当在线状态发生变化的时候，重新跳转到欢迎页
    'clientInfo.online': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (_.get(this.$router, 'currentRoute.path') === '/access/submit') {
            processController.set('/access/message')
          }
        }
      },
      deep: true
    }
  },
  created() {
    this.init()
    this.auditAlert()
  },
  beforeDestroy() {
    this.clearRedirectTime()
    if (this.auditAlarm) {
      this.auditAlarm.close()
    }
  },
  methods: {
    ...mapMutations(['setServeEntiretyConfig', 'setAuthInfo', 'setRedirectInfo', 'setClientInfo']),
    async init() {
      // const ztpUserExceed = _.get(this.authInfo, 'basic.ztpUserExceed', true)
      // if (!ztpUserExceed) {
      //   this.$message.error(this.$t('sourcePlatform.ztpUserExceed'))
      //   this.setAuthInfo(
      //     _.merge({}, this.authInfo, {
      //       basic: {
      //         ztpUserExceed: true
      //       }
      //     })
      //   )
      // }
      await this.getCheckResult() // 查询安检结果
      const fromeMessage = parseInt(_.get(this.$route, 'query.fromeMessage', 0))
      if (fromeMessage !== 1) { // 不是来自message
        if (this.checkResult === 'false') { // 关键安检项不通过
          processController.set('/access/message')
          return
        }
        this.queryNetStatusAccess()
      } else {
        this.netLetGoInNat()
        this.setNetStatusState()
        this.setRedirectConf()
        commonUtil.setLoginRetPatch()
      }
    },
    // 查询网络放开状态
    async queryNetStatusAccess() {
      const netAccessStatus = await this.expectNetStatusAccess()
      if (netAccessStatus) {
        this.setRedirectConf()
        this.routerToSource()
      }
      this.setNetStatusState()
      commonUtil.setLoginRetPatch()
    },
    /**
     * 查询网络放开状态。这里至多查询6次，并且延迟执行
     * @return {void}
     */
    async expectNetStatusAccess(maxTries = 6) {
      let netAccessStatus = false
      const request = async() => {
        const ret = await commonUtil.netRet()
        if (ret === false) {
          console.log('netStatus接口返回失败')
        }
        return parseInt(_.get(ret, 'deviceStatus', 0)) === 1
      }

      for (let i = 0; i < maxTries; i++) {
        // 如果组件已经销毁，就不再查询
        if (_.get(this.$router, 'currentRoute.path') !== '/access/submit') {
          break
        }

        try {
          // 暂停1s
          netAccessStatus = await request()
          if (netAccessStatus !== false) {
            break
          }
          await sleep(parseInt(i * 2000))
        } catch (error) {
          console.log(error)
          break
        }
      }

      return netAccessStatus
    },
    // 设置提示语，提示图片等
    setNetStatusState() {
      const accessStatus = this.computeNetAccessStatus()
      if (_.get(accessStatus, 'isAccess')) {
        const state = {
          stateImg: this.showCheckRepair ? checkFault : access,
          stateMsg: this.showCheckRepair ? '' : this.$t('networkStatus.info_4')
        }
        this.state = { ...this.state, ...state }
        if (_.get(accessStatus, 'isZtpAbnormal')) {
          this.ztpStateMsg = this.$t('isZtpAbnormal')
          this.isShowStateMsg = true
        }
      } else {
        // 这里没有查询到网络状态为放开就直接跳转到message页面
        processController.set('/access/message')
      }
    },
    handleNext() {
      const ret = this.state['stateBtnHandle']
      if (_.isFunction(ret)) {
        ret()
      }
    },
    // 判断是否需要跳转
    setRedirectConf() {
      const query = qs.parse(location.search.substring(1))
      const firstUrl = _.get(query, 'firsturl', '')
      const fromBrowser = _.get(query, 'fromBrowser', 0) // 是不是从浏览器唤起的小助手
      const count = _.get(this.serveEntiretyConfig, 'server.Redirect.redirectTime', 0)
      const redirect = _.get(this.serveEntiretyConfig, 'server.Redirect.redirectUrl', '')
      if (this.redirectInfo.haveRedirect ||
        parseInt(_.get(this.$route, 'query.fromeMessage', 0)) === 1 || // 从message跳转过来的
        count < 1 ||
        parseInt(fromBrowser) !== 1 ||
        redirect.length === 0) {
        return
      }
      this.redirectUrl = firstUrl
      const specUrl = decodeURIComponent(redirect) // 指定网站
      if (firstUrl.length > 0 && specUrl.indexOf(firstUrl) !== -1) {
        this.isSpecUrl = false
      } else {
        this.isSpecUrl = true
        this.redirectUrl = this.parestRedirectUrl(specUrl)
      }
      if (commonUtil.isExceptionRedirect(this.redirectUrl)) { // 特定的url不用跳转
        return
      }
      this.setRedirectInfo({ haveRedirect: true })
      this.cutDown(count)
    },
    // nat环境下，重新调用一次客户端的网络放开情况
    // https://item.infogo.tech/index.php?m=bug&f=view&bugID=248
    netLetGoInNat() {
      if (_.get(this.clientInfo, 'detail.IP', '') !== _.get(this.clientInfo, 'detail.GateIP', '')) {
        const apiParam = {
          WhereIsModule: 'MsacAssAutoCheck.dll',
          WhatFuncToCall: 'ControlNetwork',
          RequestParam: _.get(this.clientInfo, 'basic.CheckResult', '') === 'fault' ? '<ASM><CheckResult>fault</CheckResult></ASM>' : '<ASM><CheckResult>success</CheckResult></ASM>'
        }
        agentApi.callAgentOneFunc(apiParam)
      }
    },
    cutDown(count) {
      this.count = count
      this.cutTimer = setInterval(() => {
        this.count--
        if (this.count === -1) {
          clearInterval(this.cutTimer)
          this.cutTimer = null
          this.redirectTo()
        }
      }, 1000)
    },
    redirectTo() {
      agentApi.windowOpenUrl(this.redirectUrl)
      this.count = -1
      this.clearRedirectTime()
    },
    /**
   *  替换跳转url里面的宏变量(只有认证后的需要替换，因为只有认证后才能拿到username和password)
   *  格式如：http://*************/?ip={ipaddress}&username={username}&password={password}，
   *  其中{ipaddress}为当前认证ip，{username}和{password}分别为认证用户名和密码的宏变量。
  **/
    parestRedirectUrl(redirectUrl) {
      const ipaddress = _.get(this.clientInfo, 'detail.IP', '')
      const username = _.get(this.authInfo, 'basic.UserName', '') || _.get(this.clientInfo, 'detail.UserName', '')
      const passwordMd5 = _.get(this.authInfo, 'basic.passwordMd5', '')
      redirectUrl = redirectUrl.replace('{ipaddress}', ipaddress)
      redirectUrl = redirectUrl.replace('{username}', username)
      redirectUrl = redirectUrl.replace('{password}', passwordMd5)
      return redirectUrl
    },
    clearRedirectTime() {
      this.cutTimer && clearInterval(this.cutTimer)
      this.cutTimer = null
    },
    // 设备即将超期提醒
    auditAlert() {
      const status = this.computeNetAccessStatus()
      if (_.get(status, 'registeredStatus.auditEndAlarn')) {
        // 因为这里compute会变动几次，为了防止多次提示，加个计数器
        if (!this.auditAlarm) {
          this.auditAlarm = this.$msg({
            message: _.get(status, 'registeredStatus.auditEndAlarn'),
            type: 'warning',
            showClose: true,
            duration: 10000
          })
        }
      }
    },
    toSecurity(event) {
      if (event === 'pass' || event.target.nodeName === 'SPAN') {
        this.$router.push({ path: '/access/check', query: { CheckSource: 3 }})
      }
    },
    async getCheckResult() {
      const ret = await agentApi.getClientBaseInfo()
      const CheckResult = _.get(ret, 'ASM.CheckResult', '')
      const basic = { ...this.clientInfo.basic, ...{ CheckResult, IsOnline: _.get(ret, 'ASM.IsOnline', 0), ServerIP: _.get(ret, 'ASM.ServerIP') }}
      this.setClientInfo({ ...this.clientInfo, ...{ basic: basic }})
      this.checkResult = CheckResult
      this.IsDoSecurityCheck = parseInt(_.get(ret, 'ASM.IsDoSecurityCheck', 0))
    },
    gotoAuth() {
      processController.next()
    },
    // 默认跳转到资源
    routerToSource() {
      if (this.count === -1 && !this.showCheckRepair && authUtils.isOpenZeroTrust()) { // 倒计时重定向或安检有隐患不自动跳资源平台
        processController.set('/source/list')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#f-submit {
  position: relative;
  height: 100%;

  .u-networe-status-user {
    margin-top: 24px;
    width: 130px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #686e84;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;

    span {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;

      &:nth-last-child(1):first-child {
        padding: 0;
        text-align: center;
      }

      &:nth-of-type(1) {
        padding-right: 16px;
        text-align: right;
        flex: 0 1 auto;
      }

      &:nth-of-type(2) {
        padding-left: 16px;
        text-align: left;
        flex: 0 0 auto;
      }
    }
  }

  .sub-btn-wrapper {
    display: flex;
    justify-content: center;

    .visit-web {
      font-size: 14px;
      font-weight: 400;
      color: $--color-primary;
      line-height: 20px;
      text-decoration: underline;
      margin-top: 24px;
      cursor: pointer;
      text-align: center;
      padding: 0 16px;

      &:hover {
        color: $--color-primary-dark-2
      }
    }

    .cutoff-line {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 2px;
        height: 16px;
        border-right: 1px solid $line-color;
      }
    }
  }

  ::v-deep .slot-state-msg {
    span {
      color: $--color-primary;
      cursor: pointer;
    }
  }
}
</style>
