<template>
  <div class="layout-header">
    <div v-if="sTitle" class="header-title">
      {{ sTitle }}
    </div>
    <div v-else class="header-logo">
      <!--如果图片加载失败就隐藏-->
      <img :src="logoSrc" alt="" onload="this.style.display = 'block'" onerror="this.style.display = 'none' ">
    </div>
    <div id="u-electron-drag" />
    <ul id="u-header-menu" class="right-wrapper">
      <li v-if="isShowAccountAvatar" id="u-avator" ref="countMenu">
        <el-dropdown id="ui-headNav-header-div-account_info" placement="bottom-start" @command="userMenuHandle" @visible-change="dropdownVisiHandle">
          <div class="user-info">
            <div class="user-face">
              <img :src="avator" alt="">
            </div>
            <span class="user-name">{{ username }}</span>
          </div>
          <el-dropdown-menu slot="dropdown" class="header-count-menu">
            <el-dropdown-item v-if="hasPeremission('changePassword')" id="ui-headNav-header-li-change_password" command="changePassword">
              <i class="iconfont icon-xiugaimima" />{{ $t('header.changePassword') }}
            </el-dropdown-item>

            <el-dropdown-item v-if="hasPeremission('editRegInfo')" id="ui-headNav-header-li-alter_register_info" command="changeRegInfo">
              <i class="iconfont icon-xiugaimima" />{{ $t('header.changeRegInfo') }}
            </el-dropdown-item>

            <el-dropdown-item v-if="hasPeremission('switchNetwork')" command="switchNetwork">
              <i class="iconfont icon-neiwaiwangqiehuan" />{{ $t('header.switchNetwork') }}
            </el-dropdown-item>

            <el-dropdown-item v-if="hasPeremission('networkAccess')" command="networkAccess">
              <i class="iconfont icon-jisuruwang" />{{ $t('header.network') }}
            </el-dropdown-item>

            <el-dropdown-item id="ui-headNav-header-li-device_info" command="myDevice">
              <i class="iconfont icon-wodeshebei" />{{ $t('header.myDevice') }}
            </el-dropdown-item>

            <el-dropdown-item id="ui-headNav-header-li-cut_account" command="changeCount">
              <i class="iconfont icon-qiehuanzhanghu1" />{{ $t('header.changeCount') }}
            </el-dropdown-item>

            <el-dropdown-item v-if="hasPeremission('applyLog')" id="ui-headNav-header-li-res_application_list" command="applyLog">
              <i class="iconfont icon-shenqingjilu" />{{ $t('header.applyLog') }}
            </el-dropdown-item>

            <el-dropdown-item id="ui-headNav-header-li-cancel_account" command="lougOut">
              <i class="iconfont icon-zhuxiao" />{{ $t('header.logout') }}
            </el-dropdown-item>

          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-if="isZtpUser" id="ui-headNav-header-li-msg_list" :class="['menu-msg', unRead>0? 'is-message':'']" @click="openMsgCenter">
        <i class="iconfont icon-xiaoxizhongxin" />
      </li>
      <li>
        <el-dropdown id="ui-headNav-header-div-setting" placement="bottom-start" @command="setHandle">
          <div class="set-icon-wrapper">
            <i class="iconfont icon-shezhi" />
          </div>
          <el-dropdown-menu slot="dropdown" class="header-count-menu">
            <el-dropdown-item id="ui-headNav-header-li-lang_change" command="changeLange"><i class="iconfont icon-qianduanruwang-yuyanqiehuan" />{{ $t('header.lang') }}</el-dropdown-item>
            <el-dropdown-item v-if="isDox8021x" id="ui-headNav-header-li-cut_8021x" command="changeMode"><i class="iconfont icon-qianduanruwang-yuyanqiehuan" />{{ isDot1xMode ? $t('auth.commonMode') : $t('auth.8021xMode') }}</el-dropdown-item>
            <el-dropdown-item id="ui-headNav-header-li-check_serverip" command="getCurrentIP"><i class="iconfont icon-neiwaiwangqiehuan" />{{ $t('changeNet') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li class="window-operate" @click="minimizeWnd"><i class="iconfont icon-zuixiaohua" /></li>
      <li class="window-operate" @click="maximizeWndOrNot"><i :class="[isMaxWindow ? 'icon-tuichuquanping' : 'icon-quanping', 'iconfont']" /></li>
      <li class="window-operate" @click="closeWnd"><i class="iconfont icon-guanbichuangkou" /></li>

    </ul>
    <!-- 侧边抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
      :append-to-body="true"
      :size="drawerSize"
      :wrapper-closable="true"
      :destroy-on-close="true"
      :with-header="countCommand !== 'msgCenter'"
      @close="closeDrawer"
    >
      <component
        :is="countCommand"
        v-if="drawer"
        :edit="true"
        :device-inner-back="deviceInnerBack"
        :drawer-data="drawerData"
        @hdEvent="hdEventHandle"
        @changeVisible="changeVisible"
      />
    </el-drawer>

    <!-- 登出提示弹框 -->
    <verticalDialog
      :show="showLogout"
      :show-close="false"
      :show-foot="true"
      width="384px"
      class="loginout-m-confirm-dialog"
      :cancel-text=" $t('dialogFoot.no')"
      :confirm-text="$t('dialogFoot.yes')"
      pop-name="headNav-header"
      @cancel="showLogout = false"
      @confirm="logoutHandle"
    >
      <div slot="header" class="v-header">
        <i class="iconfont icon-putongxiangbuhegui" />
        {{ $t('tips') }}
      </div>
      <div class="g-s-diaolog-content">
        <div class="form-content">
          <p class="outline-tips">{{ logoutMsg }}</p>
        </div>
      </div>
    </verticalDialog>

    <!-- 内外网切换弹框 -->
    <ConfirmDialog :show.sync="showSwitch" width="285px" @ok="switchHandle">
      <div class="s-title">{{ $t('header.switchNetwork') }}</div>
      <div class="s-content">
        <span class="s-text">{{ $t('header.optionNetwork') }}</span>
        <el-radio v-model="IsolateType" label="1">{{ $t('header.intNetwork') }}</el-radio>
        <el-radio v-model="IsolateType" label="2">{{ $t('header.extNetwork') }}</el-radio>
      </div>
    </ConfirmDialog>

    <!-- 服务器ip -->
    <ConfirmDialog id="ip-info-dialog" pop-name="headNav-header" :cancel-text="$t('header.close')" :show.sync="showIpDialog" :show-confirm-button="false" width="285px">
      <p class="ip-content">{{ ipText }}</p>
      <!-- <ul class="netcard-list">
        <li v-for="item in netCardList" :key="item.ID">{{ item.Name }}
          <i :title="item.Status === 'true' ?$t('header.linkSuccess'):$t('header.linkFail')" :class="['iconfont', item.Status=== 'true' ? 'icon-lianjie' : 'icon-duankailianjie']" />
        </li>
      </ul> -->
    </ConfirmDialog>
  </div>
</template>
<script>
import changePassword from '../countMenu/changePassword.vue'
import changeRegInfo from '@/render/views/accessNetwork/register'
import networkAccess from '../countMenu/networkAccess.vue'
import myDevice from '../countMenu/myDevice.vue'
import changeCount from '../countMenu/changeCount.vue'
import { setLang, i18n } from '@/render/lang'
import { mapGetters, mapMutations } from 'vuex'
import { EventBus } from '@/render/eventBus'
import agentApi from '@/service/api/agentApi'
import localStorage from '@/render/utils/cache/localStorage'
const avator = require('@/render/assets/stateIllustration/avator.png')
import proxyApi from '@/service/api/proxyApi'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import authIndex from '@/render/utils/auth/index'
import authTypes from '@/render/utils/auth/authTypes'
import urlUtils from '@/render/utils/url'
import { ssoLogout } from '@/render/utils/auth/sso'
import loading from '@/render/components/globalComponents/loading/index'
import { clearToken } from '@/render/utils/token'
import authUtils from '@/render/utils/auth/index'
import applyLog from '../countMenu/applyLog'
import msgCenter from '../countMenu/msgCenter'
import serverAddr from '../countMenu/serverAddr'
import { TestQtModule } from '@/render/utils/global'
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import os_browser_info from '@/render/utils/os_browser_info'

export default {
  name: 'Header',
  components: {
    changePassword,
    changeRegInfo,
    networkAccess,
    myDevice,
    changeCount,
    applyLog,
    msgCenter,
    serverAddr
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      Qty: 0,
      drawerTitle: '',
      countCommand: 'changePassword',
      drawerSize: 424,
      showLogout: false,
      logoutMsg: '',
      logoutType: 1, // 1账户 2客户端
      isMaxWindow: false, // 当前窗口是最最大化还是正常形态
      deviceInnerBack: false,
      avator: avator,
      showSwitch: false,
      IsolateType: '',
      IsOpenIsolate: 0,
      drawerData: {},
      showIpDialog: false,
      ipText: '',
      netCardList: [],
      isDox8021x: false
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'authInfo',
      'computeNetAccessStatus',
      'msgCenter'
    ]),
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    // 是否紧急
    isDot1xEmergency() {
      return this.isDot1xMode &&
        (parseInt(_.get(this.clientInfo, 'accessStatus.isEmergency', 0)) === 1 ||
        parseInt(_.get(this.clientInfo, 'accessStatus.dot1xEscapeStatus', 0)) === 1)
    },
    // 账户名
    username() {
      let userName = _.get(this.authInfo, 'dot1x.emergentModeUserName', '')
      if (this.isDot1xEmergency && !_.isEmpty(userName)) {
        return userName
      }

      userName = _.get(this.authInfo, 'basic.UserName', '')
      if (!_.isEmpty(userName)) {
        return userName
      }
      return _.get(this.clientInfo, 'accessStatus.userName', '')
    },
    // 是否已入网
    isAccess() {
      return !!_.get(this.computeNetAccessStatus(), 'isAccess', false)
    },
    /*
    * 是否现实头像和用户名(如果是免认证，则无用户名)
    * 3746修改逻辑,认证过后也显示(或者当前网络是已放开,或者是8021.x紧急模式)
    * @ return {Boolean}
    */
    isShowAccountAvatar() {
      // 网络已经放开，并且在线表存在账户名、或者为免认证方式
      const isOnline = _.get(this.clientInfo, 'online', false)
      const netGo = this.isAccess && (!_.isEmpty(_.get(this.clientInfo, 'accessStatus.userName')) || _.get(this.clientInfo, 'accessStatus.lastAuthType') === authTypes.NoAuth)
      const dot1xemergentMode = this.isDot1xEmergency && !_.isEmpty(_.get(this.authInfo, 'dot1x.emergentModeUserName'))
      const hasAuth = !_.isEmpty(_.get(this.authInfo, 'basic.UserName', '')) || _.get(this.authInfo, 'basic.AuthType', '') === authTypes.NoAuth || _.get(this.clientInfo, 'accessStatus.userName')
      return (hasAuth || netGo || dot1xemergentMode) && isOnline
    },
    sTitle() {
      if (this.$i18n.locale === 'zh') {
        return _.get(this.computeServeEntiretyConfig, 'ADMINISTRATORINFO.sTitle', '')
      }
      return _.get(this.computeServeEntiretyConfig, 'ADMINISTRATORINFO.sTitle_en', '')
    },
    logoSrc() {
      return urlUtils.getBaseIPPort() + '/oem/logo/top.gif?time=' + new Date().getTime()
    },
    // 是否开启极速入网
    canSpeedNetAccess() {
      const currentAuthType = _.get(this.authInfo, 'basic.AuthType') || _.get(this.clientInfo, 'accessStatus.lastAuthType', 'User')

      // 当前设备认证方式不能是免认证
      if (currentAuthType === authTypes.NoAuth) {
        return false
      }

      // 场景信息里面是否开启极速入网并且不是免认证方式
      if (parseInt(_.get(this.serveEntiretyConfig, 'sceneConfig.IsAuthorizedAccess', 0)) === 0 ||
        parseInt(_.get(this.serveEntiretyConfig, 'sceneConfig.IsAuth', 0)) === 0
      ) {
        return false
      }

      // 来宾账号不显示
      if (_.get(this.clientInfo, 'accessStatus.lastAuthType') === 'Guest') {
        return false
      }

      return true
    },
    // 是否有权限展示菜单
    hasPeremission() {
      return function(type = 'editRegInfo') {
        if (type === 'editRegInfo') {
          return parseInt(_.get(this.serveEntiretyConfig, 'server.INDEXREG.IPMdReg')) === 1 && this.isAccess && _.get(this.clientInfo, 'accessStatus.lastAuthType') !== 'Guest'
        }
        if (type === 'changePassword') {
          return parseInt(_.get(this.serveEntiretyConfig, 'server.ChangePassword.IsChangePassword')) === 1
        }
        if (type === 'networkAccess') {
          return this.isAccess && this.canSpeedNetAccess && !(parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAccess', 1)) === 0)
        }
        if (type === 'switchNetwork') {
          return parseInt(this.IsOpenIsolate) === 1
        }
        if (type === 'applyLog') {
          return authUtils.isOpenZeroTrust()
        }
      }
    },
    isZtpUser() {
      return this.isAccess && authUtils.isOpenZeroTrust()
    },
    unRead() {
      return _.get(this.msgCenter, 'unRead', 0)
    },
    userId() {
      return _.get(this.clientInfo, 'accessStatus.UserID') || _.get(this.authInfo, 'basic.ID')
    },
    // 是否为网络隔离模式
    netIsoLate() {
      const osType = os_browser_info.os_type || ''
      return parseInt(_.get(this.serveEntiretyConfig, 'server.ZTP.DivideGroup')) === 1 && osType !== 'mac'
    }
  },
  watch: {
    isShowAccountAvatar() {
      this.$nextTick(() => {
        this.getCountMenuWidth()
      })
    },
    isZtpUser(newVal, oldVal) {
      this.$nextTick(() => {
        this.getCountMenuWidth()
      })
      if (newVal) {
        this.initMsgCenter()
        // 通知客户端网络信息
        if (this.netIsoLate) {
          sourceListUtil.setNotifyResGroup()
        }
      }
    },
    userId(newVal, oldVal) {
      console.log('用户id变动', newVal, oldVal)
      console.debug('用户id变动')
      commonUtil.getWebGateWay() // 更新网关状态
    }
  },
  mounted() {
    this.addEventBus()

    // 获取内外网配置
    this.getSwNetworkData()
    this.autochangeMode()
    this.getCountMenuWidth() // 小助手初始化时网络变动header还没渲染监听不到，这里补掉，bugID=16790
  },
  beforeDestroy() {
    EventBus.$off('openPassword')
  },
  methods: {
    ...mapMutations(['setAuthInfo', 'setClientInfo', 'setMsgCenter', 'setGateInfos', 'serveEntiretyConfig']),
    // 最小化
    minimizeWnd() {
      agentApi.minimizeWnd()
    },
    maximizeWndOrNot() {
      if (this.isMaxWindow) {
        agentApi.normalnizeWnd()
        this.isMaxWindow = false
      } else {
        agentApi.maximizeWnd()
        this.isMaxWindow = true
      }
    },
    autochangeMode() {
      // 802.1x模式关闭后界面切换普通认证
      const dot1x = parseInt(_.get(this.serveEntiretyConfig, 'server.dot1x', 1))
      if (dot1x === 1) {
        this.isDox8021x = true
      }
      if (this.isDox8021x === false && this.isDot1xMode === true) {
        this.changeMode()
      }
      return true
    },
    closeWnd() {
      if (!TestQtModule('UIPlatform_Window', 'HideWnd')) { // 客户端不升级没有该接口
        try {
          this.$ipcSend('UIPlatform_Window', 'TerminateWnd')
        } catch (error) {
          this.$message.error(this.$t('apiUpdateTip'))
        }
        return
      }
      EventBus.$emit('closeAssui')
      // 重置参数
      this.resetState()
      this.$nextTick(() => {
        agentApi.hideWend()
      })
    },

    async setHandle(command) {
      if (command === 'getCurrentIP') {
        this.drawerSize = 450
        this.drawerTitle = this.$t('changeNet')
        this.countCommand = 'serverAddr'
        this.drawer = true
      } else if (command === 'changeLange') {
        const locale = this.$i18n.locale
        setLang(locale === 'zh' ? 'en' : 'zh')
      } else if (command === 'changeMode') {
        this.changeMode()
      }
    },
    /**
     * 切换模式
     */
    async changeMode() {
      // 认证页面则认证页面处理，否则本页面处理
      if (this.$router.currentRoute.path === '/access/auth') {
        EventBus.$emit('Dot1x:NewAgentMode', -1)
        return
      }
      const param = { AgentMode: this.isDot1xMode ? '0' : '1' }
      if (param.AgentMode === '1') {
        await commonUtil.basic()
      } else {
        await commonUtil.checkDefaultPort()
      }
      const switchRes = await agentApi.switchAgentMode(param)
      if (parseInt(_.get(switchRes, 'ASM.Result', 0)) === 0) {
        this.setClientInfo(_.merge({}, this.clientInfo, { basic: { AgentMode: param.AgentMode }}))
        this.$message.success(this.$t('auth.switchModeSuccess'))
      } else {
        this.$message.error(this.$t('auth.switchModeErr'))
      }
    },
    userMenuHandle(command, data = {}) {
      this.countCommand = command
      switch (command) {
        case 'changePassword':
          // 如果返回失败，则不需要展示
          if (!this.changePasswordHandle(data)) {
            return
          }
          break
        case 'changeRegInfo':
          this.drawerSize = 432
          this.drawerTitle = this.$t('header.changeRegInfo')
          break
        case 'networkAccess':
          this.deviceInnerBack = true
          this.drawerSize = 500
          this.drawerTitle = this.$t('header.network')
          break
        case 'myDevice':
          this.drawerSize = 500
          this.drawerTitle = ''
          break
        case 'changeCount':
          this.drawerSize = 581
          this.drawerTitle = ''
          break
        case 'lougOut':
          this.logoutMsg = this.$t('header.logoutTip')
          this.showLogout = true
          this.logoutType = 1
          break
        case 'switchNetwork':
          this.showSwitch = true
          break
        case 'applyLog':
          this.drawerSize = 650
          this.drawerTitle = this.$t('header.applyLog')
      }
      if (command !== 'lougOut' && command !== 'switchNetwork') {
        this.drawer = true
      }
    },
    /**
     * 修改密码弹框
     */
    changePasswordHandle(data = {}) {
      if (parseInt(_.get(this.computeServeEntiretyConfig, 'ChangePassword.changePassType')) === 1) {
        if (!_.isEmpty(data) && !_.isNil(data) && _.get(data, 'username')) {
          this.drawerData = data
        } else {
          // 优先入网状态、再认证
          let username = _.get(this.clientInfo, 'accessStatus.userName')
          if (_.isUndefined(username) || _.isEmpty(username)) {
            username = _.get(this.authInfo, 'basic.UserName', '')
          }
          this.drawerData = { username }
        }
        this.drawerTitle = this.$t('header.changePassword')
        this.drawerSize = 424
        return true
      } else {
        let changePassUrl = _.get(this.computeServeEntiretyConfig, 'ChangePassword.ChangeUrl')
        if (changePassUrl.indexOf('/a/changePass.php') === 0) {
          changePassUrl = urlUtils.getBaseIPPort() + changePassUrl
          if (changePassUrl.indexOf('?') === -1) {
            changePassUrl += '?'
          } else {
            changePassUrl += '&'
          }
          changePassUrl += 'local_lguage_set=' + (i18n.locale || 'zh')
        }
        agentApi.windowOpenUrl(changePassUrl)
        return false
      }
    },
    isSsoAuth() {
      const lastAuthType = _.get(this.clientInfo, 'accessStatus.lastAuthType')
      const ssoConfig = _.get(this.serveEntiretyConfig, 'server.SSOConfig', {})
      console.log('是sso吗', ssoConfig[lastAuthType])
      return ssoConfig[lastAuthType]
    },
    // 注销
    async logoutHandle() {
      this.showLogout = false

      loading.start({ msg: i18n.t('header.logouting') })
      // 非可信设备 登出
      if (this.logoutType === 1) {
        try {
          // sso退出登录
          let logoutUrl
          if (this.isSsoAuth()) {
            logoutUrl = await ssoLogout(_.get(this.clientInfo, 'accessStatus.lastAuthType'))
          }
          // 注销账户的操作
          const ret = await proxyApi.cutoffDevice({
            device_id: _.get(this.clientInfo, 'detail.DeviceID', 0),
            remark: 'LogOut'
          })
          if (parseInt(_.get(ret, 'errcode')) !== 0) {
            if (!_.get(ret, 'errmsg')) {
              this.$message.error(this.$t('logoutErr'))
            }
            loading.destory()
            return
          }
          commonUtil.setLoginRet({ token: '', UserID: '', LoginRet: '0' })

          await agentApi.logOut({
            IsCredibleDevice: _.get(this.clientInfo, 'detail.IsTrustDev', '0')
          })
          this.setGateInfos({ state: 2, gateWayMap: {}, total: 0, VPNStatus: 0 })
          clearToken()
          // 审核和安检后审核的标记删除，再审核后全部跳转到欢迎页。
          localStorage.removeItem('auditNextStatus')
          localStorage.removeItem('auditCheckNextStatus')
          // 取消自动登录
          authIndex.config.AutoLogin = -1

          // 如是8021.模式,直接设置为离线
          if (this.isDot1xMode) {
            this.setClientInfo(_.merge({}, this.clientInfo, { basic: { IsOnline: 0 }}))
          }

          this.setAuthInfo({ ...this.authInfo, ...{ basic: {}}})
          // 先清空
          this.setClientInfo({ ...this.clientInfo, ...{ accessStatus: {}}})

          // 这里跳转的时候，加个时间戳，这样从/access/message跳转到message的时候可以强制刷新一下
          const timestamp = new Date().getTime()
          this.$router.push({ name: 'message', params: { forceTo: true }, query: { t: timestamp }})
          if (_.isString(logoutUrl) && logoutUrl !== '') {
            console.log('logoutUrl:'.logoutUrl)
            agentApi.windowOpenUrl(logoutUrl)
          }
        } catch (error) {
          console.error('退出登录错误', error)
        }
        loading.destory()
      }
    },
    /* 获取顶部头像昵称总长度*/
    getCountMenuWidth() {
      const addWidth = this.isZtpUser ? 44 : 0
      const width = parseInt(document.getElementById('u-avator') ? document.getElementById('u-avator').offsetWidth : 0)
      this.$ipcSend('UIPlatform_Window', 'SetTitleDimension', { nHeight: 50, nNameWidth: parseFloat(width) + addWidth })
    },
    hdEventHandle(data) {
      switch (data.type) {
        case 'router' :
          this.userMenuHandle(data.val)
          break
      }
    },
    // 获取内外网切换初始数据
    async getSwNetworkData() {
      const res = await agentApi.getNetIsolate({ PolicyName: 'Policy_Hostfirewall' })
      if (res) {
        this.IsolateType = _.get(res, 'ASM.IsolateType')
        this.IsOpenIsolate = _.get(res, 'ASM.IsOpenIsolate')
      }
    },
    // 内外网切换
    async switchHandle() {
      const res = await agentApi.setNetIsolate({ IsolateType: this.IsolateType })
      if (_.get(res, 'ASM.ErrCode') === '0' || _.get(res, 'ASM.ErrCode') === 0) {
        this.$message.success(this.$t('header.switchSuccess'))
      } else {
        this.$message.error(this.$t('header.switchFail'))
      }
    },
    dropdownVisiHandle(val) {
      if (val) {
        this.getSwNetworkData()
      }
    },
    closeDrawer() {
      this.deviceInnerBack = false
    },
    changeVisible(isClose) {
      this.drawer = isClose
    },
    // 添加事件总线监听
    addEventBus() {
      EventBus.$on('openPassword', (data) => {
        this.$dialogTip({
          content: this.$t('auth.mustChangePass'),
          popName: 'ui-accessNetwork-header',
          success: () => {
            this.userMenuHandle('changePassword', data)
          }
        })
      })
    },
    // 重置参数
    resetState() {
      if (sessionStorage.ipamRedirect) {
        sessionStorage.removeItem('ipamRedirect')
      }
    },
    openMsgCenter() {
      this.drawerSize = 650
      this.drawerTitle = this.$t('msgCenter.title')
      this.drawer = true
      this.countCommand = 'msgCenter'
    },
    async initMsgCenter() {
      const ret = await proxyApi.getResMsg({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        start: 0,
        limit: 1
      }, { showError: false, handleLoginDate: false })
      this.setMsgCenter({ ...this.msgCenter, ...{ unRead: _.get(ret, 'data.unReadCount', 0) }})
    }
  }
}
</script>
<style lang="scss" scoped>
.layout-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
  box-shadow: 0px 2px 6px 0px rgba(46, 60, 128, 0.2);
  color: $light-color;
  padding-left: 24px;
  .header-title {
    line-height: 50px;
    font-size: 18px;
    font-weight: 500;
  }
  .header-logo{
    height: 50px;
    display: flex;
    align-items: center;
    img{
      max-width: 79px;
      max-height: 28px;
    }
  }
  #u-electron-drag{
    display: flex;
    flex: 1;
    height: 100%;
    -webkit-app-region: drag;
  }
  .right-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    & > li:hover {
      background: $--color-primary-dark-2;
    }
    .user-info {
      display: flex;
      align-items: center;
      height: 50px;
      padding: 0 14px;
      cursor: pointer;
      .user-face {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 6px;
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
      .user-name {
        color: $light-color;
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .set-icon-wrapper, .menu-msg {
      width: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      height: 50px;
      position: relative;
      .icon-shezhi {
        color: $icon-color;
        font-size: 18px;
      }
    }
    .is-message{
      &::after {
          content: '';
          position: absolute;
          top: 17px;
          right: 13px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: $error-1;
        }
    }
    .window-operate, #ui-headNav-header-li-msg_list {
      width: 44px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .iconfont {
        color: $icon-color;
        font-size: 16px;
      }
    }
  }
}

.count-title {
  color: $default-color;
  i {
    font-style: normal;
    color: $title-color;
  }
}
.el-dropdown-menu.header-count-menu ::v-deep .el-dropdown-menu__item{
  padding-left: 40px;
  position: relative;
  i{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 16px;
    font-size: 14px;
  }
  .icon-qianduanruwang-yuyanqiehuan{
    font-size: 15px;
  }
  .icon-qiehuanzhanghu1, .icon-jisuruwang{
    font-size: 16px;
  }
  .icon-neiwaiwangqiehuan{
    font-size: 16px;
  }
}

.s-title{
  margin-top: 18px;
  margin-left: 18px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 500;
  color: $title-color;
}
.s-content{
  padding: 24px 32px 29px 32px;
  font-size: 13px;
  line-height: 18px;
  .s-text{
    color: $default-color
  }
}
.change-reg-info{
  padding-left: 8px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: $title-color;
}
</style>
<style lang="scss">
body .el-dialog-ip-box {
  width: 260px;
  .el-message-box__content {
    padding: 20px 15px;
  }
}
.s-content .el-radio{
  margin-right: 13px;
  .el-radio__label{
    padding-left: 8px;
    font-size: 13px;
    color: $title-color;
    line-height: 18px;
  }
}
#ip-info-dialog{
  .ip-content{
    margin-top: 24px;
    margin-bottom: 24px;
    padding: 0 24px;
    line-height: 20px;
    font-size: 14px;
    color: $title-color;
  }
  .netcard-list{
    margin-top: 16px;
    padding: 0 24px;
    li{
      display: flex;
      align-items: center;
      line-height: 20px;
      font-size: 14px;
      color: $title-color;
      margin-bottom: 10px;
      &:last-child{
        margin-bottom: 24px;
      }
      i{
        font-size: 16px;
        margin-left: 16px;
      }
      .icon-lianjie{
        color: $success;
      }
      .icon-duankailianjie{
        color: $error;
      }
    }
  }
  .el-dialog__footer button{
    height: 40px;
    line-height: 40px;
    border-bottom-right-radius: 4px;
  }
}

.loginout-m-confirm-dialog{
    .v-header{
        line-height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        color: $title-color;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 6px;
            font-weight: 400;
        }
    }
    .outline-tips{
        padding: 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
}
</style>
