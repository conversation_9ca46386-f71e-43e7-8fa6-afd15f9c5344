<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle softInstall">
      <p class="model-title">
        {{ $t("check.SoftInstallStat.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <div v-for="(item, index) in softList" :key="item.SoftName" class="pc-info">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.SoftInstallStat.h_3_rd") }}
                  <span>{{ item.SoftName }}</span>
                </div>
                <span v-if="item.RepairType === 'url'" :class="[checkData.hasFixed ? 'disable-link-btn': 'link-btn']" @click="fixHandle(item, index)">{{ $t('check.SoftInstallStat.js_5_s') }}</span>

                <button v-if="item.RepairType === 'path'" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t('check.SoftInstallStat.js_7_s') }}
                </button>
              </div>
              <div v-if="deviceType!=='mac'" class="optional-item margin">
                {{ $t("check.SoftInstallStat.js_11_rd") }}
                <span>{{ item.SoftCompare }}</span>
              </div>
              <div v-else class="optional-item margin">
                {{ $t("check.SoftInstallStat.js_13_rd") }}
                <span>{{ item.Certification }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.SoftInstallStat.js_12_rd") }}
                <span>{{ item.SoftVersion }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.SoftInstallStat.js_3_rd") }}
                {{ item.SoftRemark }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  name: 'CheckSoftInstallStat',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      softList: []
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    },
    deviceType() {
      return _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    }
  },
  mounted() {
    this.getSoftList(this.checkData)
  },
  methods: {
    async fixHandle(item, index) {
      if (item.RepairType === 'url') {
        if (item.Url) {
          this.openUrl(item.Url)
        }
      } else if (item.RepairType === 'path') {
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            Path: item.Path,
            Param: item.Param
          },
          RepairType: 0,
          CreateProgress: 1
        }
        await this.submitHandle({
          params,
          CheckItem: item,
          needProccess: true,
          tip: this.$t('check.SoftInstallStat.js_7_s')
        })
        this.$set(this.softList, index, item)
      }
    },
    changeLang(softcompare) {
      if (!softcompare) {
        return ''
      }
      var str = ''
      switch (softcompare) {
        case 'NoCompare':
          str = this.$t('check.SoftInstallStat.h_32_rd')
          break
        case 'MoreThan':
          str = this.$t('check.SoftInstallStat.h_33_rd')
          break
        case 'Equal':
          str = this.$t('check.SoftInstallStat.h_34_rd')
          break
        default:
          str = this.$t('check.SoftInstallStat.h_35_rd')
          break
      }
      return str
    },
    getSoftList(checkData) {
      // 看是否存在备份
      const list = _.get(this.checkData, 'CheckResult.CheckType.list')
      if (list) {
        list.forEach(item => {
          item.hasFixed = this.checkData.hasFixed
        })
        this.softList = list
        return
      }

      let mustInstall = _.get(checkData, 'CheckResult.CheckType.Info.SoftName')
      let policy = _.get(checkData, 'CheckType.Option.Item')
      if (!mustInstall || !policy) {
        return
      }
      mustInstall = mustInstall.split(',')
      if (!_.isArray(policy)) {
        policy = [policy]
      }
      const tmpObj = []
      mustInstall.forEach((item, index) => {
        for (let p = 0; p < policy.length; p++) {
          if (item === policy[p].SoftName) {
            tmpObj.push({
              SoftName: policy[p].SoftName,
              SoftVersion: policy[p].SoftVersion,
              SoftCompare: this.changeLang(policy[p].SoftCompare),
              SoftRemark: policy[p].SoftRemark,
              RepairType: policy[p].RepairType,
              Url: policy[p].Url,
              Path: policy[p].Path,
              Param: policy[p].Param,
              Certification: policy[p].Certification || ''
            })
            tmpObj[index].hasFixed = this.checkData.hasFixed
          }
        }
      })
      this.checkData.CheckResult.CheckType.list = tmpObj // 存备份下次进来使用备份
      this.softList = tmpObj
    }
  }
}
</script>
<style lang="scss" scoped>
.softInstall .model-content .pc-info .pc-info-rit {
  padding-left: 0;
}
</style>
