<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-12 14:24:21
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-24 13:58:11
 * @Description: 专门用来表示某一类状态的组件
-->
<template>
  <div class="f-state">
    <div class="u-wrap">
      <div v-if="stateImg" class="u-state-img">
        <img :src="stateImg">
      </div>
      <div class="u-state-msg">
        <div v-if="stateMsg" :id="stateId" class="u-state-msg-html" v-html="stateMsg" />
        <slot name="stateMsg" />
      </div>
      <div v-if="stateBtnTxt && (stateBtnHandle || stateBtnFun)" class="u-state-handle">
        <el-button
          :id="stateBtnId"
          :loading="stateBtnLoading"
          :type="computeType"
          :disabled="stateBtnDisabled"
          @click="stateBtnHandle"
        >
          {{ stateBtnTxt }}
        </el-button>
      </div>
      <div class="u-extend">
        <slot />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    stateImg: {
      type: String,
      default: ''
    },
    stateMsg: {
      type: String,
      default: ''
    },
    stateId: {
      type: String,
      default: ''
    },
    stateBtnTxt: {
      type: String,
      default: ''
    },
    stateBtnLoading: {
      type: Boolean,
      default: false
    },
    stateBtnDisabled: {
      type: Boolean,
      default: false
    },
    stateBtnId: {
      type: String,
      default: ''
    },
    stateBtnFun: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    computeType() {
      if (this.stateBtnDisabled) {
        return 'info'
      } else {
        return 'primary'
      }
    }
  },
  methods: {
    stateBtnHandle() {
      const btnHandle = this.stateBtnFun
      if (_.isFunction(btnHandle)) {
        btnHandle()
      }
      this.$emit('stateBtnHandle')
    }
  }
}
</script>
<style lang="scss" >
    .f-state{
        width: 100%;
        min-height:100%;
        display: flex;
        .u-wrap{
            width: 100%;
            min-height:100%;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            .u-state-img{
                img{
                    width:252px;
                    height:auto;
                }
            }
            .u-state-msg{
                width: 100%;
                margin-top:45px;
                font-size: 16px;
                font-weight: 500;
                color: #686e84;
                line-height: 22px;
                text-align:center;
                padding:0 20px;
            }
            .u-state-handle{
                margin-top:32px;
                .el-button{
                  padding:10px 144px;
                }
            }
            .u-extend{
              width: 100%;
            }
        }
    }
</style>
