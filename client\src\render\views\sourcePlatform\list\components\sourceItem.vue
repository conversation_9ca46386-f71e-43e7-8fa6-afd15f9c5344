<template>
  <div class="s-group-item-container">
    <div
      :id="`ui-ztp-resource-div-${groupResId}_${dataItem.ResID}`"
      :class="[
        'source-item',
        dataItem.IsInternet === '0' || parseInt(dataItem.IsApply) === 1 || !isCanView||isUnlink ? 'slodout-source-item' : '',
        parseInt(dataItem.IsApply) === 1 ?'apply-source-item':''
      ]"
      @mouseenter="itemEnterHandle"
      @mouseleave="itemLeaveHandle"
      @click="clickHandle"
    >
      <div class="left-wrapper">
        <div class="img-box">
          <img :src="sourceImg" alt="">
        </div>

        <div class="text-content">
          <p class="title">
            <el-tooltip
              effect="light"
              :visible-arrow="false"
              popper-class="source-tool-tip"
              class="item"
              :content="dataItem.ResName"
              placement="bottom-start"
              :open-delay="1000"
            >
              <span
                :class="[
                  dataItem.Result === '0' || dataItem.IsInternet === '0' || !isCanView||isUnlink ? 'tag-limit-name' : '',
                  'cur-name',
                ]"
              >
                {{ dataItem.ResName }}
              </span>
            </el-tooltip>

            <span class="u-tags">
              <span v-if="dataItem.Result === '0'" class="limit-tag text-clamp">
                <el-tooltip
                  effect="light"
                  :visible-arrow="false"
                  popper-class="source-tool-tip"
                  class="item"
                  :content="dataItem.ResName"
                  placement="bottom-start"
                  :open-delay="1000"
                >
                  <span
                    :class="[
                      dataItem.Result === '0' || dataItem.IsInternet === '0' ? 'tag-limit-name' : '',
                      'cur-name',
                    ]"
                  >
                    <el-tooltip
                      effect="light"
                      :visible-arrow="false"
                      popper-class="source-tool-tip"
                      class="item"
                      :content="dataItem.Reason"
                      placement="bottom-start"
                      :open-delay="1000"
                    >
                      <span>{{ $t('sourcePlatform.limitedNet') }}</span>

                    </el-tooltip>
                  </span>
                </el-tooltip>

              </span>
              <span v-if="dataItem.IsInternet === '0'" class="sold-out-tag text-clamp">
                {{ $t('sourcePlatform.slodOut') }}
              </span>
              <span v-if="showProxyUnlink" class="sold-out-tag text-clamp">
                {{ $t('sourcePlatform.proxyUnlink') }}
              </span>
            </span>
          </p>
          <el-tooltip
            effect="light"
            :visible-arrow="false"
            popper-class="source-tool-tip"
            class="item"
            :content="dataItem.Remark"
            placement="bottom-start"
            :open-delay="1000"
          >
            <p v-show="dataItem.Remark" class="sub-title text-clamp">
              {{ dataItem.Remark }}
            </p>
          </el-tooltip>
          <span v-if="parseInt(dataItem.IsApply) === 1" :id="`ui-ztp-resource-span-apply_btn_${dataItem.ResID}`" class="res-apply" @click.stop="applyHandle"><i class="iconfont icon-ziyuanshenqing" />{{ $t('applyRes.add') }}</span>
          <span v-if="dataItem.Expired" class="reamin-time sub-title ">{{ $t('msgCenter.reTime') }}：{{ dataItem.Expired }}</span>
        </div>
      </div>
      <div v-if="parseInt(dataItem.IsApply) !== 1" class="right-wrapper">
        <i v-if="parseInt(dataItem.AccessTypeID) === 312 && dataItem.accessMode === 'path'" :style="{opacity:showOpreate?1:0}" class="iconfont icon-gengduo" @click="seeDetailHandle" />
        <el-tooltip
          v-else
          :visible-arrow="false"
          :popper-class="popperClass"
          class="item"
          placement="bottom-start"
        >
          <div slot="content">
            <p class="source-detail"><span class="label">{{ $t('sourcePlatform.contact') }}</span><span>{{ dataItem.Username }}</span></p>
            <p><span class="label">{{ $t('sourcePlatform.tel') }}</span><span>{{ dataItem.Tel }}</span></p>
            <p :class="[dataItem.isIP? '':'last-p']"><span class="label">{{ $t('sourcePlatform.sourceType') }}</span><span>{{ formateType(dataItem) }}</span></p>
            <p v-if="dataItem.isIP && !isHideResDetail" class="last-p"><span class="label">{{ $t('sourcePlatform.address') }}</span><span class="see-detail" @click="toSeeDtail">{{ $t('sourcePlatform.seeDetail') }}>></span></p>
            <!-- <p v-if="dataItem.isIP" class="last-p"><span class="label">{{ $t('sourcePlatform.address') }}</span><span>{{ formateIp(dataItem.IP) }}</span></p> -->
          </div>
          <i :style="{opacity:showOpreate?1:0}" class="iconfont icon-gengduo" />
        </el-tooltip>
        <el-tooltip
          v-if="canCollect"
          effect="light"
          :visible-arrow="false"
          popper-class="source-tool-tip"
          class="item"
          :content="
            dataItem.collect
              ? $t('sourcePlatform.cancelCollect')
              : $t('sourcePlatform.collect')
          "
          placement="bottom-start"
        >
          <i
            v-show="showOpreate || dataItem.collect"
            :id="`ui-ztp-resource-i-res_${dataItem.ResID}_collect_img`"
            class="iconfont"
            :class="['iconfont', dataItem.collect ? 'icon-yishoucang' : 'icon-weishoucang']"
            @click="collectHandle"
          />
        </el-tooltip>
        <el-tooltip
          v-if=" canEdit"
          effect="light"
          :visible-arrow="false"
          popper-class="source-tool-tip"
          class="item"
          :content="$t('sourcePlatform.edit')"
          placement="bottom-start"
        >
          <i v-show="showOpreate" class="iconfont icon-xiugaimima" @click="editAccountHandle" />
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
const defaultImg = require('@/render/assets/sourcePlatform/sourceDefault.png')
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import os_browser_info from '@/render/utils/os_browser_info'
export default {
  props: {
    groupResId: {
      type: String,
      default: ''
    },
    dataItem: {
      type: Object,
      default: function() {
        return {}
      }
    },
    urlPrefix: {
      type: String,
      default: ''
    },
    ztpUserExceed: {
      type: Boolean,
      default: true
    },
    isInternal: {
      type: Number,
      default: 0
    },
    openProxy: { // 隧道连接
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      defaultImg,
      collRequesting: false,
      showOpreate: false
    }
  },
  computed: {
    sourceImg() {
      const data = this.dataItem
      return data.AppIcon
        ? this.urlPrefix + data.AppIcon
        : defaultImg
    },
    calcWidth() {
      const data = this.dataItem
      if (data.isIP) {
        return '195px'
      } else if (data.AccessTypeID === '301') {
        return '136px'
      } else {
        return '157px'
      }
    },
    popperClass() {
      return this.$i18n.locale === 'zh' ? 'source-detail-panel' : 'source-detail-panel-en'
    },
    // 是否可以访问
    isCanView() {
      const data = this.dataItem
      // 当是自定义资源
      if (parseInt(data.AccessTypeID) === 312) {
        return true
      }
      // 当是资源是内网直连资源
      if (parseInt(data.IsNat) === 1) {
        // 当环境是内网环境
        if (this.isInternal) {
          return true
        }
      }
      return this.ztpUserExceed
    },
    // 是否断开
    isUnlink() {
      if (this.dataItem.isIP && !this.openProxy) {
        return true
      }
      return false
    },
    isHideResDetail() {
      if (!this.dataItem.HideResDetail) {
        return false
      }
      return parseInt(this.dataItem.HideResDetail) === 1
    },
    showProxyUnlink() {
      return this.dataItem.isIP && !this.openProxy && parseInt(this.dataItem.IsApply) !== 1 && this.dataItem.Result !== '0' && this.dataItem.IsInternet !== '0'
    },
    canCollect() {
      return !this.dataItem.isIP || parseInt(_.get(this.dataItem, 'TunnelToWeb', 0)) === 1
    },
    canEdit() {
      return this.dataItem.AccessTypeID === '301' && (!this.dataItem.isIP || parseInt(_.get(this.dataItem, 'TunnelToWeb', 0)) === 1)
    }
  },
  methods: {
    async collectHandle(event) {
      if (!this.isCanView) {
        this.$message.error(this.$t('sourcePlatform.ztpUserExceed'))
        return
      }
      // 收藏、取消收藏
      this.stopPop(event)
      if (this.collRequesting) {
        return
      }
      this.collRequesting = true
      const bool = await sourceListUtil.collectReq(this.dataItem)
      this.collRequesting = false
      if (bool) {
        this.$emit('childEvent', { type: 'freshEvent' })
      }
    },
    editAccountHandle(event) {
      this.stopPop(event)
      this.$emit('childEvent', {
        type: 'editAccountEvent',
        data: this.dataItem
      })
    },
    clickHandle() {
      if (parseInt(this.dataItem.IsApply) === 1) {
        return
      }
      if (this.dataItem.IsInternet === '0') {
        this.$message.warning(this.$t('sourcePlatform.sourceSlodOut'))
        return
      }
      if (!this.isCanView) {
        this.$message.error(this.$t('sourcePlatform.ztpUserExceed'))
        return
      }
      if (this.dataItem.Result === '0' && this.dataItem.isIP) {
        if (parseInt(this.dataItem.ErrCode) === ********) {
          this.$emit('childEvent', { type: 'opentacticErr', data: this.dataItem })
          return
        }
        if (parseInt(this.dataItem.ErrCode) === ********) {
          this.$message.warning(this.$t('footer.ipFail'))
          return
        }
        if (this.dataItem.Reason && this.dataItem.Reason.trim()) {
          this.$message.warning(this.dataItem.Reason)
          return
        }
      }
      if (this.dataItem.Result === '0' && this.dataItem.Reason) {
        this.$message.warning(this.dataItem.Reason)
        return
      }
      if (this.dataItem.Result === '0' || (this.dataItem.Status && parseInt(this.dataItem.Status) === 2)) {
        this.$message.warning(this.$t('sourcePlatform.noHeath'))
        return
      }
      if (this.getReomteAppPermission(this.dataItem)) {
        this.$message.error(this.$t('sourcePlatform.onlyWinClientTips'))
        return
      }
      this.$emit('childEvent', { type: 'openUrlEvent', data: this.dataItem })
    },
    seeDetailHandle(event) {
      this.stopPop(event)
      this.$emit('childEvent', { type: 'customResEvent', data: this.dataItem })
    },
    applyHandle() {
      if (!this.isCanView) {
        this.$message.error(this.$t('sourcePlatform.ztpUserExceed'))
        return
      }
      this.$emit('childEvent', { type: 'applyRes', data: this.dataItem })
    },
    stopPop(e) {
      var evt = e || window.event
      if (evt.stopPropagation) {
        evt.stopPropagation()
      } else {
        evt.cancelBubble = true
      }
    },
    // 远程应用的是否仅windows客户端
    getReomteAppPermission(data) {
      // ActionType=0，windows客户端可以独立运行，1(web浏览器运行)
      if (
        parseInt(_.get(data, 'ResType')) === 3 &&
        parseInt(_.get(data, 'ActionType')) === 0 &&
        os_browser_info.os_type !== 'windows'
      ) {
        // 表示无权限运行
        return true
      }
      return false
    },
    formateType(data) {
      const id = parseInt(_.get(data, 'AccessTypeID'))
      const TunnelToWeb = parseInt(_.get(data, 'TunnelToWeb', 0))
      if (!id || TunnelToWeb === 1) {
        return this.$t('sourcePlatform.accessDomain')
      }
      const map = {
        299: this.$t('sourcePlatform.webApp'),
        300: this.$t('sourcePlatform.webApp'),
        301: this.$t('sourcePlatform.webApp'),
        302: 'RDP',
        303: 'VNC',
        304: 'SSH',
        305: 'TELNET',
        310: this.$t('sourcePlatform.remoteApp'),
        311: this.$t('sourcePlatform.micApp'),
        312: this.$t('sourcePlatform.customApp'),
        314: this.$t('sourcePlatform.fileServerApp')
      }

      return map[id]
    },
    formateIp(ip) {
      if (!ip) {
        return ''
      }
      return ip.replace(/##/g, '<br>')
    },
    itemEnterHandle() {
      this.showOpreate = true
    },
    itemLeaveHandle() {
      this.showOpreate = false
    },
    toSeeDtail() {
      this.$emit('childEvent', { type: 'IPSourceDetail', data: this.dataItem })
    }
  }
}
</script>
<style lang="scss">
.s-group-item-container {
  width: 50%;
  padding: 0 8px;
  margin-top: 16px;
  .source-item {
    cursor: pointer;
    width: 100%;
    display: flex;
    padding: 0 16px;
    height: 110px;
    border: 1px solid $gray-2;
    border-radius: 4px;
    position: relative;
    &:hover {
      box-shadow: 0px 8px 20px 0px rgba(16,36,66,.1);
      transition: all .3s;
    }
    .left-wrapper {
      display: flex;
      align-items: center;
      width: calc(100% - 37px);
      .img-box {
        width: 40px;
        height: 40px;
        margin-right: 16px;
        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
      .text-content {
        display: flex;
        justify-content: center;
        flex-direction: column;
        width: calc(100% - 62px);
        p {
          font-size: 14px;
          line-height: 20px;
          color: $title-color;
        }
        .title {
          display: flex;
          position: relative;
          font-weight: 500;
          font-family: PingFang SC, PingFang SC-Medium;
        }
        .sub-title {
          font-size: 12px;
          color: $default-color;
          margin-top: 2px;
          display: block;
          padding-left: 1px;
          line-height: 17px;
          &:focus {
            outline: none;
          }
        }
        .u-tags {
          margin-left: 4px;
        }
        .limit-tag {
          height: 18px;
          line-height: 16px;
          border: 1px solid $error-1;
          border-radius: 9.5px;
          color: $error-1;
          font-size: 12px;
          padding: 0 6px;
        }

        .cur-name {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          padding-left: 1px; // 解决多行溢出隐藏省略号显示不全问题
          &:focus {
            outline: none;
          }
        }
        .tag-limit-name{
          max-width: calc(100% - 58px);
        }
        .res-apply{
          color: $--color-primary;
          font-size: 12px;
          line-height: 17px;
          margin-top: 2px;
          cursor: pointer;
          i{
            color: $--color-primary;
            font-size: 12px;
            margin-right: 6px;
          }
        }
      }
    }
    .right-wrapper {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 16px;
      display: flex;
      flex-direction: column;
      align-content: center;
      box-sizing: border-box;
      padding: 5px 0;
      i {
        font-size: 15px;
        color: $disabled-color;
        cursor: pointer;
        padding: 6px 0;
        &:focus {
          outline: none;
        }
      }
      .icon-yishoucang{
        color:$yellow;
      }
      .icon-weishoucang{
        &:hover {
          color: $--color-primary;
        }
      }
      .icon-gengduo{
        padding-bottom: 0;
        &:hover {
          color: $--color-primary;
        }
      }
      .icon-xiugaimima{
        font-size: 14px;
        &:hover {
          color: $--color-primary;
        }
      }
      .collectd {
        color: $--color-primary;
      }
    }
  }
  .slodout-source-item {
    border-color: #ededf1;
    &:hover {
      border-color: #ededf1;
      background-color: white;
    }
    .text-content {
      .sub-title,
      .tag-limit-name {
        color: #b3b6c1 !important;
      }
      .sold-out-tag {
        border: 1px solid #e2e2e6;
        width: 62px;
        display: flex;
        justify-content: center;
        line-height: 16px;
        border-radius: 9px;
        font-size: 12px;
        padding: 0 6px;
        color: #b3b6c1;
      }
    }
    .img-box{
      img{
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -ms-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        filter: grayscale(100%);
        filter: gray;
        opacity: 0.4;
      }
    }
  }
  .apply-source-item{
    padding: 25px 16px;
    min-height: 110px;
    .text-content .title{
      color: $disabled-color!important;
    }
  }
}
.source-tool-tip {
  padding: 4px 8px;
  background: $gray-3;
  border: 1px solid $gray-2 !important;
  border-radius: 1px;
  box-shadow: 0px 0px 6px 0px rgba(16, 36, 66, 0.2);
  font-size: 12px;
  color: $title-color;
  line-height: 17px;
  max-width: 170px;
}
.source-detail-panel, .source-detail-panel-en{
  background: rgba(60,64,70,.9)!important;
  font-size: 12px;
  p{
    margin-bottom: 4px;
    line-height: 17px;
    display: flex;
    max-width: 251px;
    .label{
      width: 60px;
      text-align: right;
      flex-shrink: 0;
      word-break: normal;
    }
    span{
      word-break: break-all;
    }
    .see-detail{
      color: $yellow-1;
      cursor: pointer;
      &:hover{
        text-decoration: underline;
      }
    }
  }
  .last-p{margin-bottom: 0;}
}
.source-detail-panel-en p .label{
  width: 82px;
}
@media (min-width: 1055px) {
  .s-group-item-container {
    width: 50%;
  }
}
@media (min-width: 1055px) {
  .s-group-item-container {
    width: 33.33%;
  }
}
@media (min-width: 1320px) {
  .s-group-item-container {
    width: 25%;
  }
}
</style>
