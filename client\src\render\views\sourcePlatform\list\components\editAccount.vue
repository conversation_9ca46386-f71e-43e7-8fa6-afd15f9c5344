<!-- 编辑账户 -->
<template>
  <VerticalDialog
    :show.sync="show"
    :title="$t('sourcePlatform.changePassTitle')"
    width="440px"
    @closed="closeHandle"
  >
    <div class="dialog-content">
      <div class="form-content">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="0px"
          class="rule-form"
        >
          <el-form-item label="" prop="username">
            <el-input
              v-model="ruleForm.username"
              maxlength="50"
              :placeholder="$t('sourcePlatform.pUser')"
            >
              <i slot="prefix" class="iconfont icon-shoujihao" />
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="password">
            <el-input
              v-model="ruleForm.password"
              maxlength="50"
              type="password"
              :placeholder="$t('sourcePlatform.pPass')"
            >
              <i slot="prefix" class="iconfont icon-mima" />
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div class="content-footer">
        <div class="clear" @click="clearHandle">{{ $t('sourcePlatform.clean') }}</div>
        <div class="submit" @click="submitForm('ruleForm')">{{ $t('sourcePlatform.ok') }}</div>
      </div>
    </div>
  </VerticalDialog>
</template>

<script>
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import { Base64Decode } from '@/render/utils/global'
export default {
  name: 'EditAccount',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    editAccountData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: function() {
    const checkUser = (rule, value, callback) => {
      if (!_.trim(value)) {
        return callback(new Error(this.$t('sourcePlatform.pUser')))
      }
      callback()
    }
    const checkPass = (rule, value, callback) => {
      if (!_.trim(value)) {
        return callback(new Error(this.$t('sourcePlatform.pPass')))
      }
      callback()
    }
    return {
      ruleForm: {
        username: '',
        password: ''
      },
      rules: {
        username: [{ validator: checkUser, trigger: 'blur' }],
        password: [{ validator: checkPass, trigger: 'blur' }]
      }
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    editAccountData(val) {
      this.ruleForm.username = val.UserName ? Base64Decode(val.UserName) : ''
      this.ruleForm.password = val.Password ? Base64Decode(val.Password) : ''
    }
  },
  created() {},
  beforeDestroy() {},
  methods: {
    closeHandle() {
      this.$refs.ruleForm.resetFields()
      this.clearHandle()
    },
    clearHandle() {
      this.ruleForm = {
        username: '',
        password: ''
      }
    },
    submitForm(formName) {
      console.log(22333, formName)
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          console.log(2233)
          const ret = await sourceListUtil.saveAtpAccount({ ...this.ruleForm, ...this.editAccountData })
          console.log(6666)
          if (ret.errcode === '0') {
            this.show = false
            this.$message({
              message: this.$t('sourcePlatform.changePassTitle') + this.$t('sourcePlatform.success'),
              type: 'success'
            })
            this.clearHandle()
            this.$refs[formName].resetFields()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 24px;
  .form-content{
    padding: 0 40px;
  }
  .content-footer{
    border-top: 1px solid $line-color;
    display: flex;
    &>div{
      width: 50%;
      line-height: 40px;
      text-align: center;
      font-size: 14px;
      color: $--color-primary;
      cursor: pointer;
      &:hover{
        background: $gray-1;
      }
    }
    .clear{
      border-right: 1px solid $line-color;
      color: $default-color;
      border-bottom-left-radius: 5px;
    }
    .submit{
      border-bottom-right-radius: 5px;
    }
  }
}
</style>
