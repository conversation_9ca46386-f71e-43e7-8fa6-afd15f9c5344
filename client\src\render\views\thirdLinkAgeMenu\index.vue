<!--
 * @Author: <EMAIL>
 * @Date: 2022-03-01 15:37:52
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-31 09:41:15
 * @Description:第三方联动菜单管理
-->
<template>
  <div id="f-thirdLinkAge" v-loading="loading||fetchDataLoading">
    <div :class="[ isMsepLink ? 'u-content-msep': 'u-content' ]">
      <img v-if="isMsepLink" src="@/render/assets/msepBg.png">
      <img v-else src="@/render/assets/stateIllustration/thirdLinkAgeMenu.png">
    </div>

    <div class="u-menu">
      <template v-if="menusButtons.length>0">
        <template v-for="(item,key) in menusButtons">
          <el-tooltip :key="key" class="item" :content="item.ShowName" placement="bottom-start">
            <!--Msep联动专用-->
            <a v-if="!isMsepLink" class="public-btn" @click="open(item.Name)">
              {{ item.tmpShowName }}
            </a>
            <!--其他第三方联动样式-->
            <a v-else href="javascript:void(0)" class="public-btn-msep" @click="open(item.Name)">
              <img :src="item.IconPath" @error="showLocalImg(item, $event)">
              <span class="u-item-name">{{ item.tmpShowName }}</span>
            </a>
          </el-tooltip>
        </template>
      </template>
      <template v-else>
        <div class="empty-info">
          <div class="empty-bg">
            <img src="@/render/assets/stateIllustration/empty.png" width="260">
          </div>
          <p>{{ $t('thirdLinkAgeMenu.empty') }}</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import agentApi from '@/service/api/agentApi'
import { EventBus } from '@/render/eventBus'
import processController from '@/render/utils/processController'
import menuUtil from '@/render/utils/bussiness/menuUtil'
import { i18n } from '@/render/lang'
const AcuityInfoCheck = require('@/render/assets/msep/AcuityInfoCheck.png')
const AuthorisedCheck = require('@/render/assets/msep/AuthorisedCheck.png')
const IPConfig = require('@/render/assets/msep/IPConfig.png')
const MenuDefault = require('@/render/assets/msep/MenuDefault.png')
const PatchManage = require('@/render/assets/msep/PatchManage.png')
const PrivilegeMode = require('@/render/assets/msep/PrivilegeMode.png')
const SelftestAcuityInfo = require('@/render/assets/msep/SelftestAcuityInfo.png')
const SelftestConfig = require('@/render/assets/msep/SelftestConfig.png')
const SoftwareShop = require('@/render/assets/msep/SoftwareShop.png')
const StartRemote = require('@/render/assets/msep/StartRemote.png')
const StartRemoteApply = require('@/render/assets/msep/StartRemoteApply.png')
const ApplyMaintainConfig = require('@/render/assets/msep/ApplyMaintainConfig.png')
const HealthMonitor = require('@/render/assets/msep/HealthMonitor.png')
const HistoryMsg = require('@/render/assets/msep/HistoryMsg.png')

export default {
  data() {
    return {
      loading: false,
      fetchDataLoading: true,
      thirdServer: {},
      currentThirdServerName: '',
      isMsepLink: false,
      msepIconArr: {
        AcuityInfoCheck, AuthorisedCheck, IPConfig, MenuDefault, PatchManage, PrivilegeMode, SelftestAcuityInfo, SelftestConfig,
        SoftwareShop, StartRemote, StartRemoteApply, ApplyMaintainConfig, HealthMonitor, HistoryMsg
      }
    }
  },
  computed: {
    menusButtons() {
      var buttons = this.computedMenuButtons()
      for (var i in buttons) {
        if (!buttons[i].IconPath) {
          buttons[i].IconPath = this.msepIconArr[buttons[i].IconName] || this.msepIconArr['MenuDefault']
        }

        buttons[i].tmpShowName = buttons[i].ShowName || buttons[i].Name
        if (i18n.locale === 'en' && buttons[i].EngName) {
          buttons[i].tmpShowName = buttons[i].EngName
        }
      }
      return buttons
    }
  },
  detached() {
    EventBus.$off('refreshThirdLinkageMenu')
  },
  mounted() {
    this.init()
    this.listenUpdateMenu()
  },
  methods: {
    init() {
      this.getThirdServerConf()
      this.isMsep()
      this.getThirdLinkageMenu()
    },
    /**
     * 获取当前联动服务的名称
     * @return {void}
     */
    getThirdServerConf() {
      this.currentThirdServerName = _.get(this.$route.query, 'Name', '')
    },
    /**
     * 是否是msep联动
     * @return {void}
     */
    isMsep() {
      this.isMsepLink = parseInt(_.get(this.$route.query, 'MSEPLinkage', 0)) === 1
    },
    /**
     * 监听消息更新第三方客户端的菜单
     * @return {void}
     */
    listenUpdateMenu() {
      EventBus.$on('refreshThirdLinkageMenu', (data) => {
        this.thirdServer = this.matchThirdLinkageMenu(data)
      })
    },

    /**
     * 获取匹配的第三方联动菜单
     * @return {void}
     */
    async getThirdLinkageMenu() {
      this.fetchDataLoading = true

      try {
        // 获取全部的第三方联动菜单
        const thirdServerConfig = await menuUtil.getLinkageMenu()
        this.thirdServer = this.matchThirdLinkageMenu(thirdServerConfig)
      } catch (error) {
        console.log(error)
      }

      this.fetchDataLoading = false
    },
    /**
     * 匹配当前联动菜单的配置
     * @return {void}
     */
    matchThirdLinkageMenu(thirdServerConfig = []) {
      const currentThird = thirdServerConfig.find(item => {
        return _.get(item, 'Name', '') === this.currentThirdServerName
      })

      // 如果没有匹配的联动菜单项，则跳转到欢迎页
      if (_.isEmpty(currentThird)) {
        this.jumptoMessage()
      }

      return currentThird || {}
    },
    /**
     * 跳转到欢迎页
     * @return {void}
     */
    jumptoMessage() {
      processController.set('/access/message')
    },
    /**
     * 计算联动服务的菜单操作名称
     * @return {Array}
     */
    computedMenuButtons() {
      const menus = _.get(this.thirdServer, 'MenuList.Menu', '')
      if (_.isEmpty(menus)) {
        return []
      }
      if (_.isArray(menus)) {
        return menus
      } else {
        return [menus]
      }
    },
    /**
     * 打开客户端的联动的菜单
     * @return {void}
     */
    async open(menu) {
      this.loading = true
      try {
        const params = {
          ThirdServer: {
            Name: this.currentThirdServerName,
            Menu: {
              Name: menu
            }
          }
        }
        await agentApi.operateThirdLinkageMenu(params)
        this.loading = false
      } catch (error) {
        console.log(error)
        this.loading = false
      }
    },
    /**
     * 显示应用图片
     * @return {void}
     */
    showLocalImg(item, event) {
      var img = event.srcElement
      if (this.msepIconArr[item.IconName]) {
        img.src = this.msepIconArr[item.IconName]
      } else {
        img.src = this.msepIconArr['MenuDefault']
      }
      img.onerror = null
    }
  }
}
</script>
<style lang="scss" scoped>
#f-thirdLinkAge {
  height: 100%;
  text-align: center;
  overflow: auto;
  .u-content {
    display: inline-block;
    margin-top: 100px;
    img {
      display: inline-block;
      width: 280px;
      height: auto;
    }
  }
  .u-content-msep{
    img {
      display: inline-block;
      width: 100%;
      height: auto;
    }
  }
  .u-menu{
    display: flex;
    width: 80%;
    margin :0 auto;
    flex-wrap: wrap;
    justify-content: flex-start;
    .empty-info {
      width: 100%;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #686E84;
      line-height: 22px;
      margin-top: 40px;
      font-weight: 500;
      .empty-bg{
        width: 100%;
        text-align: center;
        img{
          display: inline-block;
          margin-bottom: 20px;
        }
      }
    }
  }
  .public-btn {
    width: 188px;
    height: 40px;
    background: #fff;
    border:1px solid #536ce6;
    border-radius: 4px;
    text-align: center;
    line-height: 40px;
    font-size: 15px;
    color: #536ce6;
    cursor: pointer;
    overflow: hidden;
    margin: 20px 10px 10px;
    padding:0 5px;
  }
  .public-btn-msep{
    margin-top: 40px;
    display: flex;
    width: 208px;
    overflow: hidden;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #3c404d;
    img{
      width: 56px;
      height: 56px;
      margin-right: 10px;
    }
  }
  .u-item-name {
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}
</style>
