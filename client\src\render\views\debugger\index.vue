<template>
  <div id="f-debugger-tools">
    <div class="u-operate">
      <a class="u-close" href="javascript:void(0)" alt="cloese" @click="close"><i class="iconfont icon-guanbichuangkou" /></a>
      <a class="u-mini" href="javascript:void(0)" alt="mini" @click="mini"><i class="iconfont icon-zuixiaohua" /></a>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="network" name="network" />
      <el-tab-pane label="store" name="store" />
      <el-tab-pane label="format" name="format" />
      <el-tab-pane label="inspect" name="inspect" />
    </el-tabs>
    <div class="f-atvice">
      <component
        :is="activeName"
      />
    </div>
  </div>
</template>
<script>
import network from './network.vue'
import store from './store.vue'
import format from './format.vue'
import inspect from './chromeInspect.vue'
export default {
  components: {
    network,
    store,
    format,
    inspect
  },
  data() {
    return {
      activeName: 'network'
    }
  },
  methods: {
    mini() {
      this.$emit('mini')
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss">
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  background: none;
  overflow: auto;
  #f-debugger-tools {
    position: relative;
    border-top: 1px solid #ccc;
    background: #fff;
    height: 100%;
    overflow: hidden;
    .u-operate{
      position: absolute;
      top: 0;
      right: 10px;
      z-index: 1;
      height: 26px;
      line-height: 25px;
      a{
        color: #999;
        .iconfont{
          font-size: 13px;
        }
      }
      .u-close{
        padding-right: 5px;
      }
    }
    .el-tabs__nav-wrap:after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-color: #ccc;
        z-index: 1;
    }
    .el-tabs__header{
      padding: 0 20px;
      margin:0;
         background-color: #f3f3f3;
         border-bottom: 1px solid #ccc;
         .el-tabs__item{
            height: 26px;
            line-height: 26px;
            font-size: 11px;
         }
    }
    .el-tabs__item.is-active{
      color: #333;
    }
    .el-tabs__active-bar{
      color:#3E82F7
    }
    .tabbed-pane-header-contents {
      flex: auto;
      pointer-events: none;
      margin-left: 5px;
      position: relative;
    }
    .tabbed-pane-header-contents > * {
      pointer-events: initial;
    }
    .tabbed-pane-header-tab {
      float: left;
      margin-top: 2px;
      padding: 2px 4px 2px 4px;
      height: 24px;
      border: 1px solid transparent;
      border-bottom: none;
      line-height: 15px;
      white-space: nowrap;
      cursor: default;
      display: flex;
      align-items: center;
      color: #5a5a5a;
    }
    .tabbed-pane-header-tab,
    .tabbed-pane-header-tab.selected {
      height: 26px;
      margin: 0;
      background: #f3f3f3;
      border: none;
      border-left: 2px solid transparent;
      border-right: 2px solid transparent;
      padding: 0 6px;
    }
    .tabbed-pane-header-tab.selected {
        border-width: 0 2px 0 2px;
    }
    .tabbed-pane-header-tab-title {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .f-atvice{
      height: 100%;
    }
  }
}
</style>
