/**
* 杂项菜单，用来处理一些业务，父菜单可以不必是layout.vue的
**/
const sundry = [
  // 高级动态身份认证(资源认证)
  {
    path: '/resourceAuth',
    name: 'resourceAuth',
    component: () => import('@/render/views/sundry/resourceAuth/index.vue')
  },
  // 开机自动认证安检
  {
    path: '/bootAutoAuthCheck',
    name: 'bootAutoAuthCheck',
    component: () => import('@/render/views/sundry/bootAutoAuthCheck/index.vue')
  }
]

export default sundry
