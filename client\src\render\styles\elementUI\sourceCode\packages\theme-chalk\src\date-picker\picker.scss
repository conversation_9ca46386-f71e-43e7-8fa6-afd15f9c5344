@import "../mixins/mixins";
@import "../common/var";
@import "../common/transition";

@include b(date-editor) {
  position: relative;
  display: inline-block;
  text-align: left;

  &.el-input,
  &.el-input__inner {
    width: 220px;
  }

  @include m((monthrange)) {
    &.el-input,
    &.el-input__inner {
      width: 300px;
    }
  }

  @include m((daterange, timerange)) {
    &.el-input,
    &.el-input__inner {
      width: 350px;
    }
  }

  @include m(datetimerange) {
    &.el-input,
    &.el-input__inner {
      width: 400px;
    }
  }

  @include m(dates) {
    .el-input__inner {
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .el-icon-circle-close {
    cursor: pointer;
  }

  .el-range__icon {
    font-size: 16px;
    margin-left: -5px;
    color: $--color-text-placeholder;
    float: left;
    line-height: 32px;
  }

  .el-range-input {
    appearance: none;
    border: none;
    outline: none;
    display: inline-block;
    height: 100%;
    margin: 0;
    padding: 0;
    width: 39%;
    text-align: left;
    font-size: $--font-size-base;
    color: $--color-text-regular;
    padding-left: 11px;

    &::placeholder {
      color: $--color-text-placeholder;
    }
  }

  .el-range-separator {
    display: inline-block;
    height: 100%;
    padding: 0 5px;
    margin: 0;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    width: 5%;
    color: $--color-text-primary;
  }

  .el-range__close-icon {
    font-size: 14px;
    color: $--color-text-placeholder;
    width: 25px;
    display: inline-block;
    float: right;
    line-height: 32px;
  }
}

@include b(range-editor) {
  &.el-input__inner {
    display: inline-flex;
    align-items: center;
    padding: 3px 10px;
  }

  .el-range-input {
    line-height: 1;
  }

  @include when(active) {
    border-color: $--color-primary;

    &:hover {
      border-color: $--color-primary;
    }
  }

  @include m(medium) {
    &.el-input__inner {
      height: $--input-medium-height;
    }

    .el-range-separator {
      line-height: 28px;
      font-size: $--input-medium-font-size;
    }

    .el-range-input {
      font-size: $--input-medium-font-size;
    }

    .el-range__icon,
    .el-range__close-icon {
      line-height: 28px;
    }
  }

  @include m(small) {
    &.el-input__inner {
      height: $--input-small-height;
    }

    .el-range-separator {
      line-height: 24px;
      font-size: $--input-small-font-size;
    }

    .el-range-input {
      font-size: $--input-small-font-size;
    }

    .el-range__icon,
    .el-range__close-icon {
      line-height: 24px;
    }
  }

  @include m(mini) {
    &.el-input__inner {
      height: $--input-mini-height;
    }

    .el-range-separator {
      line-height: 20px;
      font-size: $--input-mini-font-size;
    }

    .el-range-input {
      font-size: $--input-mini-font-size;
    }

    .el-range__icon,
    .el-range__close-icon {
      line-height: 20px;
    }
  }

  @include when(disabled) {
    background-color: $--input-disabled-fill;
    border-color: $--input-disabled-border;
    color: $--input-disabled-color;
    cursor: not-allowed;

    &:hover, &:focus {
      border-color: $--input-disabled-border;
    }

    input {
      background-color: $--input-disabled-fill;
      color: $--input-disabled-color;
      cursor: not-allowed;
      &::placeholder {
        color: $--input-disabled-placeholder-color;
      }
    }

    .el-range-separator {
      color: $--input-disabled-color;
    }
  }
}
