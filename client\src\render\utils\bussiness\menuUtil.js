/*
 * @Author: <EMAIL>
 * @Date: 2022-04-18 08:42:53
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-07-06 10:25:12
 * @Description: 客户端菜单的辅助方法
 */
import agentApi from '@/service/api/agentApi'
import router from '@/render/router'
import store from '@/render/store'
import _ from 'lodash'
import os_browser_info from '../os_browser_info'
import authTypes from '@/render/utils/auth/authTypes'

const menuUtil = {
  /**
   * 获取第三方联动菜单或第一方联动菜单
   * @return {Array[Object]}
  */
  linkAgeCode: 0,

  async getLinkageMenu() {
    let thirdLinkAgeMenu = []
    try {
      const extendMenu = await agentApi.getThirdLinkageMenu()
      if (parseInt(_.get(extendMenu, 'ASM.IsShowMenu', 0)) === 1 && _.get(extendMenu, 'ASM.ThirdServer')) {
        // 客户端的奇葩机制这里数据类型竟然会变
        thirdLinkAgeMenu = _.isArray(_.get(extendMenu, 'ASM.ThirdServer')) ? _.get(extendMenu, 'ASM.ThirdServer') : [_.get(extendMenu, 'ASM.ThirdServer')]
      }
    } catch (error) {
      console.error('获取联动菜单失败')
      console.error(error)
    }
    return thirdLinkAgeMenu
  },

  /**
   * 遍利联动菜单，并且加入到vue-router里面
   * thirdMenus接口返回第三方路由信息(GetThirdLinkageMenu -> ThirdServer)
   */
  mapLinkAgeMenu(thirdMenus = []) {
    if (thirdMenus.length === 0) {
      return []
    }

    const thirdmenuRoutes = []

    const allRegisterRouter = router.getRoutes()
    thirdMenus.forEach((item) => {
      const key = this.linkAgeCode++
      const routeCode = String(401 + key) // 第三方联动占位code码401, 生成唯一code码
      const routeName = `thirdLinkAgeMenu_${_.get(item, 'Name')}`
      // 菜单名称接口只返回了中文，终端安全支持中英文特殊处理
      const menuName = _.get(item, 'Name') === '终端安全' ? 'nav.thirdLinkAgeMenu' : _.get(item, 'Name')
      // 先看下现在的vue-router里面是否已添加了该路由
      const isExist = _.find(allRegisterRouter, (item) => {
        return _.get(item, 'name') === routeName
      })
      const thirdRoute = {
        path: `/thirdLinkAgeMenu/${encodeURIComponent(_.get(item, 'Name'))}`,
        name: routeName,
        component: () => import('@/render/views/thirdLinkAgeMenu/index.vue'),
        platform: ['windows'],
        meta: {
          code: routeCode, // 保证唯一性，用于侧边栏判断是否显示激活状态（通过前3位判断）
          menu: { // 不需要显示到侧边导航栏不配
            name: menuName,
            icon: parseInt(_.get(item, 'MSEPLinkage', 0)) === 1 ? 'icon-tongyong5' : 'icon-disanfangyingyong',
            moduleName: 'nav.thirdLinkAgeMenu' // 所属模块名称
          }
        },
        params: item
      }
      if (!isExist) {
        router.addRoute('accessIndex', thirdRoute)
        thirdmenuRoutes.push(thirdRoute)
      } else {
        // 把meta.code重置为之前的code
        thirdRoute.meta.code = isExist.meta.code
        thirdmenuRoutes.push(thirdRoute)
      }
    })

    return thirdmenuRoutes
  },

  /**
   * 对需要授权的菜单(类似远程管理)进行授权判断
   * @param {Array[Object]} menus 菜单数组
   * @return {Array[Object]} 过滤掉没有授权的菜单
   */
  mapAuthorizeMenu(routesData) {
    // 判断有无远程管理菜单授权。如果没有授权过滤掉该项
    if (parseInt(_.get(store.state.serveEntiretyConfig, 'server.RemoteCtrl', 0)) !== 1) {
      routesData = routesData.filter(v => v.path !== '/remoteAssistance')
    }

    return routesData
  },

  /**
   * 过滤掉特殊架构不支持的菜单(例如linux的x86不支持远程协助)
   * @param {*} routesData
   * @return {Array[Object]} 过滤掉特殊架构支持的菜单
   */
  mapArchitectureMenu(routesData) {
    if (_.get(os_browser_info, 'os_Linux_kernel_arch') === 'x86_64') {
      routesData = routesData.filter(v => v.path !== '/remoteAssistance')
    }
    return routesData
  },

  mapPlatformMenu(routesData) {
    routesData = routesData.filter(v => {
      if (!_.get(v, 'platform')) {
        return true
      }
      return v.platform.indexOf(os_browser_info.os_type) !== -1
    })
    return routesData
  },
  // 未开启补丁检查不显示补丁管理
  mapPatchMenu(routesData) {
    if (parseInt(_.get(store.state.serveEntiretyConfig, 'sceneConfig.CheckPatch', 0)) === 0) {
      routesData = routesData.filter(v => v.path !== '/patchView' && v.path !== '/patchRepair')
    }
    return routesData
  },

  // 如果是面认证不显示资源平台
  mapResourceMenu(routesData) {
    const currentAuthType = _.get(store.state.authInfo, 'basic.AuthType', '') || _.get(store.state.clientInfo, 'accessStatus.lastAuthType', '')

    if (currentAuthType === authTypes.NoAuth) {
      routesData = routesData.filter(v => v.path !== '/source/list')
      return routesData
    } else {
      return routesData
    }
  },
  // 场景配置的小助手是否开启第三方自定义菜单
  mapDiyMenu(routesData) {
    const activeMenu = _.get(store.state.serveEntiretyConfig, 'sceneConfig.IsActiveMenu', 0)
    const deviceStatus = _.get(store.state, 'clientInfo.accessStatus.deviceStatus')
    if (parseInt(activeMenu) !== 1 || parseInt(deviceStatus) !== 1) {
      routesData = routesData.filter(v => v.path !== '/diyMenu')
      return routesData
    } else {
      routesData = routesData.map(item => {
        if (item.path === '/diyMenu') {
          _.set(item, 'meta.menu.name', _.get(store.state.serveEntiretyConfig, 'sceneConfig.ActiveMenuName', '自定义菜单'))
        }
        return item
      })

      return routesData
    }
  }

}

export default menuUtil
