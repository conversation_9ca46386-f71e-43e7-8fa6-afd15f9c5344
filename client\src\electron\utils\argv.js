const { parseInt } = require('lodash')

const queryArgv = (name, type) => {
  const agsArr = parseArgv()

  if (type === 'number' && agsArr[name]) {
    return parseInt(agsArr[name])
  }

  return agsArr[name] || false
}

const parseArgv = (args = null) => {
  args = args || process.argv
  // 解析命令行参数中的选项参数
  const options = {}
  let optionName = null

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]

    // 如果是以 '--' 开头，则可能是一个选项参数
    if (arg.startsWith('--')) {
      optionName = arg.slice(2) // 去掉 '-' 符号
      options[optionName] = true // 默认为 true
    } else if (optionName) {
      // 如果当前有未处理的选项参数，则将当前参数视为其值
      options[optionName] = arg
      optionName = null // 重置选项参数名称
    }
  }

  return options
}

module.exports = { queryArgv }
