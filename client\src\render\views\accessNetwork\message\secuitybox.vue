<template>
  <div class="status-area">
    <div class="u-state-img">
      <img :src="loadingImg">
    </div>

    <div class="">{{ message }}</div>
  </div>
</template>

<script>
import agentApi from '@/service/api/agentApi'
const loading = require('@/render/assets/stateIllustration/loading.png') // loading 图片
export default {
  name: 'SecuityBox',
  components: {},
  data() {
    return {
      isInstall: true,
      loadingImg: loading,
      sdcStatus: 'no_install',
      message: ''
    }
  },

  computed: {},

  created() {
    this.message = this.$t('SDC_init')
    this.init()
  },

  mounted() { },

  methods: {
    init() {
      const install = this.checkSdcInstall()
      this.checkInstall()
      this.isInstall = install
    },
    checkSdcInstall() {
      setTimeout(async() => {
        const status = await this.checkInstall()
        status !== '1' && this.checkSdcInstall()
      }, 2000)
    },
    async checkInstall() {
      const installStatus = await agent<PERSON>pi.checkSdcInstall()
      if (installStatus === '1') {
        this.message = this.$t('SDC_init_complete')
        this.openSdcApp({ type: 'action' })
        this.$ipcSend('UIPlatform_Window', 'TerminateWnd')
      }
      return installStatus
    },
    openSdcApp(params) {
      return agentApi.openSdcApp(params)
    }
  }
}

</script>
<style lang='scss'>
.status-area {
  width: 100%;
  min-height:100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .u-state-img {
    img {
      width: 252px;
      height: auto;
    }
  }
}
</style>
