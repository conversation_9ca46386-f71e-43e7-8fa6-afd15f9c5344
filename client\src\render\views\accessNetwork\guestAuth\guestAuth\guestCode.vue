<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-15 10:15:08
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-09-08 09:02:54
 * @Description: 来宾码认证
-->
<template>
  <div id="f-guest-code">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="20px"
      @submit.native.prevent
    >
      <el-form-item prop="guestCode.code" class="u-guestCode">
        <i class="iconfont icon-laibinma input-icon" />
        <el-input
          id="ui-guest-auth-input-guest_code"
          v-model="ruleForm.guestCode.code"
          :placeholder="$t('guestAuth.guest.info_7')"
        />
        <!-- <i slot="prefix" class="iconfont icon-laibinma" /></el-input> -->
      </el-form-item>
      <p class="hint-info">{{ $t('guestAuth.guest.info_8') }}</p>
    </el-form>
  </div>
</template>

<script>
import proxyApi from '@/service/api/proxyApi'
import { mapState, mapMutations } from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'
export default {
  data() {
    return {
      ruleForm: {
        guestCode: {
          code: ''
        }
      },
      rules: {
        guestCode: {
          code: [
            {
              required: true,
              validator: this.guestCodeValidator,
              trigger: 'blur'
            }
          ]
        }
      }
    }
  },
  computed: {
    ...mapState(['clientInfo']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID')
    }
  },
  methods: {
    ...mapMutations(['setClientInfo']),
    guestCodeValidator(rule, value, callback) {
      if (!value || String(value).trim() === '') {
        return callback(new Error(this.$t('auth.needGuestCode')))
      } else {
        callback()
      }
    },
    submit() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(async(valid) => {
          if (valid) {
            // 敲门-无需敲门或者敲门成功返回true
            const knockParam = ['Guest', this.ruleForm.guestCode.code, '', '']
            const knockIsSuccess = await commonUtil.knockPort(knockParam)
            if (!knockIsSuccess) {
              resolve()
              return false
            }
            const apiParam = {
              type: 'Guest',
              deviceid: this.deviceid,
              net_code: this.ruleForm.guestCode.code
            }
            // 提交认证
            const ret = await proxyApi.authIndex(apiParam)
            resolve(ret)
          } else {
            resolve()
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
#f-guest-code{
  .hint-info {
    width:100%;
    text-align: left;
    color: $disabled-color;
    margin-bottom: 20px;
    margin-left: 20px;
  }
  .u-guestCode{
      margin-bottom: 10px!important;
  }
}
</style>
