const { app, BrowserWindow } = require('electron')
const { moveTopOnce } = require('./handle')
const windowKeeper = require('../../utils/windowStateKeep')
const debounce = require('lodash/debounce')

// 注册监听所有的"信号"
const registerEvent = ({ log, mainWindow, argv }) => {
  // 这个事件一般是在macos上出现
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      // @todo
    }
    log.info('window active event')
  })

  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit()
    }
  })

  // 忽略证书错误
  app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    // 忽略证书错误
    console.log('不信任的证书')
    event.preventDefault()
    callback(true)
  })

  // 多进程模式下的不去监控"second-instance"方法
  const supportMutilProcess = argv['supportMutilProcess'] || false
  if (!supportMutilProcess) {
    app.on('second-instance', (event, commandLine, workingDirectory) => {
      log.info('second-instance evnet listen')
      if (mainWindow) {
        if (mainWindow.isMinimized()) {
          mainWindow.show()
        } else if (!mainWindow.isVisible()) {
          mainWindow.show()
        } else {
          moveTopOnce(mainWindow)
        }
      }
    })
  }

  // 监控当window被打开的时候
  mainWindow.once('ready-to-show', () => {
    const hideMenu = argv['hideWindow'] || false
    log.info('get ready-to-show event,hideMenu = ' + hideMenu)
    if (!hideMenu) {
      mainWindow.show() // 初始CreateWindow的时候属性{show:false},所以必须在这里show
      moveTopOnce(mainWindow) // 置顶
    }
  })

  // resize的时候持久化缓存
  // 保存位置: app.getPath('userData')+window-state.json
  if (windowKeeper.mainWindowState) {
    mainWindow.on('resize', debounce(windowKeeper.mainWindowState.saveState, 500))
  }

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL, isMainFrame) => {
    // 可以在这里进一步处理错误，比如记录到文件或者发送到远程服务
    log.error('资源加载失败')
    log.error(validatedURL)
    log.error(errorDescription)
  })
}

module.exports = registerEvent
