/*
 * @Author: <EMAIL>
 * @Date: 2021-08-04 14:46:36
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-03-27 17:00:21
 * @Description: ipc客户端用来实例化ipc通讯接口前的操作
 */
import QtWebChannelIpcInterface from './qtWebChannelIpcInterface'
import ipcHelpers from './utils/helper'
import _ from 'lodash'
import overtime from '@/render/utils/overtime'
import wsIpcInterface from './websocketIpcInterface'
import QtWebKitIpcInterface from './qtWebKitIpcInterface'
import { isQtWebChannel, isQtWebKit, isWebScoketEnv } from './utils/index'
import networkList from '@/render/utils/debugger/networkList'
import qs from 'qs'
// import { post } from 'jquery'

class IpcClient {
  // @param {*} callback 和QT建立通讯完后的回调
  constructor(cb) {
    this.responseEvent = 'ResponseToWeb'
    this.callbackList = {}
    this.qtObject = null
    this.processId = 0
    this.initProcessId()
    // this.initServiceWorker()

    // @notice 这里返回的是一个Promise
    return this.initIpcInstance()
  }

  // 初始化小助手的AssUI的进程id
  initProcessId() {
    const query = qs.parse(location.search.substring(1))
    this.processId = _.get(query, 'ProcessId', 0)
  }

  // 兼容三种不同的通信方式P
  async initIpcInstance() {
    if (isWebScoketEnv()) {
      this.ipcInstance = await wsIpcInterface.init(true)
      this.addResponseListener({ websocketIpc: 'websocketIpc' }, this.responseEvent)
    } else if (isQtWebChannel()) {
      this.ipcInstance = new QtWebChannelIpcInterface((qtObject) => {
        this.addResponseListener(qtObject, this.responseEvent)
      })
    } else if (isQtWebKit()) {
      this.ipcInstance = new QtWebKitIpcInterface()
      this.addResponseListener({ webkitIpc: 'webkitIpc' }, this.responseEvent)
    } else {
      this.ipcInstance = null
    }

    return this
  }

  send(module, action, request, config) {
    let data = {}
    let done = (resolve, reject) => {
      if (config.isNeedId) {
        request.id = ipcHelpers.getStrSerial(this.processId)
        const stackTrace = isQtWebChannel() ? new Error().stack.split('\n') : []
        const requestBody = {
          resolve,
          reject,
          request: { module: module, action: action, request: request, startTime: new Date().getTime() },
          stackTrace
        }
        this.callbackList[request.id] = requestBody
        networkList.add({ key: request.id, value: { ...requestBody.request, ...{ stackTrace }}})
      }

      // 存到store里面,可以在debugger工具的network里面查看
      try {
        data = ipcHelpers.interceptors(request, action)
      } catch (error) {
        console.log(error)
        throw new Error('参数转换错误')
      }

      this.ipcInstance.send({
        module: module,
        action: action,
        strSerial: config.isNeedId ? request.id : -1,
        data,
        resolve,
        reject
      })
    }
    if (_.isSafeInteger(_.get(config, 'timeout.time'))) {
      const timeout = _.merge({
        callback: done,
        request: {
          module, action, data
        }
      }, config.timeout)
      done = overtime(timeout)
    } else {
      done = new Promise(done)
    }
    return done
  }

  on(module, eventName, callback) {
    this.ipcInstance.on(module, eventName, callback)
  }

  off(moduleName, eventName, callback) {
    this.ipcInstance.off(moduleName, eventName, callback)
  }

  // 用来创建一个专门接收send方法结果的信号
  // 监听所有客户端通过QT挂载在JS的moudle里面的responseEvent信号
  addResponseListener(qtObject, responseEvent) {
    const callBack = (serial, type = null, responseStr = null) => {
      try {
        // 这里qtwebkit和qtWebChannel返回的格式不一样,有可能是json字符串或者js对象
        let response = {}

        if (!_.isNil(responseStr) && !_.isEmpty(responseStr)) {
          if (_.isString(responseStr)) {
            response = JSON.parse(responseStr)
          } else {
            response = responseStr
          }
        }

        if (_.isUndefined(serial) && _.isEmpty(serial)) {
          throw new Error('serial 为空或者未定义')
        }
        const requestBody = this.callbackList[serial]
        if (!_.isUndefined(requestBody)) {
          requestBody.resolve(response.result)
          requestBody.request.response = response.result || {}
          requestBody.request.endTime = new Date().getTime()

          networkList.update({ key: serial, value: { ...requestBody.request, ...{ stackTrace: (requestBody.stackTrace || []) }}})
        }
        // 需要观察下delete 是否会造成内存泄漏
        delete this.callbackList[serial]
      } catch (e) {
        console.error('小助手返回错误=')
        console.error(e)
      }
    }

    if (_.isObject(qtObject)) {
      Object.keys(qtObject).forEach(module => {
        this.ipcInstance.on(module, responseEvent, callBack)
      })
    }
  }
}

export default IpcClient
