const { app, BrowserWindow } = require('electron')
const electron = require('electron')
const url = require('url')
const path = require('path')
const windowStateKeep = require('../../../utils/windowStateKeep')

const create = async({ log, argv }) => {
  log.info('获取到creat参数=', argv)
  await app.whenReady()
  const mainWindowState = windowStateKeep.init()
  const mainWindow = initMainWindow(mainWindowState, argv, log)
  return { mainWindow, log, argv }
}

// @todo 这里的defaultWidth应该从配置文件里面取
const initMainWindow = (mainWindowState = null, argv, log) => {
  const { target } = getTargetUrl(argv)
  const { width, height, title, minWidth, minHeight, frame, x, y } = parseBasicParams(argv, mainWindowState, log)
  const win = new BrowserWindow({
    title,
    width,
    height,
    minWidth,
    minHeight,
    autoHideMenuBar: true, // 设置为 true 隐藏菜单栏
    frame,
    show: false, // 在准备好前不显示
    webPreferences: {
      contextIsolation: true,
      preload: path.join(__dirname, '/preload.js')
    }
  })

  win.loadURL(pareseProcessID(target))

  if (mainWindowState) {
    mainWindowState.manage(win)
  }

  if (x && y) {
    win.setPosition(x, y)
  }

  return win
}

const getTargetUrl = (argv) => {
  let target = argv['target'] || false
  let type = 'file'

  if (!target) {
    console.error('--target 参数不能为空')
    process.exit(1)
  }

  if (isUrl(target)) {
    type = 'url'
  }

  // 文件格式化的target,转换成"file://"协议
  if (type === 'file') {
    target = url.format({
      pathname: target,
      protocol: 'file:',
      slashes: true
    })
    // 会带有hash值。在这里decodeURIComponent一下
    target = decodeURIComponent(target)
  }

  return { target, type }
}

// 处理url里面的进程id
const pareseProcessID = (target) => {
  const processID = process.pid
  // 创建一个URL对象
  const urlObj = new URL(target)

  // 获取URL的查询参数
  const params = new URLSearchParams(urlObj.search)

  // 设置或覆盖参数值
  params.set('ProcessId', processID)

  // 更新URL的查询参数
  urlObj.search = params.toString()

  return urlObj.toString()
}

const parseBasicParams = (argv, mainWindowState) => {
  let { width, height, title } = argv
  const { position, frame } = argv
  let x = 0
  let y = 0
  let minWidth = 0
  let minHeight = 0

  minWidth = width || 900
  minHeight = height || 600
  // @todo 先去掉这里的windowStateKeep功能,没法区分主window
  width = width || 900
  height = height || 600
  title = title || '入网小助手'

  if (position === 'RightDown') {
    const { width: screenWidth, height: screenHeight } = electron.screen.getPrimaryDisplay().workAreaSize
    x = screenWidth - width
    y = screenHeight - height
  }

  return { width, height, minWidth, minHeight, frame, title, x, y }
}

const isUrl = (str) => {
  try {
    new URL(str)
    return true
  } catch (_) {
    return false
  }
}

module.exports = create
