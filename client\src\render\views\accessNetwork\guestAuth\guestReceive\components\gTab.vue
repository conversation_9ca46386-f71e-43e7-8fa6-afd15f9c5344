<template>
  <ul class="g-tab">
    <li v-for="item in list" v-show="item.permission" :id="'ui-guest-receive-model-li-'+item.key" :key="item.key" :class="['tab-item', activeTab === item.key?'active-item': '']" @click="setTab(item.key)">
      <img :src="activeTab === item.key ? item.activeImg : item.img" alt="">
      <div class="right-wrapper">
        <p class="t-title">{{ item.title }}</p>
        <el-tooltip class="item" effect="dark" :visible-arrow="false" :content="item.desc" placement="bottom">
          <p class="desc">{{ item.desc }}</p>
        </el-tooltip>
      </div>
    </li>
  </ul>
</template>
<script>
import { mapState } from 'vuex'
const managerActive = require('@/render/assets/g-manager-active.png')
const manager = require('@/render/assets/g-manager.png')
const receiverActive = require('@/render/assets/g-receiver-acitve.png')
const receiver = require('@/render/assets/g-receiver.png')
const auditActive = require('@/render/assets/g-audit-active.png')
const audit = require('@/render/assets/g-audit.png')
export default {
  props: {
    value: {
      default: 'receiver',
      type: String
    }
  },
  data() {
    return {
    }
  },
  computed: {
    ...mapState(['serveEntiretyConfig', 'clientInfo']),
    activeTab: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    list() {
      // 场景是否有权限
      const { NetCode, IsNeedAppoint, AllowGuestTeam } = _.get(this.clientInfo, 'accessStatus', {})
      const receivePerm = parseInt(NetCode) === 1 || parseInt(IsNeedAppoint) === 1 || parseInt(AllowGuestTeam) === 1
      console.log(receivePerm)
      return [
        {
          title: this.$t('guestAuth.guest.info_33'),
          desc: this.$t('guestAuth.guest.info_34'),
          key: 'receive',
          img: receiver,
          activeImg: receiverActive,
          permission: receivePerm
        },
        {
          title: this.$t('guestAuth.guest.info_35'),
          desc: this.$t('guestAuth.guest.info_36'),
          key: 'audit',
          img: audit,
          activeImg: auditActive,
          permission: true
        },
        {
          title: this.$t('guestAuth.guest.info_37'),
          desc: this.$t('guestAuth.guest.info_38'),
          key: 'manage',
          img: manager,
          activeImg: managerActive,
          permission: true
        }
      ]
    }
  },
  methods: {
    setTab(val) {
      if (val !== this.activeTab) {
        this.activeTab = val
      }
    }
  }
}
</script>

<style lang="scss" scoped>
    .g-tab{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: -16px;
        .tab-item{
            display: flex;
            align-items: center;
            flex: 1;
            cursor: pointer;
            margin-left: 16px;
            padding: 16px;
            background: $light-color2;
            border-radius: 2px;
            height: 94px;
            img{
                width: 48px;
                height: 52px;
                flex-shrink: 0;
            }
            .right-wrapper{
                margin-left: 16px;
                .t-title{
                    text-align: left;
                    font-size: 16px;
                    font-weight: 500;
                    color: $title-color;
                    line-height: 22px;
                    margin-bottom: 4px;
                }
                .desc{
                    font-size: 13px;
                    color: $default-color;
                    text-align: left;
                    line-height: 18px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }
        }
        .active-item{
            position: relative;
            &::before{
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 2px;
              background: linear-gradient(153deg,#72cbff, #546ff8 83%);
            }
        }
    }
</style>
