@import "mixins/mixins";
@import "common/var";


@mixin gentheme($backgroundcolorweight, $bordercolorweight, $fontcolorweight, $hovercolorweight,$type) {

  @if $type =="normal" {
    background-color: $--color-primary-light-1;
    border-color: $--color-primary-light-2;
    color:$--color-primary ;

    @include when(hit) {
      border-color: $--color-primary ;
    }

    .el-tag__close {
      color: $--color-primary;
      &:hover {
        color: $--color-white;
        background-color: $--color-primary;
      }
    }
  }

  &.el-tag--info {
    background-color: mix($--tag-info-color, $--color-white, $backgroundcolorweight);
    border-color: mix($--tag-info-color, $--color-white, $bordercolorweight);
    color: mix($--tag-info-color, $--color-white, $fontcolorweight);

    @include when(hit) {
      border-color: $--tag-info-color;
    }

    .el-tag__close {
      color: mix($--tag-info-color, $--color-white, $fontcolorweight);
      &:hover {
        color: $--color-white;
        background-color: mix($--tag-info-color, $--color-white, $hovercolorweight);
      }
    }
  }

  &.el-tag--success {
    background-color: mix($--tag-success-color, $--color-white, $backgroundcolorweight);
    border-color: mix($--tag-success-color, $--color-white, $bordercolorweight);
    color: mix($--tag-success-color, $--color-white, $fontcolorweight);

    @include when(hit) {
      border-color: $--tag-success-color;
    }

    .el-tag__close {
      color: mix($--tag-success-color, $--color-white, $fontcolorweight);
      &:hover {
        color: $--color-white;
        background-color: mix($--tag-success-color, $--color-white, $hovercolorweight);
      }
    }
  }

  &.el-tag--warning {
    background-color: mix($--tag-warning-color, $--color-white, $backgroundcolorweight);
    border-color: mix($--tag-warning-color, $--color-white, $bordercolorweight);
    color: mix($--tag-warning-color, $--color-white, $fontcolorweight);

    @include when(hit) {
      border-color: $--tag-warning-color;
    }

    .el-tag__close {
      color: mix($--tag-warning-color, $--color-white, $fontcolorweight);
      &:hover {
        color: $--color-white;
        background-color: mix($--tag-warning-color, $--color-white, $hovercolorweight);
      }
    }
  }

  &.el-tag--danger {
    background-color: mix($--tag-danger-color, $--color-white, $backgroundcolorweight);
    border-color: mix($--tag-danger-color, $--color-white, $bordercolorweight);
    color: mix($--tag-danger-color, $--color-white, $fontcolorweight);

    @include when(hit) {
      border-color: $--tag-danger-color;
    }

    .el-tag__close {
      color: mix($--tag-danger-color, $--color-white, $fontcolorweight);
      &:hover {
        color: $--color-white;
        background-color: mix($--tag-danger-color, $--color-white, $hovercolorweight);
      }
    }
  }
}

@include b(tag) {
  @include gentheme(10%, 20%, 100%, 100%,'normal');
  display: inline-block;
  height: 32px;
  padding: $--tag-padding;
  line-height: 30px;
  font-size: $--tag-font-size;
  color: $--color-primary ;
  border-width: 1px;
  border-style: solid;
  border-radius: $--tag-border-radius;
  box-sizing: border-box;
  white-space: nowrap;

  .el-icon-close {
    border-radius: 50%;
    text-align: center;
    position: relative;
    cursor: pointer;
    font-size: 12px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    vertical-align: middle;
    top: -1px;
    right: -5px;

    &::before {
      display: block;
    }
  }

  @include m(dark) {
    @include gentheme(100%, 100%, 0, 80%,'dark');
  }

  @include m(plain) {
    @include gentheme(0, 40%, 100%, 100%,'plain');
  }

  @include m(medium) {
    height: 28px;
    line-height: 26px;

    .el-icon-close {
      transform: scale(.8);
    }
  }

  @include m(small) {
    height: 24px;
    padding: 0 8px;
    line-height: 22px;

    .el-icon-close {
      transform: scale(.8);
    }
  }

  @include m(mini) {
    height: 20px;
    padding: 0 5px;
    line-height: 19px;

    .el-icon-close {
      margin-left: -3px;
      transform: scale(.7);
    }
  }
}
