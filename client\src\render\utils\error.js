// 错误日志处理，不需要上报，存在客户端本地即可
import Vue from 'vue'
import StackTracey from 'stacktracey'
import proxyApi from '@/service/api/proxyApi'
import { Base64Encode } from './global'
import store from '@/render/store'

const ErrorUtils = {
  init() {
    window.onerror = async function(message, source, lineno, colno, error) {
      const stack = (await new StackTracey(error.stack).withSourcesAsync()).asTable()
      const errStr = JSON.stringify({
        from: 'onerror',
        message,
        stack
      })

      const baseErr = Base64Encode(errStr)
      if (ErrorUtils.isCycErr(baseErr)) { // 阻止进入死循环
        this.saveSessionTag(baseErr)
        return
      }
      this.saveSessionTag(baseErr)
      ErrorUtils.sendErr(baseErr)
      console.error(errStr)
      return false
    }

    // 当资源（如img或script）加载失败，加载资源的元素会触发一个Event接口的error事件，并执行该元素上的onerror()处理函数。
    // 这些error事件不会向上冒泡到window，但可以在捕获阶段被捕获
    // 因此如果要全局监听资源加载错误，需要在捕获阶段捕获事件
    window.addEventListener('error', (error) => {
      let message = error
      if (error.target) {
        if (error.target.tagName === 'SCRIPT') {
          message = `script--src:${error.target.src}加载失败`
        } else if (error.target.tagName === 'LINK') {
          message = `link--href:${error.target.href}加载失败`
        } else if (error.target.tagName === 'IMG') {
          message = `img--href:${error.target.src}加载失败`
        }
      }
      const errStr = JSON.stringify({
        from: 'window.addEventListener:error',
        error: message
      })
      const baseErr = Base64Encode(errStr)
      if (ErrorUtils.isCycErr(baseErr)) { // 阻止进入死循环
        this.saveSessionTag(baseErr)
        return
      }
      this.saveSessionTag(baseErr)
      ErrorUtils.sendErr(baseErr)
      console.error(errStr)
    }, true)

    window.addEventListener('unhandledrejection', async(e) => {
      e.preventDefault()
      const stack = (await new StackTracey(e.stack).withSourcesAsync()).asTable()
      const errStr = JSON.stringify({
        from: 'unhandledrejection',
        stack,
        message: e.reason
      })
      const baseErr = Base64Encode(errStr)
      if (ErrorUtils.isCycErr(baseErr)) { // 阻止进入死循环
        this.saveSessionTag(baseErr)
        return
      }
      this.saveSessionTag(baseErr)
      ErrorUtils.sendErr(baseErr)
      console.error(errStr)
      return false
    })

    Vue.config.errorHandler = async(error, vm, info) => {
      new StackTracey(error)
      // vm为抛出异常的 Vue 实例
      // info为 Vue 特定的错误信息，比如错误所在的生命周期钩子
      const {
        message, // 异常信息
        name, // 异常名称
        script, // 异常脚本url
        line, // 异常行号
        column, // 异常列号
        stack // 异常堆栈信息
      } = error

      const errStr = JSON.stringify({
        from: 'Vue.config.errorHandler',
        info,
        message, // 异常信息
        name, // 异常名称
        script, // 异常脚本url
        line, // 异常行号
        column, // 异常列号
        path: _.get(vm, '$route.fullPath'), // 不能直接打印vm会报错
        stack // 异常
      })
      const baseErr = Base64Encode(errStr)
      if (ErrorUtils.isCycErr(baseErr)) { // 阻止进入死循环
        this.saveSessionTag(baseErr)
        return
      }
      this.saveSessionTag(baseErr)
      ErrorUtils.sendErr(baseErr)
      console.error(errStr)
    }
  },
  isCycErr(errStr) { // 是否循环报错
    const errHistory = sessionStorage.errWriteLog
    if (errHistory) {
      try {
        const errInfo = JSON.parse(errHistory)
        if (errInfo.msg === errStr && new Date().getTime() - errInfo.time < 30000) {
          return true
        }
      } catch (e) {
        console.log(e)
      }
    }
    return false
  },
  saveSessionTag(errStr) {
    sessionStorage.errWriteLog = JSON.stringify({ msg: errStr, time: new Date().getTime() })
  },
  sendErr(str) {
    try {
      if (!str || !_.get(store, 'state.clientInfo.online', false)) {
        return
      }
      proxyApi.writelog({ content: str })
    } catch (error) {
      console.log('日志上报错误,error=' + error)
    }
  }
}

export default ErrorUtils
