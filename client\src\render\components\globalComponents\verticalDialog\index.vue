<template>
  <el-dialog
    ref="currentDialog"
    :visible.sync="show"
    :width="width"
    :show-close="false"
    :destroy-on-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    :modal-append-to-body="false"
    :before-close="beforeClose"
    top="0"
    custom-class="yg-vertical-dialog"
    @closed="closedHandle"
  >
    <template v-slot:title>
      <slot name="header">
        <div class="dialog-header">
          {{ title }}
          <a v-if="showClose" href="javascript:void(0)" class="close-btn" @click="closeHandle">
            <i class="el-dialog__close el-icon el-icon-close" />
          </a>
        </div>
      </slot>
    </template>
    <slot />
    <div v-show="showFoot" class="d-dialog-foot-wrapper">
      <div v-if="showCancel" :id="`ui-${popName}-div-contain`" :class="['cancel', 'btn', loading|| cancelLoading ? 'disabled-btn':'']" @click="cancel"> <i v-if="cancelLoading" class="iconfont el-icon-loading" />{{ _cancelText }}</div>
      <div :id="`ui-${popName}-div-cancel`" :class="['btn', loading || cancelLoading ? 'disabled-btn':'', !showCancel?'full-btn': '']" @click="confirm"> <i v-if="loading" class="iconfont el-icon-loading" />{{ _confirmText }}</div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'VerticalDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '480px'
    },
    title: {
      type: String,
      default: ''
    },
    showClose: {
      type: Boolean,
      default: true
    },
    showFoot: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    cancelLoading: {
      type: Boolean,
      default: false
    },
    popName: {
      type: String,
      default: ''
    }
  },
  computed: {
    _cancelText() {
      return this.cancelText || this.$t('dialogFoot.no')
    },
    _confirmText() {
      return this.confirmText || this.$t('dialogFoot.yes')
    }
  },
  mounted() {
  },
  methods: {
    closeHandle() {
      this.$emit('update:show', false)
      this.$emit('beforeClose')
    },
    beforeClose() {
      this.$emit('beforeClose')
    },
    closedHandle() {
      this.$emit('closed')
    },
    cancel() {
      if (this.loading) {
        return
      }
      this.$emit('cancel')
    },
    confirm() {
      if (this.loading) {
        return
      }
      this.$emit('confirm')
    }
  }
}
</script>
<style lang="scss">
.yg-vertical-dialog {
  margin: 0;
  border-radius: 5px;
  .dialog-header {
    font-size: 16px;
    font-weight: 500;
    color: $title-color;
    line-height: 22px;
    padding: 14px 24px;
    border-bottom: 1px solid $line-color;
    .close-btn {
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      position: absolute;
      top: 17px;
      right: 22px;
      cursor: pointer;
      i {
        color: $disabled-color;
        font-weight: 400;
      }

     &:hover i{
      color: $--color-primary;
      }
    }
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__header {
    padding: 0;
    position: relative;
    .el-dialog__headerbtn {
      top: 15px;
    }
  }
  .d-dialog-foot-wrapper{
    display: flex;
    .btn{
      width: 50%;
      line-height: 38px;
      border-top: 1px solid $line-color;
      text-align: center;
      font-size: 14px;
      color: $--color-primary;
      cursor: pointer;
      &:hover {
        background: $--color-primary;
        color: white;
        border-left-color: $--color-primary;
        border-top-color: $--color-primary;
      }
      .iconfont{
        line-height: 38px;
        margin-right: 5px;
      }
    }
    .full-btn{
      width: 100%;
    }
    .disabled-btn{
      opacity: 0.8;
    }
    .cancel{
      color: $default-color;
      border-right: 1px solid $line-color;
    }
  }
}
</style>
