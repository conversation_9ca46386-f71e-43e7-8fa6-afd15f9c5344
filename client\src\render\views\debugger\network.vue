<!--
 * @Author: <EMAIL>
 * @Date: 2021-09-19 19:16:52
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-11-30 17:31:47
 * @Description: debugger工具条里面的网络tab页
-->
<template>
  <div id="f-debugger-network">
    <div class="f-debugger-handle clearfix">
      <div
        class="u-onoff"
        :class="{ trunOff: networkRecordOnOff == 0 }"
        title="开启/关闭记录"
        @click="stopOrStart"
      />
      <div class="u-del" title="清空" @click="clearAll">
        <i class="el-icon-delete" />
      </div>
      <div class="u-filter">
        <input v-model="filter" type="text" placeholder="Filter">
      </div>
    </div>

    <el-row class="h100">
      <el-col class="h100" :span="computeSpanRow('list')">
        <div class="f-debugger-content">
          <el-table
            :data="netWorkList"
            row-key="index"
            stripe
            size="mini"
            :border="!drawer"
            :row-class-name="checkRowClassName"
            style="width: 100%"
          >
            <el-table-column
              label="名称"
            >
              <template slot-scope="scope">
                <div :title="computeUrl(scope.row)" style="cursor:pointer;height: 20px;" @click="showDetails(scope.row,scope.$index)">
                  <i v-if="computeStatus(scope.row)==='pending' " style="color:orange" class="el-icon-sort-up" />
                  <i v-else style="color:blue" class="el-icon-sort" />
                  <span>{{ computeUrl(scope.row) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="!drawer" label="方法" width="80">
              <template slot-scope="scope">
                <div>
                  {{ computeType(scope.row) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="!drawer" label="状态" width="80">
              <template slot-scope="scope">
                <div>
                  {{ computeStatus(scope.row) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="!drawer" label="时间" width="80">
              <template slot-scope="scope">
                <div>
                  {{ computeTimes(scope.row) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="!drawer" label="请求时间" width="150">
              <template slot-scope="scope">
                <div>
                  {{ computeStartTime(scope.row) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col class="h100" :span="computeSpanRow('detail')">
        <div class="f-detail">
          <div class="u-title">
            <i class="el-icon-caret-left u-close" @click="closeDetails" />
            <el-tabs v-model="activeName">
              <el-tab-pane label="标头请求" name="request">
                <h3 class="u-content-title">常规</h3>
                <div class="u-content">
                  <span class="u-sort">请求网址(小助手dll)：</span>
                  <span> {{ computedDetail('requstDll',detail) }} </span>
                </div>
                <div class="u-content">
                  <span class="u-sort">请求类型：</span>
                  <span> {{ computedDetail('requstType',detail) }} </span>
                </div>

                <template v-if="computedDetail('requstHeadShow',detail)">
                  <h3 class="u-content-title">请求标头</h3>
                  <div v-for="(item,key) in computedDetail('requstHeadList',detail)" :key="'request_'+key" class="u-content">
                    <span class="u-sort">{{ item.Name }} ：</span>
                    <span> {{ item.Value }} </span>
                  </div>
                </template>

                <h3 class="u-content-title">请求参数</h3>
                <div v-for="(item,index) in computedDetail('requstList',detail)" :key="'params_'+index" class="u-content">
                  <span class="u-sort">{{ item.Name }} ：</span>
                  <span> {{ item.Value }} </span>
                </div>

              </el-tab-pane>
              <el-tab-pane label="返回数据" name="response">
                <pre class="u-response">
{{ detail.response || {} }}
                </pre>
              </el-tab-pane>
              <el-tab-pane label="调用栈" name="time">
                <h3 class="u-content-title">时间</h3>
                <div class="u-content">
                  <span class="u-sort">开始时间：</span>
                  <span> {{ computeStartTime({value:detail}) }} </span>
                </div>

                <div class="u-content">
                  <span class="u-sort">结束时间：</span>
                  <span> {{ computeEndTime({value:detail}) }} </span>
                </div>

                <div class="u-content">
                  <span class="u-sort">持续时间：</span>
                  <span> {{ computeTimes({value:detail}) }} </span>
                </div>

                <h3 class="u-content-title">调用栈</h3>
                <pre class="u-response">
{{ computedDetail('stackTrace',detail) }}
                </pre>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

  </div>
</template>
<script>
import dateUtil from '@/render/utils/date/dateUtil'
import networkListUtils from '@/render/utils/debugger/networkList'
import { EventBus } from '@/render/eventBus'

export default {
  data() {
    return {
      networkData: [],
      drawer: false,
      currentDetailIndex: '',
      detail: {},
      activeName: 'request',
      networkRecordOnOff: 0,
      filter: ''
    }
  },
  computed: {
    computeSpanRow() {
      return (type) => {
        return this.drawer ? (type === 'list' ? 10 : 14) : (type === 'list' ? 24 : 0)
      }
    },
    netWorkList() {
      let ret = this.networkData
      if (this.filter) {
        ret = ret.filter(({ value }) => {
          let url = ''
          if (
            value.module === 'AssUIPluginWebProxy' &&
            _.get(value, 'request.strURL')
          ) {
            url = _.get(value, 'request.strURL')
          } else {
            url = `/?module=${value.module}&action=${value.action}`
          }
          return url.indexOf(this.filter) !== -1
        })
      }
      return _.orderBy(ret, ['startTime'], ['asc'])
    },
    computeUrl() {
      return ({ value }) => {
        if (
          value.module === 'AssUIPluginWebProxy' &&
          _.get(value, 'request.strURL')
        ) {
          return _.get(value, 'request.strURL')
        } else {
          return `module=${value.module}&action=${value.action}`
        }
      }
    },
    computeType() {
      return ({ value }) => {
        if (
          value.module === 'AssUIPluginWebProxy' &&
          _.get(value, 'request.strURL')
        ) {
          return _.get(value, 'request.strMethod')
        } else {
          return 'qt'
        }
      }
    },
    computeStatus() {
      return ({ value }) => {
        if (_.get(value, 'response')) {
          return 200
        } else {
          return 'pending'
        }
      }
    },
    computeTimes() {
      return ({ value }) => {
        if (_.get(value, 'endTime')) {
          return (
            parseInt(_.get(value, 'endTime')) -
            parseInt(_.get(value, 'startTime')) +
            'ms'
          )
        } else {
          return '--'
        }
      }
    },
    computeStartTime() {
      return ({ value }) => {
        return dateUtil.timeStampToDate(_.get(value, 'startTime')) || '--'
      }
    },
    computeEndTime() {
      return ({ value }) => {
        return _.get(value, 'endTime', false) ? dateUtil.timeStampToDate(_.get(value, 'endTime', '')) : '--'
      }
    },
    computeRequest() {
      return ({ value }) => {
        const strBody = _.get(value, 'request.strBody.Argument')
        if (strBody) {
          return strBody
        } else {
          return []
        }
      }
    },
    computedDetail() {
      return (type, detail) => {
        if (type === 'requstType') {
          return (_.get(detail, 'module', '') === 'AssUIPluginWebProxy') ? '代理转发' : '小助手接口'
        }
        if (type === 'requstDll') {
          if (detail.module === 'AssUIPluginWebProxy') {
            return _.get(detail, 'request.strURL')
          } else {
            return `${detail.module}(dll),${detail.action}(dll方法)`
          }
        }

        if (type === 'requstHeadShow') {
          if (!_.isEmpty(_.get(detail, 'request.strHead.Argument', ''))) {
            return true
          } else {
            return false
          }
        }

        if (type === 'requstHeadList') {
          return _.get(detail, 'request.strHead.Argument', [])
        }

        if (type === 'requstList') {
          if (detail.module === 'AssUIPluginWebProxy') {
            const argument = _.cloneDeep(_.get(detail, 'request.strBody.Argument', []))
            // 加入id到argument列表
            argument.push({
              Name: 'id',
              Value: _.get(detail, 'request.id', '')
            })

            return argument
          } else {
            const params = _.get(detail, 'request', {})
            const paramsArr = []
            Object.keys(params).forEach(key => {
              if (key !== 'stackTrace') {
                paramsArr.push({
                  Name: key,
                  Value: params[key]
                })
              }
            })
            return paramsArr
          }
        }

        if (type === 'stackTrace') {
          return _.get(detail, 'stackTrace', {})
        }
      }
    }

  },
  created() {
    this.initNetworkOnOff()
    this.initListData()
    this.listListen()
  },
  methods: {
    initListData() {
      this.networkData = _.cloneDeep(networkListUtils.list)
    },
    showDetails({ value }, index) {
      this.drawer = true
      this.detail = value
      this.currentDetailIndex = index
    },
    // 改变选中项的颜色
    checkRowClassName({ rowIndex }) {
      if (rowIndex === this.currentDetailIndex) {
        return 'u-check-row'
      }
      return ''
    },
    closeDetails() {
      this.drawer = false
    },
    // 判断记录网络日志的开关是打开还是关闭的
    initNetworkOnOff() {
      const onOff = localStorage.getItem('debugg_network_on_off')
      if (parseInt(onOff) === 1) {
        this.networkRecordOnOff = 1
      } else {
        this.networkRecordOnOff = 0
      }
    },
    // 开启或停止记录日志，存储到cookie里面
    stopOrStart() {
      this.networkRecordOnOff = parseInt(this.networkRecordOnOff) === 1 ? 0 : 1
      localStorage.setItem('debugg_network_on_off', this.networkRecordOnOff)
    },
    listListen() {
      EventBus.$on('refreshDebuggerNetwork', (data) => {
        this.refreshDebuggerNetwork(data)
      })
    },
    // receive notifaction,update network list
    refreshDebuggerNetwork({ type, item }) {
      if (type === 'add') {
        // 数据超过5000会变的很卡，这里清除掉
        if (this.networkData.length >= 5000) {
          this.clearAll()
        }
        this.networkData.push(item)
      } else {
        const index = this.networkData.findIndex(single => {
          return single.key === item.key
        })

        if (index) {
          this.$set(this.networkData, index, item)
        }
      }
    },
    // 清空所有日志记录
    clearAll() {
      this.networkData = []
      this.drawer = false
      networkListUtils.clearAll()
    }
  }
}
</script>
<style lang="scss">
#f-debugger-network {
  height: 100%;
  .f-debugger-handle {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    display: flex;
    align-items: center;
    background: #f3f3f3;
    .u-onoff {
      width: 11px;
      height: 11px;
      border-radius: 50%;
      background: red;
      margin-left: 12px;
      padding: 0;
      box-sizing: content-box;
      border: 2px solid rgba(60, 64, 77, 0.37);
    }
    .trunOff {
      background: rgba(60, 64, 77, 0.37);
    }
    .u-del {
      width: 16px;
      height: 16px;
      text-align: center;
      margin-left: 12px;
      padding: 0;
      line-height: 15px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.365);
      box-sizing: content-box;
      i {
        font-size: 10px;
      }
    }

    .u-filter {
      margin-left: 12px;
      input {
        border: 1px solid #eee;
        background: #fff;
        text-indent: 3px;
        border-radius: 2px;
        height: 22px;
        width: 138px;
      }
    }
  }
  .f-debugger-content{
    height: calc(100% - 58px);
    overflow:auto;
  }

  .jv-container.boxed {
    border: 1px solid #eee;
    border-radius: 0;
  }
  .jv-container .jv-code {
    padding: 5px;
  }
  .el-table__expanded-cell {
    padding: 10px;
  }
  .el-table--mini th {
    padding: 1px;
    font-size: 11px;
    color: #000;
    background: #f9f9f9;
  }
  .el-table--mini td {
    padding: 1px;
    font-size: 10px;
    color: #222;
  }
  .f-detail{
    border-left: 1px solid #eee;
      height: calc(100% - 58px);
    overflow:auto;
    .u-title{
        padding-left: 5px;
        background: #f9f9f9;
        font-size: 11px;
        color: #000;
        height: 26px;
        line-height: 26px;
        border-bottom: 1px solid #F5F6F8;
        display: flex;
        .u-close{
          font-size: 15px;
          cursor: pointer;
          line-height: 26px;
          border-right: 1px solid #f0f0f0;
          padding-right: 7px;
        }
        .el-tabs__header{
          padding:0 10px!important;
          background-color: #f9f9f9!important;
        }
        .el-tabs__nav-wrap:after{
          background: none!important;;
        }
        .el-tabs__header{
          border:0!important
        }
    }
    .u-content-title{
      font-size:13px;
      margin-top:1em;
      border-bottom:1px solid #eee;
      padding-bottom: 5px;
    }
    .u-response{
      font-size: 11px;
      line-height: 1.5;
      color: #000;
      margin-top: 1em;
    }
    .u-content{
      padding-left:1em ;
      .u-sort{
        font-size: 10px;
        font-weight: bolder;
      }
    }
  }
  .h100{
    height: 100%;
  }
  .u-check-row{
    .el-table__cell{
      background: #4994ec ;
      color: #fff;
    }
  }
}
</style>
