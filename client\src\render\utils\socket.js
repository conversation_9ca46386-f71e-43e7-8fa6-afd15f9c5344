import io from 'socket.io-client'

class SocketIo {
  constructor(options = {}) {
    this.timer = null
    this.timeout = null
    this.wsInstance = null
    this.userInfo = null
    this.call = null
    this.bindTry = 0 // 绑定尝试次数
    const defaultOption = {
      path: '/access/ws/socket.io/',
      pingTimeout: 60000
    }
    this._options = { ...defaultOption, ...options }
  }
  initSocket(option, msgCall) {
    if (this.wsInstance) {
      return
    }
    console.log('初始化', option.ws)
    if (option.userInfo) {
      this.userInfo = option.userInfo
    }
    if (msgCall) {
      this.call = msgCall
    }
    this.bindTry = 0
    this.wsInstance = io(option.ws, this._options)
    this.onConnect()
    this.onError()
  }
  onConnect() {
    this.wsInstance.on('connect', () => {
      this.timeout = setTimeout(() => { // connect后立即调用bind可能出现多次连接问题
        clearTimeout(this.timeout)
        this.timeout = null
        console.log('socket.io === connect')
        this.bindUser()
        this.onBindRet()
      }, 1000)
    })
  }
  onError() {
    this.wsInstance.on('connect_error', (error) => {
      console.error('connect_error=', error)
    })
    this.wsInstance.on('disconnect', function(err) {
      console.error('断开连接=', err)
    })
    this.wsInstance.on('reconnect', function(attempt) {
      console.error('重新连接=', attempt)
    })
    this.wsInstance.on('reconnect_failed', function(attempt) {
      console.error('重新连接错误=', attempt)
    })
  }
  bindUser(_userInfo) {
    this.bindTry = this.bindTry + 1
    const userInfo = _userInfo || this.userInfo
    if (!userInfo.id || !userInfo.token) {
      return
    }
    console.log('绑定用户', userInfo)
    this.wsInstance.emit('bind', JSON.stringify({ user_id: Number(userInfo.id), token: userInfo.token }))
  }
  onBindRet() {
    this.wsInstance.on('bind', data => {
      console.log('bind', data)
      if (parseInt(_.get(data, 'code')) === 1) {
        this.heartbeat()
        this.onHearBeat()
        this.onMessage()
      } else {
        // 绑定失败，可能是服务器出问题了，要一直重试
        this.timeout = setTimeout(() => {
          this.bindUser()
          clearTimeout(this.timeout)
        }, this.bindTry * 2000)
      }
    })
  }

  heartbeat() {
    this.clearInterTime()
    this.timer = setInterval(() => {
      console.log('socket.io === heartbeat')
      this.wsInstance.emit('heartbeat', '1')
    }, 50000)
  }

  onHearBeat() {
    this.wsInstance.on('heartbeat', data => {
      console.log('on-heartbeat', data)
    })
  }

  closeSocket() {
    this.wsInstance && this.wsInstance.close()
    this.wsInstance = null
    this.clearInterTime()
  }

  onMessage() {
    this.wsInstance.on('message', data => {
      console.log('message', data)
      try {
        const info = JSON.parse(data)
        this.call && this.call(info)
      } catch (error) {
        this.call && this.call(data)
      }
    })
  }
  clearInterTime() {
    this.timer && clearInterval(this.timer)
    this.timer = null
  }
}

export default new SocketIo()
