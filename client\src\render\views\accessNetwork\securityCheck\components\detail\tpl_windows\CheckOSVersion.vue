<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.OSVersion.h_1_rd") }}<span>{{ version }}</span>
          </p>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
export default {
  name: 'CheckOSVersion',
  components: {
    checkResult,
    howToFix
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      zh: {
        Professional: '专业版',
        Home: '家庭版',
        Embedded: '定制版',
        'Datacenter Server': '数据中心版',
        'Advanced Server': '高级服务器版',
        Server: '服务器版',
        Starter: '初级版',
        Ultimate: '旗舰版',
        Enterprise: '企业版',
        Business: '商业版',
        Education: '教育版',
        'Compute Cluster': '计算机集群版',
        Datacenter: '数据中心版',
        Web: 'Web版',
        Standard: '标准版',
        'Web Server': 'Web服务器版',
        'Itanium-based Systems': '安腾版',
        All: '所有版本',
        MoreThan: '大于',
        Equal: '等于',
        LessThan: '小于'
      },
      en: {
        Professional: 'Professional Edition',
        Home: 'Home Edition',
        Embedded: 'Embedded Edition',
        'Datacenter Server': 'Datacenter Server',
        'Advanced Server': 'Advanced Server',
        Server: 'Server Edition',
        Starter: 'Starter Edition',
        Ultimate: 'Ultimate Edition',
        Enterprise: 'Enterprise Edition',
        Business: 'Business Edition',
        Education: 'Education Edition',
        'Compute Cluster': 'Compute Cluster Edition',
        Datacenter: 'Datacenter Edition',
        Web: 'Web Edition',
        Standard: 'Standard Edition',
        'Web Server': 'Web Server Edition',
        'Itanium-based Systems': 'Itanium-based Systems Edition',
        All: 'All',
        MoreThan: 'more than',
        Equal: 'equal',
        LessThan: 'less than'
      }
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    version() {
      const softCheckInfo = _.get(this.checkData, 'CheckResult.CheckType.Info.OSVersion', '') + ' ' + _.get(this.checkData, 'CheckResult.CheckType.Info.SP', '')
      return softCheckInfo
    },
    fixData() {
      const option = _.get(this.checkData, 'CheckType.Option')
      const LG = this[this.$i18n.locale]
      let str = `${this.$t('check.OSVersion.js_1_rd')}<br>`
      let flag = 0
      if (typeof option.Type !== 'undefined' && option.Type === 'NewOS') {
        if (typeof option.NewOS === 'object') {
          let NewOS = option.NewOS
          if (!_.isArray(NewOS)) {
            NewOS = [NewOS]
          }
          NewOS.forEach(item => {
            str += `<span class="error-color">${item.OSVersion} ${LG[item.OSKind]} ${LG[item.OSCompare]} ${item.OSVersionNum}</span><br/>`
          })
        } else {
          str += `<span class="error-color">${this.$t('check.OSVersion.h_2_rs')}</span><br/>`
        }
      } else {
        if (typeof option.OS === 'object') {
          let OS = option.OS
          if (!_.isArray(OS)) {
            OS = [OS]
          }
          OS.forEach(customer => {
            if (customer.SP !== 'Forbidden') {
              flag = 1
              if (customer.SP) {
                str += `<span class="error-color">${customer.OSVersion} ${customer.SP}</span> ${this.$t('check.OSVersion.js_3_rs')}<br/>`
              } else {
                str += `<span class="error-color">${customer.OSVersion}</span><br/>`
              }
            }
          })
        }
        if (flag === 0) {
          // 全部为禁止使用
          str += `<span class="error-color">${this.$t('check.OSVersion.h_2_rs')}</span>`
        }
      }
      return {
        modelTitle: this.$t('check.OSVersion.h_3_rs'),
        fixSteps: [this.$t('check.OSVersion.h_5_rs'), str]
      }
    }
  },
  mounted() {}
}
</script>
