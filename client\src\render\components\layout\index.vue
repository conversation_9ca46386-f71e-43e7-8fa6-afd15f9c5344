<template>
  <div v-if="initFinished" class="layout-page">
    <!--公共顶部菜单--->
    <layout-header />

    <div class="layout-wrap">
      <!--公共侧边栏菜单-->
      <layout-menu :linkage-menu="linkageMenu" />

      <div id="layoutMain" class="layout-main">
        <!--主流程路由渲染点-->
        <router-view :key="$route.fullPath" />
        <!--公共的底部组件-->
        <layout-footer />
      </div>
    </div>
  </div>
</template>

<script>
import Header from './header.vue'
import Menu from './menu.vue'
import Footer from './footer.vue'
import { mapState } from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import agentMsgListening from '@/service/api/agentListening'
import { getRemoteLangPack } from '@/render/lang/index'
import dot1xCommon from '@/render/utils/auth/dot1x'
import menuUtil from '@/render/utils/bussiness/menuUtil'
import { EventBus } from '@/render/eventBus'
import processController from '@/render/utils/processController'
import agentApi from '@/service/api/agentApi'
import { compareVersions } from '@/render/utils/global'
export default {
  name: 'Layout',
  components: {
    layoutHeader: Header,
    layoutMenu: Menu,
    layoutFooter: Footer
  },
  data() {
    return {
      initFinished: false,
      linkageMenu: []
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig'])
  },
  watch: {
    'clientInfo.accessStatus.IsOpenSDCLinkage': {
      handler(newV, oldV) {
        if (newV !== oldV) {
          agentApi.openSdcApp({ type: 'status', isOpenSDCLinkAge: newV })
        }
      },
      deep: true
    }
  },
  created() {
    this.init()
    this.initOnce()
    this.lazyinit()
    this.getAndListenLinkageMenu()
    this.listenReopenEvent()
    console.debug('webUrl:' + window.location.href)
  },
  methods: {
    async init() {
      try {
        // 获取基础信息
        console.debug((new Date().toLocaleString()) + ' ui init start.')
        Promise.all([commonUtil.getDot1xInfo(), commonUtil.basic(), commonUtil.checkDefaultPort(true), commonUtil.initToken()]).then((ret) => {
          Promise.all([commonUtil.detail('layout'), commonUtil.server('layout'), getRemoteLangPack(), commonUtil.getSpaToAuthInfo()]).then(() => {
            this.initFinished = true
            console.debug((new Date().toLocaleString()) + ' ui init finish.')
            if (this.isNeedUpdateClient()) {
              processController.set('/access/updateClient')
              return
            }
            commonUtil.fromBrowserVpnDial() // vpn拨号
          }).catch(e => {
            console.log(e)
            this.initFinished = true
            console.debug('捕获初始化错误2')
          })
        }).catch(e => {
          console.log(e)
          this.initFinished = true
          console.debug('捕获初始化错误1')
        })
      } catch (e) {
        console.log(e)
        console.debug('捕获初始化错误3')
        this.initFinished = true
      }
    },
    // 不影响页面渲染的初始化
    lazyinit() {
      dot1xCommon.getEmergentModeUserName()
    },
    // 只需要调用一次的方法(一定要避免重复监听)
    initOnce() {
      // 全局客户端事件监听
      agentMsgListening.init()
    },

    /**
     * 获取联动服务器的菜单[第一方or第三方联动服务器的菜单(联动msep或者联软等终端安全系统)]
     * @return {void}
     */
    async getAndListenLinkageMenu() {
      // 主动去取一次联动服务器的菜单配置
      this.linkageMenu = await menuUtil.getLinkageMenu()

      // 被动监听刷新联动菜单的信号
      EventBus.$on('refreshThirdLinkageMenu', (data) => {
        this.linkageMenu = data
      })
    },

    // 3746R001里面修改关闭小助手并不会真的关闭，只是把窗口隐藏了。重新打开小助手的时候发送reopen信号
    // 一定要避免重复监听
    async listenReopenEvent() {
      EventBus.$on('reopen', (data) => {
        processController.set({ path: '/access/message', query: { from: 'reopen' }})
        this.initFinished = false
        this.init()
        this.lazyinit()
      })

      EventBus.$on('closeAssui', () => {
        this.initFinished = false
      })
    },
    // 是否需要升级
    isNeedUpdateClient() {
      const AgentVersion_Inner = _.get(this.clientInfo, 'basic.AgentVersion_Inner')
      const webVersion = _.get(this.serveEntiretyConfig, 'server.MiniSupportClientEngine', '')
      if (!_.get(this.clientInfo, 'online') || !webVersion) {
        return false
      }
      if (!AgentVersion_Inner) { // LTS02版本之前客户端没有版本号强制升级
        return true
      }
      return compareVersions(AgentVersion_Inner, webVersion) < 0 // 客户端版本小于web要求最低版本
    }
  }
}
</script>
<style lang="scss" scoped>
.layout-page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fff;

  .layout-wrap{
    width: 100%;
    height: calc(100% - 83px);
    display: flex;
  }

  .layout-header {
    width: 100%;
    height: 50px;
    z-index: 10;
  }

  .layout-main {
    width: 100%;
    height: 100%;
    overflow: auto;
    flex:1;
    background: #fff;
  }
}
</style>
