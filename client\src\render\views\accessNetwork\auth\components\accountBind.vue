<template>
  <div class="account-bind-page">
    <div class="sign-wrapper">
      <span class="auth-tag"> <i class="iconfont icon-renzheng " />{{ $t('auth.auth') }} </span>
      <span class="mode-wrapper" @click="goBack">
        <i class="iconfont icon-fanhui ml12" />
        {{ $t('header.goBack') }}
      </span>
    </div>

    <div class="content">
      <p class="tab-pane-nev">
        <span class="is-active">
          {{ $t('auth.bindAccount', {name: mainName}) }}
        </span>
      </p>
      <div v-if="!isScanBind" class="tab-pane-content ">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          class="rule-form"
        >
          <component
            :is="currentCom"
            v-if="ruleForm.authType"
            ref="auth"
            :server-info="serverInfo"
            :auth-data.sync="authData"
            :bind-data="bindData"
            :auth-type="ruleForm.authType"
            @loading="changeLoading"
            @emitHandle="emitHandle"
          />

        </el-form>
      </div>
      <!-- 扫码认证 -->
      <div
        v-if="isScanBind && qrcodeOptions.length > 0"
        class="tab-pane-content scan-code-wrap"
      >
        <component
          :is="qrcodeType"
          ref="qrcode"
          :bind-data="bindData"
          :is-qrcode="isScanBind"
          @emitHandle="emitHandle"
        />
      </div>
      <p v-if="showSkipBind" class="bind-box"><span @click="skipBind">{{ $t('auth.skipBind') }}</span></p>
    </div>
  </div>
</template>
<script>

import { mapGetters } from 'vuex'
import authTypes from '@/render/utils/auth/authTypes'
import AccountAuth from './account.vue'
import WeWork from './wework'
import DingTalk from './dingtalk'
import FeiShu from './feishu'
import Sso from './sso.vue'
import authIndex from '@/render/utils/auth/index'
import processController from '@/render/utils/processController'
import proxyApi from '@/service/api/proxyApi'
export default {
  components: {
    User: AccountAuth,
    DingTalk,
    WeWork,
    FeiShu,
    Sso
  },
  props: {
  },
  data() {
    return {
      qrcodeType: '',
      authData: { // 认证数据（用户名、密码等）
        userName: '',
        password: '',
        verifyCode: '',
        userAutoLogin: false,
        userAutoAuth: false,
        cert: '',
        uKeyName: '',
        uKeyPassword: '',
        uKeyPin: '',
        uKeyAutoLogin: false,
        uKeyAutoAuth: false,
        uKeyCertDefault: '' // 证书的默认值
      },
      bindData: {},
      ruleForm: {
        authType: ''
      }
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'clientInfo',
      'authInfo'
    ]),
    // 是否是扫码绑定
    isScanBind() {
      const MainAccountType = _.get(this.computeServeEntiretyConfig, 'User.MainAccountType', '')
      return authTypes.getQrcode().indexOf(MainAccountType) > -1
    },
    /**
     * 是否用户名密码认证
     */
    isAccountAuthType() {
      return authTypes.isAccountAuth(this.ruleForm.authType)
    },
    currentCom() {
      if (this.isAccountAuthType) {
        return authTypes.User
      }
      const ssoConfig = _.get(this.computeServeEntiretyConfig, 'SSOConfig', {})
      const isSso = this.ruleForm.authType && ssoConfig[this.ruleForm.authType]
      return isSso ? 'Sso' : this.ruleForm.authType
    },
    /**
     * 二维码可选认证方式
     */
    qrcodeOptions() {
      const options = []

      const MainAccountType = _.get(this.computeServeEntiretyConfig, 'User.MainAccountType', '')
      if (this.isScanBind) { // 扫码方式
        options.push({
          value: MainAccountType,
          label: this.$t('accountBind.' + MainAccountType)
        })
      }
      return options
    },
    // 服务器配置
    serverInfo() {
      return {
        // 是否允许保存用户名
        clientSaveName: parseInt(_.get(this.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSaveName', 0)) === 1,
        // 是否允许保存密码
        clientSavePass: parseInt(_.get(this.computeServeEntiretyConfig, 'CLIENTCHECK.ClientSavePass', 0)) === 1,
        verifyCode: parseInt(_.get(this.computeServeEntiretyConfig, 'AUTHPARAM.verifyCode', 0)) === 1,
        // 用户名密码邮箱辅助输入 802.1x后续再支持
        address: _.get(this.serveEntiretyConfig.server, 'Address', []) || []
      }
    },
    mainName() {
      const MainAccountType = _.get(this.computeServeEntiretyConfig, 'User.MainAccountType', '')
      if (!this.isScanBind) { // 非扫码方式
        let serverAlias = _.get(this.computeServeEntiretyConfig, 'User.AuthServerAlias')
        try {
          serverAlias = JSON.parse(serverAlias)
        } catch (e) {
          serverAlias = []
        }
        const alias = serverAlias.find(item => item.name === MainAccountType)
        if (alias) {
          return alias['value']
        }
        const ssoConfig = _.get(this.computeServeEntiretyConfig, 'SSOConfig', {})
        const Sso = ssoConfig[MainAccountType]
        if (Sso) {
          return Sso.Nickname
        }
        return this.$t('accountBind.' + MainAccountType)
      } else {
        return this.$t('accountBind.' + MainAccountType)
      }
    },
    showSkipBind() {
      return parseInt(_.get(this.authInfo, 'basic.SkipBindAuth', 0)) === 1
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const MainAccountType = _.get(this.computeServeEntiretyConfig, 'User.MainAccountType', '')
      this.ruleForm.authType = MainAccountType
      this.qrcodeType = MainAccountType

      const bindData = {
        authFrom: 'bind',
        bindAuthServer: MainAccountType,
        userid: _.get(this.authInfo, 'basic.UserID')
      }
      this.bindData = bindData
    },
    changeLoading(value) {
      this.$emit('emitHandle', { type: 'loading', value })
    },
    emitHandle(data) {
      console.log('emitHandle', data)
      switch (data.type) {
        case 'AuthSuccess':
          this.AuthSuccess()
          break
        case 'revoke:show':
          // 超出最大可登录设备数
          this.$emit('emitHandle', data)
          break
        default:
          return false
      }
    },
    /**
     * 认证成功
     */
    async AuthSuccess() {
      this.$emit('emitHandle', { type: 'loading', value: true })
      const authType = !this.isScanBind ? this.ruleForm.authType : this.qrcodeType
      const isAccountAuthType = !this.isScanBind ? this.isAccountAuthType : false
      authIndex.authSuccess({
        ...this.authData,
        authType,
        isAccountAuthType
      }).finally(() => {
        this.$emit('emitHandle', { type: 'loading', value: !!authIndex.config.IsServerIPChanged })
        console.log('认证成功')
      })
    },
    goBack() {
      this.$router.push({ path: '/access/auth', query: { time: new Date().getTime() }})
    },
    submit() {
      if (!this.isScanBind) {
        this.$refs.auth.submitForm('ruleForm')
      } else {
        this.$refs.qrcode.submitForm('ruleForm')
      }
    },
    async skipBind() {
      const ret = await proxyApi.skipBindApi({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0)
      })
      if (parseInt(parseInt(_.get(ret, 'errcode', -1))) === 0) {
        processController.next({ fromAuth: true })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.is-active{
  margin: 0 auto;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC-Medium
}
  .bind-box{
    text-align: center;
    span{
      color: $default-color;
      cursor: pointer;
      &:hover{
        color: $--color-primary;
      }
    }
  }
</style>
