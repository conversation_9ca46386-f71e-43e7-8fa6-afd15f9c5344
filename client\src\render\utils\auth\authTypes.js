// 认证方式
const authTypes = {
  User: 'User',
  UKey: 'UKey',
  Mobile: 'Mobile',
  Finger: 'Finger',
  Guest: 'Guest',
  NoAuth: 'NoAuth',
  TwoFactor: 'TwoFactor',
  ADAutoLogin: 'ADAutoLogin',
  Permit: 'Permit',

  // 用户名密码
  Localhost: 'Localhost',
  AdDomain: 'AdDomain',
  LDAP: 'LDAP',
  Other: 'Other',
  Email: 'Email',
  Ra<PERSON>: 'Radius',
  Sso: 'Sso',
  WebAuth: 'WebAuth',

  // 扫码
  WeWork: 'WeWork',
  DingTalk: 'DingTalk',
  FeiShu: 'FeiShu',
  NAC: 'NAC',

  // 扫码认证方式
  getQrcode() {
    return [
      this.WeWork,
      this.DingTalk,
      this.FeiShu,
      this.NAC
    ]
  },
  // 是否扫码认证
  isQrCode(type) {
    return this.getQrcode().indexOf(type) !== -1
  },
  // 用户名密码
  getAccountTypes() {
    return [
      this.User,
      this.Localhost,
      this.AdDomain,
      this.LDAP,
      this.Other,
      this.Email,
      this.Radius
    ]
  },
  // 是否用户名密码
  isAccountAuth(type) {
    return this.getAccountTypes().indexOf(type) !== -1
  }
}

export default authTypes
