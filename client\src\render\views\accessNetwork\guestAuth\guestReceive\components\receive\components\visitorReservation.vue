<!--访客预约-->
<template>
  <div id="reserve-receive-page" @keydown="keyDown">
    <div class="form-wrapper">
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-width="14px"
        class="guest-apply-form"
        :validate-on-rule-change="false"
      >
        <!--input输入框-->
        <el-form-item prop="guestname">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_85')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_85') }}：</span>
          </el-tooltip> -->
          <el-input
            id="ui-guest-receive-order-input-guest_name"
            v-model="form.guestname"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_85')"
          >
            <i slot="prefix" class="iconfont icon-laibinrenzheng" />
          </el-input>
        </el-form-item>
        <el-form-item prop="guestcompany">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_86')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_86') }}：</span>
          </el-tooltip> -->
          <el-input
            id="ui-guest-receive-order-input-guest_company"
            v-model="form.guestcompany"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_86')"
          >
            <i slot="prefix" class="iconfont icon-laibinrenzheng" />
          </el-input>
        </el-form-item>
        <el-form-item prop="guestmobile">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_87')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_87') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-input
            id="ui-guest-receive-order-input-guest_iphone"
            v-model="form.guestmobile"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_87')"
          >
            <i slot="prefix" class="iconfont icon-laibinrenzheng" />
          </el-input>
        </el-form-item>
        <el-form-item v-if="allowSMS" class="lower-item" prop="issendsms">
          <el-checkbox id="ui-guest-receive-order-checkbox-send_code" v-model="form.issendsms" class="s-send-sms" true-label="1" false-label="0">{{ $t('guestAuth.guest.info_88') }}</el-checkbox>
        </el-form-item>
        <el-form-item prop="GuestStartTime">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_42')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_42') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-date-picker
            id="ui-guest-receive-order-date-timetable"
            v-model="form.GuestStartTime"
            type="daterange"
            value-format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            :range-separator="$t('guestAuth.guest.info_48')"
            :start-placeholder="$t('guestAuth.guest.info_49')"
            :end-placeholder="$t('guestAuth.guest.info_50')"
            :picker-options="pickerOptions"
            prefix-icon="iconfont icon-shangwangshichang"
          />
        </el-form-item>
        <el-form-item prop="AllowTime">
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_43')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_43') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <el-input
            id="ui-guest-receive-order-input-network_time"
            v-model="form.AllowTime"
            :placeholder="$t('guestAuth.guest.info_68')+$t('guestAuth.guest.info_43')"
            class="hour-input"
          >
            <i slot="prefix" class="iconfont icon-shangwangshichang" />
          </el-input>
          <span class="input-info">{{ $t('guestAuth.guest.info_46') }}</span>
        </el-form-item>
        <el-form-item
          prop="AllowRegionIDs"
          class="select-form-box"
        >
          <!-- <el-tooltip
            slot="label"
            effect="dark"
            :content="$t('guestAuth.guest.info_44')"
            placement="top-start"
            :tabindex="-1"
          >
            <span class="text-clamp">{{ $t('guestAuth.guest.info_44') }}：</span>
          </el-tooltip> -->
          <span slot="label" />
          <i class="iconfont icon-wangluoyu" />
          <el-select
            id="ui-guest-receive-order-select-network_domain"
            v-model="form.AllowRegionIDs"
            v-title="true"
            clearable
            :placeholder="$t('guestAuth.guest.info_47')+$t('guestAuth.guest.info_44')"
            multiple
            collapse-tags
            class="g-mult-se"
          >
            <el-option v-for="item in propRegions" :id="'ui-guest-receive-order-option-network_'+item.id" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="submit-wrapper">
      <p id="ui-guest-receive-order-p-submit" class="public-btn" @click="submitForm()"><i v-if="loading" class="el-icon-loading" />{{ $t('reg.submit') }}</p>
    </div>

  </div>
</template>
<script>
import { mapState } from 'vuex'
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'
import guestFormUtils from '@/render/utils/bussiness/guestFormUtils'
import { Base64Encode } from '@/render/utils/global'
export default {
  directives: {
    title: {
      inserted: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      },
      componentUpdated: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      }

    }
  },
  props: {
    propRegions: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      form: {
        AllowRegionIDs: [],
        guestname: '',
        guestcompany: '',
        guestmobile: '',
        AllowTime: 8,
        GuestStartTime: '',
        issendsms: '1'
      },
      rules: {},
      loading: false,
      pickerOptions: {
        disabledDate(time) {
          const oneDay = 60 * 60 * 24 * 1000
          return time.getTime() < Date.now() - oneDay
        }
      }
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig']),
    allowSMS() {
      return parseInt(_.get(this.serveEntiretyConfig, 'server.GUESTAUTH.AllowSMS', 0)) === 1
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.getRules()
      this.$nextTick(() => {
        this.$refs['ruleForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['ruleForm'].validateField(item.labelFor)
          }
        })
      })
    }
  },
  mounted() {
    this.getRules()
  },
  methods: {
    addTitle(el) {
      this.$nextTick(() => {
        const tags = el.querySelectorAll('.el-tag')
        for (let i = 0; i < tags.length; i++) {
          tags[i].title = tags[i].querySelector('.el-select__tags-text').innerHTML
        }
      })
    },
    getRules() {
      const validateName = (rule, value, callback) => {
        if (value && !regular.rules.Default.test(value)) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_85')} ] ${regular.errorTipMap().Default}`))
        } else {
          callback()
        }
      }
      const validateUnit = (rule, value, callback) => {
        if (value && !regular.rules.Default.test(value)) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_86')} ] ${regular.errorTipMap().Default}`))
        } else {
          callback()
        }
      }
      const validateTel = (rule, value, callback) => {
        if (value && !regular.rules.Tel.test(value)) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_87')} ] ${regular.errorTipMap().Tel}`))
        } else {
          callback()
        }
      }
      var validateRegion = (rule, value, callback) => {
        if (value && value.length === 0) {
          callback(new Error(`[ ${this.$t('guestAuth.guest.info_44')} ] ${this.$t('auth.emptyErr')} `))
        } else {
          callback()
        }
      }
      var validateTime = (rule, value, callback) => {
        if (value && !regular.rules['1-999Num'].test(value)) {
          callback(new Error(`[${this.$t('guestAuth.guest.info_43')}] ${this.$t('changeRegInfo.styleErro')} ${regular.errorTipMap()['1-999Num']}`))
        } else {
          callback()
        }
      }

      this.rules = {
        guestname: [
          { validator: validateName, trigger: 'blur' }
        ],
        guestcompany: [
          { validator: validateUnit, trigger: 'blur' }
        ],
        guestmobile: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_87')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateTel, trigger: 'blur' }
        ],
        AllowTime: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_43')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateTime, trigger: 'blur' }
        ],
        GuestStartTime: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_42')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' }
        ],
        AllowRegionIDs: [
          { required: true, message: `[ ${this.$t('guestAuth.guest.info_44')} ] ${this.$t('auth.emptyErr')} `, trigger: 'blur' },
          { validator: validateRegion, trigger: 'change' }
        ]
      }
    },
    async submitForm() {
      this.$refs['ruleForm'].validate(async(valid, obj) => {
        if (valid) {
          const params = guestFormUtils.formateFormParams(this.form)
          console.log(params)
          const { UserID, roleID, userName } = _.get(this.clientInfo, 'accessStatus')
          const { DeviceID } = _.get(this.clientInfo, 'detail')
          const fixedParams = {
            deviceId: DeviceID,
            UserID,
            roleid: roleID,
            user_name: Base64Encode(userName)
          }
          const _params = { ...params, ...fixedParams }
          _params.issendsms = this.allowSMS ? _params.issendsms : '0'
          this.loading = true
          const ret = await proxyApi.guestReserve(_params)
          this.loading = false
          if (parseInt(ret.errcode) === 0) {
            this.$refs['ruleForm'].resetFields()
            this.$message({
              message: this.$t('guestAuth.guest.info_101'),
              type: 'success'
            })
          }
        } else {
          // 滚动到校验不通过项位置
        }
      })
    },
    // 回车提交
    keyDown(event) {
      if (parseInt(event.keyCode) !== 13 || event.srcElement.type === 'textarea') {
        return
      }
      // 非加载中
      if (!this.loading) {
        this.submitForm()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
    #reserve-receive-page {
        // width: 526px;
        width: calc(100% + 48px);
        margin: 0 auto;
        height: 100%;
        margin-right: -24px;
        margin-left: -24px;
        .el-form-item:last-child{
            margin-bottom: 0;
        }
        ::v-deep .el-form-item__label{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: $title-color;
        }
        ::v-deep .el-form-item__content{
            width: 360px;
            text-align: left;
            .el-radio{
                line-height: 40px;
            }
            .el-select{
                .el-input__inner{
                    padding-left: 15px;
                }
                .el-select__tags > span{
                    display: flex;
                }
            }
            .el-range-editor.el-input__inner{
                width: 100%;
                .el-input__icon{
                  margin-left: 0;
                  color: $disabled-color;
                }
            }
            .el-range-separator{
                box-sizing: content-box;
            }
            // .hour-input{
            //     width: 140px;
            //     margin-right: 10px;
            // }
            .input-info{
              position: absolute;
              right: 15px;
              color: $title-color;
            }
            .el-range-input{
                line-height: 32px;
                color: $title-color;
            }
        }
        .form-wrapper{
          max-height: calc(100% - 77px);
          padding: 0 16px;
          overflow: auto;
          &::-webkit-scrollbar-track{
            background-color: #fff;
          }
          &::-webkit-scrollbar-button{
            background-color: #fff;
          } /* 滑轨两头的监听按钮颜色 */
          &::-webkit-scrollbar-corner{
            background-color: #fff;
          }
          .guest-apply-form{
            width: 380px;
            margin: 0 auto;
          }
        }
        .submit-wrapper{
            padding-top: 24px;
            text-align: left;
            padding-left: 0;
            display: flex;
            justify-content: center;
            button{
                width: 360px;
            }
        }
        ::v-deep .g-mult-se{
          .el-tag{
            max-width: 100px;
          }
        }
        ::v-deep .lower-item{
            .el-form-item__content{
                line-height: 20px;
                .el-checkbox__label{
                  font-weight: 400;
                }
            }
            .s-send-sms .el-checkbox__label{
              color: $title-color
            }
        }
        ::v-deep .thin-form-item{
          .el-form-item__label, .el-form-item__content{
            line-height: 20px;
            .el-radio, .el-checkbox{
              line-height: 20px;
              margin-right: 24px;
              font-weight: 400;
            }
            .el-radio__label{
              color: $title-color
            }
          }
        }
}
</style>
