<template>
  <div class="apply-result-page">
    <div class="content">
      <div class="a-code-wrapper">
        <span :id="'ui-guest-receive-'+idKey+'-span-guest_code'">{{ code }}</span>
        <el-popover
          v-model="visible"
          placement="bottom-start"
          trigger="manual"
          popper-class="g-copy-pop"
          :visible-arrow="false"
        >
          <div class="pop-content">
            <i :class="['iconfont', infoType === 'success'? 'icon-anjianheguixiang':'icon-guanjianxiangbuhegui']" />
            {{ message }}
          </div>
          <i
            :id="'ui-guest-receive-'+idKey+'-i-duplicate'"
            slot="reference"
            v-clipboard:copy="code"
            v-clipboard:success="copyCodeFn"
            v-clipboard:error="onError"
            class="iconfont icon-fuzhi"
          />
        </el-popover>
      </div>
      <div v-if="result.type === 'team'" class="a-qr-wrapper">
        <img id="ui-guest-receive-team-img-teamcode_img" :src="result.data && result.data.qrcodeUrl" alt="">
      </div>
    </div>
    <p class="apply-agin"><span :id="'ui-guest-receive-'+idKey+'-p-re_apply'" @click="applyAgin">{{ $t('guestAuth.guest.info_57') }}</span></p>
  </div>
</template>
<script>
import { sleep } from '@/render/utils/global'
export default {
  props: {
    result: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      timer: null,
      visible: false,
      message: '',
      infoType: 'success'
    }
  },
  computed: {
    code() {
      return _.get(this.result, 'data.Code')
    },
    idKey() {
      return this.result.type === 'team' ? 'team' : 'single'
    }
  },
  methods: {
    applyAgin() {
      console.log('changeMode')
      this.$emit('changeMode', '')
    },
    // 复制来宾码
    async copyCodeFn() {
      this.showTip('success')
    },
    async onError() {
      this.showTip('fail')
    },
    async showTip(type) {
      if (this.infoType === type && this.visible) {
        return
      }
      this.infoType = type
      this.visible = true
      this.message = type === 'success' ? this.$t('guestAuth.administrator.info_8') : this.$t('guestAuth.administrator.info_20')
      await sleep(2000)
      this.visible = false
    }
  }
}
</script>
<style lang="scss">
.apply-result-page{
    height: 100%;
    padding-top: 32px;
    .content{
        width: 360px;
        margin: 0 auto;
    }
    .a-title{
        width: 360px;
        line-height: 20px;
        font-size: 14px;
        color: $title-color;
        text-align: left;
        padding-bottom: 16px;
    }
    .a-code-wrapper{
        width: 360px;
        position: relative;
        border: 1px dashed $gray-2;
        margin-bottom: 32px;
        font-size: 24px;
        font-weight: 600;
        border-radius: 4px;
        color: $title-color;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        .icon-fuzhi{
            font-size: 16px;
            color: $--color-primary;
            cursor: pointer;
            font-weight: 400;
            margin-left: 16px;
            vertical-align: middle;
        }
    }
    .a-qr-wrapper{
        width: 200px;
        height: 200px;
        background: url('../../../../../../../assets/scan_boder.png') ;
        background-size: cover;
        margin: 0 auto;
        margin-bottom: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 160px;
          height: 160px;
        }
    }
    .btn-box{
      width: 435px;
      padding-left: 30px;
      margin: 0 auto;
    }
    .apply-agin{
      text-align: center;
      color: $--color-primary;
      span{
        cursor: pointer;
      }
    }
}
.g-copy-pop{
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 80px;
  .pop-content{
    line-height: 17px;
    font-size: 12px;
    color: $title-color;
    .iconfont{
      font-size: 14px;
      color:$error;
    }
    .icon-anjianheguixiang{
      color: $success
    }

  }
}
</style>
