<template>
  <checkResult :result-data="resultData" :loading="loading" :has-fixed="checkData.hasFixed" :show-btn="showBtn" @fix="fixHandle" />
</template>
<script>
import tplMixins from '../mixins/tpl_windows'
import checkResult from '../checkResult/checkResult.vue'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    // 只有windows展示修复按钮
    showBtn() {
      return _.get(G_VARIABLE, 'os_browser_info.os_type') === 'windows'
    }
  },
  methods: {
    fixHandle() {
      if (this.checkData.CheckType.Option.DomainURL.length > 0) {
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            filepath: this.checkData.CheckType.Option.DomainURL,
            filepath_arl: this.checkData.CheckType.Option.DomainURL_Arg
          },
          RepairType: 0,
          CreateProgress: 1
        }
        this.submitHandle({
          params,
          CheckItem: this.checkData,
          tip: this.$t('check.DomainUser.js_2_d')
        })
      } else {
        this.$message({
          message: this.$t('check.DomainUser.js_3_d'),
          type: 'error'
        })
      }
    }
  }
}

</script>
