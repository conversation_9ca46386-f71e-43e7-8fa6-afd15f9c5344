.computer-details-modle,
.how-to-fix,
.stand-detail-modle,
.result-detail-modle {
  .model-title {
    height: 40px;
    background: $default-bg;
    display: flex;
    width: 100%;
    padding: 0 16px;
    align-items: center;
    justify-content: space-between;
    color: $default-color;
    margin-bottom: 16px;
    i {
      cursor: pointer;
      font-size: 16px;
    }
  }
  .result-model-content {
    .result-content {
      padding: 0 0 16px 16px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .left-wrapper{
        display: flex;
        flex:1;
        margin-top: 3px;
        padding-right: 16px;
      }
      span {
        flex: 1;
        color: $title-color;
        line-height: 20px;
        padding-left: 10px;
      }
      .public-medium-btn{
        flex-shrink: 0;
      }
    }
    .iconfont {
      font-size: 16px;
      line-height: 20px;
      &.err {
        color: $error;
      }
      &.warn {
        color: $waring;
      }
      &.success {
        color: $success;
      }
    }
    .loading-disable{
      cursor: not-allowed;
    }
  }
  .model-content {
    padding: 0 0 16px 16px;
    .tit-info {
      margin-bottom: 16px;
      color: $title-color;
    }
    .optional-item {
      font-size: 12px;
      color: $default-color;
      line-height: 17px;
      span {
        color: $title-color;
        word-break: break-all;
      }
    }
    .pc-info {
      display: flex;
      width: 100%;
      align-items: center;
      margin-bottom: 16px;
      &:last-child{
        margin-bottom: 0;
      }
      img {
        width: 53px;
        height: 53px;
      }
      .pc-info-rit {
        flex: 1;
        padding-left: 16px;
        font-size: 12px;
        line-height: 17px;
        .custom-name {
          display: flex;
          justify-content: space-between;
          align-content: center;
          line-height: 17px;
          flex: 1;
          .optional-item {
            display: flex;
            align-items: center;
            word-break: keep-all;
            padding-right: 10px;
            span {
              word-break: break-all;
            }
          }
          .btn-small {
            flex-shrink: 0;
          }
        }
        .margin-style{
          margin-bottom: 8px;
        }
        .margin{
          margin-top: 4px;
          margin-bottom: 8px;
        }
        .loading-disable{
          cursor: not-allowed;
        }
      }
    }
    .fix-btn-wrapper {
      text-align: right;
      padding-top: 24px;
      .loading-disable{
        cursor: not-allowed;
      }
    }
  }
  .stand-model-content {
    .el-table {
      margin-bottom: 16px;
      .cell {
        .patch-title {
          cursor: pointer;
          display: inline-block;
          width: auto;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      thead {
        .el-table_1_column_4.is-leaf {
          .cell {
            padding-right: 5px;
          }
        }
      }
    }
    .loading-disable{
      cursor: not-allowed;
    }
  }
  .fix-model-content {
    .el-row {
      margin-bottom: 16px;
      border: 1px solid $line-color;
      padding: 16px;
      .question {
        font-weight: 500;
        text-align: left;
        color: $title-color;
        line-height: 20px;
        margin-bottom: 10px;
      }
      .way {
        color: $default-color;
        line-height: 24px;
      }
    }
  }
  .link-btn{
    color: $--color-primary;
    text-decoration: underline;
    cursor: pointer;
    flex-shrink: 0;
  }
  .disable-link-btn{
    cursor: not-allowed;
    color: $disabled-color;
  }
  .btn-disabled{
    background-color: $line-color;
    box-sizing: border-box;
    font-size: 12px;
    text-align: center;
    line-height: 26px;
    border-radius: 4px;
    color: $disabled-color;
    cursor: not-allowed;
    width: 80px;
  }
  .btn-max-disabled{
    background-color: $line-color;
    box-sizing: border-box;
    font-size: 14px;
    text-align: center;
    line-height: 32px;
    border-radius: 4px;
    color: $disabled-color;
    cursor: not-allowed;
    width: 100px;
  }
}
