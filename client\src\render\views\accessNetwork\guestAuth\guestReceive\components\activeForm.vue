<template>
  <div class="active-form-wrapper">
    <div class="form-wrapper">
      <el-form
        ref="ruleForm"
        :model="form"
        label-width="14px"
        class="guest-active-form"
      >
        <template v-for="item in formFields">
          <!--input输入框-->
          <el-form-item
            v-if="item.InputType === 'text'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-input
              :id="'ui-'+formName+'-input-'+item.Column"
              v-model="form[item.Name]"
              :disabled="item.disabled"
              :placeholder="item.Placeholder"
            >
              <i slot="prefix" :class="item.icon" />
            </el-input>
          </el-form-item>
          <!--slot-->
          <el-form-item
            v-if="item.addSlot"
            :key="item.Name"
            :class="'form-item-'+item.Name"
          >
            <slot />
          </el-form-item>

          <!--textare输入框-->
          <el-form-item
            v-if="item.InputType === 'textarea'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-input
              :id="'ui-'+formName+'-input-'+item.Column"
              v-model="form[item.Name]"
              class="mark-input"
              type="textarea"
              :disabled="item.disabled"
              :placeholder="item.Placeholder"
            />
          </el-form-item>
          <!-- 下拉选择 -->
          <el-form-item
            v-if="item.InputType === 'select'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
            class="select-form-box"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <i :class="item.icon" />
            <el-select
              :id="'ui-'+formName+'-select-'+item.Column"
              v-model="form[item.Name]"
              v-title="item.Name === 'AllowRegionIDs'"
              :class="item.Name === 'AllowRegionIDs' ?'g-mult-se':''"
              clearable
              :disabled="item.disabled"
              :placeholder="item.Placeholder"
              collapse-tags
              :multiple="item.Name === 'AllowRegionIDs'"
            >
              <el-option
                v-for="opt in item.Options"
                :id="'ui-'+formName+'-option-'+item.Column+'_'+opt.id"
                :key="opt.id"
                :label="opt.label"
                :value="opt.id"
              />
            </el-select>
          </el-form-item>

          <!--checkbox表单-->
          <el-form-item
            v-if="item.InputType === 'checkbox'"
            :key="item.Column"
            class="check-item-form"
            :rules="item.Rule"
            :label="item.Title + ':'"
            :prop="item.Name"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-checkbox-group v-model="form[item.Name]" :disabled="item.disabled">
              <el-checkbox
                v-for="label in item.Options"
                :id="'ui-'+formName+'-checkbox-'+item.Column"
                :key="label"
                :label="label"
              />
            </el-checkbox-group>
          </el-form-item>

          <!--radio表单-->
          <el-form-item
            v-if="item.InputType === 'radio'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
            class="check-item-form"
            :label="item.Title + ':'"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-radio-group v-model="form[item.Name]" :rules="item.Rule" :disabled="item.disabled">
              <el-radio
                v-for="label in item.Options"
                :id="'ui-'+formName+'-radio-'+label"
                :key="label"
                :label="label"
              >{{ label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 日期范围选择 -->
          <el-form-item
            v-if="item.InputType === 'dayRange'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
            class="date-item-form"
            :label="item.Title + ':'"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-date-picker
              :id="'ui-'+formName+'-date-'+item.Column"
              v-model="form[item.Name]"
              type="daterange"
              :disabled="item.disabled"
              value-format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              :range-separator="$t('guestAuth.guest.info_48')"
              :start-placeholder="$t('guestAuth.guest.info_49')"
              :end-placeholder="$t('guestAuth.guest.info_50')"
              :picker-options="pickerOptions"
              prefix-icon="iconfont icon-shangwangshichang"
            />
          </el-form-item>
          <!-- 小时 -->
          <el-form-item
            v-if="item.InputType === 'hour'"
            :key="item.Column"
            :rules="item.Rule"
            :prop="item.Name"
            class="hour-item-form"
            :label="item.Title + ':'"
          >
            <!-- <el-tooltip
              slot="label"
              effect="dark"
              :content="item.Title"
              placement="top-start"
              :tabindex="-1"
            >
              <span class="text-clamp">{{ item.Title }}：</span>
            </el-tooltip> -->
            <span slot="label" />
            <el-input
              :id="'ui-'+formName+'-input-'+item.Column"
              v-model="form[item.Name]"
              :disabled="item.disabled"
              class="hour-input"
            >
              <i slot="prefix" :class="item.icon" />
            </el-input>
            <span class="input-info">{{ $t('guestAuth.guest.info_46') }}</span>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div :class="['submit-wrapper', formFields.length === 0 ? 'fix-left': '']">
      <p :id="'ui-'+formName+'-p-submit'" class="public-btn" @click="submitForm()"><i v-if="loading" class="el-icon-loading" />{{ $t('reg.submit') }}</p>
    </div>
  </div>
</template>
<script>
import { i18n } from '@/render/lang'
export default {
  directives: {
    title: {
      inserted: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      },
      componentUpdated: function(el, binding, vnode) {
        if (binding.value) {
          vnode.context.addTitle(el)
        }
      }

    }
  },
  props: {
    formFields: {
      type: Array,
      default: function() {
        return []
      }
    },
    form: {
      type: Object,
      default: function() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    submitText: {
      default: i18n.t('reg.submit'),
      type: String
    },
    formName: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          const oneDay = 60 * 60 * 24 * 1000
          return time.getTime() < Date.now() - oneDay
        }
      }
    }
  },
  methods: {
    addTitle(el) {
      this.$nextTick(() => {
        const tags = el.querySelectorAll('.el-tag')
        for (let i = 0; i < tags.length; i++) {
          tags[i].title = tags[i].querySelector('.el-select__tags-text').innerHTML
        }
      })
    },
    async submitForm() {
      if (this.loading) {
        return
      }
      this.$refs['ruleForm'].validate(async(valid, obj) => {
        if (valid) {
          this.$emit('submit', valid)
        } else {
          // 滚动到校验不通过项位置
          this.scrollToErr(obj)
        }
      })
    },
    scrollToErr(errObj) {
      // 获取第一个错误项
      const keys = Object.keys(errObj)
      let idx = this.formFields.length - 1
      keys.forEach(item => {
        const index = this.formFields.findIndex(o => o.Name === item)
        if (index !== -1 && index < idx) {
          idx = index
        }
      })
      try {
        const parent = document.getElementsByClassName('guest-active-form')[0]
        const current = parent.getElementsByClassName('el-form-item')[idx]
        const scrollWrapper = document.getElementsByClassName('form-wrapper')[0]
        const top = current.offsetTop - scrollWrapper.offsetTop
        if (top >= 0) {
          scrollWrapper.scrollTop = top
        }
      } catch (e) {
        console.log(e)
      }
    },
    restForm() {
      this.$refs['ruleForm'].fields.forEach(item => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
    .active-form-wrapper{
        height: 100%;
        .el-form-item:last-child{
            margin-bottom: 0;
        }
        ::v-deep .el-form-item__label{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: $title-color;
        }
        ::v-deep .el-form-item__content{
            width: 360px;
            text-align: left;
            .el-radio{
                line-height: 40px;
            }
            .el-select{
                .el-input__inner{
                    padding-left: 15px;
                }
            }
            .el-range-editor.el-input__inner{
                width: 100%;
                .el-input__icon{
                  margin-left: 0;
                  color: $disabled-color;
                }
            }
            .el-range-separator{
                box-sizing: content-box;
            }
            // .hour-input{
            //     width: 140px;
            //     margin-right: 10px;
            // }
            .el-range-input{
                line-height: 32px;
                color: $title-color;
            }
            .input-info{
              position: absolute;
              right: 15px;
              color: $title-color;
            }
        }
        ::v-deep .el-select .el-select__tags > span{
            display: flex;
        }
        .form-wrapper{
            max-height: calc(100% - 77px);
            padding: 0 16px;
            overflow: auto;
        }
        .submit-wrapper{
            padding-top: 24px;
            text-align: left;
            padding-left: 15px;
            display: flex;
            justify-content: center;
            .el-icon-loading{
              font-size: 16px;
              margin-right: 5px;
            }
            button{
                width: 360px;
            }
        }
        .fix-left{
          padding-left: 0;
        }
        ::v-deep .g-mult-se{
          .el-tag{
            max-width: 100px;
          }
        }
        ::v-deep .form-item-guestmobile .el-form-item__content{
          line-height: 20px;
          .el-checkbox__label{
            font-weight: 400;
          }
        }
        ::v-deep .check-item-form{
          .el-form-item__label, .el-form-item__content{
            line-height: 20px;
            .el-radio, .el-checkbox{
              line-height: 20px;
              margin-right: 24px;
              font-weight: 400;
            }
          }
        }
    }
    .guest-reg-form{
      .form-wrapper{
        max-height: calc(100% - 72px);
      }
      .submit-wrapper{
        justify-content: flex-start;
        padding-left: 30px;
      }
    }
</style>
