<template>
  <div class="my-device-page">
    <div class="device-title">
      {{ $t('myDevice.title') }} <i>{{ deviceList.length }}</i> {{ $t('myDevice.tai') }}
      <span
        v-if="deviceInnerBack"
        class="go-back"
        @click="returnNetWorkAccess"
      >&lt;{{ $t('header.goBack') }}</span>
    </div>

    <el-table :data="deviceList" stripe style="width: 100%" class="public-no-boder-table count-menu-table">
      <el-table-column
        prop="DevName"
        width="170"
        :label="$t('myDevice.compute')"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="patch-title">
            <span v-if="scope.row.isCurrent" class="table-current-tag">
              {{ $t('myDevice.currentDevice') }}
            </span>
            {{ scope.row.DevName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="AuthTime" width="175" :label="$t('myDevice.regTime')" />
      <el-table-column :label="$t('myDevice.operate')" align="right">
        <template slot-scope="scope">
          <span v-if="scope.row.isCurrent">-</span>
          <el-popconfirm v-else id="tip-pop" :title="$t('myDevice.logout')" :hide-icon="true" width="228" popper-class="table-pover" @confirm="disconnectHandle(scope.row)">
            <el-button id="ui-accessNetwork-my_device-button-off_line" slot="reference" type="text" size="small" :title="$t('myDevice.outline')"><i class="iconfont icon-qiangzhixiaxian" /></el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 吐司 -->
    <News-hint :visible.sync="toastVisible" :info-type="infoType" :message="message" />
  </div>
</template>

<script>
import './common/common.scss'
import _ from 'lodash'
import proxyApi from '@/service/api/proxyApi'
import { mapGetters } from 'vuex'

export default {
  props: {
    deviceInnerBack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      originalDeviceList: [],
      toastVisible: false,
      message: '',
      infoType: ''
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo'
    ]),
    deviceList() {
      let data = []
      if (_.isArray(this.originalDeviceList) && !_.isEmpty(this.originalDeviceList)) {
        data = _.cloneDeep(this.originalDeviceList)
      }
      this.$emit('hdEvent', {
        type: 'deviceNumber',
        val: data.length
      })
      return data
    }
  },
  mounted() {
    this.getOnlineDevice()
  },
  methods: {
    async disconnectHandle(row) {
      this.dialogVisible = true
      const result = await proxyApi.netCutoff({
        device_id: row.DeviceID,
        msg: this.$t('auth.manualOffline', {
          username: _.get(this.clientInfo, 'accessStatus.userName', ''),
          ip: _.get(this.clientInfo, 'detail.IP', '')
        }),
        isSendCutOffMsg: '1',
        currDeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0),
        remark: 'LogOut'
      })
      this.loading = false
      if (result.errcode === '0') {
        this.infoType = 'success'
        this.message = result.errmsg
        this.toastVisible = true
        this.getOnlineDevice()
      } else {
        this.infoType = 'error'
        this.message = result.errmsg
        this.toastVisible = true
      }
    },
    async getOnlineDevice() {
      const data = {
        DeviceID: _.get(this.clientInfo, 'detail.DeviceID', 0),
        UserName: _.get(this.clientInfo, 'accessStatus.userName', ''),
        AuthType: _.get(this.clientInfo, 'accessStatus.lastAuthType', 'User')
      }
      const result = await proxyApi.deviceOnline(data)
      if (result.errcode === '0') {
        this.originalDeviceList = result.data.data
        const idx = this.originalDeviceList.findIndex(item => String(item.DeviceID) === String(data.DeviceID))
        if (idx > -1) {
          this.originalDeviceList[idx].isCurrent = true
          // 保证当前设备在第一个
          if (idx !== 0) {
            const temp = this.originalDeviceList[0]
            this.originalDeviceList[0] = this.originalDeviceList[idx]
            this.originalDeviceList[idx] = temp
          }
        }
      }
    },
    // 返回极速入网页面
    returnNetWorkAccess() {
      this.$emit('hdEvent', { type: 'router', val: 'networkAccess' })
    }
  }
}
</script>

<style lang="scss">
.my-device-page {
  padding: 8px 32px;
  .icon-qiangzhixiaxian {
    font-size: 14px;
  }
  .device-title {
  color: $title-color;
  margin-bottom:10px;
  font-weight: 600;
  i {
    font-style: normal;
    color: $--color-primary;
  }
  .go-back{
    float: right;
    margin-right: 16px;
    line-height: 20px;
    font-size: 14px;
    color:$--color-primary;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 400;
  }
}
}
</style>
