<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
export default {
  name: 'CheckIpMacBind',
  components: {
    checkResult,
    howToFix
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {}
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },

    checkResult() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result')
    }
  },
  mounted() {
    this.getFixTip()
  },
  methods: {
    getFixTip() {
      this.fixData = {
        modelTitle: this.$t('accessNetwork.securityCheck.info_19'),
        fixSteps: [
          {
            question: this.$t('check.IpMacBind.h_3_rd'),
            wayList: [
              this.$t('check.IpMacBind.h_5_rs'),
              this.$t('check.IpMacBind.h_7_rs'),
              this.$t('check.IpMacBind.h_9_rs'),
              this.$t('check.IpMacBind.h_11_rs'),
              this.$t('check.IpMacBind.h_21_rs')
            ]
          },
          {
            question: this.$t('check.IpMacBind.h_15_rd'),
            wayList: [
              this.$t('check.IpMacBind.h_17_rs'),
              this.$t('check.IpMacBind.h_19_rs'),
              this.$t('check.IpMacBind.h_21_rs')
            ]
          }
        ]
      }
    }
  }
}
</script>
