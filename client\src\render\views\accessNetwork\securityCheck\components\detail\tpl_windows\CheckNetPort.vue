<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.NetPort.h_3_rs") }}
          </p>
          <div v-for="(item, index) in portInfo" :key="item.ProcessId" class="pc-info">
            <img :src="imgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.NetPort.h_5_rd") }}
                  <span>{{ item.ProcessName }}</span>
                </div>
                <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.NetPort.js_3_d") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.NetPort.h_7_rd") }}
                <span>{{ item.ProtocolType }}</span>
              </div>
              <div class="optional-item margin-style ">
                {{ $t("check.NetPort.h_9_rd") }}
                <span>{{ item.ExePath }} </span>
              </div>
              <div class="optional-item margin-style ">
                {{ $t("check.NetPort.h_11_rd") }}
                <span>{{ item.WorkingSetSize }} K</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.NetPort.h_13_rd") }}
                <span>{{ item.StartTime }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.NetPort.h_15_rd") }}
                <span>{{ item.LocalPort }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
export default {
  name: 'CheckNetPort',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      portInfo: [],
      imgSrc: require('@/render/assets/CheckNetPort.gif')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getPortInfo()
  },
  methods: {
    getPortInfo() {
      let portCheckInfo = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!portCheckInfo) {
        return
      }
      if (!_.isArray(portCheckInfo)) {
        portCheckInfo = [portCheckInfo]
      }
      portCheckInfo.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.portInfo = portCheckInfo
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          ProcessId: item.ProcessId
        },
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.NetPort.js_3_d')
      })
      this.$set(this.portInfo, index, item)
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
