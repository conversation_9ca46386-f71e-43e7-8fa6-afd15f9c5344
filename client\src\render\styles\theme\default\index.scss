//主题的色彩变量值由Js的“css-vars-ponyfill”控制(兼容xp下的webkit内核的safari3.11)
/**最主要的组成皮肤的部分**/

$--color-primary: #536CE6;
// 浅色色系主题
$--color-primary-light-1: #647be9;
$--color-primary-light-2: #7589eb;
$--color-primary-light-3: #8798ee;
$--color-primary-light-4: #98a7f0;
$--color-primary-light-5: #a9b6f3;
$--color-primary-light-6: #bac4f5;
$--color-primary-light-7: #cbd3f8;
$--color-primary-light-8: #dde2fa;
$--color-primary-light-9: #eef0fd;

// 暗色色系主题
$--color-primary-dark-1:#4b61cf;
$--color-primary-dark-2:#4256b8;
$--color-primary-dark-3:#3a4ca1;
$--color-primary-dark-4:#32418a;
$--color-primary-dark-5:#2a3673;
$--color-primary-dark-6:#212b5c;
$--color-primary-dark-7:#192045;
$--color-primary-dark-8:#11162e;
$--color-primary-dark-9:#080b17;
// 半透明主题色调
$--color-primary-light-transparent-5: rgba(80, 145, 230, 0.5);

//这种var变量的办法不支持qtWebkit的内核，后期需要打包两套css
// $--color-primary:var(--color-primary);
// $--color-primary-light-1: var(--color-primary-light-1) !default; 
// $--color-primary-light-2: var(--color-primary-light-2) !default; 
// $--color-primary-light-3: var(--color-primary-light-3) !default; 
// $--color-primary-light-4: var(--color-primary-light-4) !default; 
// $--color-primary-light-5: var(--color-primary-light-5) !default; 
// $--color-primary-light-6: var(--color-primary-light-6) !default; 
// $--color-primary-light-7: var(--color-primary-light-7) !default; 
// $--color-primary-light-8: var(--color-primary-light-8) !default; 
// $--color-primary-light-9: var(--color-primary-light-9) !default; 
// $--color-primary-dark-1: var(--color-primary-dark-1) !default; 
// $--color-primary-dark-2: var(--color-primary-dark-2) !default; 
// $--color-primary-dark-3: var(--color-primary-dark-3) !default; 
// $--color-primary-dark-4: var(--color-primary-dark-4) !default; 
// $--color-primary-dark-5: var(--color-primary-dark-5) !default; 
// $--color-primary-dark-6: var(--color-primary-dark-6) !default; 
// $--color-primary-dark-7: var(--color-primary-dark-7) !default; 
// $--color-primary-dark-8: var(--color-primary-dark-8) !default; 
// $--color-primary-dark-9: var(--color-primary-dark-9) !default; 
//
// $--color-primary-light-transparent-5: var(--color-primary-light-transparent-5) !default; 
/**最主要的组成皮肤的部分**/


$progress-color-1:#72cbff; // 进度条渐变色
$progress-color-2:#546ff8;
$progress-color-3: #8095FF;


$--color-white:#fff;
$list-hover-bg:#F2F4FD ;//用于下拉菜单和列表中，鼠标移入时的背景色
$error: #E65353;
$error-1:#FF4D4D;
$error-2:#FFF5F5;
$red: #EE6666; 
$waring:#F2A918;
$yellow: #F9C858;
$yellow-2: #FDF6E7;
$yellow-1: #FFBF00;
$yellow-3: #ffeebc;
$yellow-4: #fffaeb;
$success:#29CC88;
$green: #90CC76;
$title-color:#3C404D; //用于标题、重要的文本信息（如：输入框中填入的文本、列表文本、已安检的安检项文本、安检状态文本）
$default-color:#686E84; //用于默认的文本信息（如：左侧菜单默认状态、缺省页提示语、列表的表头文本）、次要的文本信息、默认的图标颜色（如：菜单中的图标）
$disabled-color:#B3B6C1; //用于默认的文本信息（如：输入框的提示文本）、不可点击的文本信息（如：未安检的安检项文本）、默认的图标颜色、需谨慎操作的图标颜色（如：列表中的删除按钮）
$scroll-color:#D8D9DD; // 滚动条颜色
$line-color:#EDEDF1;//用于输入框、分割线、不可点击按钮、左侧菜单鼠标移入背景色
$default-bg:#F0F0F3; //用于列表表头背景色
$menu-bg:#F5F6F8;
$row-bg:#FCFCFD;
$icon-color:#BAC4F5;
$light-color:#fff;
$light-color2: #F5F9FF;
$check-bg: #FBFBFC; // 安检头部背景色
$gray-1: #f6f6f7;
$gray-2: #e2e2e6;
$gray-3: #f6f6f8;
$gray-4: #A6A8B3;
$gray-5: #f6f6fa;
$gray-6: #F1F1F2;
$gray-7: #D9DAE0;
$blue-1: #EEF2FE;
$blue-2: #5B86FA;
$blue-3: #198CFF;
$green-1: #F3F9F1;
$green-2: #7DBF60;
$green-3: #29CC65;
$green-4: #6CA653;
$green-5: #C6F1D6;
$green-6: #EEFBF3;
$green-7: #ebf7e9;

