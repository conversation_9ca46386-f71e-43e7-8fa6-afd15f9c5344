<template>
  <div class="empty-wrapper">
    <img :style="{width: imgWidth}" :src="emptyImg" alt="">
    <slot><p id="ui-ztp-empty-p-text">{{ msg || $t('sourcePlatform.notSource') }}</p></slot>
  </div>
</template>
<script>
const emptyImg = require('@/render/assets/stateIllustration/empty.png')
export default {
  props: {
    msg: {
      type: String,
      default: ''
    },
    imgWidth: {
      type: String,
      default: '180px'
    }
  },
  data() {
    return {
      emptyImg
    }
  }
}
</script>
<style lang="scss" scoped>
.empty-wrapper{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{

        margin-bottom: 16px;
    }
    p{
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
        color: $default-color;
    }
}
</style>
