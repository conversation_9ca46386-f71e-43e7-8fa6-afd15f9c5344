export default [
  {
    path: '/remoteAssistance',
    name: 'remoteAssistance',
    component: () => import('@/render/views/commonTools/remoteAssistance'),
    platform: ['windows', 'linux'],
    meta: {
      code: '201',
      menu: {
        name: 'nav.assistance',
        icon: 'icon-yuancheng<PERSON>ezhu',
        moduleName: 'nav.commonTools',
        uiId: 'ui-menu-remoteAssistance-li-remote_assist'
      }
    }
  },
  {
    path: '/system',
    name: 'stystem',
    component: () => import('@/render/views/commonTools/system'),
    platform: ['windows', 'linux', 'mac'],
    meta: {
      code: '203',
      menu: {
        name: 'nav.system',
        icon: 'icon-xitongxinxi',
        moduleName: 'nav.commonTools',
        uiId: 'ui-menu-stystem-li-stystem'
      }
    }
  },
  {
    path: '/diyMenu',
    name: 'diyMenu',
    component: () => import('@/render/views/commonTools/diyMenu'),
    platform: ['windows', 'linux', 'mac'],
    meta: {
      code: '204',
      menu: {
        name: 'nav.diyMenu',
        icon: 'icon-disanfangyingyong',
        moduleName: 'nav.commonTools',
        uiId: 'ui-menu-stystem-li-diyMenu'
      }
    }
  }

]
