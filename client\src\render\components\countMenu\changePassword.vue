<template>
  <div class="change-password-page">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="0px">
      <el-form-item label="" prop="oldpassword">
        <el-input
          id="ui-accessNetwork-changePassword-input-old_passwd"
          v-model="ruleForm.oldpassword"
          type="password"
          maxlength="50"
          :show-password="true"
          :placeholder="$t('changepassword.currentpassword')"
        >
          <i slot="prefix" class="iconfont icon-mima" />
        </el-input>
      </el-form-item>
      <el-form-item label="" prop="newpassword">
        <el-input
          id="ui-accessNetwork-changePassword-input-new_passwd"
          v-model="ruleForm.newpassword"
          type="password"
          :show-password="true"
          maxlength="50"
          :placeholder="$t('changepassword.newpassword')"
        >
          <i slot="prefix" class="iconfont icon-mima" />
        </el-input>
      </el-form-item>
      <el-form-item label="" prop="newpassword2">
        <el-input
          id="ui-accessNetwork-changePassword-input-new_passwd_2"
          v-model="ruleForm.newpassword2"
          type="password"
          :show-password="true"
          maxlength="50"
          :placeholder="$t('changepassword.confirmpassword')"
        >
          <i slot="prefix" class="iconfont icon-mima" />
        </el-input>
      </el-form-item>
      <el-form-item>
        <p id="ui-accessNetwork-changePassword-p-alter_submit" class="public-medium-btn" @click="submitForm('ruleForm')">{{ $t('changepassword.submitchange') }}</p>
      </el-form-item>
    </el-form>
    <!-- 吐司 -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'
import JSEncrypt from 'jsencrypt'
import authTypes from '@/render/utils/auth/authTypes'
export default {
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    const validateCurrentPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changepassword.currentpassword')))
      } else {
        if (!regular.lenRange(value, 2, 50) || regular.hasChars(value, [',', '"']) || regular.flEmpty(value)) {
          callback(new Error(this.$t('changepassword.defaultErrTip')))
        }
        callback()
      }
    }
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changepassword.newpassword')))
      } else {
        if (this.ruleForm.newpassword2 !== '') {
          this.$refs.ruleForm.validateField('newpassword2')
        }
        const { ChangePassword } = this.computeServeEntiretyConfig
        const { PassWordType, PassWordsetType, PassWordLength, errPassword, dbUserName, dbStr } = ChangePassword
        const errChar = errPassword && errPassword.split('||||')
        switch (PassWordType) {
        // 默认模式
          case '0':
            if (!regular.lenRange(value, 2, 50) || regular.hasChars(value, [',', '"']) || regular.flEmpty(value)) {
              callback(new Error(this.$t('changepassword.defaultErrTip')))
            }
            break
          // 复杂密码模式
          case '1':
          // 长度校验
            if (value.length < PassWordLength) {
              callback(new Error(this.$t('changepassword.lengthTip', { PassWordLength })))
            }
            // 不包含完整用户名
            if (dbUserName === '1' && this.username) {
              if (regular.hasChars(value, [this.username])) {
                callback(new Error(this.$t('changepassword.excludeNameTip')))
              }
            }
            // 不使用重复的数字和字母
            if (dbStr === '1') {
              if (regular.haveRepeat(value)) {
                callback(new Error(this.$t('changepassword.norepeateTip')))
              }
            }
            // 不可等于设置字符
            if (errChar && errChar.length) {
              if (errChar.indexOf(value) > -1) {
                callback(new Error(this.$t('changepassword.noeqTip', { value })))
              }
            }
            // 密码字符组成种类
            switch (PassWordsetType) {
            // 数字+字母
              case '1':
                if (!regular.numAndChart(value)) {
                  callback(new Error(this.$t('changepassword.numAndChat')))
                }
                break
              case '2':
              // 必须由大写字母、小写字母、数字、特殊字符中三者组合
                if (!regular.threeTypePass(value)) {
                  callback(new Error(this.$t('changepassword.complexPassTip')))
                }
            }
        }
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changepassword.confirmpassword')))
      } else {
        if (value !== this.ruleForm.newpassword && this.ruleForm.newpassword) {
          callback(new Error(this.$t('changepassword.comfirPassErrTip')))
        }
        callback()
      }
    }
    return {
      ruleForm: {
        oldpassword: '',
        newpassword: '',
        newpassword2: ''
      },
      rules: {
        oldpassword: [
          { validator: validateCurrentPassword, trigger: 'blur' }
        ],
        newpassword: [
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        newpassword2: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'computeServeEntiretyConfig', 'authInfo', 'clientInfo']),
    // 账户名
    username() {
      const username = _.get(this.drawerData, 'username', '')
      return username
    }
  },
  mounted() {},
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          const data = {
            user: this.username,
            type: authTypes.User
          }
          if (this.loading) {
            return
          }
          this.loading = true
          const crypt = new JSEncrypt()
          crypt.setPublicKey(this.computeServeEntiretyConfig.pubKey)
          for (var k in this.ruleForm) {
            data[k] = crypt.encrypt(this.ruleForm[k])
          }
          try {
            const result = await proxyApi.changePass(data)
            this.loading = false
            if (result.errcode === '0') {
              this.$message.success(result.errmsg)
              this.$emit('changeVisible', false)
            }
          } catch (error) {
            console.log(error)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.change-password-page{
  padding: 8px 32px 32px 32px;
}
</style>
