import Vue from 'vue'
import Tip from './index.vue'
import { i18n } from '@/render/lang'

const TipConstuctructor = Vue.extend(Tip)

export function dialogTip(options = {}) {
  const defaultOption = {
    cancelText: i18n.t('dialogFoot.no'),
    confirmText: i18n.t('dialogFoot.yes'),
    title: i18n.t('tips')
  }
  const Tipcom = new TipConstuctructor({ propsData: { ...defaultOption, ...options }}).$mount()
  document.body.appendChild(Tipcom.$el)
}
