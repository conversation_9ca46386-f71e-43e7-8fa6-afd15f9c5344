/*
 * @Author: <EMAIL>
 * @Date: 2022-03-11 10:02:57
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-11 17:41:30
 * @Description: file content
 */
class Storage {
  /**
   * 设置缓存
   * @param {string} name
   * @param {any} value
   * @param {number} expires 这里的单位是秒(考虑实用性，没有使用毫秒)
   * @param {number} startTime
   * @return {void} null
   */
  setItem(name, value, expires) {
    expires = expires * 1000
    const obj = {
      name,
      value,
      expires,
      startTime: new Date().getTime()
    }
    const options = {}
    // 将obj和传进来的params合并
    Object.assign(options, obj)
    if (options.expires) {
      // 如果options.expires设置了的话
      // 以options.name为key，options为值放进去
      localStorage.setItem(options.name, JSON.stringify(options))
    } else {
      // 如果value是对象或者数组对象的类型，就先用JSON.stringify转一下，再存进去
      if (Object.prototype.toString.call(options.value) === '[object Object]') {
        options.value = JSON.stringify(options.value)
      }
      if (Object.prototype.toString.call(options.value) === '[object Array]') {
        options.value = JSON.stringify(options.value)
      }
      localStorage.setItem(options.name, options.value)
    }
  }
  /**
   * 返回缓存值
   * @param {string} name
   * @returns {any}
   */
  getItem(name) {
    const original = localStorage.getItem(name)
    // 先将拿到的试着进行json转为对象的形式
    let item
    try {
      item = JSON.parse(original)
    } catch (error) {
      // 如果不行就不是json的字符串，就直接返回
      item = original
    }

    if (_.get(item, 'startTime', false) && _.get(item, 'expires', false)) {
      const date = new Date().getTime()
      if (date - parseInt(item.startTime) > parseInt(item.expires)) {
        // 缓存过期，清除缓存，返回false
        localStorage.removeItem(name)
        return false
      } else {
        return item.value
      }
    } else {
      return item
    }
  }
  /**
   * 移除缓存
   * @param {string} name
   * @return {void}
   */
  removeItem(name) {
    localStorage.removeItem(name)
  }

  /**
   * 清除所有缓存
   * @return {void}
   */
  clear() {
    localStorage.clear()
  }
}

export default new Storage()
