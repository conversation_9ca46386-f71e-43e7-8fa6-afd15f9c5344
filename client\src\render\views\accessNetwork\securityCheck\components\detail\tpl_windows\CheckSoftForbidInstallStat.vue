<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.SoftForbidInstallStat.h_3_rs") }}
          </p>
          <div v-for="(item, index) in forbidInfo" :key="item.SoftName + index" class="pc-info">
            <img :src="forImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.SoftForbidInstallStat.h_5_rd") }}
                  <span>{{ item.SoftName }}</span>
                </div>
                <button v-show="item.SoftUninstall" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.SoftForbidInstallStat.js_3_d") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.SoftForbidInstallStat.h_6_rd") }}
                <span>{{ item.DisplayVersion }}</span>
              </div>
              <div class="optional-item margin-style ">
                {{ $t("check.SoftForbidInstallStat.h_7_rd") }}
                <span>{{ item.SoftInstallPath }} </span>
              </div>
              <div class="optional-item">
                {{ $t("check.SoftForbidInstallStat.h_9_rd") }}
                <span>{{ item.Publisher }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix v-if="deviceType !== 'mac'" :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  name: 'CheckIEActiveX',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {},
      collapseFlag: true,
      forbidInfo: [],
      forImgSrc: require('@/render/assets/forbidSoft.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    },
    deviceType() {
      return _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    }
  },
  mounted() {
    this.initFixTip()
    this.getForbidInfo()
  },
  methods: {
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.SoftForbidInstallStat.h_11_rs'),
        fixSteps: [this.$t('check.SoftForbidInstallStat.h_13_rs'), this.$t('check.SoftForbidInstallStat.h_15_rs')]
      }
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          ApplicationName: '',
          CommandLine: item.SoftUninstall,
          WaitApplicationExit: 'No'
        },
        RepairType: 0,
        CreateProgress: 0
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.SoftForbidInstallStat.js_3_d')
      })
      this.$set(this.forbidInfo, index, item)
    },
    getForbidInfo() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.forbidInfo = list
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
