<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.RubbishFile.h_3_rs") }}
          </p>
          <div class="pc-info">
            <img :src="rbImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.RubbishFile.h_5_rd") }}
                </div>
                <button :class="['btn-small',checkData.hasFixed ? 'btn-disabled': 'public-medium-btn', loading ? 'loading-disable': '']" @click="clearRubbish">
                  <i v-show="loading" class="el-icon-loading" />
                  {{ $t("check.RubbishFile.js_3_d") }}
                </button>
              </div>
              <div class="optional-item rule-desc">
                {{ $t("check.RubbishFile.h_7") }}<span>{{ rubbishInfo.FileNumRule }}</span>{{ $t('check.RubbishFile.h_18') }}<span>{{ rubbishInfo.FileSizeRule }}</span>MB
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckRubbishFile',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {},
      collapseFlag: true,
      rbImgSrc: require('@/render/assets/CheckRubbishFile.gif')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    rubbishInfo() {
      const policy = _.get(this.checkData, 'CheckType.Option')
      const size = policy.FileSizeRule ? policy.FileSizeRule / 1024 : 0
      return {
        FileNumRule: policy.FileNumRule,
        FileSizeRule: size
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.initFixTip()
  },
  methods: {
    clearRubbish() {
      let RubbishType = _.get(this.checkData, 'CheckResult.Info.RubbishType')
      if (RubbishType && !_.isArray(RubbishType)) {
        RubbishType = [RubbishType]
      }
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          RubbishType
        },
        RepairType: 0,
        CreateProgress: 1
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.RubbishFile.js_3_d')
      })
    },
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.RubbishFile.h_9_rs'),
        fixSteps: [this.$t('check.RubbishFile.h_11_rs'),
          this.$t('check.RubbishFile.h_13_rs'),
          this.$t('check.RubbishFile.h_17_rs')
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.rule-desc{
  margin-top: 4px;
}
</style>
