<template>
  <div>
    <checkResult check-name="patch" :result-data="resultData" />
    <div v-if="checkFail" class="stand-detail-modle patch-modle">
      <p class="model-title">
        {{ $t("check.Patch.h_1_rs")
        }}<i
          :class="modelIsOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="modelIsOpen = !modelIsOpen"
        />
      </p>
      <el-collapse-transition>
        <div v-show="modelIsOpen" class="stand-model-content">
          <p class="tit-info">
            {{ $t("check.Patch.h_3_rd") }}<span id="ui-check-patch-p-acount">{{ tableData.length }}</span>{{ $t("check.Patch.h_4_rd") }}
          </p>
          <div v-if="installState === 1" class="install-prccess-wrapper">
            <div class="img-box">
              <img :src="pdImgSrc" alt="">
            </div>
            <div class="proccess-content">
              <div class="count-wrapper">
                {{ $t("check.Patch.h_6_rd") }}<span>{{ total }}</span>{{ $t("check.Patch.h_7_rd") }}<span>{{ current }}</span>{{ $t("check.Patch.h_8_rd") }}
              </div>
              <div class="proccess-wrapper">
                <div id="ui-check-patch-div-progress" class="proccess" :style="{width: totalProccess +'%'}" />
              </div>
              <div id="ui-check-patch-div-curent_patch" class="current-patch text-clamp" :title="`${$t('check.Patch.js_6_s')+currentPath}`">
                {{ $t("check.Patch.js_6_s") + currentPath }}
              </div>
            </div>
          </div>
          <el-table
            v-if="installState !== 2"
            :data="tableData"
            stripe
            max-height="300"
            style="width: 100%"
            class="public-no-boder-table"
            :cell-class-name="setCellClass"
          >
            <el-table-column show-overflow-tooltip prop="Title" fixed width="180" :label="$t('check.Patch.h_18_rd')" />
            <el-table-column
              prop="Level"
              width="120"
              :label="$t('check.Patch.h_14_rd')"
            />
            <el-table-column
              prop="KB"
              width="130"
              :show-overflow-tooltip="true"
              :label="$t('check.Patch.h_16_rd')"
            />
            <el-table-column
              prop="Date"
              width="120"
              :show-overflow-tooltip="true"
              :label="$t('check.Patch.h_20_rd')"
            />
            <el-table-column
              prop="State"
              width="120"
              fixed="right"
              :show-overflow-tooltip="true"
              :label="$t('check.Patch.h_22_rs')"
            />
          </el-table>
          <div v-if="installState !== 2" class="bottom-wrapper">
            <el-checkbox id="ui-check-patch-checkbox-shutdown" v-model="auto_shutdown">{{
              $t("check.Patch.h_26_rd")
            }}</el-checkbox>
            <button id="ui-check-patch-button-install" :class="['public-medium-btn', loading ? 'loading-disable': '']" @click="fixHandle">
              <i v-show="loading" class="el-icon-loading" />
              {{ $t("check.Patch.js_3_d") }}
            </button>
          </div>
          <div v-if="installState === 2" class="success-panel">
            <div class="success-wrapper">
              <div class="img-box">
                <img :src="scImgSrc" alt="">
              </div>
              <div class="success-tip-wrapper">
                {{ $t("check.Patch.h_12_rd") }}
              </div>
            </div>
            <button id="ui-check-patch-button-shutdownHandle" class="btn-small public-medium-btn" @click="shutdownHandle">
              {{ $t("check.Patch.js_7_s") }}
            </button>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <ConfirmDialog :show.sync="showConfirm" :msg="confirmMsg" @ok="closeComputer" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
import agentApi from '@/service/api/agentApi'
import { mapGetters } from 'vuex'

export default {
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    progressInfo: {
      type: String,
      default: ''
    },
    canClose: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      modelIsOpen: true,
      tabHeight: '240',
      tableData: [],
      auto_shutdown: false,
      totalProccess: 0,
      total: 0,
      current: 0,
      currentPath: '',
      installState: 0, // 0 初始状态 1正在安装 2安装成功
      showConfirm: false,
      confirmMsg: '',
      scImgSrc: require('@/render/assets/PatchSuccess.png'),
      pdImgSrc: require('@/render/assets/PatchDown.png')
    }
  },
  computed: {
    ...mapGetters(['clientInfo']),
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  watch: {
    // 进度条
    progressInfo(newVal, oldVal) {
      if (newVal) {
        console.log('newval', newVal)
        const ASM = JSON.parse(newVal)
        if (ASM.Title) {
          this.total = ASM.total
          this.current = ASM.current
          const obj = this.tableData.find(item => item.KB === ASM.Title)
          if (obj) {
            this.currentPath = obj.Title
            if (ASM.Progress.indexOf('Rate') > -1) {
              const resArr = ASM.Progress.split('|')
              let rate = resArr[resArr.length - 1]
              rate = rate.split(':')[1]
              if (rate && parseFloat(rate) > 0) {
                const itemProccess = (1 / ASM.total) * 100
                const totalProccess = itemProccess * (rate / 100) * 0.96 + (ASM.current - 1) * itemProccess
                this.totalProccess = totalProccess
              }
              obj.State = this.$t('check.Patch.h_10_rd')
            } else {
              obj.State = ASM.Progress
              if (this.completeItem(ASM.Progress) && ASM.total && ASM.current) {
                this.totalProccess = (ASM.current / ASM.total) * 100
                if (ASM.total === ASM.current) {
                  this.installState = 2
                  this.totalProccess = 100
                  this.$emit('update:canClose', true)
                  if (this.auto_shutdown) {
                    this.closeComputer()
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  mounted() {
    this.getPatchList()
  },
  methods: {
    async getPatchList() {
      const res = await agentApi.getPatchRepairDetailInfo()
      let list = _.get(res, 'ASM.UpdateList.Update', [])
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        item.Date = item.Date.split(' ')[0]
        item.State = this.$t('check.Patch.h_24_rd')
        item.TitleDetailHtml = ''
      })
      this.tableData = list
    },
    // 详情拼接
    setPatchDetail(row, data) {
      const title = `<span class="patch-detail-title">${this.$t('check.Patch.js_13_s')}</span><br>`
      const pachTitle = `<span class="patch-detail-content">${this.$t('check.Patch.js_14_s') + data.Title}</span><br>`
      const pathNum = `<span class="patch-detail-content">${this.$t('check.Patch.js_15_s') + data.KBIDS}</span><br>`
      const pathLevel = `<span class="patch-detail-content">${this.$t('check.Patch.js_16_s') + data.UpdateType}</span><br>`
      const pathDate = `<span class="patch-detail-content">${this.$t('check.Patch.js_17_s') + data.UpdateTime}</span><br>`
      const pathDesc = `<span class="patch-detail-content">${this.$t('check.Patch.js_18_s') + data.Description}</span>`
      row.TitleDetailHtml = title + pachTitle + pathNum + pathLevel + pathDate + pathDesc
    },
    // 设置单元格样式
    setCellClass(row) {
      if (row.columnIndex === 1) {
        if (row.row.Level === this.$t('check.Patch.js_1_d')) {
          return 'error-color'
        } else if (row.row.Level === this.$t('check.Patch.js_2_d')) {
          return 'waring-color'
        }
      } else if (row.columnIndex === 4) {
        if (row.row.State === this.$t('check.Patch.h_24_rd') || row.row.State === this.$t('check.Patch.js_5_s')) {
          return 'error-color'
        } else if (row.row.Level === this.$t('check.Patch.js_8_s')) {
          return 'success-color'
        }
      }
    },
    completeItem(proccess) {
      const completeArr = ['成功', 'Successful', '失败', 'failed']
      for (let i = 0; i < completeArr.length; i++) {
        if (proccess.indexOf(completeArr[i]) > -1) {
          return true
        }
      }
      return false
    },
    // 确认关机弹框
    shutdownHandle() {
      this.confirmMsg = this.$t('check.Patch.js_11_s')
      this.showConfirm = true
    },
    // 关机
    async closeComputer() {
      const ip = _.get(this.clientInfo, 'basic.ServerIP', '')
      if (!ip) {
        this.$message({
          message: '获取不到ip',
          type: 'error'
        })
        return
      }
      const path = 'https://' + ip + '/download/shutdown.exe'
      const param = {
        WhereIsModule: path,
        WhatFuncToCall: '',
        WhatToDo: 'RunOneExe',
        RequestParam: '-r -f -t 60 -c "' + this.$t('check.Patch.js_12_s') + '"'
      }
      console.log('关机', param)
      const res = agentApi.callAgentOneFunc(param)
      console.log(res)
    },
    // 开始安装
    async fixHandle() {
      if (this.installState !== 0) {
        return
      }
      this.$emit('update:canClose', false)
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          auto_shutdown: this.auto_shutdown
        },
        RepairType: 0,
        CreateProgress: 1
      }
      this.installState = 1
      const ret = await this.submitHandle({
        params,
        CheckItem: this.checkData,
        showTip: false
      })
      const errcode = _.get(ret, 'ASM.errcode')
      const errmsg = _.get(ret, 'ASM.errmsg')
      if (errcode === '0' && this.totalProccess > 96) {
        this.$message({
          message: this.$t('check.Patch.js_8_s') + this.$t('check.success'),
          type: 'success'
        })
      } else {
        this.$message({
          message: errmsg || this.$t('check.Patch.js_8_s') + this.$t('check.fail'),
          type: 'error'
        })
        this.initData()
      }
    },
    initData() {
      this.totalProccess = 0
      this.total = 0
      this.current = 0
      this.currentPath = ''
      this.installState = 0 // 0 初始状态 1正在安装 2安装成功
    }
  }
}
</script>
<style lang="scss">
.path-table-popper {
  max-width: 400px !important;
}
.patch-modle {
  .tit-info {
    color: $title-color;
    font-size: 14px;
    margin-bottom: 16px;
    padding-left: 16px;
  }
  .stand-model-content {
    .error-color .cell {
      color: $error;
    }
    .waring-color .cell {
      color: $waring;
    }
    .success-color .cell {
      color: $success;
    }
  }
  .install-prccess-wrapper {
    border: 1px solid $line-color;
    padding: 11px 20px 11px 80px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    position: relative;
    .img-box {
      position: absolute;
      left: 18px;
      top: 20px;
      width: 50px;
      height: 47px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .proccess-content {
      .proccess-wrapper {
        border: 1px solid $line-color;
        height: 16px;
        width: 393px;
        margin: 6px 0;
        position: relative;
        .proccess {
          position: absolute;
          top: -1px;
          left: 0;
          height: 16px;
          background: linear-gradient(132deg, #72cbff 10%, #546ff8 82%);
          transition: width .1s;
        }
      }
      .current-patch{
        width: 393px;
      }
      & > div {
        font-size: 12px;
        color: $default-color;
        line-height: 17px;
        span {
          color: $--color-primary;
        }
      }
    }
  }
  .success-panel {
      overflow: hidden;
      padding-bottom: 16px;
    .success-wrapper {
      border: 1px solid $line-color;
      padding: 20px;
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 20px;
      color: $default-color;
      margin-bottom: 16px;
      .img-box {
        padding-top: 10px;
        width: 44px;
        height: 54px;
        margin-right: 17px;
        flex-shrink: 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .public-medium-btn{
        float: right;

    }
  }

  .bottom-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
  }
  .public-medium-btn{
        width: auto;
        padding: 0 16px;
    }
}
.patch-detail-title{
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 8px;
  }
  .patch-detail-content{
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 4px;
  }
</style>

