<template>
  <div v-loading="loading" class="sys-info">{{ SystemInfo }}</div>
</template>
<script>
import agentApi from '@/service/api/agentApi'
export default {
  data() {
    return {
      SystemInfo: '',
      loading: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      if (sessionStorage.SystemInfo) {
        this.SystemInfo = sessionStorage.SystemInfo
        return
      }
      this.loading = true
      const ret = await agentApi.getSystemInfo()
      this.loading = false
      console.log(ret)
      const SystemInfo = _.get(ret, 'ASM.SystemInfo')
      if (!SystemInfo) {
        this.$msg({ type: 'error', message: this.$t('interfaceErr') })
        return
      }
      this.SystemInfo = SystemInfo
      sessionStorage.SystemInfo = SystemInfo
    }
  }
}
</script>
<style lang="scss" scoped>
  .sys-info{
    color: $title-color;
    line-height: 24px;
    height: 100%;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: "宋体";
  }
</style>
