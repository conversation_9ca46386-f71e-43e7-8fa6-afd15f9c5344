<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.SystemServices.h_3_rs") }}
          </p>
          <div v-for="(item, index) in list" :key="item.ServerName" class="pc-info">
            <img :src="sysImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.SystemServices.h_5_rd") }}
                  <span>{{ item.ServerName }}</span>
                </div>
                <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.SystemServices.js_9_d") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.SystemServices.h_7_rd") }}
                <span>{{ item._ServerState }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.SystemServices.h_9_rd") }}
                <span>{{ item.ServerPath }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.SystemServices.h_11_rd") }}
                {{ item.ServerDesc }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckCustom',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      list: [],
      sysImgSrc: require('@/render/assets/CheckSystemServices.gif')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      let info = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!info) {
        return
      }
      if (!_.isArray(info)) {
        info = [info]
      }
      info.forEach(item => {
        let _ServerState = ''
        if (item.ServerState === 'Running') {
          _ServerState = this.$t('check.SystemServices.js_3_s')
        } else if (item.ServerState === 'Stoped') {
          _ServerState = this.$t('check.SystemServices.js_5_s')
        } else {
          _ServerState = this.$t('check.SystemServices.js_7_s')
        }
        item._ServerState = _ServerState
        item.hasFixed = this.checkData.hasFixed
      })
      this.list = info
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          server_name: item.ServerName
        },
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.SystemServices.js_9_d')
      })
      this.$set(this.list, index, item)
    }
  }
}
</script>
