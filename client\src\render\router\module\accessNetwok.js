export default [
  // 欢迎页
  {
    path: '/access/message',
    name: 'message',
    component: () => import('@/render/views/accessNetwork/message'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '101',
      menu: {
        name: 'nav.status',
        icon: 'icon-ruwangzhuangtai', // 侧边栏显示图标
        moduleName: 'nav.commonFun', // 所属模块名称
        uiId: 'ui-menu-message-li-staff_auth' // UI自动化Id
      }
    }
  },
  // 待审核页面
  {
    path: '/access/audit',
    name: 'audit',
    component: () => import('@/render/views/accessNetwork/message/audit'),
    platform: ['windows', 'mac', 'linux']
  },
  // 已入网页面
  {
    path: '/access/submit',
    name: 'submit',
    component: () => import('@/render/views/accessNetwork/message/submit'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1012'
    }
  },

  // 员工认证
  {
    path: '/access/auth',
    component: () => import('@/render/views/accessNetwork/auth'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1013'
    }
  },
  // 来宾认证
  {
    path: '/access/guestAuth',
    name: 'guestAuth',
    component: () => import('@/render/views/accessNetwork/guestAuth'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1016'
    }
  },
  // 来宾注册
  {
    path: '/access/guestRegister',
    name: 'guestRegister',
    component: () => import('@/render/views/accessNetwork/guestAuth/guestRegister'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1017'
    }
  },
  // 来宾注册
  {
    path: '/access/guestAudit',
    name: 'guestRegister',
    component: () => import('@/render/views/accessNetwork/guestAuth/guestApplyAudit/index.vue'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1018'
    }
  },
  // 来宾接待页面。和【来宾认证】只有name不同
  {
    path: '/access/guestReceive',
    name: 'guestReceive',
    component: () => import('@/render/views/accessNetwork/guestAuth'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '103',
      menu: {
        access: true,
        name: 'nav.guestReceive',
        icon: 'icon-laibinrenzheng',
        moduleName: 'nav.commonFun',
        uiId: 'ui-menu-guestReceive-li-guest_receive'
      }
    }
  },
  // 来宾认证-自助申请-审核状态页面
  {
    path: '/access/guestAuthState',
    name: 'guestAuthState',
    component: () => import('@/render/views/accessNetwork/guestAuth/guestApplyAudit'),
    platform: ['windows', 'mac', 'linux']
  },
  // 立即注册账号
  {
    path: '/access/regAccount',
    component: () => import('@/render/views/accessNetwork/register/account'),
    platform: ['windows', 'mac', 'linux']
  },

  // 注册页面
  {
    path: '/access/register',
    component: () => import('@/render/views/accessNetwork/register'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1014'
    }
  },
  // 安检页面
  {
    path: '/access/check',
    component: () => import('@/render/views/accessNetwork/securityCheck'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '1015'
    }
  },
  // 安全空间
  {
    path: '/message/secuitybox',
    component: () => import('@/render/views/accessNetwork/message/secuitybox'),
    platform: ['windows'],
    meta: {
      code: '1020',
      menu: {
        name: 'nav.security',
        icon: 'icon-anquankongjian', // 侧边栏显示图标
        moduleName: 'nav.commonFun' // 所属模块名称
      }
    }
  },
  // 提示升级
  {
    path: '/access/updateClient',
    component: () => import('@/render/views/other/updateClient'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '10111'
    }
  }
]
