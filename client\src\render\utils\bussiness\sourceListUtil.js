
import proxyApi from '@/service/api/proxyApi'
import store from '@/render/store'
import { i18n } from '@/render/lang'
import { Message } from 'element-ui'
import { Base64Encode } from '@/render/utils/global'
import agentApi from '@/service/api/agentApi'
import os_browser_info from '@/render/utils/os_browser_info'

const sourceListUtil = {
  // 账号认证具体类型名称-接口传值字典
  accountAuthTypeDic: {
    User: 'Localhost',
    AdDomain: 'AdDomain',
    LDAP: 'LDAP',
    WebAuth: 'Other',
    Email: 'Email',
    Radius: 'Radius'
  },
  async getSourceInfo(_params = {}, config = {}) {
    const deviceid = _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store, 'state.clientInfo.basic.AgentID', 0)
    const params = {
      deviceid,
      listType: 0
    }
    const ret = await proxyApi.getZtpResource({ ...params, ..._params }, config)
    let list = []
    const collectIds = []
    const errorCode = parseInt(_.get(ret, 'errcode'))
    if (errorCode === 0) {
      const {
        historyResLists,
        collectLists,
        returnLists,
        applyLists
      } = ret.data
      console.log('结果', ret.data)
      if (collectLists && collectLists.length) {
        const filterCustom = sourceListUtil.customAppFilterPlatform(collectLists) // 自定义应用过滤
        filterCustom.forEach((item) => {
          item.collect = true
          collectIds.push(item.ResID)
        })
        if (filterCustom.length) {
          list.push({
            GroupName: i18n.t('sourcePlatform.collect'),
            Data: filterCustom,
            GroupResId: 'spCollect'
          })
        }
      }
      if (historyResLists && historyResLists.length) {
        const filterCustom = sourceListUtil.customAppFilterPlatform(historyResLists) // 自定义应用过滤
        if (filterCustom.length) {
          sourceListUtil.setCollect(filterCustom, collectIds)
          list.push({
            GroupName: i18n.t('sourcePlatform.recently'),
            Data: filterCustom,
            GroupResId: 'spHistory'
          })
        }
      }

      if (returnLists) {
        const customGroup = []
        returnLists.forEach(item => {
          if (item.Data && item.Data.length) {
            item.Data = sourceListUtil.customAppFilterPlatform(item.Data) // 自定义应用过滤
            sourceListUtil.setCollect(item.Data, collectIds) // 添加收藏标记
            if (item.Data.length) {
              sourceListUtil.addIpSourceTag(item.Data) // 添加ip资源标记
              item.GroupResId = 'sp' + item.GroupResId
              customGroup.push(item)
            }
          }
        })
        list = [...list, ...customGroup]
      }
      const resApplyList = applyLists && applyLists.resApplyList
      if (resApplyList && resApplyList.length) {
        const filterCustom = sourceListUtil.customAppFilterPlatform(resApplyList) // 自定义应用过滤
        if (filterCustom.length) {
          sourceListUtil.addIpSourceTag(filterCustom) // 添加ip资源标记
          list.push({
            GroupName: i18n.t('msgCenter.appRes'),
            Data: filterCustom,
            GroupResId: 'applyRes'
          })
        }
      }
    } else {
      const retIsStr = typeof (ret) === 'string'
      Message({
        message: retIsStr ? i18n.t('netError') : _.get(ret, 'errmsg'),
        type: 'error'
      })
      if (retIsStr) {
        return { errcode: 404 }
      }
      return ret
    }
    ret.list = list
    return ret
  },
  // ip资源增加标记
  addIpSourceTag(list) {
    if (_.isEmpty(list)) {
      return list
    }
    list.forEach((item) => {
      if (parseInt(item.ResType) === 1) {
        item.isIP = true
      }
    })
  },
  // 自定义应用按平台过滤，如windows客户端只展示适用windows平台的资源
  customAppFilterPlatform(list) {
    if (_.isEmpty(list)) {
      return list
    }
    const deviceTypeMap = {
      'windows': '1',
      'linux': '2',
      'mac': '3'
    }
    const currentPlatformVal = deviceTypeMap[os_browser_info.os_type]
    const filterList = list.filter(item => {
      if (parseInt(item.AccessTypeID) !== 312) {
        return true
      }
      return item.accessMode === 'browser' || !item.DeviceType || item.DeviceType.indexOf(currentPlatformVal) > -1
    })
    return filterList
  },
  setCollect(arr, collectIds) {
    if (arr.length === 0 || collectIds.length === 0) {
      return
    }
    arr.forEach(item => {
      if (collectIds.indexOf(item.ResID) > -1) {
        item.collect = true
      } else {
        item.collect = false
      }
    })
  },
  // 收藏、取消收藏
  async collectReq(source) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: source.ResID
    }
    const isCollect = source.collect
    let ret
    if (isCollect) {
      ret = await proxyApi.cancelCollect(params)
    } else {
      ret = await proxyApi.sourceCollect(params)
    }
    if (parseInt(ret.errcode) === 0) {
      const msg = isCollect ? i18n.t('sourcePlatform.cancelCollect') : i18n.t('sourcePlatform.collect')
      Message({
        message: msg + i18n.t('sourcePlatform.success'),
        type: 'success'
      })
      return true
    }
    return false
  },
  // 资源编辑获取账号密码
  async getAtpAccount(sourceData) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: sourceData.ResID
    }
    const ret = await proxyApi.getAtpAccount(params)
    return ret
  },
  async saveAtpAccount(sourceData) {
    const apiParam = {
      username: Base64Encode(sourceData.username),
      password: Base64Encode(sourceData.password),
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: sourceData.ResID
    }
    const res = await proxyApi.saveAtpAccount(apiParam)
    return res
  },
  async checkSource(sourceData) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: sourceData.ResID
    }
    const ret = await proxyApi.checkSource(params)
    return ret
  },
  async getAccessUrl(id) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: id
    }
    const ret = await proxyApi.getAccessUrl(params)
    return ret
  },
  async recodLog(id) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: id
    }
    const ret = await proxyApi.sourceRecordLog(params)
    return ret
  },
  async policyCheck(id) {
    const params = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID'),
      resId: id
    }
    const ret = await proxyApi.policyCheck(params)
    if (parseInt(ret.errcode) === 0) {
      return true
    }
    return false
  },
  // 获取网络数据
  async getDivideList() {
    const ret = await proxyApi.ztpDivideList()
    if (parseInt(ret.errcode) === 0) {
      const { List, CurrDivideID, UrlPrefix } = _.get(ret, 'data', {})
      return { list: List, curId: CurrDivideID, UrlPrefix }
    } else {
      return {}
    }
  },
  // 发送网络数据给客户端
  async setNotifyResGroup(diviInfo) {
    if (!diviInfo || !_.isObject(diviInfo)) {
      diviInfo = await sourceListUtil.getDivideList()
    }
    const { list, curId, UrlPrefix } = diviInfo
    if (!_.isArray(list) || !curId) {
      console.debug('notifyResGroup发送数据失败')
      return
    }
    const ret = await agentApi.notifyResGroup({ CurGroupID: curId, GroupInfos: list, UrlPrefix })
    return ret
  },
  // 客户端推送切换
  async clientChangeDivide(id, isSourcePage) {
    const ret = await proxyApi.ztpDivideChange({ divideID: id, deviceid: _.get(store.state.clientInfo, 'detail.DeviceID') }, true)
    if (!isSourcePage) { // 在资源页面时让资源页面处理
      sourceListUtil.setNotifyResGroup()
    }
    return parseInt(_.get(ret, 'errcode')) === 0
  },
  // 资源自定义配置存到小助手文件
  /* 数据结构
  {
    key: val
  }
    暂时只存了自定义应用数据
    {
      customConf: {'resId-userid': path}
    }
  */
  async saveResConfToClientLocal(key, value) {
    let conf = await sourceListUtil.readResConfFromClientLocal()
    if (typeof conf !== 'object') {
      conf = {}
    }
    if (!conf[key]) {
      conf[key] = {}
    }
    conf[key] = { ...conf[key], ...value }
    await agentApi.fileTools.ActionLocalFile('webResourceConfigTmp', 'save', JSON.stringify(conf))
  },
  // 读取资源自定义配置
  async readResConfFromClientLocal(path) {
    let ret = await agentApi.fileTools.ActionLocalFile('webResourceConfigTmp', 'read')
    if (ret) {
      const getAttr = (obj, path) => {
        const parts = path.split('.')
        let result
        for (let i = 0; i < parts.length; i++) {
          if (!obj[parts[0]] || (result && !result[parts[i]])) {
            return ''
          }
          result = i === 0 ? obj[parts[i]] : result[parts[i]]
        }
        return result
      }
      try {
        if (typeof ret === 'string') {
          ret = JSON.parse(ret)
        }
        if (path) {
          return getAttr(ret, path)
        }
        return ret
      } catch (e) {
        return ''
      }
    }
    return ret
  },
  // 删除数据
  async delResConfClientLocal(path) {
    const parts = path.split('.')
    const obj = await sourceListUtil.readResConfFromClientLocal()
    if (typeof obj !== 'object') {
      return
    }
    const lastKey = parts.pop()
    const parent = parts.reduce((obj_, key) => obj_[key] || {}, obj)
    // 如果父对象存在且包含需要删除的属性，则删除它
    if (parent && parent[lastKey] !== undefined) {
      delete parent[lastKey]
    } else {
      console.log('未找到删除对象')
    }

    await agentApi.fileTools.ActionLocalFile('webResourceConfigTmp', 'save', JSON.stringify(obj))
  }
}

export default sourceListUtil
