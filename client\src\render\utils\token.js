import agent<PERSON>pi from '@/service/api/agentApi'
import store from '@/render/store'
import _ from 'lodash'

export async function readToken() {
  const store_token = _.get(store, 'state.authInfo.tokenInfo', '')
  if (store_token) {
    return store_token
  }
  try {
    const file_token = await agentApi.fileTools.ActionLocalFile('assui-token', 'read')
    if (file_token) {
      store.commit('setAuthInfo', { ...store.state.authInfo, ...{ tokenInfo: { token: file_token.token, tokenTimestamp: file_token.tokenTimestamp, UserID: file_token.UserID }}}) // 更新本地token
      return file_token
    }
  } catch (e) {
    console.log('文件未读取到token')
  }

  return { token: '', tokenTimestamp: 0, UserID: '' }
}

export function readTokenAsync() {
  const store_token = _.get(store, 'state.authInfo.tokenInfo', '')
  if (store_token) {
    return store_token
  }
  return { token: '', tokenTimestamp: 0, UserID: '' }
}

export async function saveToken(tokenInfo, compare = false) { // deviceId暂时只有浏览器会读取（访问资源重定向跳转认证，等待客户端认证完成回跳资源）
  const { token, tokenTimestamp, UserID, deviceId } = tokenInfo
  if (!token || !tokenTimestamp) {
    return
  }
  if (compare) {
    const _tokenInfo = await readToken()
    if (_.get(_tokenInfo, 'tokenTimestamp', 0) > tokenTimestamp) { // 不是最新的token不存
      return
    }
  }
  store.commit('setAuthInfo', { ...store.state.authInfo, ...{ tokenInfo: { token, tokenTimestamp, UserID, deviceId }}})
  await agentApi.fileTools.ActionLocalFile('assui-token', 'save', JSON.stringify({ token, tokenTimestamp, UserID, deviceId }))
}

export function clearToken() {
  store.commit('setAuthInfo', { ...store.state.authInfo, ...{ tokenInfo: '' }})
  agentApi.fileTools.ActionLocalFile('assui-token', 'del')
}

// 同步文件内token到vuex
export async function asyncFileToken() {
  try {
    const file_token = await agentApi.fileTools.ActionLocalFile('assui-token', 'read')
    if (file_token) {
      store.commit('setAuthInfo', { ...store.state.authInfo, ...{ tokenInfo: { token: file_token.token, tokenTimestamp: file_token.tokenTimestamp, UserID: file_token.UserID }}}) // 更新本地token
      return file_token
    }
  } catch (e) {
    console.log('文件未读取到token')
  }
}
