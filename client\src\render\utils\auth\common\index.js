import agentApi from '@/service/api/agentApi'
import { Base64Encode } from '@/render/utils/global'
import store from '@/render/store'
import _ from 'lodash'
import authIndex from '../index'
import opform from '@/render/utils/opform'
const authCommon = {
  /**
   * 认证完调用
   * 通知小助手角色等信息
   * @returns {Promise<void>}
   */
  async authEnd(params) {
    const apiParams = {
      DeviceID: authIndex.getDevice(),
      RoleID: _.get(store.state.authInfo.basic, 'RoleID', 0),
      PolicyID: _.get(store.state.authInfo.basic, 'PolicyID', 0),
      AuthType: params.type,
      IsSafeCheck: _.get(store, 'state.authInfo.basic.IsSafeCheck', 0),
      IsAutoAuth: _.get(params, 'autoAuth', '0'),
      IsOpenSDCLinkage: _.get(store.state.authInfo.basic, 'IsOpenSDCLinkage', 0),
      IsOpenDLPLinkage: _.get(store.state.authInfo.basic, 'IsOpenDLPLinkage', 0)
    }

    const userName = _.get(store.state.authInfo.basic, 'UserName', '')
    if (_.isString(userName) && !_.isEmpty(userName)) {
      apiParams.UserName = Base64Encode(userName)
    }
    if (_.isString(params.password) && !_.isEmpty(params.password)) {
      apiParams.Password = Base64Encode(params.password)
    }
    if (params.authResponse !== false) {
      apiParams.AuthResponse = Base64Encode(JSON.stringify(store.state.authInfo.basic))
    }
    if (_.isString(params.certbase64) && params.certbase64 !== '') {
      apiParams.UKeyAuthCert = params.certbase64
    }

    opform.set('authEndParams', apiParams)
    await agentApi.AuthEnd(apiParams)
  },

  /**
   * 从小助手获取ad域防伪造信息
   */
  async isDomainNoFake() {
    const fakeDomain = await agentApi.isDomainNoFake()
    return _.get(fakeDomain, 'Result.IsInDomain', false)
  }
}

export default authCommon

