/*
 * @Author: <EMAIL>
 * @Date: 2023-03-14 17:22:58
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-03-14 17:22:59
 * @Description: file content
 */
// 开机自动认证安检的流程
import commonUtil from '@/render/utils/bussiness/commonUtil'
import { getRemoteLangPack } from '@/render/lang/index'
import authIndex from '@/render/utils/auth/index'
import store from '@/render/store'
import scene from '@/render/utils/bussiness/scene'
import NoAuth from '@/render/utils/auth/noAuth'
import _ from 'lodash'
import authTypes from '../auth/authTypes'
import proxyApi from '@/service/api/proxyApi'
import Account from '@/render/utils/auth/account'
import agentApi from '@/service/api/agentApi'
import securityUtil from '@/render/utils/bussiness/securityUtil'
import UKey from '@/render/utils/auth/uKey'
import AdAuto from '@/render/utils/auth/adAuto'
import { sleep } from '../global'
import Vue from 'vue'
import accessNetwork from '@/render/utils/accessNetwork'
import os_browser_info from '@/render/utils/os_browser_info'
class BootAutoAuthCheck {
  // 启动
  constructor() {
    this.processId = Vue.prototype.$processId
  }

  async boot() {
    try {
      await this.initConfig()

      // 欢迎页的判断
      await this.message()

      // 自动认证(通过保存的用户名和密码),或者场景配置的是免认证
      await this.auth()

      await this.register()

      await this.check()
    } catch (error) {
      this.closeAssUI(error)
    }
  }

  // 关闭当前AssUI
  async closeAssUI(error) {
    if (!_.isNull(error)) {
      console.error('全局错误捕获')
      console.log(error)
      const msg = _.get(error, 'message', '')
      const params = {
        ExcuteReslut: 0, // ExcuteReslut的值应该都是0,表示执行结果
        AssUIProcessId: this.processId,
        ErrorMsg: msg
      }
      // 通知小助手安检失败
      await agentApi.notifyAssUIExcuteResult(params)
    }

    agentApi.terminateWnd()
  }

  // 获取基本的配置信息等
  async initConfig() {
    try {
      await Promise.all([commonUtil.getDot1xInfo(), commonUtil.basic(), commonUtil.checkDefaultPort(true)])

      // 如果angetId都获取失败，那么中断继续
      const deviceId = parseInt(_.get(store, 'state.clientInfo.basic.AgentID', 0))
      if (deviceId === 0 || isNaN(deviceId)) {
        throw new Error('获取基础信息失败')
      }

      // 开机自动认证安检暂时不支持敲端口模式(所以如果非8021.x下不在线则自动退出)
      if (!authIndex.isDot1xMode() && !_.get(store.state.clientInfo, 'online', false)) {
        throw new Error('当前设备不在线并且不是8021.x模式')
      }

      await Promise.all([commonUtil.detail(), commonUtil.server(), getRemoteLangPack()])
    } catch (error) {
      const msg = _.get(error, 'message', '')
      throw new Error(msg || '获取基础信息失败')
    }
  }

  // 判断是不是待审核，违规等情况
  async message() {
    const status = store.getters.computeNetAccessStatus()

    if (_.get(status, 'registeredStatus.needAduit')) {
      throw new Error('设备待审核中')
    }

    if (_.get(status, 'cutOff')) {
      throw new Error('设备已隔离')
    }

    if (_.get(status, 'ipMacBindIllegal')) {
      throw new Error('IP/Mac绑定违规')
    }

    if (_.get(status, 'fingerIllegal')) {
      throw new Error('指纹违规')
    }
  }

  // 自动认证或者免认证
  async auth() {
    try {
      if (!authIndex.isDot1xMode()) {
        const sceneRet = await this.getScene(1)
        if (parseInt(_.get(sceneRet, 'IsAuth', 1)) === 0) {
          await this.noAuth()
          return
        }
      }

      const dot1xParams = {}
      // 如果是8021.x模式，获取网卡列表，然后选择默认的网卡
      if (authIndex.isDot1xMode()) {
        try {
          await accessNetwork.init()

          if (_.isEmpty(accessNetwork.getAccessNetwork())) {
            throw new Error('认证失败，8021.x模式下获取推荐或者缓存的网卡失败')
          }

          dot1xParams.accessNetwork = accessNetwork.getAccessNetwork()
          dot1xParams.isWireLess = accessNetwork.store.isWireLess()
        } catch (error) {
          console.error('初始化8021.x网卡失败')
          console.error(error)
          const msg = _.get(error, 'message', '') || '认证失败,8021.x模式下，获取网卡列表失败'
          throw new Error(msg)
        }
      }

      // 如果符合Ad域单点登录的条件,先优先执行Ad域单点登录
      if (this.adDomainAutoLoginFlag()) {
        // 执行AD域单点登录
        this.adDomainInstance = new AdAuto()
        const ret = await this.adDomainAutoAuth(dot1xParams)
        console.log('[ad域登录结果=]', ret)
        if (ret !== false) {
          return
        }
      }

      // 判断是否符合自动认证的条件
      const { allowAutoAuth, defaultAuthType } = await this.whetherAllowAutoAuth()
      if (!allowAutoAuth) {
        throw new Error('未开启，或者不符合自动认证安检的条件')
      }

      // 用户名密码自动认证
      if (defaultAuthType === authTypes.User) {
        this.accountInstance = new Account()
        const params = await this.getAccountAuthParams()
        await this.accountAuth(params, dot1xParams)
        return
      }

      // Ukey自动认证
      if (defaultAuthType === authTypes.UKey) {
        this.uKeyAuthInstance = new UKey()
        const params = await this.getUkeyAuthParams(dot1xParams)
        await this.ukeyAuth(params)
        return
      }

      // @todo Ad域单点登录的支持
    } catch (error) {
      const msg = _.get(error, 'message', '')
      throw new Error(msg || '获取基础信息失败')
    }
  }

  // 符合Ad域单点登录的条件
  adDomainAutoLoginFlag() {
    if (os_browser_info.os_Linux) {
      console.log('[Ad域点登录]:linux不执行')
      return false
    }
    if (_.indexOf(_.get(store.getters.computeServeEntiretyConfig, 'AUTHPARAM.AllowAuthType', []), authTypes.User) === -1) {
      console.log('[Ad域单点登录]:没有开启User认证')
      return false
    }
    const authServerAliasConfig = _.get(store.getters.computeServeEntiretyConfig, 'User.AuthServer', '')
    if (!_.isString(authServerAliasConfig) || _.isEmpty(authServerAliasConfig)) {
      console.log('[Ad域单点登录]:没有开启Ad域认证服务器')
      return false
    }
    if (parseInt(authServerAliasConfig.indexOf(authTypes.AdDomain)) === -1) {
      console.log('[Ad域单点登录]:没有开启Ad域单点登录')
      return false
    }

    return true
  }

  async getAccountAuthParams() {
    const ret = await authIndex.lastAuthCache()
    return ret
  }

  // 执行AdDomain单点登录
  async adDomainAutoAuth() {
    const authRes = await this.adDomainInstance.auth()
    if (authRes === false) {
      return false
    }

    const isAccountAuthType = true
    const authType = 'AdDomain'
    const authData = {}
    const successRes = await authIndex.authSuccess({
      fromBootAuth: true,
      ...authData,
      authType,
      isAccountAuthType
    })
    if (!successRes) {
      throw new Error('认证失败，可能需要主账号绑定，或者ip发生变动等情况')
    }

    return successRes
  }

  // ukey认证
  async ukeyAuth(params) {
    const { apiParams, authData, authType } = params
    const authRet = await this.uKeyAuthInstance.auth(
      {
        ...apiParams,
        autoAuth: 1
      }
    )
    if (authRet === false) {
      throw new Error('[ukey认证]认证失败，返回=', authRet)
    } else if (_.get(authRet, 'revoke', false) === true) {
      throw new Error('[ukey认证]认证失败，超出设备最大登录条目限制')
    }

    // @todo 还有双因子，主账号绑定，修改账户名密码限制等
    const isAccountAuthType = false
    const successRes = await authIndex.authSuccess({
      fromBootAuth: true,
      ...authData,
      authType,
      isAccountAuthType
    })

    if (!successRes) {
      throw new Error('认证失败，可能需要主账号绑定，或者ip发生变动等情况')
    }

    return successRes
  }

  // 获取Ukey自动认证的参数
  async getUkeyAuthParams(dot1xParams) {
    let isWireLess = ''
    let accessNetwork = ''

    const { authData, authTypeFromWebAuthTypeTmp } = await authIndex.lastAuthCache()
    // 去异步实时获取证书，并且判断缓存的证书在实时获取的证书列表里面
    if (_.isEmpty(_.get(authData, 'uKeyCertDefault', ''))) {
      throw new Error('[Ukey]认证失败,未获取到缓存的证书序列号')
    }

    const certInfo = await this.getCert(_.get(authData, 'uKeyCertDefault', ''))

    if (certInfo === false) {
      throw new Error('[Ukey]认证失败,缓存的证书序列未在当前获取的证书内')
    }

    // @todo 8021.x模式才需要判断是否是wifi和获取选中的网卡
    if (authIndex.isDot1xMode()) {
      accessNetwork = dot1xParams.accessNetwork || ''
      isWireLess = dot1xParams.isWireLess || ''
    }

    const ret = await this.uKeyAuthInstance.authParamsPrepare(authData, accessNetwork, isWireLess, certInfo)

    if (!_.get(ret, 'result', false) || _.isEmpty(_.get(ret, 'params'))) {
      const msg = _.get(ret, 'msg') || this.$t('auth.insertUKey')
      throw new Error(msg)
    }

    const apiParams = _.get(ret, 'params')

    return { apiParams, authData, authType: authTypeFromWebAuthTypeTmp }
  }

  // 判断默认的证书序列号是否实时获取的证书列表里面(电脑存储的证书或者插入的usb证书)
  // 这里多轮询几次，应对证书异步加载找不到证书的情况；
  async getCert(defaultCertSeria) {
    const queryCert = async() => {
      try {
        const certList = await this.uKeyAuthInstance.getCert()

        if (_.isArray(certList) && !_.isEmpty(certList)) {
          // 已加载完证书，根据缓存的序列号来判断是否能自动认证

          if (_.isEmpty(defaultCertSeria) || !_.isString(defaultCertSeria)) {
            return false
          }

          const defaultCert = certList.find(item => item.value === defaultCertSeria)
          if (_.isEmpty(defaultCert)) {
            return false
          }

          return defaultCert
        } else {
          return false
        }
      } catch (error) {
        console.log('[Ukey认证]请求获取证书接口失败')
        console.log(error)
      }
    }

    let certInfo = false

    for (let index = 0; index < 3; index++) {
      certInfo = await queryCert()

      if (certInfo !== false) {
        break
      } else {
        await sleep(1000)
      }
    }

    return certInfo
  }

  // 判断是否符合开机自动认证安检的条件
  async whetherAllowAutoAuth() {
    const { authConfigFromWebAuthConfigTmp, authData, authTypeFromWebAuthTypeTmp } = await authIndex.lastAuthCache()

    const defaultAuthType = authIndex.queryDefaultAuthType(authConfigFromWebAuthConfigTmp, authTypeFromWebAuthTypeTmp)

    const allowAutoAuth = authIndex.whetherAllowAutoAuth(authConfigFromWebAuthConfigTmp, defaultAuthType, authData, true)

    return { allowAutoAuth, defaultAuthType }
  }

  // 免认证
  async noAuth() {
    const noAuth = new NoAuth()
    const res = await noAuth.auth()
    if (!res) {
      throw new Error('认证失败，认证方式为免认证')
    }

    const isAccountAuthType = false
    const authType = 'noAuth'
    const successRes = await authIndex.authSuccess({
      fromBootAuth: true,
      authType,
      isAccountAuthType
    })

    if (!successRes) {
      throw new Error('认证失败，可能需要主账号绑定，或者ip发生变动等情况')
    }

    return successRes
  }

  async accountAuth(params, dot1xParams) {
    const { authData, authType } = params

    // 这里的基本上都是符合自动认证的条件了
    const res = await this.accountInstance.auth({
      ...{ ...authData, ...dot1xParams },
      authType: authType,
      isVerifyCode: false,
      autoAuth: 1
    })

    if (parseInt(_.get(res, 'errcode', -1)) !== 0) {
      throw new Error('认证失败，返回=' + JSON.stringify(res))
    }

    // 如果需要双因子，则认证失败
    const factorAuth = _.get(store.state.authInfo, 'basic.FactorAuth')
    if (parseInt(factorAuth)) {
      throw new Error('认证失败，需要双因子认证')
    }

    // 是否用户名密码认证
    const isAccountAuthType = true
    const successRes = await authIndex.authSuccess({
      fromBootAuth: true,
      ...authData,
      authType,
      isAccountAuthType
    })

    if (!successRes) {
      throw new Error('认证失败，可能需要主账号绑定，或者ip发生变动等情况')
    }

    return successRes
  }

  // 自动注册流程
  async register() {
    // 当前是来宾场景
    const sceneRet = await this.getScene()

    // 如果设备已注册，那么这里直接返回
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === 1) {
      return true
    }

    // 待审核的话退出自动认证
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === 0) {
      throw new Error('当前设备为待审核，自动认证失败')
    }

    // 未注册,重新获取场景信息
    if (parseInt(_.get(sceneRet, 'IsRegistered', 1)) === 1) {
      throw new Error('设备未注册(且匹配到的场景需要注册)，自动认证失败')
    }

    // 判断授权点数是否能继续注册
    const authorizationAbleRet = await this.authorizationAble()
    if (!authorizationAbleRet) {
      throw new Error('系统尚未授权或授权已过期、或者注册设备数已超设备型号所能支撑的最大数目, 请与经销商联系！')
    }

    await this.autoReg()
  }
  // 查询授权点数，是否能继续注册
  async authorizationAble() {
    const res = await proxyApi.getAuthorization({ cache: new Date().getTime() })
    if (parseInt(res.errcode) === 0) {
      if (!res.data.val) {
        return false
      }
    } else {
      return false
    }
    return true
  }

  async autoReg() {
    const apiParam = {
      depart_id: '',
      username:
        parseInt(
          _.get(store.state.serveEntiretyConfig, 'server.UserDevBind.IsUpDevName')
        ) === 1
          ? parseInt(
            _.get(
              store.state.serveEntiretyConfig,
              'server.UserDevBind.IsUpdateUserToDev'
            )
          ) === 1
            ? _.get(store.state.authInfo, 'basic.UserName', '')
            : _.get(store.state.authInfo, 'basic.TrueNames', '')
          : '',
      tel: _.get(store.state.clientInfo, 'detail.Tel') || _.get(store.state.authInfo, 'basic.Tel') || '',
      remark: _.get(store.state.clientInfo, 'detail.Remark') || '',
      email: _.get(store.state.clientInfo, 'detail.EMail') || _.get(store.state.authInfo, 'basic.EMail') || ''
    }

    apiParam['roleid'] = _.get(store.state.authInfo, 'basic.RoleID', '')
    apiParam['device_id'] = _.get(store.state.clientInfo, 'detail.DeviceID')
    apiParam['from'] = 'IsAgent'
    apiParam['sceneId'] = _.get(store.state.serveEntiretyConfig, 'scene.SceneID', '')
    apiParam['device_type'] = '101'

    // 请求自动注册接口
    const ret = await proxyApi.regSubmit(apiParam)
    if (parseInt(_.get(ret, 'errcode')) === 0 && _.get(ret, 'data.Registered', false)) {
      // 更新clientInfo,设备应该变成已注册才能继续往下走流程
      const clientInfo = _.set(store.state.clientInfo, 'detail.Registered', _.get(ret, 'data.Registered'))
      store.commit('setClientInfo', clientInfo)
    } else {
      throw new Error('自动注册失败,ret=', ret)
    }

    // 待审核的话退出自动认证
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === 0) {
      throw new Error('注册后当前设备为待审核，退出流程')
    }
  }

  // 安检流程
  async check() {
    // 重新获取场景信息，判断是否需要安检
    const sceneRet = await this.getScene()
    if (parseInt(_.get(sceneRet, 'IsSafeCheck')) === 0) {
      try {
        await commonUtil.setDevAccessInfo()
        await commonUtil.securityCheckRePort({ AssUIProcessId: this.processId, IsAutoCheck: 1 })
        this.closeAssUI(null)
      } catch (error) {
        throw new Error('免安检，上报安检结果失败！error=', error)
      }
    } else {
      try {
        await commonUtil.setDevAccessInfo()
        await this.autoCheck()
        // 上报安检结果
        await commonUtil.securityCheckRePort({ AssUIProcessId: this.processId, IsAutoCheck: 1 })
        this.closeAssUI(null)
      } catch (error) {
        throw new Error('安检失败！error=' + _.get(error, 'message'))
      }
    }
  }

  // 自动安检
  async autoCheck() {
    const res = await agentApi.getSecurityCheckPolicy()

    if (_.isEmpty(_.get(res, 'ASM.SecurityCheckPolicy', ''))) {
      console.error('安检项返回=', res)
      throw new Error('获取安检项失败')
    }

    const policyKey = securityUtil.getPolicysKey()

    let checkItems = _.get(res, `ASM.SecurityCheckPolicy.CheckItems.${policyKey}`)
    if (checkItems && parseInt(_.get(res, 'ASM.SecurityCheckPolicy.CheckParam.IsSafeCheck', 0)) === 1) {
      // 奇怪的可变的数据类型
      if (Object.prototype.toString.call(checkItems) === '[object Object]') {
        checkItems = [checkItems]
      }

      const canCheck = await securityUtil.checkPrecondition()
      if (canCheck) {
        await this.checkAllItem(checkItems)
      } else {
        throw new Error('前置安检项检查失败')
      }
    } else {
      // 开启了安检但是没有配置安检项的这是
      console.log('[安检结束]开启了安检,但是没有配置安检项')
    }
  }

  async checkAllItem(checkList) {
    for (let index = 0; index < checkList.length; index++) {
      const item = checkList[index]

      const res = await securityUtil.requestCheckResult(item)
      if (_.isEmpty(res)) {
        console.error('安检项返回', res)
        throw new Error('安检项检查失败')
      }
    }
  }

  async getScene(type) {
    const sceneRet = await scene.getDeviceScene(type)

    if (!sceneRet || _.isEmpty(sceneRet)) {
      throw new Error('获取场景信息失败,sceneRet=', sceneRet)
    }
    return sceneRet
  }
}

export default BootAutoAuthCheck

