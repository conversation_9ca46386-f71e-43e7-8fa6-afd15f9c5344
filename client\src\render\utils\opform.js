import _ from 'lodash'

const opform = {
  is_guest: 0,
  deviceId: undefined,
  auth_type: '',
  /**
   * 是否已经提交过一次注册信息（重注册）
   */
  DevRegSubmitted: 0,
  /**
   * 最后认证id
   */
  LastAuthID: 0,
  /**
   * 角色是否改变
   */
  IsRoleChange: 0,
  /**
   * 角色ID
   */
  roleID: 0,
  /**
   * 小助手
   */
  isActive: 0,
  ControlPostion: undefined,
  is_safecheck: undefined,
  /**
   * 账户名
   */
  user_name: '',
  /**
   * 是否自动认证
   */
  is_auto_auth: 'No',
  /**
   * 设置自动登录
   */
  AutoLogin: '0',
  /**
   * 认证服务器
   */
  auth_type_server: '',
  // 认证完后通知小助手的参数，可以用来获取安检项的一些关键数据
  authEndParams: {},

  set(key, value) {
    if (!_.isUndefined(key)) {
      opform[key] = value
    }
    return false
  },
  get(key) {
    return opform[key]
  }
}

export default opform
