<template>
  <ul class="tab-wrapper">
    <li v-for="item in list" :key="item.id" :class="['tab-item', activeTab === item.id?'active-item': '']" @click="change(item)">
      {{ item.name }}
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    value: {
      default: 'devInfo',
      type: String
    }
  },
  data() {
    return {

    }
  },
  computed: {
    list() {
      return [
        { name: this.$t('system.devInfo'), id: 'devInfo' }
        // { name: this.$t('system.sysInfo'), id: 'sysInfo' }
      ]
    },
    activeTab: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    change(item) {
      console.log(item)
      if (item.id === this.value) {
        return
      }
      this.activeTab = item.id
    }
  }
}
</script>
<style lang="scss" scoped>
    .tab-wrapper{
        width: 100%;
        display: flex;
        border-bottom: 1px solid $line-color;
        .tab-item{
            padding: 6px 16px;
            line-height: 20px;
            font-size: 14px;
            color: $title-color;
            margin-right: 4px;
            border-radius: 4px 4px 0px 0px;
            border: 1px solid $line-color;
            border-bottom: none;
            background: $gray-5;
            cursor: pointer;
            position: relative;
            bottom: -1px;
            &:hover{
                background: $line-color;
                border-color: $gray-2;
            }
        }
        .active-item{
            background: $green;
            color: $light-color;
            border-color: $green;
            &:hover{
                background: $green;
                border-color: $green;
            }
        }
    }
</style>
