<!-- 编辑账户 -->
<template>
  <div>
    <VerticalDialog
      :show.sync="show"
      :show-close="false"
      :title="$t('auth.auth')"
      width="440px"
      @closed="closeHandle"
    >
      <div class="dialog-content">
        <div v-loading="loading" class="form-content file-res-form">
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            :label-width="labelWidth"
            class="rule-form"
          >
            <el-form-item v-if="fileData.GrantType === 'FTP'" :label="$t('fileRes.aliName')" prop="Alias">
              <el-input
                v-model="ruleForm.Alias"
                maxlength="50"
                :placeholder="$t('fileRes.pAliName')"
              >
                <i slot="prefix" class="iconfont icon-CheckGuestUser" />
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('fileRes.account')" prop="Account">
              <el-input
                v-model="ruleForm.Account"
                maxlength="50"
                :placeholder="$t('fileRes.pAccount')"
              >
                <i slot="prefix" class="iconfont icon-zhanghu" />
              </el-input>
            </el-form-item>
            <el-form-item v-if="fileData.GrantType === 'SMB'" class="smb-password-input" :label="$t('fileRes.password')" prop="Password">
              <el-input
                v-model="ruleForm.Password"
                maxlength="400"
                :type="passwordType"
                :placeholder="$t('fileRes.pPassword')"
              >
                <i slot="prefix" class="iconfont icon-mima" />
                <i v-show="ruleForm.Password" slot="suffix" :class="['iconfont', passwordType === 'password'? 'icon-yincang':'icon-xianshi']" @click="changeInputType" />
              </el-input>
            </el-form-item>
            <el-form-item v-if="fileData.GrantType == 'SMB'" class="select-form-box" :label="$t('fileRes.qd')" prop="Driver">
              <i class="iconfont icon-leixing slect-icon" />
              <el-select
                id="ui-accessNetwork-auth-div-authtype"
                v-model="ruleForm.Driver"
                :placeholder="$t('fileRes.pQd')"
              >
                <el-option
                  v-for="item in qdOptions"
                  :id="`ui-accessNetwork-file_res-li-${item.value}`"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <div class="content-footer">
          <div class="clear" @click="cancelHandle">{{ $t('confirmDialog.no') }}</div>
          <div :class="['submit', submiting || loading?'disable':'']" @click="submitForm('ruleForm')"> <i v-if="submiting" class="el-icon-loading" />{{ $t('sourcePlatform.ok') }}</div>
        </div>
      </div>
    </VerticalDialog>
    <!-- 重复挂在确认框 -->
    <verticalDialog
      :show="showConfirm"
      :show-close="false"
      :show-foot="true"
      width="384px"
      class="file-res-m-confirm-dialog"
      :cancel-text=" $t('dialogFoot.no')"
      :confirm-text="$t('dialogFoot.yes')"
      pop-name="fileRes-header"
      @cancel="cancelCover"
      @confirm="coverHandle"
    >
      <div slot="header" class="v-header">
        <i class="iconfont icon-putongxiangbuhegui" />
        {{ $t('tips') }}
      </div>
      <div class="g-s-diaolog-content">
        <div class="form-content">
          <p class="outline-tips">{{ $t('sourcePlatform.coverFile') }}</p>
        </div>
      </div>
    </verticalDialog>
  </div>

</template>

<script>
import { mapGetters } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
import { Base64Encode } from '@/render/utils/global'
import agentApi from '@/service/api/agentApi'
import regular from '@/render/utils/regular'
export default {
  name: 'EditAccount',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    fileData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: function() {
    const validateAccount = (rule, value, callback) => {
      if (!value) {
        if (_.get(this.fileData, 'GrantType') === 'SMB') {
          callback()
        }
        callback(new Error(this.$t('sourcePlatform.pEnter')))
      } else {
        const reg = regular.rules.windowsAccount
        if (!reg.test(value)) {
          callback(new Error(this.$t('sourcePlatform.windwosAccountErr')))
        }
        callback()
      }
    }
    return {
      ruleForm: {
        Account: '',
        Password: '',
        Alias: '',
        Driver: ''
      },
      rules: {
        Account: [
          { validator: validateAccount, trigger: 'blur' }
        ],
        Alias: [
          { validator: validateAccount, trigger: 'blur' }
        ],
        Driver: [{ required: true, message: this.$t('fileRes.pQd'), trigger: 'change' }]
      },
      qdOptions: [],
      submiting: false,
      passwordType: 'password',
      showConfirm: false,
      coverParams: null,
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'authInfo'
    ]),
    labelWidth() {
      if (this.$i18n.locale === 'zh') {
        return '80px'
      }
      return '90px'
    },
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    value(val) {
      if (val) {
        this.passwordType = 'password'
        const ruleForm = { ...this.fileData }
        this.ruleForm = ruleForm
        this.submiting = false
        if (_.get(this.fileData, 'GrantType') === 'SMB') {
          this.getFreeDriverLetter()
        }
      }
    }
  },
  created() {},
  beforeDestroy() {},
  methods: {
    async getFreeDriverLetter() {
      this.loading = true
      const ret = await agentApi.getFreeDriverLetter()
      this.loading = false
      const drivers = _.get(ret, 'ASM.FreeDriverLetters', '')
      if (drivers) {
        const arr_driver = drivers.split(',')
        this.qdOptions = arr_driver.map(item => ({ value: item, label: item }))
        console.log('驱动器', this.qdOptions)
        return
      }
      this.qdOptions = []
    },
    changeInputType() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password'
    },
    closeHandle() {
      this.$refs.ruleForm.resetFields()
    },
    resetForm() {
      this.ruleForm = {
        Account: '',
        Password: '',
        Alias: '',
        Driver: ''
      }
    },
    cancelHandle() {
      this.closeHandle()
      this.resetForm()
      this.loading = false
      this.show = false
    },
    async savePassword() {
      const UserId = _.get(this.clientInfo, 'accessStatus.UserID') || _.get(this.authInfo, 'basic.ID')
      const { GrantType, ResID } = this.fileData
      const { Account, Password, Alias, Driver } = this.ruleForm

      const params = {
        ResID,
        UserId,
        Account: Base64Encode(Account),
        FSARType: GrantType
      }
      if (GrantType === 'FTP') {
        params.Alias = Alias
      } else {
        params.Password = Base64Encode(Password)
        params.Driver = Driver
      }
      proxyApi.rememberFSARPassword(params)
    },
    async coverHandle() {
      const params = this.coverParams
      params.InfosFrom = '2' // 强制覆盖
      this.showConfirm = false
      const ret = await agentApi.windowFileServeOpenUrl(params)
      this.submiting = false
      if (parseInt(_.get(ret, 'ASM.ErrCode')) !== 0) {
        this.$message.error(ret.ASM.ErrMsg)
        return
      }
      this.savePassword()
      this.cancelHandle()
    },
    cancelCover() {
      this.showConfirm = false
      this.submiting = false
    },
    submitForm(formName) {
      if (this.loading) {
        return
      }
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          if (this.submiting) {
            return
          }
          this.submiting = true
          const { GrantType, DocumentServerUrl } = this.fileData
          const { Account, Password, Alias, Driver } = this.ruleForm
          const params = {
            File: GrantType,
            Params: DocumentServerUrl,
            ShowType: '0',
            ResType: '1',
            NeedWait: 0,
            InfosFrom: '0',
            UserName: Account,
            MountDriverLetter: GrantType === 'SMB' ? Driver : Alias
          }
          if (GrantType === 'SMB') {
            params.UserPSW = Password
          }
          const ret = await agentApi.windowFileServeOpenUrl(params)
          if (parseInt(_.get(ret, 'ASM.ErrCode')) !== 0) {
            if (parseInt(_.get(ret, 'ASM.PromptPSW')) === 2) { // 重名了
              this.showConfirm = true
              this.coverParams = params
              return
            }
            this.$message.error(ret.ASM.ErrMsg)
            this.submiting = false
            return
          }
          this.savePassword()
          this.submiting = false
          this.cancelHandle()
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .dialog-content {
    padding-top: 24px;
    .form-content{
      padding: 0 40px;
      ::v-deep .smb-password-input .el-input__suffix{
        right: 10px;
        cursor: pointer;
      }
    }
    .content-footer{
      border-top: 1px solid $line-color;
      display: flex;
      &>div{
        width: 50%;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: $--color-primary;
        cursor: pointer;
        &:hover{
          background: $gray-1;
        }
      }
      .clear{
        border-right: 1px solid $line-color;
        color: $default-color;
        border-bottom-left-radius: 5px;
      }
      .submit{
        border-bottom-right-radius: 5px;
      }
      .el-icon-loading{
        margin-right: 4px;
      }
      .disable{
        color: $disabled-color;
        cursor: not-allowed;
        background: $gray-1;
      }
    }
  }
  </style>

