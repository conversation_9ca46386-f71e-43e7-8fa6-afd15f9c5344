<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" id="WSUS-current-computer" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.WSUS.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.WSUS.h_3_rs") }}
          </p>
          <el-table
            :data="tableData"
            stripe
            style="width: 100%"
            class="public-no-boder-table"
            :cell-class-name="cellStyleHandle"
          >
            <el-table-column
              prop="name"
              label=""
              width="140"
              class-name="header-column"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="self"
              width="178"
              :show-overflow-tooltip="true"
              :label="$t('check.WSUS.h_5_rd')"
              :formatter="emptyLine"
            >
              <template slot="header">
                <el-tooltip class="item" effect="dark" :content="$t('check.WSUS.h_5_rd')" placement="top">
                  <span>{{ $t('check.WSUS.h_5_rd') }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="admin"
              width="175"
              :show-overflow-tooltip="true"
              :label="$t('check.WSUS.h_7_rd')"
              :formatter="emptyLine"
            >
              <template slot="header">
                <el-tooltip class="item" effect="dark" :content="$t('check.WSUS.h_7_rd')" placement="top">
                  <span>{{ $t('check.WSUS.h_7_rd') }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <div class="fix-btn-wrapper">
            <button :class="[checkData.hasFixed ? 'btn-max-disabled': 'public-medium-btn', loading ? 'loading-disable': '']" @click="fixHandle">
              <i v-if="loading" class="el-icon-loading" /> {{ $t("check.WSUS.js_14_d") }}
            </button>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckWSUS',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      imgSrc: '',
      tableData: [
        {
          name: this.$t('check.WSUS.h_9_rd'),
          self: '',
          admin: '',
          error: []
        },
        {
          name: this.$t('check.WSUS.h_11_rd'),
          self: '',
          admin: '',
          error: []
        },
        {
          name: this.$t('check.WSUS.h_13_rd'),
          self: '',
          admin: '',
          error: []
        },
        {
          name: this.$t('check.WSUS.h_15_rd'),
          self: '',
          admin: '',
          error: []
        }
      ]
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async fixHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {},
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.fix')
      })
    },
    getTableData() {
      const info = _.get(
        this.checkData,
        'CheckResult.CheckType.Info',
        {}
      )
      let InstallOption = _.get(info, 'InstallOption', '')
      if (info.Active === 'false') {
        InstallOption = 'False'
      }

      const InstallDay = _.get(info, 'InstallDay', '')

      const InstallTime = _.get(info, 'InstallTime', '')

      const NoAutoReboot = _.get(info, 'NoAutoReboot', 'false')

      const WUServer = _.get(info, 'WUServer', '')

      const DetectionFrequency = _.get(info, 'DetectionFrequency', '')

      if (InstallOption === 'False') {
        this.tableData[0].self = this.$t('check.WSUS.js_1_s')
      } else if (InstallOption === 'AutoInstall') {
        let _InstallOption = this.$t('check.WSUS.js_3_s')
        const dateStr = this.getDayName(InstallDay) + ' ' + (InstallTime ? InstallTime + ':00' : '')
        _InstallOption += dateStr !== ' ' ? ' （' + dateStr + '）' : ''
        this.tableData[0].self = _InstallOption
      } else if (InstallOption === 'NotifyInstall') {
        this.tableData[0].self = this.$t('check.WSUS.js_5_s')
      } else if (InstallOption === 'NotifyDownload') {
        this.tableData[0].self = this.$t('check.WSUS.js_7_s')
      }

      this.tableData[1].self = WUServer
      this.tableData[2].self = DetectionFrequency ? DetectionFrequency + this.$t('check.WSUS.js_9_s') : ''
      this.tableData[3].self = NoAutoReboot === 'false' ? this.$t('check.WSUS.js_11_s') : this.$t('check.WSUS.js_12_s')

      // 配置部分
      const rule = _.get(this.checkData, 'CheckType.Option', {})
      const install_day = _.get(rule, 'InstallDay', '')
      const install_time = _.get(rule, 'InstallTime', '')
      const install_value = _.get(rule, 'InstallOption', '')
      const install_WUServer = _.get(rule, 'WUServer', '')
      const install_DetectionFrequency = _.get(rule, 'DetectionFrequency', '')
      const install_NoAutoReboot = _.get(rule, 'NoAutoReboot', 'false')
      if (install_value !== InstallOption) {
        this.tableData[0].error.push('self')
      }
      if (install_value === InstallOption && InstallOption === 'AutoInstall') {
        if (InstallDay !== install_day || InstallTime !== install_time) {
          this.tableData[0].error.push('self')
        }
      }
      let _install_value = ''
      if (install_value === 'AutoInstall') {
        _install_value = this.$t('check.WSUS.js_3_s')
        const adateStr = this.getDayName(install_day) + ' ' + (install_time ? install_time + ':00' : '')
        _install_value += adateStr !== ' ' ? ' （' + adateStr + '）' : ''
      } else if (install_value === 'NotifyInstall') {
        _install_value = this.$t('check.WSUS.js_5_s')
      } else if (install_value === 'NotifyDownload') {
        _install_value = this.$t('check.WSUS.js_7_s')
      }
      this.tableData[0].admin = _install_value

      this.tableData[1].admin = install_WUServer
      if (install_WUServer) {
        const wusArr = install_WUServer.split(',')
        if (!wusArr.includes(WUServer)) {
          this.tableData[1].error.push('self')
        }
      } else if (WUServer) {
        this.tableData[1].error.push('self')
      }

      this.tableData[2].admin = install_DetectionFrequency ? install_DetectionFrequency + this.$t('check.WSUS.js_9_s') : ''
      if (install_DetectionFrequency !== DetectionFrequency) {
        this.tableData[2].error.push('self')
      }

      let reboot_value = install_NoAutoReboot
      if (reboot_value === 'false') {
        reboot_value = this.$t('check.WSUS.js_11_s')
      } else {
        reboot_value = this.$t('check.WSUS.js_12_s')
      }
      this.tableData[3].admin = reboot_value

      if (NoAutoReboot !== install_NoAutoReboot) {
        this.tableData[3].error.push('self')
      }
    },
    getDayName(n) {
      var str = ''
      switch (n) {
        case '0':
          str = this.$t('check.WSUS.h_16_rd')
          break
        case '1':
          str = this.$t('check.WSUS.h_17_rd')
          break
        case '2':
          str = this.$t('check.WSUS.h_18_rd')
          break
        case '3':
          str = this.$t('check.WSUS.h_19_rd')
          break
        case '4':
          str = this.$t('check.WSUS.h_20_rd')
          break
        case '5':
          str = this.$t('check.WSUS.h_21_rd')
          break
        case '6':
          str = this.$t('check.WSUS.h_22_rd')
          break
        case '7':
          str = this.$t('check.WSUS.h_23_rd')
          break
        default:
          str = ''
          break
      }
      return str
    },
    cellStyleHandle({ row, columnIndex }) {
      if (columnIndex === 1 && row.error.includes('self') && row.self) {
        return 'red-color'
      }
      return ''
    },
    emptyLine(row, column, cellValue, index) {
      if (!cellValue) {
        return '-'
      }
      return cellValue
    }
  }
}
</script>
<style lang="scss">
#WSUS-current-computer {
  .model-content {
    padding-left: 0;
    .tit-info {
      padding-left: 16px;
    }
  }
  .header-column {
    .cell {
      color: $default-color;
      background: $default-bg;
    }
  }
  .red-color{
    .cell{
      color: red
    }
  }
}
</style>
