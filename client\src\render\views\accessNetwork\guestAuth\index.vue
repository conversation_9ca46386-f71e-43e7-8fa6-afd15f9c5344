<!-- 来宾接待，来宾认证,暂无来宾认证权限 -->
<template>
  <div v-loading="loading" class="guest-auth-wrap">
    <layout-state
      v-if="(!clientInfo.online && !isDot1xMode && !isKnockPort) || isForbid"
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-txt="state['stateBtnTxt']"
      @stateBtnHandle="state['stateBtnHandle']"
    />
    <!-- style="margin-top: 50px;" -->
    <template v-else>
      <component :is="currentComponent" v-if="currentComponent" />
    </template>
  </div>
</template>

<script>
import guestAuth from './guestAuth/index.vue'
import guestApplyAudit from './guestApplyAudit/index.vue'
import guestReceive from './guestReceive/index.vue'
import noReceivePermission from './guestReceive/noReceivePermission.vue'
import { mapGetters, mapMutations } from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import authIndex from '@/render/utils/auth/index'
import { EventBus } from '@/render/eventBus'
import state from '@/render/components/layout/state'
import authUtils from '@/render/utils/auth/index'
const serverNotWorking = require('@/render/assets/serverNotWorking.png') // 网络连接不通的问题
const forbid = require('@/render/assets/stateIllustration/forbid.png')

export default {
  components: { guestAuth, guestApplyAudit, guestReceive, noReceivePermission, layoutState: state },
  data() {
    return {
      loading: true,
      currentComponent: '',
      serverNotWorking: serverNotWorking,
      state: {
        stateImg: serverNotWorking,
        stateMsg: this.$t('auth.unableConnectToNetwork'),
        stateBtnTxt: this.$t('refresh'),
        stateBtnHandle: 'refresh'
      },
      isForbid: false
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'serveEntiretyConfig',
      'computeNetAccessStatus',
      'authInfo'
    ]),
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    // 是否是敲端口模式
    isKnockPort() {
      return _.get(this.clientInfo, 'webSlot.isKnockPort', false)
    }
  },
  async created() {
    // 禁止接入且不在来宾专属IP段内
    this.isForbid = this.forbidNextStatus()
    if (this.isForbid) {
      const state = {
        stateImg: forbid,
        stateMsg: this.$t('oWelcome.Web_OnAccesss'),
        stateBtnHandle: 'refresh'
      }
      this.loading = false
      this.state = state
      return
    }
    // 再一次判断下是否开启来宾认证和是否对单台终端禁用来宾
    Promise.all([commonUtil.server(), commonUtil.detail()]).then(() => {
      this.init()
    }).finally(() => {
      this.loading = false
    })
  },
  mounted() {
    // 监听自助申请提交完后的消息，当前组件切换为自主申请审核页面
    EventBus.$on('setApplyAudit', (args) => {
      if (args) {
        this.currentComponent = 'guestApplyAudit'
      } else {
        this.currentComponent = 'guestAuth'
      }
    })

    // 监听是否是ztp模式下的noAuth认证方式
    EventBus.$on('ztpNoAuth', () => {
      this.currentComponent = 'noReceivePermission'
    })
  },
  methods: {
    ...mapMutations(['setClientInfo']),
    init() {
      // 主要判断当前是否入网，已入网后判断是否具有来宾接待权限
      const status = this.computeNetAccessStatus()
      // 如果查询不到当前的入网状态，重新更新一遍
      if (_.isNull(_.get(status, 'isAccess')) && _.get(this.clientInfo, 'online', false)) {
        const netRet = commonUtil.netRet()
        if (_.get(netRet, 'deviceStatus') !== undefined) {
          this.init()
        }
      } else {
        if (_.get(status, 'isAccess') && this.$route.path === '/access/guestReceive') {
          this.currentComponent = 'guestReceive'
        } else {
          if (authUtils.isOpenGuestAuth()) {
            this.currentComponent = 'guestAuth'
          } else {
            // 无来宾认证权限，显示……
            this.currentComponent = 'noReceivePermission'
          }
        }
      }
    },
    refresh() {
      window.location.reload()
    },
    // 场景禁用
    forbidNextStatus() {
      return parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAccess', 1)) === 0
    }
  }

}
</script>
<style lang="scss" scoped>
.guest-auth-wrap {
  height: 100%;
  text-align: center;
}
</style>
