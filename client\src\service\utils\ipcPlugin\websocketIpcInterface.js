import { sleep } from '@/render/utils/global'
import { isEmpty, isFunction } from 'lodash'
import { parseXmlRequest, unpareseXmlResponese } from './utils/formate'
import { isQtWebKit } from './utils'
import qs from 'qs'
import agent<PERSON>pi from '@/service/api/agentApi'
const wsIpcInterface = {
  wsInstance: null,
  wsUrl: 'ws://127.0.0.1:36610',
  reConnectTaskId: null,

  // wsUrl: 'ws://************:36610 ',
  timeout: 500,
  reTry: 3,
  init: async function() {
    if (isQtWebKit()) {
      return false
    }

    for (let index = 1; index <= this.reTry; index++) {
      try {
        this.wsInstance = await this.tryWebSocket()
      } catch (error) {
        console.error(error)
      }
      if (!this.wsInstance) {
        await sleep(index * 500)
        console.log('websocket连接失败')
      } else {
        break
      }
    }
    return this
  },
  /**
   *尝试单次连接
   * 失败返回false
   * 成功返回实例
   */
  tryWebSocket() {
    return new Promise((resolve, reject) => {
      const instance = new WebSocket(this.parseWsUrl())

      const timeoutTaskId = setTimeout(() => {
        reject('连接超时')
      }, this.timeout)

      // 判断是否连接成功
      instance.onopen = () => {
        clearTimeout(timeoutTaskId)
        this.initOnRecvMsg()
        this.initWsListen(instance)
        console.log('websocket on open,连接成功')
        resolve(instance)
      }

      instance.onerror = (event) => {
        clearTimeout(timeoutTaskId)
        reject(event)
      }

      // 断线后,进行重连
      instance.onclose = () => {
        console.error('websocket断开连接,尝试重连接--')
        this.wsCloseEvent()
        this.wsInstance = null
        this.reConnectTaskId = null
        clearTimeout(this.reConnectTaskId)
        // 进行重连
        this.reConnectTaskId = setTimeout(async() => {
          console.log('开始重连')
          this.wsInstance = await this.tryWebSocket()
        }, 3000)
      }
    })
  },

  parseWsUrl() {
    const query = qs.parse(location.search.substring(1))
    const ws = _.get(query, 'ws', '')
    if (ws) {
      return 'ws://' + ws + ':36610'
    }

    return this.wsUrl
  },

  initOnRecvMsg() {
    window.onRecvMsg = (msg, serial, type) => {
      if (isEmpty(msg)) {
        console.error('msg不能为空')
        throw new Error('msg不能为空')
      }

      const { replyStr, headStr, requestStr } = unpareseXmlResponese(unescape(msg))

      let head = {}
      try {
        head = JSON.parse(headStr)
      } catch (error) {
        console.error('head头格式错误')
        console.error('return :' + msg)
      }

      // 分为请求回应和主动通知两种类型的消息
      // ToRequestWeb是客户端主动通知我们的消息;
      // ResponseToWeb是客户端被动响应我们的请求消息
      try {
        // 屏蔽掉server/info,太多一坨了
        if (requestStr.includes('server/info') ||
          requestStr.includes('server/parameter')
        ) {
          agentApi.elelWriteLog('收到响应,serial=' + serial)
          agentApi.elelWriteLog('server.info和server.parameter日志过长,省略...')
        } else {
          agentApi.elelWriteLog('收到响应,serial=' + serial)
          agentApi.elelWriteLog(requestStr)
          agentApi.elelWriteLog(replyStr)
        }
      } catch (error) {
        console.log('写日志失败')
      }

      if (_.get(head, 'result.Head.TradeCode') === 'AgtReqstAui') {
        const cb = window.ToRequestWeb
        if (isFunction(cb)) {
          cb(_.get(head, 'result.Head.WhatToDo'), _.get(head, 'result.Head.WhatToDo'), requestStr)
        }
      } else {
        const cb = window.ResponseToWeb
        if (isFunction(cb)) {
          cb(serial, 'end', replyStr)
        }
      }
    }
  },
  // 初始化成功后,绑定接收消息事件
  initWsListen(wsInstance) {
    // 这里客户端返回的还是十几年前的旧格式"onRecvMessage("")"
    wsInstance.onmessage = (message) => {
      try {
        // eslint-disable-next-line no-eval
        eval(message.data)
      } catch (error) {
        console.error('返回数据格式有错误')
        console.error(error)
        console.error(message)
      }
    }
  },

  send({ module, action, strSerial, data = '', reject }) {
    if (this.wsInstance) {
      const dataStr = parseXmlRequest({ module, action, strSerial, data })
      const message = `fromassui?type=end&urlencode=0&serial=${strSerial}&data=${dataStr}`
      this.wsInstance.send(message)
      try {
        agentApi.elelWriteLog('发起请求:serial=' + strSerial)
        agentApi.elelWriteLog(message)
      } catch (error) {
        console.log('写日志失败')
      }
    } else {
      console.log('wsIntacne为空')
      reject('websocket尚未建立连接')
    }
  },

  // 挂载在window上
  // ToRequestWeb是客户端主动通知我们的消息;
  // ResponseToWeb是客户端被动响应我们的请求消息
  on(module, event, callback) {
    module = window
    module[event] = callback
  },

  // @todo
  // 把等待中的任务全部重置失败
  wsCloseEvent() {

  }
}

export default wsIpcInterface
