
// 非正式环境模拟QT的环境。
// 因为非QT下的chromium环境，拿不到window.qt变量(该变量通过QT使用V8 template注入chromiun内核)会造成错误
export const imitateQTEnv = function() {
  console.log(window.qt)
  // if (!__DEV__) {
  //   throwErr(window && window.qt && window.qt.webChannelTransport, "'qt' 或者 'qt.webChannelTransport' 需要被 QtWebEngine 注入到页面中！")
  // }

  if (!isQtWebChannel()) {
    window.qt = {
      webChannelTransport: {
        send() {
          logInfo('QWebChannel simulator activated !')
        },
        onmessage() {

        }
      }
    }
  }
}

// 判断是否是使用websocket和客户端通讯
export const isWebScoketEnv = () => {
  return typeof window.eleIPC !== 'undefined' || window.location.href.includes('isWsIPC=1')
}

// 判断是否在electron环境内
export const isElectron = () => {
  return typeof window.eleIPC !== 'undefined'
}

// 判断是否是QTWebkit为了兼容xp客户端，客户端把QtWebEngine更换为QTWebkit
export const isQtWebKit = (function() {
  return navigator.userAgent.includes('WebKit') && typeof window.UIPlatform_Window !== 'undefined'
})

/* eslint-disable no-undef */
export const isQtWebChannel = (function() {
  return navigator.userAgent.includes('QtWebEngine') && typeof window.qt !== 'undefined'
})

export const __DEV__ = process.env.NODE_ENV === 'development'

export function throwErr(condition, msg) {
  if (!condition) {
    throw new Error(`[ASSERT]: ${msg || condition}`)
  }
}

export function logInfo(msg) {
  console.log(`%c${msg}`, 'font-weight: bold;')
}

export function dispathSender(QtServer) {
  return ({ module, action, strSerial, data = '', promise = null }) => {
    return new Promise((resolve, reject) => {
      if (promise && promise.reject && promise.resolve) {
        resolve = promise.resolve
        reject = promise.reject
      }

      if (!Object.keys(QtServer).includes(module)) {
        return reject(new Error('[SENDER]: 该module' + module + ' 不存在 !'))
      }

      if (!Object.keys(QtServer[module]).includes(action)) {
        return reject(new Error('[SENDER]: 该action' + action + ' 不存在 !'))
      }

      if (typeof QtServer[module][action] !== 'function') {
        return reject(
          new Error(
            typeof QtServer[module][action].connect === 'function'
              ? `[SENDER]: ${action} 不是一个QT信号或者QT方法`
              : `[SENDER]:  action : ${action} 不是一个QT函数 !`
          )
        )
      }
      if (strSerial === -1) {
        QtServer[module][action](data, resolve)
      } else {
        QtServer[module][action](strSerial, data, resolve)
      }
    })
  }
}

export function addListener(QtServer) {
  return (module, event, callback) => {
    if (!_.get(QtServer, `${module}.${event}`)) {
      throw new Error(`[LISTENER]: ${event} is not a Qt signa!`)
    }

    if (typeof QtServer[module][event].connect !== 'function') {
      throw new Error(`[LISTENER]: No Connect Function!`)
    }
    QtServer[module][event].connect(callback)
  }
}

export function removeListener(QtServer) {
  return (module, event, callback) => {
    if (!Object.keys(QtServer).includes(event)) {
      return reject(new Error('[LISTENER]: Unknown event name!'))
    }

    if (!Object.keys(QtServer[event]).includes('disconnect')) {
      return reject(
        new Error(`[LISTENER]: ${event} is not a Qt signa!`)
      )
    }

    if (typeof QtServer[event].disconnect !== 'function') {
      return reject(
        new Error(`[LISTENER]: No Disconnect Function!`)
      )
    }
    QtServer[module][event].disconnect(callback)
  }
}

