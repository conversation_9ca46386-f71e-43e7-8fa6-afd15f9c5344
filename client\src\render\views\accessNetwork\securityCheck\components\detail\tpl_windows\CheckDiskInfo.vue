<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" @click="collapseFlag = !collapseFlag" />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.diskInfo.h_3_rs") }}
          </p>
          <div v-for="(item,index) in diskInfo" :key="item.VolumeName" class="disk-info">
            <div class="box-left">
              <i class="iconfont icon-cipan" />
              <div class="disk-name">{{ item.VolumeName }}<span v-if="item.IsSystemPartion === 'Yes'">{{ $t("check.diskInfo.js_3_s") }}</span></div>
            </div>
            <div class="box-center">
              <div class="proccess-wrapper" :class="item.color">
                <div class="proccess-val">{{ item.Percentage }}%</div>
                <div class="proccess" :class="item.color" :style="{ width: item.Percentage + '%'}" />
              </div>
              <div class="optional-item disk-size">
                {{ $t("check.diskInfo.h_5_rd") }}<span>{{ item.TotalSpace }}/{{ item.FreeSpace }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.diskInfo.h_8_rd") }}<span>{{ item.FileSystem }}</span>
              </div>
              <div class="button-wrapper">
                <button v-if="deviceType!=='linux'" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.diskInfo.js_5_s") }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  name: 'CheckDiskInfo',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      imgSrc: 10,
      diskInfo: []
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkItemData() {
      return this.checkData
    },
    deviceType() {
      return _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    }
  },
  mounted() {
    this.getDiskInfo()
  },
  methods: {
    getDiskInfo() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return []
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        const TotalSpace = parseFloat(item.TotalSpace)
        const FreeSpace = parseFloat(item.FreeSpace)
        const UseSpace = TotalSpace - FreeSpace
        const rate = Math.ceil(UseSpace / TotalSpace * 100)
        item.Percentage = isNaN(rate) ? 0 : rate
        item.color = '' // 处理条形颜色
        item.hasFixed = this.checkData.hasFixed
        const FreeRate = 100 - rate
        if (FreeRate <= 10) {
          item.color = 'error'
        } else if (FreeRate > 10 && FreeRate <= 15) {
          item.color = 'warning'
        }
      })
      this.diskInfo = list
    },
    // 修复方法
    async fixHandle(item, index) {
      if (item.hasFixed) {
        return
      }
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          VolumeName: item.VolumeName
        },
        RepairType: 0,
        CreateProgress: 1
      }
      const ret = await this.submitHandle({
        params,
        CheckItem: item,
        showTip: false,
        tip: ''
      })
      const errcode = _.get(ret, 'ASM.errcode')
      const errmsg = _.get(ret, 'ASM.errmsg')
      if (errcode === '0') {
        item.hasFixed = true
        this.$message({
          message: this.$t('check.diskInfo.js_5_s') + this.$t('check.success'),
          type: 'success'
        })
      } else {
        this.$message({
          message: errmsg || this.$t('check.diskInfo.js_5_s') + this.$t('check.fail'),
          type: 'error'
        })
      }
      this.$set(this.diskInfo, index, item)
    }
  }
}
</script>
<style lang="scss" scoped>
.disk-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  .box-left {
    flex-shrink: 0;
    text-align: center;
    color: $title-color;
    position: relative;
    width: 62px;
    .iconfont {
      font-size: 32px;
      color: $default-color;
      position: absolute;
      top: 5px;
      left: 50%;
      transform: translateX(-50%);
    }
    .disk-name {
      font-size: 12px;
      font-weight: 400;
      color: $title-color;
      line-height: 17px;
      padding-top: 44px;
      word-break: break-all;
    }
  }
  .box-center {
    flex: 1;
    padding-left: 16px;
    position: relative;
    .proccess-wrapper {
      width: 260px;
      height: 16px;
      position: relative;
      border: 1px solid $green;
      margin-bottom: 12px;
      .proccess {
        height: 100%;
        background: $green;
        border:none;
      }
      .error {
        background: $red;
      }
      .warning {
        background: $yellow;
      }
      .proccess-val {
        position: absolute;
        top: 0;
        line-height: 14px;
        height: 14px;
        width: 100%;
        text-align: center;
        left: 0;
        color: $title-color;
        font-size: 12px;
      }
    }
    .error {
      border: 1px solid $error;
    }
    .warning {
      border: 1px solid $waring;
    }
    .button-wrapper {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .disk-size{
    margin-bottom: 8px;
  }
}
</style>
