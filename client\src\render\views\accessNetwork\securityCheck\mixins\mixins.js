export default {
  data() {
    return {
    }
  },
  methods: {
    // 数据格式化
    dataFormat(list) {
      const checkCount = {
        success: 0,
        waring: 0,
        error: 0
      }
      const arr = [],
        errObj = {
          children: [],
          showChildren: true,
          classResultType: 1
        },
        warnObj = {
          children: [],
          showChildren: true,
          classResultType: 2
        },
        successObj = {
          children: [],
          showChildren: false,
          classResultType: 3
        }
      let type = 2 // 总结果显示类型
      list.forEach((v) => {
        switch (v.itemResultType) {
          case 2:
            errObj.children.push(v)
            checkCount.error++
            break
          case 1:
            warnObj.children.push(v)
            checkCount.waring++
            break
          case 0:
            successObj.children.push(v)
            checkCount.success++
            break
        }
      })
      if (errObj.children.length > 0 || warnObj.children.length > 0) {
        type = 3
      }
      if (errObj.children.length > 0) {
        arr.push(errObj)
        type = 3
      }
      if (warnObj.children.length > 0) {
        arr.push(warnObj)
        if (errObj.children.length === 0) {
          type = 4
        }
      }
      if (successObj.children.length > 0) {
        arr.push(successObj)
      }
      console.log(arr)
      return { data: arr, resultType: type, checkCount }
    },
    // 设置滚动条
    setScrollPostion(dom, y) {
      if (!dom) {
        return
      }
      if (dom.scrollHeight > dom.clientHeight || dom.offsetHeight > dom.clientHeight
      ) {
        dom.scrollTop = y
      }
    },
    checkResultHandle(result = {}, policy) {
      // 是否为关键检查项
      const isKeyItem = policy.Key === 'Yes'
      if (result.CheckType) {
        // 是否安检通过
        const isPass = result.CheckType.Result === 'Yes'
        policy.itemResultType = isPass ? 0 : isKeyItem ? 2 : 1
      } else {
        policy.itemResultType = isKeyItem ? 2 : 1
      }
      policy.checkStep = 2
      policy.CheckResult = result // 存储安检结果
    }
  }
}

