<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
export default {
  name: 'CheckIPRange',
  components: {
    checkResult
  },
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    }
  }
}
</script>

