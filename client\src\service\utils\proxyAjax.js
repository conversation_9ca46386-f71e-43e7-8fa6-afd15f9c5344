/*
 * @Author: <EMAIL>
 * @Date: 2021-08-04 15:36:51
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-07-14 14:17:14
 * @Description: 运行在qt中的js请求服务器端的接口，实际都是通过qt帮忙代理转发的，
 *               这里把这个代理转发的函数进行统一封装。也写成get和post方法,是为了和浏览器端的进行统一
 */
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import _ from 'lodash'
import Vue from 'vue'
import { Message } from 'element-ui'
import processController from '@/render/utils/processController'
import axios from 'axios'
import store from '@/render/store'
import commonUtil from '@/render/utils/bussiness/commonUtil'

const proxyAjax = {
  timer: null,
  /**
   *
   * @param {string} url
   * @param {object} header
   * @param {object} config  (showError:是否显示错误)
   * @param {string} timeout  (通知小助手代理使用get请求，并指定请求超时时间为30S)
   * @returns
   */
  get(url, config = { showError: true }, timeout = 30) {
    return new Promise((resolve, reject) => {
      Vue.prototype.$ipcSend('AssUIPluginWebProxy', 'WebCall_HTTPRequest', {
        strMethod: 'get',
        strURL: url,
        strTimeOut: timeout,
        strBody: {}
      }, config).then(res => {
        this.handleError(res, config)
        resolve(res)
      })
    })
  },

  post(url, params = {}, config = { showError: true }, timeout = 30) {
    return new Promise((resolve, reject) => {
      Vue.prototype.$ipcSend('AssUIPluginWebProxy', 'WebCall_HTTPRequest', {
        strMethod: 'post',
        strURL: url,
        strTimeOut: timeout,
        strBody: params
      }, config).then(res => {
        this.handleError(res, config)
        resolve(res)
      })
    })
  },

  axiosGet(baseURL, path, timeout = 100) {
    return new Promise((resolve, reject) => {
      var instance = axios.create({
        baseURL: baseURL,
        timeout: timeout
      })
      instance.get(path)
        .then(function(res) {
          resolve(res.data)
        })
        .catch(function(error) {
          resolve({ errcode: 11100003, errmsg: error.message })
          console.log(baseURL + path + ' axios error:' + error.message)
        })
    })
  },

  // 处理公共的错误
  handleError(res, config) {
    if (_.get(config, 'showError')) {
      if (parseInt(_.get(res, 'errcode')) !== 0 && _.get(res, 'errmsg') && !_.isEmpty(_.get(res, 'errmsg'))) {
        Message({
          message: _.get(res, 'errmsg'),
          type: 'error'
        })
      }
    }
    if (_.get(config, 'handleLoginDate', true)) {
      this.LoginDateHandle(res)
    }
    // 特性错误码强制升级
    if (this.isForceUpdate(res)) {
      processController.set('/access/updateClient')
    }
  },

  // 设备类型：mac,windows,linux
  formatUrl(url, module = 'access', type = 'json', assignDeviceType = null) {
    let deviceType = 'windows'
    if (assignDeviceType) {
      deviceType = assignDeviceType
    } else {
      deviceType = _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    }
    return `/${module}/1.0/${deviceType}/${type}/` + url
  },

  // 获取请求url
  formatUrlPrefix() {
    const basic = store.getters.clientInfo.basic
    const protocol = _.get(basic, 'WebProtocol', '')
    const serverIP = _.get(basic, 'ServerIP', '')
    const webPort = _.get(basic, 'WebPort', '')
    if (serverIP === '') {
      return ''
    }
    return protocol + '://' + serverIP + ':' + webPort
  },

  // 登录过期
  LoginDateHandle(res) {
    if (_.get(res, 'errcode') === '21120055') {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        clearTimeout(this.timer)
        this.timer = null
        console.log('登录过期')
        commonUtil.setLoginRet({ token: '', UserID: '', LoginRet: '0' })
        processController.set('/access/auth')
      }, 1000)
    }
  },

  isForceUpdate(res) {
    return _.get(res, 'errcode') === '21101017'
  }
}

export default proxyAjax

