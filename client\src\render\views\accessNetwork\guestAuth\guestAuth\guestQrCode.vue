<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-15 20:59:40
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-09-25 18:05:09
 * @Description: 来宾认证扫码
-->
<template>
  <div id="f-guest-qr-code">
    <div class="scan-code-wrap">
      <div class="u-ewm-box">
        <img id="ui-guest-auth-img-guest_img" :src="qrCodeSrc" alt="">
      </div>
      <p class="hint" v-html="tips" />
    </div>
  </div>
</template>

<script>
import proxyApi from '@/service/api/proxyApi'
import { mapState } from 'vuex'
import { EventBus } from '@/render/eventBus'
import processController from '@/render/utils/processController'
import { sleep } from '@/render/utils/global'
export default {
  data() {
    return {
      qrCodeSrc: '',
      taskId: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig']),
    // 扫码的提示
    tips() {
      return this.$t('guestAuth.guest.info_21')
    }
  },
  created() {
    this.init()
  },
  beforeDestroy() {
    this.taskId = false
  },
  methods: {
    async init() {
      try {
        let ret
        if (!_.get(this.clientInfo, 'detail.DeviceID')) {
          ret = await this.draw(_.get(this.clientInfo, 'detail.DevMAC'))
        } else {
          ret = await this.draw(_.get(this.clientInfo, 'detail.DeviceID'))
        }
        // @todo 翻译
        if (!_.get(ret, 'qrcodeUrl')) {
          throw new Error(this.$t('auth.markFail'))
        }
        this.qrCodeSrc = _.get(ret, 'qrcodeUrl')
        const scanRet = await this.expectScanRes(_.get(ret, 'guestId'))
        if (scanRet && !_.isEmpty(scanRet)) {
          // 往父组件发送信号
          EventBus.$emit('guestQrCodeScan', scanRet)
        }
      } catch (e) {
        this.$msg({ message: e.message, type: 'error' })
      }
    },
    // 绘制二维码图片
    async draw(Guestmacordevid) {
      var apiParam = {
        deviceid: Guestmacordevid
      }
      const ret = await proxyApi.drawQrCode(apiParam)
      if (!_.get(ret, 'errcode') || parseInt(_.get(ret, 'errcode')) !== 0 || !_.get(ret, 'data.qrcodeUrl')) {
        throw new Error(_.get(ret, 'errmsg') || 'fail to draw qr code')
      }
      return _.get(ret, 'data')
    },
    // 定时获取用户是否扫码
    async expectScanRes(guestId) {
      let scanRet = false
      this.taskId = true
      while (scanRet === false && this.taskId) {
        await sleep(2000)
        scanRet = await this.checkScaned(guestId)
      }
      return scanRet
    },
    // 定时调用的方法,检测二维码是否被扫描,被扫描则通过检测以来宾身份入网
    async checkScaned(guestId) {
      const apiParam = {
        GuestId: guestId
      }
      const ret = await proxyApi.getScanGuestRes(apiParam)
      if (!_.get(ret, 'errcode') || parseInt(_.get(ret, 'errcode')) !== 0) {
        throw new Error(_.get(ret, 'errmsg') || 'query failed')
      }

      // 如果返回true则代表已经扫码，然后调用获取设备信息接口
      if (_.get(ret, 'data.RoleID') !== undefined) {
        return await this.getRes(guestId)
      }

      return false
    },
    async getRes(guestId) {
      const apiParam = {
        action: 'oppo',
        type: 'Guest',
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        GuestId: guestId
      }
      const ret = await proxyApi.authIndex(apiParam, { showError: false })

      if (!_.get(ret, 'errcode') || parseInt(_.get(ret, 'errcode')) !== 0) {
        this.$message.error(_.get(ret, 'errmsg') || this.$t('auth.authFail'))
        if (parseInt(_.get(ret, 'errcode')) === 21120068) {
          processController.set('/access/message')
        }
        return {}
      }

      return ret
    }
  }
}
</script>

<style lang="scss">
    #f-guest-qr-code {
    .scan-code-wrap {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        .u-ewm-box {
          margin: 10px 0 24px;
          width: 240px;
          height: 240px;
          background: url(../../../../assets/frame.png) no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            opacity: 0.7;
            width: 200px;
            height: 200px;
          }
        }
        .hint {
          color: $default-color;
          line-height: 20px;
        }
      }
    }
</style>
