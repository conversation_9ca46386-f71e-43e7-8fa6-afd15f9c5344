<template>
  <div v-loading="loading" class="receive-wrapper">
    <!-- 申请模块 -->
    <template v-if="isApply">
      <!-- tab切换 -->
      <ul class="tab-wrapper">
        <li v-if="singlePermision" :class="['tab-item', activeTab === 0 ? 'active-item' : '' ]">
          <span id="ui-guest-receive-receive-span-single_recept" @click="changeTab(0)">{{ $t('guestAuth.guest.info_39') }}</span>
        </li>
        <li v-if="IsNeedAppoint" :class="['tab-item', activeTab === 1 ? 'active-item' : '' ]">
          <span id="ui-guest-receive-receive-span-appoint" @click="changeTab(1)">{{ $t('guestAuth.guest.info_84') }}</span>
        </li>
        <li v-if="teamPermision" :class="['tab-item', activeTab === 2 ? 'active-item' : '' ]">
          <span id="ui-guest-receive-receive-span-team_recept" @click="changeTab(2)">{{ $t('guestAuth.guest.info_40') }}</span>
        </li>
      </ul>

      <!-- 单个、团队接待表单 -->
      <div class="active-wrapper">
        <component :is="activeType" v-if="regionList.length" :prop-regions="regionList" @applySuccess="successHandle" />
      </div>

    </template>

    <!-- 生成的来宾码、二维码 -->
    <applyResult v-else :result="codeData" @changeMode="isApply = true" />

  </div>
</template>
<script>
import singleReceive from './components/singleReceive.vue'
import teamReceive from './components//teamReceive.vue'
import applyResult from './components/applyResult.vue'
import visitorReservation from './components/visitorReservation.vue'
import { mapState } from 'vuex'
export default {
  components: {
    singleReceive,
    teamReceive,
    applyResult,
    visitorReservation
  },
  data() {
    return {
      activeTab: 0,
      isApply: true,
      codeData: {},
      loading: false,
      regionList: [],
      activeTabList: ['singleReceive', 'visitorReservation', 'teamReceive']
    }
  },
  computed: {
    ...mapState(['clientInfo', 'serveEntiretyConfig']),
    activeType() {
      return this.activeTabList[this.activeTab]
    },
    singlePermision() {
      const { NetCode } = _.get(this.clientInfo, 'accessStatus', {})
      return parseInt(NetCode) === 1
    },
    teamPermision() {
      const { AllowGuestTeam } = _.get(this.clientInfo, 'accessStatus', {})
      return parseInt(AllowGuestTeam)
    },
    IsNeedAppoint() {
      const { IsNeedAppoint } = _.get(this.clientInfo, 'accessStatus', {})
      return parseInt(IsNeedAppoint) === 1
    }
  },
  created() {
    this.getRegionList()
    this.initTab()
  },
  methods: {
    initTab() {
      this.activeTab = this.singlePermision ? 0 : this.IsNeedAppoint ? 1 : 2
    },
    changeTab(val) {
      if (this.activeTab === val) {
        return
      }
      this.activeTab = val
    },
    successHandle(data) {
      this.codeData = data
      this.isApply = false
    },
    getRegionList() {
      const list = _.get(this.serveEntiretyConfig, 'sceneConfig.sceneinfo.allocationRegionData', [])
      this.regionList = list.map(item => ({ id: item.RID, label: item.RegionName }))
    }
  }
}
</script>
<style lang="scss" scoped>
  .receive-wrapper{
    height: 100%;
    .tab-wrapper{
      padding-top: 24px;
      margin-bottom: 22px;
      width: 100%;
      display: flex;
      border-bottom: 1px solid $line-color;
      .tab-item{
        padding: 6px 16px;
        line-height: 20px;
        font-size: 14px;
        color: $title-color;
        margin-right: 4px;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid $line-color;
        border-bottom: none;
        background: $gray-5;
        cursor: pointer;
        position: relative;
        bottom: -1px;
        &:hover{
          background: $line-color;
          border-color: $gray-2;
        }
      }
      .active-item{
        background: $green;
        color: $light-color;
        border-color: $green;
        &:hover{
          background: $green;
          border-color: $green;
        }
      }
    }
    ::v-deep.active-wrapper{
      height: calc(100% - 73px);
      .el-form-item__label{
        padding-right: 4px;
      }
    }
  }
</style>
