<template>
  <div v-loading="!inited && loading" class="msg-center-content">
    <div class="msg-self-header">
      <span class="title">{{ $t('msgCenter.title') }}</span>
      <div class="right">
        <span class="all-read" @click="allRead">
          <i class="iconfont icon-quanbuyidu" />
          {{ $t('msgCenter.read') }}
        </span>
        <i class="el-icon-close" @click="closeHandle" />
      </div>

    </div>
    <empty v-if="inited && !tableData.length" class="msg-center-empty" />
    <el-table
      v-if="inited && tableData.length"
      v-loading="inited && loading"
      :data="tableData"
      border
      stripe
      height="calc( 100% - 120px)"
      style="width: 100%; margin-bottom: 24px"
      class="guest-formate-style-table"
    >
      <el-table-column
        prop="Title"
        show-overflow-tooltip
        :label="$t('msgCenter.msgContent')"
      >
        <template slot-scope="scope">
          <span
            :id="`ui-ztp-message--msg_${scope.row.NoticeID}`"
            :class="[
              'msg-title',
              parseInt(scope.row.Status) === 0 && !isClear ? 'red-tag' : '',
            ]"
          >
            {{ scope.row.Type === 'default' ? scope.row.Content :
              calcState(scope.row.ActionParams) === 1 ? $t('msgCenter.pass', {name: scope.row.Title}) : $t('msgCenter.refuse', {name: scope.row.Title}) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="SendUser"
        show-overflow-tooltip
        min-width="60"
        :label="$t('msgCenter.send')"
      />
      <el-table-column
        prop="InsertTime"
        width="190"
        :label="$t('msgCenter.sendTime')"
      />
    </el-table>
    <el-pagination
      v-if="inited && tableData.length"
      :current-page.sync="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size.sync="pageSize"
      :pager-count="5"
      class="public-pag-bag"
      layout="prev, pager, next, sizes, jumper"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="getList"
    >
      <!-- hide-on-single-page -->
    </el-pagination>
  </div>
</template>
<script>
import '@/render/styles/guestTable.scss'
import empty from '@/render/views/accessNetwork/guestAuth/guestReceive/components/audit/components/empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    empty
  },
  data() {
    return {
      inited: 0,
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      isClear: false,
      total: 0
    }
  },
  computed: {
    ...mapState(['clientInfo', 'msgCenter'])
  },
  mounted() {
    this.getList()
  },
  methods: {
    ...mapMutations(['setMsgCenter']),
    async getList() {
      this.loading = true
      const start = (this.currentPage - 1) * this.pageSize
      const ret = await proxyApi.getResMsg({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        start,
        limit: this.pageSize,
        isSetRead: 1
      })
      this.setMsgCenter({ ...this.msgCenter, ...{ unRead: 0 }})
      console.log('结果', ret.data.list)
      const data = ret.data.list
      console.log('结果11', data)
      this.tableData = data
      this.total = parseInt(ret.data.total)
      this.loading = false
      this.inited = true
    },
    closeHandle() {
      this.$emit('changeVisible', false)
    },
    allRead() {
      this.isClear = true
    },
    handleSizeChange(val) {
      console.log(val)
      this.currentPage = 1 // 当前页码
      this.pageSize = val
      this.getList()
    },
    calcState(data) {
      if (_.isObject(data)) {
        return parseInt(data.isPass) === 1
      } else {
        try {
          const _data = JSON.parse(data)
          return parseInt(_data.isPass)
        } catch (e) {
          console.error('数据格式错误')
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.msg-center-content {
  height: 100%;
  padding: 0 32px 34px 32px;
  .msg-self-header {
    display: flex;
    justify-content: space-between;
    padding: 24px 0;
    .title {
      font-weight: 600;
      color: $title-color;
      line-height: 20px;
      font-size: 16px;
    }
    .right{
      display: flex;
      align-items: center;
      .all-read {
        color: $--color-primary;
        display: flex;
        align-items: center;
        margin-right: 24px;
        cursor: pointer;
        i {
          font-size: 14px;
          margin-right: 6px;
        }
      }
      .el-icon-close{
        font-size: 16px;
        cursor: pointer;
        color: $disabled-color;
      }
    }
  }
  &::v-deep .el-table__header .el-table__cell {
    padding: 8px 0;
    color: $title-color;
  }
  &::v-deep .el-table__row .el-table__cell {
    padding: 9px 0;
  }
}
.icon-a-qingchushanchu-02 {
  color: $error;
  padding: 0 6px;
  cursor: pointer;
}
.type-tag {
  line-height: 22px;
  border-radius: 11px;
  background: $blue-1;
  color: $blue-2;
  padding: 0 8px;
  font-size: 13px;
}
.reserve-type {
  background: $green-1;
  color: $green-2;
}
.guest-m-confirm-dialog {
  .v-header {
    line-height: 45px;
    border-bottom: 1px solid $line-color;
    padding: 0 24px;
    font-size: 16px;
    font-weight: 600;
    color: $title-color;
    i {
      font-size: 16px;
      color: $yellow-1;
      margin-right: 6px;
      font-weight: 400;
    }
  }
  .outline-tips {
    padding: 34px 24px;
    line-height: 20px;
    color: $title-color;
    font-size: 14px;
  }
}
.msg-center-empty{
  height:calc(100% - 70px)
}
.red-tag{
  &::before{
    content: "";
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
    background: $error-1;
    margin-right: 1px;
  }
}
</style>
