/*
 * @Author: <EMAIL>
 * @Date: 2023-01-28 15:24:03
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-28 15:47:18
 * @Description: 主题颜色配置，主要是要兼容不支持css var属性的浏览器
 */
import cssVars from 'css-vars-ponyfill'
const initThemeColor = () => {
  cssVars({
    variables: {
      '--color-primary': '#536CE6',
      // 浅色色系主题
      '--color-primary-light-1': '#647be9',
      '--color-primary-light-2': '#7589eb',
      '--color-primary-light-3': '#8798ee',
      '--color-primary-light-4': '#98a7f0',
      '--color-primary-light-5': '#a9b6f3',
      '--color-primary-light-6': '#bac4f5',
      '--color-primary-light-7': '#cbd3f8',
      '--color-primary-light-8': '#dde2fa',
      '--color-primary-light-9': '#eef0fd',

      // 暗色色系主题
      '--color-primary-dark-1': '#4b61cf',
      '--color-primary-dark-2': '#4256b8',
      '--color-primary-dark-3': '#3a4ca1',
      '--color-primary-dark-4': '#32418a',
      '--color-primary-dark-5': '#2a3673',
      '--color-primary-dark-6': '#212b5c',
      '--color-primary-dark-7': '#192045',
      '--color-primary-dark-8': '#11162e',
      '--color-primary-dark-9': '#080b17',
      // 半透明主题色调
      '--color-primary-light-transparent-5': 'rgba(80, 145, 230, 0.5)'
    }
  })
}

export { initThemeColor }
