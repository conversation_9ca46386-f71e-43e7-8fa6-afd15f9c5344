<template>
  <div>
    <div id="ding_qrcode_login" :class="['u-wework-qr-img', authData.authFrom === 'addition' ? 'addition-style': '', ServerProxy?'selfQr':'', ServerProxy ? 'dingtalk-server-proxy' : '']" />
    <div id="qrcode" ref="ding_qrcode" @click="reloadDingTalkQr" />
    <div v-if="ServerProxy" class="tips">
      <div v-if="codeStatus==1" class="">{{ $t('auth.qrdefault') }} <a class="refresh" @click="reloadDingTalkQr">{{ $t('auth.refresh') }}</a></div>
      <div v-if="codeStatus==2" class="scaned">{{ $t('auth.scaned') }}</div>
      <div v-if="codeStatus==3" class="logining">{{ $t('auth.logining') }}</div>
      <div v-if="codeStatus==4" class="expire">{{ $t('auth.expire') }} <a class="refresh" @click="reloadDingTalkQr">{{ $t('auth.refresh') }}</a></div>
    </div><iframe id="dingTalk" :src="qrCodeSrc" style="display: none" />
  </div>
</template>
<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import qs from 'qs'
import qrCodeCommon from '@/render/utils/auth/qrCodeCommon'
import proxyAjax from '@/service/utils/proxyAjax'
import proxyApi from '@/service/api/proxyApi'
import { EventBus } from '@/render/eventBus'
import urlUtils from '@/render/utils/url'
import Qrcode from 'qrcodejs2'
import umx from '@/render/utils/um'
import { sleep } from '@/render/utils/global'
export default {
  props: {
    isQrcode: {
      type: Boolean,
      default: false
    },
    authData: { // 双因子认证时有值
      type: Object,
      default: function() {
        return {}
      }
    },
    bindData: { // 主账号绑定时有值
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      qrCodeUrl: 'https://login.dingtalk.com/login/qrcode.htm',
      authorizeUrl: 'https://oapi.dingtalk.com/connect/oauth2/sns_authorize',
      qrCodeSrc: '',
      taskId: '',
      username: '',
      password: '',
      indexParam: '',
      time: '',
      qrCheckCount: 0,
      redHost: '',
      ServerProxy: false, // 钉钉扫码模式，1 例外配置模式 2 无例外配置模式
      codeStatus: 1 // 钉钉二维码状态 1 正常 2 已扫描 3 用户确认 4 已过期
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'authInfo']),
    deviceid() {
      return _.get(this.clientInfo, 'detail.DeviceID') || _.get(this.clientInfo, 'basic.AgentID', 0)
    }
  },
  watch: {
    isQrcode(value) {
      if (value) {
        this.drawQrCode()
      } else {
        this.clearQrInterval()
      }
    }
  },
  mounted() {
    this.ServerProxy = _.get(this.serveEntiretyConfig, 'server.DingTalkConfig.ServerProxy', false)
    this.drawQrCode()
    EventBus.$on('revoke:refresh', this.drawQrCode)
  },
  beforeDestroy() {
    this.clearQrInterval()
    EventBus.$off('revoke:refresh', this.drawQrCode)
    if (typeof window.addEventListener !== 'undefined') {
      window.removeEventListener('message', this.handleMessage, false)
    } else if (typeof window.attachEvent !== 'undefined') {
      window.detachEvent('onmessage', this.handleMessage)
    }
  },
  methods: {
    // 绘制二维码
    drawQrCode() {
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      this.time = new Date().getTime()
      this.qrCheckCount = 0
      this.clearQrInterval()
      if (this.ServerProxy) { // 是否新模式
        this.reloadDingTalkQr()
        return
      }
      // appid参数优先使用组件传入
      const appid = this.authData.AppID || _.get(this.serveEntiretyConfig, 'server.DingTalkConfig.appIDScanner')
      const urlParam = {
        deviceid: this.deviceid,
        time: this.time
      }
      const redHost = qrCodeCommon.serverHost() || _.get(this.serveEntiretyConfig, 'server.ControlUrl')
      console.log('redHostwwww', redHost)
      const url = encodeURIComponent(redHost +
          proxyAjax.formatUrl('dingtalk/user?' + qs.stringify(urlParam, { encode: false })))

      this.indexParam = {
        appid,
        response_type: 'code',
        scope: 'snsapi_login',
        state: 'STATE',
        redirect_uri: url
      }
      const goto = encodeURIComponent(this.authorizeUrl + '?' + qs.stringify(this.indexParam, { encode: false }))

      this.DDLogin({
        id: 'ding_qrcode_login',
        goto: goto,
        style: 'border:none;background-color:#FFFFFF;',
        width: 260,
        height: 300
      })

      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', this.handleMessage, false)
      } else if (typeof window.attachEvent !== 'undefined') {
        window.attachEvent('onmessage', this.handleMessage)
      }
    },
    // 重新加载
    async reloadDingTalkQr() {
      this.codeStatus = 1
      const appid = _.get(this.serveEntiretyConfig, 'server.DingTalkConfig.appIDScanner')
      const pdmToken = umx.getToken()
      const params = {
        bizScene: 'http_third_party',
        sceneId: appid
      }
      const qrcodeRes = await proxyApi.getDingTalkQrCode(params)
      console.log(qrcodeRes)
      const qrCode = qrcodeRes.result
      console.log(qrCode)
      this.$refs.ding_qrcode.innerHTML = ''
      const urlParam = {
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        time: this.time
      }
      const redHost = qrCodeCommon.serverHost() || _.get(this.serveEntiretyConfig, 'server.ControlUrl')
      const url = encodeURIComponent(redHost +
          proxyAjax.formatUrl('dingtalk/user?' + qs.stringify(urlParam, { encode: false })))

      new Qrcode('qrcode', {
        width: 220, // 设置宽度，单位像素
        height: 220, // 设置高度，单位像素
        text: 'https://oapi.dingtalk.com/connect/qrcommit?showmenu=false&code=' + qrCode + '&appid=' + appid + '&redirect_uri=' + url,
        colorDark: '#000', // 二维码颜色
        colorLight: '#ffffff', // 二维码背景色
        correctLevel: 3
      })
      console.log('二维码绘制完成')
      this.queryDingTalkScanProcess(appid, pdmToken, qrCode)
    },
    queryDingTalkScanProcess(appid, pdmToken, qrCode) {
      const urlParam = {
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        time: this.time
      }
      const redHost = qrCodeCommon.serverHost() || _.get(this.serveEntiretyConfig, 'server.ControlUrl')
      const url = encodeURIComponent(redHost +
          proxyAjax.formatUrl('dingtalk/user?' + qs.stringify(urlParam, { encode: false })))
      this.indexParam = {
        appid,
        response_type: 'code',
        scope: 'snsapi_login',
        state: 'STATE',
        redirect_uri: url
      }
      // const authorizeUrl = urlUtils.getBaseIPPort() + '/connect/oauth2/sns_authorize'

      const goto = this.authorizeUrl + '?' + qs.stringify(this.indexParam, { encode: false })
      console.log(goto)
      const params = {
        qrCode: qrCode,
        goto: goto,
        pdmToken: pdmToken,
        scencId: appid
      }

      clearInterval(this.taskId)
      this.taskId = null
      this.taskId = setInterval(() => {
        this.queryDingTalkScanRes(params)
      }, 3000)
    },
    // 查询钉钉扫码的结果
    async queryDingTalkScanRes(params) {
      console.log('扫码轮询')
      const scanRes = await proxyApi.getDingTalkScanRes(params)
      const nowCodeStatus = this.codeStatus
      console.log(scanRes)
      if (scanRes.success) {
        clearInterval(this.taskId)
        this.taskId = null
        this.qrCodeSrc = scanRes.data.replace('https://oapi.dingtalk.com', urlUtils.getBaseIPPort())
        console.log(this.qrCodeSrc)
        this.codeStatus = 3
        this.queryScanRes()
      } else {
        if (nowCodeStatus === 3) {
          return false
        }

        if (_.isString(scanRes)) {
          if (scanRes.indexOf('11041') > -1) {
            this.codeStatus = 2
            return
          }

          if (scanRes.indexOf('11019') > -1) {
            this.codeStatus = 4
            this.clearQrInterval()
            return
          }
        } else {
          switch (scanRes.code) {
            case '11041': // 已扫描
              this.codeStatus = 2
              break
            case '11019': // 二维码已失效
              this.codeStatus = 4
              this.clearQrInterval()
              break
            default:
          }
        }
      }
    },
    /**
     * 处理钉钉返回
     */
    async handleMessage(event) {
      const origin = event.origin
      if (origin === 'https://login.dingtalk.com') { // 判断是否来自ddLogin扫码事件。
        const loginTmpCode = event.data // 拿到loginTmpCode后就可以在这里构造跳转链接进行跳转了
        this.indexParam['loginTmpCode'] = loginTmpCode

        this.qrCodeSrc = this.authorizeUrl + '?' + qs.stringify(this.indexParam, { encode: false })
        console.log(this.qrCodeSrc)
        this.queryScanRes()
      }
    },
    // 查询扫码结果
    queryScanRes() {
      this.clearQrInterval()
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }

      this.qrCheck()
    },
    /**
   * 清除定时
   */
    clearQrInterval() {
      const taskId = this.taskId
      this.qrCheckCount = 0
      if (taskId) {
        clearInterval(taskId)
        this.taskId = null
      }
    },
    /**
   * 查询扫码结果
   */
    async qrCheck() {
      var qrRes = true

      while (qrRes) {
        await sleep(2000)
        const apiParam = {
          deviceid: this.deviceid,
          action: 'check',
          time: this.time
        }
        this.qrCheckCount++
        const result = await proxyApi.getDingTalkUser(apiParam, qrCodeCommon.serverHost())
        if (_.get(result, 'data.state') === false && _.get(result, 'data.type') === 'Prescaned') {
          qrRes = false
          this.$message.warning(_.get(result, 'data.message'), result.errmsg)
          this.clearQrInterval()
          this.drawQrCode()
        } else if (_.get(result, 'data.state') === true) {
        // 扫描成功
          qrRes = false
          this.clearQrInterval()
          this.username = result.data.username
          this.password = result.data.token
          this.submitForm()
        } else {
          if (this.qrCheckCount > 2) {
            qrRes = false
            this.$message.warning(_.get(result, 'data.message', result.errmsg) || this.$t('interfaceErr'))
            this.drawQrCode()
          }
        }
      }
    },
    async confirmBind(type) {
      return new Promise((resolve, reject) => {
        this.$emit('bind', { resolve, type })
      })
    },
    /**
     * 提交（支持撤销登录）
     */
    async submitForm() {
      const params = {
        guestType: 'dingqrlogin',
        username: this.username,
        password: this.password,
        isQrcode: this.isQrcode
      }
      if (this.authData.isTwoFactors) { // 双因子
        params.factorType = 'DingTalk'
        const notBind = _.get(this.authInfo, 'basic.User.FactorAuthRelation.DingTalk') === false
        let autoBind = false
        if (notBind) {
          autoBind = await this.confirmBind('dingtalk')
        }
        if (autoBind !== false) {
          params.autoBind = autoBind
        }
      }
      const res = await qrCodeCommon.auth({ ...params, ...this.authData, ...this.bindData })
      console.log(res)
      if (res) {
        if (_.isObject(res) && _.get(res, 'revoke', false) === true) {
          this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
          return
        }
        if (this.authData.isTwoFactors) { // 双因子认证
          this.$emit('towFactorSuccess')
          return
        }
        this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      } else {
        this.drawQrCode()
      }
    },
    DDLogin(a) {
      const dingTalkUrl = this.qrCodeUrl
      const c = document.createElement('iframe')
      let d = dingTalkUrl + '?goto=' + a.goto
      d += a.style ? '&style=' + encodeURIComponent(a.style) : ''
      d += a.href ? '&href=' + a.href : ''
      c.src = d
      c.frameBorder = '0'
      c.allowTransparency = 'true'
      c.scrolling = 'no'
      c.width = a.width ? a.width + 'px' : '365px'
      c.height = a.height ? a.height + 'px' : '400px'
      c.id = 'dingiframe'
      const e = document.getElementById(a.id)
      e.innerHTML = ''
      e.appendChild(c)
    }
  }
}
</script>
<style lang="scss" scoped>
.addition-style{
  text-align: center;
  padding-bottom: 24px;
}
.selfQr{
  margin: 30px 0 0 0;
  background: #aaa;
  position: relative;
}
.dingtalk-server-proxy{
  padding-top: 24px;
  background: #fff;
}
#qrcode{
  width: 220px;
  margin: 0 auto;
}
.reload{
position: absolute;
left: 80px;
width: 40px;
height: 40px;
top: 80px;
}
.tips{
  margin-top: 30px;
  text-align: center;
}
.tips.scaned{
  color: #666;
}
.tips.logining{
  color: #2d66e0;
}
.refresh{
  color: #2d66e0;
  cursor: pointer;
}
</style>
