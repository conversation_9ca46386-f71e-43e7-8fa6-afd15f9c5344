<!-- 进度条 -->
<template>
  <div class="progress-box">
    <div class="progress" :style="{ width: percentage + '%' }" />
    <div class="progress2" :style="{ width: percentage + '%' }" />
  </div>
</template>

<script>
export default {
  name: 'ProgressCom',
  components: {},
  props: {
    percentage: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },

  computed: {},
  mounted() {},

  methods: {}
}
</script>
<style lang="scss" scoped>
.progress-box {
  position: relative;
  .progress {
    height: 4px;
    max-width: 100%;
    border-radius: 10px;
    background: linear-gradient(132deg, $progress-color-1 10%, $progress-color-2 82%);
    transition: all 0.1s linear;
  }
  .progress2 {
    height: 4px;
    max-width: 100%;
    border-radius: 2px;
    background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 53%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%,
      transparent
    );
    position: absolute;
    top: 0;
    background-size: 10px 10px;
  }
}
</style>
