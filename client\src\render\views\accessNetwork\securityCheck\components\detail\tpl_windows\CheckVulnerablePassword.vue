<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>

      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.VulnerablePassword.h_3_rs") }}
          </p>

          <div v-for="(item, index) in list" :key="item.UserName" class="count-item">
            <img
              :src="vnImgSrc"
              alt=""
            >

            <div class="count-content">
              <div class="title-wrapper">
                <div class="title-content optional-item">
                  {{ $t("check.VulnerablePassword.h_5_rs") }}

                  <span class="count-name">{{ item.UserName }}</span>

                  <span v-if="item.IsCurUser === 'Yes'" class="tag">{{
                    $t("check.VulnerablePassword.js_3_rs")
                  }}</span>
                </div>

                <div class="button-wrapper">
                  <button
                    v-if="item.btnText1"
                    :class="['btn-small', item.hasForbiden ? 'btn-disabled': 'public-medium-btn']"
                    @click="forbidenHandle(item, index)"
                  >
                    {{ item.btnText1 }}
                  </button>

                  <button
                    v-if="item.btnText2"
                    :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']"
                    @click="fixHandle(item, index)"
                  >
                    {{ item.btnText2 }}
                  </button>
                </div>
              </div>

              <div class="optional-item complete-name">
                {{ $t("check.VulnerablePassword.h_7_rd")
                }}<span>{{ item.FullName }}</span>
              </div>

              <div class="optional-item">
                {{ $t("check.VulnerablePassword.h_9_rd") + item.Description }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 设置密码确认提示弹框 -->
      <VerticalDialog :show.sync="passwordConfirmVisible" width="396px">
        <div slot="header" class="confirm-set-password-header">
          <i class="iconfont icon-putongxiangbuhegui" />
          {{ $t('check.VulnerablePassword.h_22_rd') }}
        </div>
        <div class="confirm-set-password-content dialog-content">
          <p>{{ $t('check.VulnerablePassword.h_23_rd') }}</p>
          <p>{{ $t('check.VulnerablePassword.h_26_rd') }}</p>
          <div class="confirm-text">{{ $t('check.VulnerablePassword.h_24_rd') + currentItem.UserName + $t('check.VulnerablePassword.h_25_rd') }}</div>
          <div class="dialog-footer">
            <div class="cancel-btn btn" @click="passwordConfirmVisible = false">
              {{ $t("check.VulnerablePassword.h_18_rd") }}
            </div>

            <div class="confirm-btn btn" @click="confirmSet">
              {{ $t("check.VulnerablePassword.h_19_rd") }}
            </div>
          </div>
        </div>
      </VerticalDialog>

      <!-- 修改密码 -->

      <VerticalDialog :show.sync="passwordVisible" :title="changePasswordTitle">
        <div id="checkChangePassword" class="dialog-content">
          <div class="form-content">
            <el-form
              ref="ruleForm"
              :model="ruleForm"
              :rules="rules"
              label-width="0px"
            >
              <el-form-item v-show="currentItem && currentItem.IsCurUser === 'No'" label="" prop="old_password">
                <el-input
                  v-model="ruleForm.old_password"
                  type="password"
                  :show-password="true"
                  :placeholder="$t('check.VulnerablePassword.h_10_rd')"
                ><i
                  slot="prefix"
                  class="iconfont icon-mima"
                /></el-input>
              </el-form-item>

              <p v-show="currentItem && currentItem.IsCurUser === 'No'" class="old-password-tip">
                {{ $t("check.VulnerablePassword.h_20_rd") }}
              </p>

              <el-form-item label="" prop="password">
                <el-input
                  v-model="ruleForm.password"
                  type="password"
                  :show-password="true"
                  :placeholder="$t('check.VulnerablePassword.h_11_rd')"
                ><i
                  slot="prefix"
                  class="iconfont icon-mima"
                /></el-input>
              </el-form-item>

              <el-form-item label="" prop="check_password">
                <el-input
                  v-model="ruleForm.check_password"
                  type="password"
                  :show-password="true"
                  :placeholder="$t('check.VulnerablePassword.h_12_rd')"
                ><i
                  slot="prefix"
                  class="iconfont icon-mima"
                /></el-input>
              </el-form-item>
            </el-form>

            <div class="rule-content">
              <div>{{ $t("check.VulnerablePassword.h_13_rd") }}</div>

              <div>1) {{ $t("check.VulnerablePassword.h_14_rd") }}</div>

              <div>2) {{ $t("check.VulnerablePassword.h_15_rd") }}</div>

              <div>3) {{ $t("check.VulnerablePassword.h_16_rd") }}</div>

              <div
                class="mutil-rule"
                v-html="$t('check.VulnerablePassword.h_17_rd')"
              />
              <div>4) {{ $t("check.VulnerablePassword.h_29_rd") }}</div>
            </div>
          </div>

          <div class="dialog-footer">
            <div class="cancel-btn btn" @click="passwordVisible = false">
              {{ $t("check.VulnerablePassword.h_18_rd") }}
            </div>

            <div class="confirm-btn btn" @click="submitForm('ruleForm')">
              {{ $t("check.VulnerablePassword.h_19_rd") }}
            </div>
          </div>
        </div>
      </VerticalDialog>

      <!-- 禁用提示弹框 -->
      <ConfirmDialog :show.sync="showConfirm" :msg="confirmMsg" @ok="confirmOkHandle" />
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import { mapState } from 'vuex'
import tplMixins from '../mixins/tpl_windows'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  name: 'CheckOSVersion',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    const validateCurrentPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('check.VulnerablePassword.h_11_rd')))
      } else if (new RegExp('[\\u4E00-\\u9FFF]+', 'g').test(value)) {
        callback(new Error(this.$t('check.VulnerablePassword.h_28_rd')))
      } else {
        callback()
      }
    }

    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changepassword.confirmpassword')))
      } else if (new RegExp('[\\u4E00-\\u9FFF]+', 'g').test(value)) {
        callback(new Error(this.$t('check.VulnerablePassword.h_28_rd')))
      } else {
        if (value !== this.ruleForm.password && this.ruleForm.password) {
          callback(new Error(this.$t('changepassword.comfirPassErrTip')))
        }
        callback()
      }
    }

    return {
      collapseFlag: true,
      passwordVisible: false,
      ruleForm: {
        old_password: '',
        password: '',
        check_password: ''
      },
      rules: {
        password: [{ validator: validateCurrentPassword, trigger: 'blur' }],
        check_password: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      list: [],
      showConfirm: false,
      confirmMsg: '',
      currentItem: {},
      currentIndex: '',
      changePasswordTitle: '',
      passwordConfirmVisible: false,
      vnImgSrc: require('@/render/assets/VulnerablePassword.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    },
    deviceType() {
      return _.get(G_VARIABLE, 'os_browser_info.os_type') || 'windows'
    },
    fixData() {
      const fixDes = this.deviceType !== 'mac' ? this.$t('check.VulnerablePassword.h_19_rs') : this.$t('check.VulnerablePassword.h_29_rs')
      return {
        modelTitle: this.$t('check.VulnerablePassword.h_11_rs'),
        fixSteps: [
          fixDes,
          this.$t('check.VulnerablePassword.h_27_rs')
        ]
      }
    },
    ...mapState(['authInfo'])
  },
  watch: {
    passwordVisible(val) {
      if (!val) {
        this.$refs.ruleForm.resetFields()
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const IsDisable =
        _.get(this.checkData, 'CheckType.Repair.IsDisable') === '0'
      let info = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!info) {
        return
      }
      if (!_.isArray(info)) {
        info = [info]
      }
      info.forEach((item) => {
        if (
          item.IsCurUser !== 'Yes' &&
          item.DisableUser !== 'Yes' &&
          IsDisable
        ) {
          item.btnText1 = this.$t('check.VulnerablePassword.js_5_d')
          item._fixType = 1
          item.hasForbiden = this.checkData.hasFixed
        }
        const btn_name =
            item.IsCurUser === 'Yes'
              ? this.$t('check.VulnerablePassword.js_7_d')
              : this.$t('check.VulnerablePassword.js_8_d')
        item.btnText2 = btn_name
        item._fixType = 3
        item.hasFixed = this.checkData.hasFixed
      })
      this.list = info
    },
    forbidenHandle(item, index) {
      if (item.hasForbiden) {
        return
      }
      this.currentItem = item
      this.currentIndex = index
      this.confirmMsg = this.$t('check.VulnerablePassword.js_1_s') + item.UserName + this.$t('check.VulnerablePassword.js_4_s')
      this.showConfirm = true
    },
    confirmSet() {
      this.passwordConfirmVisible = false
      this.changePasswordTitle = this.$t('check.VulnerablePassword.js_7_d')
      this.passwordVisible = true
    },
    async confirmOkHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          user_name: this.currentItem.UserName
        },
        RepairMode: 0,
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: this.currentItem,
        tip: this.$t('check.VulnerablePassword.js_5_d'),
        fixKey: 'hasForbiden'
      })
      this.$set(this.list, this.currentIndex, this.currentItem)
    },
    async fixHandle(item, index) {
      if (item.hasFixed) {
        return
      }
      this.currentItem = item
      this.currentIndex = index

      if (item._fixType === 2) { // 修复
        const Repair = _.get(this.checkData, 'CheckType.Repair')
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            VpRepair: {
              Item: {
                FullName: item.Domain,
                UserName: item.UserName,
                RepairStyle: Repair.RepairStyle,
                RepairPass: Repair.RepairPass
              }
            }
          },
          RepairMode: 3,
          RepairType: 0,
          CreateProgress: 1
        }
        await this.submitHandle({
          params,
          CheckItem: this.currentItem,
          tip: this.$t('check.fix')
        })
        this.$set(this.list, this.currentIndex, this.currentItem)
      } else if (item._fixType === 3) {
        if (item.IsCurUser === 'Yes') {
          this.changePasswordTitle = this.$t('check.VulnerablePassword.js_7_d')
          // MAC客户端的弱口令检查直接通知一下客户端就好了
          if (this.deviceType === 'mac') {
            const params = {
              ItemID: this.checkData.ItemID,
              InsideName: this.checkData.InsideName,
              RepairParam: {},
              RepairType: 0,
              CreateProgress: 0
            }
            await this.submitHandle({
              params,
              CheckItem: this.currentItem,
              tip: this.changePasswordTitle
            })
          } else {
            this.passwordConfirmVisible = true
          }
        } else {
          this.changePasswordTitle = this.$t('check.VulnerablePassword.js_8_d')
          this.passwordVisible = true
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          let RepairMode = 2
          let param = this.currentItem.Domain + '<infogo>' + this.currentItem.UserName + '<infogo>' + this.ruleForm.password
          if (this.currentItem.IsCurUser !== 'Yes') {
            RepairMode = 1
            param = this.currentItem.Domain + '<infogo>' + this.currentItem.UserName + '<infogo>' + this.ruleForm.old_password + '<infogo>' + this.ruleForm.password
          }
          const params = {
            ItemID: this.checkData.ItemID,
            InsideName: this.checkData.InsideName,
            RepairParam: {
              param
            },
            RepairMode,
            RepairType: 0,
            CreateProgress: 1
          }
          await this.submitHandle({
            params,
            CheckItem: this.currentItem,
            tip: this.changePasswordTitle
          })
          this.$set(this.list, this.currentIndex, this.currentItem)
          this.$refs[formName].resetFields()
          this.passwordVisible = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-content {
  padding-right: 0;
  .count-item {
    display: flex;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }

    img {
      width: 56px;
      height: 56px;
      flex-shrink: 0;
      margin-top: 5px;
    }

    .count-content {
      flex: 1;
      padding-left: 16px;
      .title-wrapper {
        display: flex;
        .title-content {
          display: flex;
          align-items: center;
          flex: 1;
          word-break: keep-all;
          .tag {
            height: 18px;
            font-size: 12px;
            color: #536ce6;
            line-height: 16px;
            background: #f2f4fd;
            border: 1px solid rgba(83, 108, 230, 0.1);
            border-radius: 10px;
            padding: 0 6px;
            margin-left: 10px;
            word-break: keep-all;
          }

          .count-name {
            word-break: break-all;
          }
        }

        .button-wrapper {
          flex-shrink: 0;
          .btn-small {
            margin-left: 10px;
          }
        }
      }

      .complete-name {
        margin-top: 4px;
        margin-bottom: 8px;
      }
    }
  }
}

.form-content {
  padding: 24px 60px;
  .rule-content {
    word-break: break-word;
    & > div {
      color: $default-color;
      font-size: 12px;
    }
    .mutil-rule {
      margin-left: 13px;
    }
  }

  .old-password-tip {
    line-height: 17px;
    font-size: 12px;
    color: $disabled-color;
    margin-top: 4px;
    margin-bottom: 3px;
    word-break: break-word;
  }
}

.dialog-footer {
  display: flex;
  .btn {
    width: 50%;
    height: 32px;
    line-height: 31px;
    text-align: center;
    font-size: 13px;
    color: $default-color;
    border-top: 1px solid $line-color;
    cursor: pointer;
    &:hover {
      background: $--color-primary;
      color: white;
      border-left-color: $--color-primary;
      border-top-color: $--color-primary;
    }
  }
  .cancel-btn {
    border-bottom-left-radius: 5px;
  }
  .confirm-btn {
    border-left: 1px solid $line-color;
    color: $--color-primary;
    border-bottom-right-radius: 5px;
  }
}
.confirm-set-password-header{
  padding: 16px;
  line-height: 19px;
  .iconfont{
    color: #F2A918;
    font-size: 16px;
    margin-right: 8px;
  }
}
.confirm-set-password-content{
  padding: 0;
  word-break: break-word;
  &>p{
    font-size: 13px;
    line-height: 19px;
    margin-bottom: 8px;
    color: $title-color;
    padding: 0 32px;
    word-break: break-word;
  }
  .confirm-text{
    font-weight: 500;
    margin-bottom: 24px;
    padding: 0 32px;
  }
}
#checkChangePassword ::v-deep .el-form-item__error {
  position: absolute;
  top: 100%;
}

#checkChangePassword ::v-deep .el-form-item:first-child {
  margin-bottom: 0;
}

#checkChangePassword ::v-deep .el-form-item {
  margin-bottom: 22px;
}
</style>
