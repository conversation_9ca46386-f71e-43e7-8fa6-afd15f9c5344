<template>
  <div class="change-count-page">
    <div class="count-title">
      {{ $t('header.currentCount') }}：
      <i>{{ userName }}</i>
    </div>
    <div class="add-count-wrapper">
      <div v-show="!showAddForm" class="add-btn-wrapper">
        <p id="ui-accessNetwork-change_account-p-add" class="public-medium-btn" @click="addAcountHandle">+ {{ $t('changeCount.add') }}</p>
        <span class="add-tips">{{ $t('changeCount.addTips') }}</span>
      </div>
      <div v-show="showAddForm" v-loading="loading" class="form-wrapper">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="0px">
          <el-form-item label="" prop="account">
            <el-input id="ui-accessNetwork-change_account-input-account_name" v-model="ruleForm.account" :placeholder="$t('changeCount.enterName')"><i slot="prefix" class="iconfont icon-zhanghu" /></el-input>
          </el-form-item>
          <el-form-item label="" prop="password">
            <el-input
              id="ui-accessNetwork-change_account-input-account_password"
              v-model="ruleForm.password"
              type="password"
              :show-password="true"
              :placeholder="$t('changeCount.enterPassword')"
            >
              <i slot="prefix" class="iconfont icon-mima" />
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="mark">
            <el-input id="ui-accessNetwork-change_account-input-mark" v-model="ruleForm.mark" class="mark-input" type="textarea" :placeholder="$t('changeCount.mark')" />
          </el-form-item>
          <el-form-item>
            <div class="submit-form">
              <button id="ui-accessNetwork-change_account-button-cancel" class="public-line-medium-btn" @click.prevent="closeAddFormHandle">
                {{ $t('changeCount.cancel') }} </button>
              <button id="ui-accessNetwork-change_account-button-submit" class="public-medium-btn" @click.prevent="submitForm('ruleForm')">
                {{ $t('changeCount.submit') }} </button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData" tooltip-effect="dark" stripe style="width: 100%" class="public-no-boder-table count-menu-table">
      <el-table-column prop="userName" show-overflow-tooltip width="245" :label="$t('changeCount.countName')">
        <template slot-scope="scope">
          <span
            class="patch-title"
          ><span v-if="scope.row.userName === userName" class="table-current-tag">{{ $t('changeCount.currentCount') }}</span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" show-overflow-tooltip width="160" :label="$t('changeCount.tableMark')" />
      <el-table-column :label="$t('changeCount.operating')" width="110">
        <template slot-scope="scope">
          <el-popconfirm
            id="tip-pop"
            :title="$t('changeCount.isChangeCount')"
            :hide-icon="true"
            width="210"
            popper-class="table-pover"
            @confirm="changeCountHandle(scope.row)"
          >
            <el-button
              :id="`ui-accessNetwork-change_account-button-change_${scope.row.userName}`"
              slot="reference"
              type="text"
              size="small"
              :title="$t('changeCount.changeCount')"
            >
              <i class="iconfont icon-qiehuanzhanghu" />
            </el-button>
          </el-popconfirm>

          <el-popconfirm
            id="tip-pop"
            :title="$t('changeCount.isDeleteCount')"
            :hide-icon="true"
            width="210"
            popper-class="table-pover"
            @confirm="deleteCountHandle(scope.row)"
          >
            <el-button
              :id="`ui-accessNetwork-change_account-button-del_${scope.row.userName}`"
              slot="reference"
              type="text"
              size="small"
              :title="$t('changeCount.deleteCount')"
            >
              <i class="iconfont icon-shanchuzhanghu" />
            </el-button>
          </el-popconfirm>

          <el-button
            :id="`ui-accessNetwork-change_account-button-edit_${scope.row.userName}`"
            slot="reference"
            type="text"
            size="small"
            :title="$t('changeCount.editCount')"
            @click="editCountHandle(scope.$index,scope.row)"
          >
            <i class="iconfont icon-peizhishichang" />
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import './common/common.scss'
import { mapGetters } from 'vuex'
import agentApi from '@/service/api/agentApi'
import regular from '@/render/utils/regular'
import { EventBus } from '@/render/eventBus'
import { Base64Encode } from '@/render/utils/global'
import authIndex from '@/render/utils/auth/index'

export default {
  data() {
    const validateAccount = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changeCount.enterName')))
      } else {
        if (!regular.rules.accountTitle.test(value)) {
          callback(new Error(regular.errorTipMap()['accountTitle']))
        }
        const item = this.tableData.find((item, index) => {
          if (item.userName === value) {
            // 编辑时排除自己
            if (!this.isAdd && index === this.oldForm.id) {
              return false
            }
            return true
          }
          return false
        })
        if (!_.isUndefined(item)) {
          callback(new Error(this.$t('changeCount.nameRepeatTip')))
        }
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('changeCount.enterPassword')))
      } else {
        if (!regular.lenRange(value, 1, 50) || regular.hasChars(value, [',', '"']) || regular.flEmpty(value)) {
          callback(new Error(this.$t('changeCount.defaultErrTip')))
        }
        callback()
      }
    }
    const validateRemark = (rule, value, callback) => {
      if (!value) {
        callback()
      } else {
        const partern = /^[^\\/<>~^'"&+{}=]{0,120}$/
        if (!partern.test(value)) {
          callback(new Error(this.$t('changeCount.markErrorTip')))
        }
        callback()
      }
    }
    return {
      visible: false,
      showAddForm: false,
      tableData: [],
      loading: false,
      isAdd: true, // true添加 false编辑
      ruleForm: {
        account: '',
        password: '',
        mark: ''
      },
      oldForm: {
        id: '',
        account: '',
        password: '',
        mark: ''
      },
      rules: {
        account: [
          { validator: validateAccount, trigger: 'blur' }
        ],
        password: [
          { validator: validatePass, trigger: 'blur' }
        ],
        mark: [
          { validator: validateRemark, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['authInfo', 'clientInfo']),
    userName() {
      let userName = _.get(this.clientInfo.accessStatus, 'userName')
      if (_.isUndefined(userName)) {
        userName = _.get(this.authInfo.basic, 'UserName', '')
      }
      return userName
    }
  },
  mounted() {
    this.initUser()
  },
  methods: {
    async initUser() {
      const initParam = {
        WhereIsModule: 'MsacAssConfigMgr.dll',
        WhatFuncToCall: 'Init',
        RequestParam: ''
      }
      await agentApi.callAgentOneFunc(initParam)

      const userParam = {
        WhereIsModule: 'MsacAssAutoCheck.dll',
        WhatFuncToCall: 'GetUserSwitchList',
        RequestParam: ''
      }
      const runResult = await agentApi.callAgentOneFunc(userParam)
      this.initShowNewUser(runResult)
    },
    initShowNewUser(userXml) {
      if (_.isEmpty(userXml)) {
        return
      }
      let userInfo = _.get(userXml, 'ASM.UserInfo', [])
      if (_.isObject(userInfo) && !_.isArray(userInfo)) {
        userInfo = [userInfo]
      }

      const userNum = userInfo.length
      for (let i = 0; i < userNum; i++) {
        try {
          var userName = userInfo[i]['UserName']
          var password = userInfo[i]['Password']
          var remark = userInfo[i]['Remark']
          if (userName) {
            this.tableData.push({
              userName: userName,
              password: password,
              remark: remark
            })
          }
        } catch (e) {
          console.log(e.message)
        }
      }
    },
    changeCountHandle(row) {
      if ((!_.isString(row.userName) || row.userName === '') ||
     (!_.isString(row.password) || row.password === '')) {
        this.$message.error(this.$t('auth.userNameOrPasswordError'))
        return false
      }

      authIndex.config.AutoLogin = 0
      const query = {
        switchUser: 1,
        userName: Base64Encode(row.userName),
        password: Base64Encode(row.password)
      }
      // 如果当前页面是认证页面，则通过事件通知，否则跳转至认证页面
      if (this.$router.currentRoute.path === '/access/auth') {
        EventBus.$emit('switchUser', query)
      } else {
        this.$router.push({
          path: '/access/auth',
          query
        })
      }
      this.$emit('changeVisible', false)
    },
    async deleteCountHandle(row) {
      await this.UserSwitchAddUser({
        UserName: row.userName,
        Password: row.password,
        Remark: ''
      }, false)
      const i = this.tableData.findIndex((value) => value.userName === row.userName)
      this.tableData.splice(i, 1)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.switchUser()
        } else {
          return false
        }
      })
    },
    /**
     * 增加防抖处理
     */
    switchUser: _.debounce(async function() {
      // 正在加载中则结束
      if (this.loading) {
        return
      }
      this.loading = true

      try {
        if (this.isAdd) {
          await this.UserSwitchAddUser({
            UserName: this.ruleForm['account'],
            Password: this.ruleForm['password'],
            Remark: this.ruleForm['mark']
          }, true)
          this.tableData.push({
            userName: this.ruleForm['account'],
            password: this.ruleForm['password'],
            remark: this.ruleForm['mark']
          })
        } else {
          // 先删除
          await this.UserSwitchAddUser({
            UserName: this.oldForm['account'],
            Password: this.oldForm['password'],
            Remark: this.oldForm['mark']
          }, false)
          // 再添加
          await this.UserSwitchAddUser({
            UserName: this.ruleForm['account'],
            Password: this.ruleForm['password'],
            Remark: this.ruleForm['mark']
          }, true)
          this.$set(this.tableData, this.oldForm.id, {
            userName: this.ruleForm['account'],
            password: this.ruleForm['password'],
            remark: this.ruleForm['mark']
          })
        }

        this.closeAddFormHandle()
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    }, 1000, { 'leading': true, 'trailing': false }),
    closeAddFormHandle() {
      this.showAddForm = false
      this.$refs.ruleForm.resetFields()
    },
    addAcountHandle() {
      if (this.tableData.length > 9) {
        this.$message({
          message: this.$t('changeCount.outlineOperate'),
          type: 'warning'
        })
      } else {
        this.isAdd = true
        this.showAddForm = true
      }
    },
    editCountHandle(id, row) {
      this.isAdd = false
      // 保存旧的，用于删除
      this.oldForm = {
        id,
        account: row.userName,
        password: row.password,
        mark: row.remark
      }
      this.ruleForm = {
        account: row.userName,
        password: row.password,
        mark: row.remark
      }
      this.showAddForm = true
    },
    async UserSwitchAddUser(row, isAdd) {
      const param = {
        WhereIsModule: 'MsacAssAutoCheck.dll',
        WhatFuncToCall: 'UserSwitchAddUser',
        RequestParam: '<ASM>' +
        '<UserName>' + row.UserName + '</UserName>' +
        '<Password>' + row.Password + '</Password>' +
        '<Remark>' + row.Remark + '</Remark>' +
        '<IsAdd>' + (isAdd ? '1' : '0') + '</IsAdd>' +
        '</ASM>'
      }
      await agentApi.callAgentOneFunc(param)
    }
  }
}
</script>

<style lang="scss">
.change-count-page {
  padding: 0 32px;
  .count-title {
    color: $default-color;
    margin-bottom: 24px;
    i {
      font-style: normal;
      color: $title-color;
    }
  }
  .add-btn-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .add-tips {
      font-size: 14px;
      line-height: 20px;
      color: $disabled-color;
      margin-left: 16px;
    }
  }
  .submit-form {
    display: flex;
    align-content: center;
    .public-line-medium-btn {
      margin-right: 12px;
    }
  }
  .iconfont {
    font-size: 14px;
  }
  .icon-shanchuzhanghu{
    color: $disabled-color;
    margin-left: 8px;
    font-size: 16px;
  }
  .icon-peizhishichang{
    margin-left: 8px;
    font-size: 16px;
  }
}

</style>
