<template>
  <!-- 账号密码登录 -->
  <div class="count-auth-page">
    <el-form
      ref="ruleForm"
      :model="authData"
      :rules="rules"
      label-width="0px"
      class="rule-form"
    >
      <el-form-item label="" prop="userName">
        <el-autocomplete
          id="ui-accessNetwork-account-input-username"
          ref="autoComplete"
          v-model="authData.userName"
          :placeholder="customConf.clientUser"
          :fetch-suggestions="querySearch"
          :debounce="0"
          :trigger-on-focus="false"
          :style="{ width: '360px' }"
          :maxlength="50"
          @keydown.native="keyDown"
          @select="handleAutocomplete"
        >
          <i slot="prefix" class="iconfont icon-zhanghu" />
        </el-autocomplete>
      </el-form-item>

      <el-form-item label="" prop="password">
        <el-input
          id="ui-accessNetwork-account-input-password"
          v-model="authData.password"
          type="password"
          maxlength="50"
          :placeholder="customConf.clientPassword"
        >
          <i slot="prefix" class="iconfont icon-mima" />
        </el-input>
      </el-form-item>

      <networkList ref="networkList" />

      <div v-if="isVerifyCode" class="specal-form-item">
        <div class="form-item-wrapper">
          <el-form-item label="" prop="verifyCode">
            <el-input
              v-model="authData.verifyCode"
              maxlength="4"
              :placeholder="$t('auth.enterCode')"
            >
              <i slot="prefix" class="iconfont icon-yanzhengma" />
            </el-input>
          </el-form-item>
        </div>
        <div class="sms-wrapper">
          <img
            :src="codeImg"
            :title="$t('auth.switchVerifyCode')"
            :alt="$t('auth.verifyCode')"
            @click="changeVerifyCode"
          >
        </div>
      </div>
      <div v-if="bindData.authFrom !== 'bind'" class="checkbox-wrapper">
        <el-checkbox
          v-if="serverInfo.clientSaveName"
          id="ui-accessNetwork-account-label-remember_account"
          v-model="authData.userAutoAuth"
          @change="changeAutoAuth"
        >
          {{ $t('auth.remenberCount') }}
        </el-checkbox>
        <el-checkbox
          v-if="serverInfo.clientSaveName && serverInfo.clientSavePass"
          id="ui-accessNetwork-account-label-auto_login"
          v-model="authData.userAutoLogin"
          @change="changeAutoLogin"
        >
          {{ $t('auth.autoLogin') }}
        </el-checkbox>
        <div v-if="isForgetPassword" class="u-forget-password">
          <a href="javascript:void(0)" @click="forgetPassNow">
            {{ $t('auth.forgetpass') }}
          </a>
        </div>
      </div>
      <el-form-item class="custom-submit-mb">
        <p
          id="ui-accessNetwork-account-p-login_auth"
          :class="[
            bindData.authFrom === 'bind' ? 'bind-submit' : '',
            'public-btn',
          ]"
          @click="submitForm('ruleForm')"
        >
          {{ $t('auth.submit') }}
        </p>
      </el-form-item>
    </el-form>

    <div class="after-auth-box">
      <div v-if="isAllowReg" class="u-reg-account">
        {{ $t('auth.notCount') }}
        <a href="javascript:void(0)" @click="regAccountNow">
          {{ $t('auth.reg') }}
        </a>
      </div>
    </div>

    <!-- 双因子认证 -->
    <two-factor
      ref="sms"
      :mobile="smsMobile"
      :user-name="authData.userName"
      @beforeClose="smsBeforeClose"
      @smsAuthSuccess="smsAuthSuccess"
    />

    <!-- 忘记密码 -->
    <el-drawer
      :visible.sync="showDraw"
      direction="rtl"
      size="424px"
      class="preview-detail-drawer"
      :destroy-on-close="true"
    >
      <div slot="title" class="detail-drawer-title text-bord">
        <span>{{ $t('auth.changePass') }}</span>
      </div>
      <forget-password
        ref="forgetpass"
        :user-name="authData.userName"
        @closeDraw="closeDraw"
      />
    </el-drawer>
  </div>
</template>
<script>
import authMixin from '../mixins'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import { mapGetters, mapMutations } from 'vuex'
import { EventBus } from '@/render/eventBus'
import _ from 'lodash'
import agentApi from '@/service/api/agentApi'
import networkList from './networkList'
import Account from '@/render/utils/auth/account'
import dot1xCommon from '@/render/utils/auth/dot1x'
import authIndex from '@/render/utils/auth/index'
import qs from 'qs'
import proxyAjax from '@/service/utils/proxyAjax'
import { Base64Encode, randomString } from '@/render/utils/global'
import twoFactor from './twoFactors.vue'
import authTypes from '@/render/utils/auth/authTypes'
import urlUtils from '@/render/utils/url'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'
import forgetPassword from './forgetPassword'
import { saveToken } from '@/render/utils/token'

export default {
  name: 'AccountAuth',
  components: {
    networkList,
    twoFactor,
    forgetPassword
  },
  mixins: [authMixin],
  props: {
    serverInfo: {
      type: Object,
      default: function() {
        return {}
      }
    },
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    authType: {
      type: String,
      default: authTypes.User
    },
    // 账号绑定是才有值
    bindData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    const validateUserName = (rule, value, callback) => {
      if (value === '') {
        callback(
          new Error(
            this.$t('auth.inputUserName', { user: this.customConf.clientUser })
          )
        )
      } else {
        const regular = /^.{0,5000}$/
        if (!regular.exec(value)) {
          callback(
            new Error(
              this.$t('auth.noSpecialCharUserName', {
                user: this.customConf.clientUser
              })
            )
          )
        }
        callback()
      }
    }

    const validatePasswordName = (rule, value, callback) => {
      if (value === '') {
        callback(
          new Error(
            this.$t('auth.inputPassword', {
              password: this.customConf.clientPassword
            })
          )
        )
      } else {
        callback()
      }
    }
    return {
      rules: {
        verifyCode: [
          {
            required: true,
            message: this.$t('auth.inputVerifyCode'),
            trigger: 'blur'
          }
        ],
        password: [{ validator: validatePasswordName, trigger: 'blur' }],
        userName: [{ validator: validateUserName, trigger: 'blur' }]
      },
      smsMobile: '', // 双因子联系电话
      verifyCodeCache: new Date().getTime(),
      nowTime: 0,
      isAutocomplete: false,
      showDraw: false,
      authing: false // 认证中，802.1x双因子用
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'clientInfo',
      'authInfo'
    ]),
    /**
     * 是否开启身份认证验证码
     * @returns {boolean}
     */
    isVerifyCode() {
      if (
        authIndex.isDot1xMode() ||
        _.get(this.clientInfo, 'webSlot.isKnockPort')
      ) {
        return false
      }
      return (
        parseInt(
          _.get(this.serveEntiretyConfig, 'server.AUTHPARAM.verifyCode', 0)
        ) === 1
      )
    },
    codeImg() {
      if (!this.isVerifyCode) {
        return ''
      }
      const apiParam = {
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        cache: this.verifyCodeCache
      }
      return (
        urlUtils.getBaseIPPort() +
        proxyAjax.formatUrl(
          'server/imgcode?' + qs.stringify(apiParam, { encode: false })
        )
      )
    },
    /**
     * 是否需要修改用户名和密码前面的名称  自定义输入框标题
     * @returns {{clientUser, clientPassword}}
     */
    customConf() {
      const isClientAuth =
        parseInt(
          _.get(this.computeServeEntiretyConfig, 'ClientAuth.IsClientAuth', 0)
        ) === 1

      let clientUser = this.$t('auth.ClientUser')
      let clientPassword = this.$t('auth.ClientPassword')
      if (isClientAuth) {
        if (this.$i18n.locale === 'zh') {
          clientUser = _.get(
            this.computeServeEntiretyConfig,
            'ClientAuth.ClientUser',
            this.$t('auth.ClientUser')
          )
          clientPassword = _.get(
            this.computeServeEntiretyConfig,
            'ClientAuth.ClientPassword',
            this.$t('auth.ClientPassword')
          )
        } else {
          clientUser = _.get(
            this.computeServeEntiretyConfig,
            'ClientAuth.ClientUser_en',
            this.$t('auth.ClientUser')
          )
          clientPassword = _.get(
            this.computeServeEntiretyConfig,
            'ClientAuth.ClientPassword_en',
            this.$t('auth.ClientPassword')
          )
        }
      }
      return {
        clientUser,
        clientPassword
      }
    },
    /**
     * 是否展示立即注册
     */
    isAllowReg() {
      return (
        !authIndex.isDot1xMode() &&
        parseInt(
          _.get(this.computeServeEntiretyConfig, 'CLIENTCHECK.IsAllowReg', 0)
        ) === 1 &&
        !this.bindData.authFrom
      )
    },
    /**
     * 是否展示忘记密码
     */
    isForgetPassword() {
      return (
        !authIndex.isDot1xMode() &&
        parseInt(
          _.get(
            this.computeServeEntiretyConfig,
            'ChangePassword.IsForgetPassword',
            0
          )
        ) === 1 &&
        !this.bindData.authFrom
      )
    },
    /**
     * 域名辅助输入
     */
    Address() {
      let rawOptions = []
      if (this.authType === authTypes.User) {
        let authServer = _.get(
          this.computeServeEntiretyConfig,
          'User.AuthServer',
          ''
        )
        if (_.isString(authServer) && authServer !== '') {
          authServer = authServer.split('|')
          // 启用ad域
          if (authServer.indexOf(authTypes.AdDomain) !== -1) {
            rawOptions = _.union(
              rawOptions,
              _.get(this.computeServeEntiretyConfig, 'AdDomainAddress', [])
            )
          }
          // 启用ldap
          if (authServer.indexOf(authTypes.LDAP) !== -1) {
            rawOptions = _.union(
              rawOptions,
              _.get(this.computeServeEntiretyConfig, 'LDAPAddress', [])
            )
          }
        } else {
          // 如果无法获取是否开启ad、ldap，则启用全部
          rawOptions = _.get(this.computeServeEntiretyConfig, 'Address', [])
        }
      } else if (this.authType === authTypes.AdDomain) {
        rawOptions = _.get(
          this.computeServeEntiretyConfig,
          'AdDomainAddress',
          []
        )
      } else if (this.authType === authTypes.LDAP) {
        rawOptions = _.get(this.computeServeEntiretyConfig, 'LDAPAddress', [])
      }
      return rawOptions
    }
  },
  watch: {
    // 中英文切换时重新校验失败条目
    '$i18n.locale': function() {
      this.$refs['ruleForm'].fields.forEach((item) => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    }
  },
  mounted() {
    console.log('bindData', this.bindData)
  },
  beforeDestroy() {},
  methods: {
    ...mapMutations(['setAuthInfo']),
    /**
     * 更新验证码缓存
     */
    changeVerifyCode() {
      if (this.isVerifyCode) {
        this.verifyCodeCache = new Date().getTime()
      }
    },
    /**
     * 搜索建议
     * value 查询字符串
     * cb 回调，返回搜索建议 {value:''}
     */
    querySearch(value, cb) {
      const options = []
      if (value === '') {
        cb(options)
        return
      }
      value = value.split('@')

      const rawOptions = this.Address
      if (!_.isArray(rawOptions) || _.isEmpty(rawOptions)) {
        cb(options)
        return
      }
      rawOptions.forEach((element) => {
        if (
          value.length !== 2 ||
          value[1] === '' ||
          element.indexOf(value[1].toLowerCase()) === 0
        ) {
          const text = value[0] + '@' + element
          if (text.length <= 50) {
            options.push({ value: text })
          }
        }
      })
      cb(options)
    },
    /**
     * 触发校验，校验通过则调用实际认证
     */
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 收起el-autocomplete组件下拉提示面板，enter建提交内容不会自动关闭，面板层级太高会盖住双因子弹窗
          this.hideAutoCompletePanel()
          this.auth()
        } else {
          this.authFail(false)
        }
      })
    },
    /**
     * 实际认证，增加防抖处理，防止重复认证
     */
    auth: _.debounce(
      async function() {
        this.$emit('loading', true)
        // 敲门
        let kAuthServer = ''
        if (
          _.isString(this.authType) &&
          this.authType !== '' &&
          this.authType !== authTypes.User
        ) {
          kAuthServer = this.authType
        }
        const knockParam = [
          authTypes.User,
          Base64Encode(kAuthServer),
          this.authData.userName,
          this.authData.password,
          this.authData.verifyCode
        ] // 认证类型、authserver、用户名、密码、验证码
        const knockIsSuccess = await commonUtil.knockPort(knockParam)
        if (!knockIsSuccess) {
          this.authFail(false)
          return false
        }
        // 前置判断不过则返回
        if (!(await this.beforeAuth())) {
          this.authFail(false)
          return false
        }

        try {
          const account = new Account()
          this.authing = true
          if (!this.$refs.networkList) { // 认证过程客户端推送离线，变成404组件获取不到networkList
            return
          }
          const res = await account.auth({
            ...this.bindData,
            ...this.authData,
            authType: this.authType,
            autoAuth: this.authData.userAutoAuth,
            autoLogin: this.authData.userAutoLogin,
            isVerifyCode: this.isVerifyCode,
            accessNetwork: this.$refs.networkList.accessNetwork,
            isWireLess: this.$refs.networkList.isWireLess
          })
          this.authing = false
          if (res === false) {
            this.authFail()
            return false
          }
          if (!(await this.CheckUserTest(res))) {
            this.authFail()
          }
        } catch (e) {
          console.error(e)
          this.authFail()
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    /**
     * 认证失败处理
     */
    authFail(isRefreshCode = true) {
      this.$emit('loading', false)
      this.$emit('emitHandle', { type: 'AuthFailed', value: '' })
      if (isRefreshCode) {
        this.changeVerifyCode()
      }
    },
    /**
     * 认证前校验
     */
    async beforeAuth() {
      if (authIndex.isDot1xMode()) {
        const accessNetwork = this.$refs.networkList.accessNetwork
        if (!_.isString(accessNetwork) || accessNetwork === '') {
          this.$message.error(this.$t('auth.selectNetworkList'))
          return false
        }
        this.setAuthInfo(
          _.merge({}, this.authInfo, {
            basic: {
              dot1xTwoFactors: false
            }
          })
        )
        EventBus.$off('Dot1x:GetCheckCode:SMSFactor')
        EventBus.$on('Dot1x:GetCheckCode:SMSFactor', (qMsgRequest) => {
          this.setAuthInfo(
            _.merge({}, this.authInfo, {
              basic: {
                dot1xTwoFactors: true
              }
            })
          )
          const phone = _.get(qMsgRequest, 'result.ASM.Phone', '')
          EventBus.$emit('TwoFactors:show', true)
          this.$emit('loading', false)
          EventBus.$emit('client:show')
          this.smsMobile = phone + ''
        })
      } else {
        // 必须修改密码
        if (await authIndex.getIsMustChangePass(this.authData.userName)) {
          EventBus.$emit('openPassword', {
            username: this.authData.userName,
            msg: this.$t('auth.mustChangePass')
          })

          return false
        }
      }
      return true
    },
    smsAuthSuccess() {
      const isMacWireLessAuth = _.get(
        this.authInfo,
        'dot1x.isMacWireLessAuth',
        false
      )
      if (!authIndex.isDot1xMode() || isMacWireLessAuth) {
        this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      } else {
        this.$emit('loading', this.authing)
      }
    },
    /**
     * 认证结果回调
     * @param res
     * @constructor
     */
    async CheckUserTest(res) {
      console.log('CheckUserTest', res)

      G_VARIABLE.g_getOnLineIp = 0
      authIndex.config.outonLineIp = ''

      // 错误信息处理
      const errorCode = parseInt(_.get(res, 'errcode', -1))
      console.log('errorCode', errorCode)

      if (errorCode !== 0) {
        switch (errorCode) {
          case 21120030:
            // 超出最大可登录设备数
            this.$emit('emitHandle', {
              type: 'revoke:show',
              value: {
                msg: res.errmsg,
                ...res.data
              }
            })
            // 不需要刷新二维码
            return true
          case 21133003: // 本次认证必须修改密码
          case 21133015: // 密码已过期
          case 21133017: // 密码复杂度
            this.openPassword({
              username: this.authData.userName,
              msg: res.errmsg
            })
            break
          default:
            this.$message.error(
              _.get(res, 'errmsg', this.$t('auth.dot1xAuthFail'))
            )
            return false
        }
        return false
      }
      // 802.1x客户端认证成功情况下
      // 需要判断是否需要修改密码
      if (
        authIndex.isDot1xMode() &&
        parseInt(_.get(res, 'pwdCheckResCode', 1)) === 0
      ) {
        this.openPassword({
          username: this.authData.userName,
          msg: _.get(res, 'pwdCheckResMsg')
        })
        return false
      }

      const isClientSaveName = this.serverInfo.clientSaveName
      const isClientSavePass = this.serverInfo.clientSavePass
      const isUserAutoAuth = this.authData.userAutoAuth

      // 第三方web认证，中石化统一身份认证项目，暂时去除，后续需要时再补充

      const not_save_pw =
        parseInt(_.get(this.authInfo, 'basic.not_save_pw', 0)) !== 1

      if (not_save_pw) {
        await agentApi.fileTools.DeleteOneFileNever('WebAuthTypeTmp')
      }
      if (isClientSaveName && isUserAutoAuth) {
        if (not_save_pw) {
          let str =
            Base64Encode(authTypes.User) +
            '###' +
            Base64Encode(this.authData.userName)
          if (isClientSavePass) {
            str +=
              '###' +
              Base64Encode(randomString(10)) +
              '||||' +
              randomString(5) +
              Base64Encode(this.authData.password) +
              randomString(5)
          }
          await agentApi.fileTools.ActionLocalFile(
            'WebAuthTypeTmp',
            'save',
            str
          )
        }
      }

      // 是否需要双因子，不需要的话通过
      const factorAuth = _.get(this.authInfo, 'basic.FactorAuth')
      const isMacWireLessAuth = _.get(
        this.authInfo,
        'dot1x.isMacWireLessAuth',
        false
      )
      if (
        factorAuth &&
        (!authIndex.isDot1xMode() || isMacWireLessAuth) &&
        parseInt(res.data.BindAuth) !== 1
      ) {
        // 账号密码认证完可能有双因子所以提前更新token
        saveToken({ token: _.get(res, 'data.Token', ''), tokenTimestamp: _.get(res, 'data.TokenTimestamp', ''), deviceId: _.get(this.clientInfo, 'detail.DeviceID', 0), UserID: _.get(res, 'data.UserID', '') })
        // 需要双因子认证
        EventBus.$emit('TwoFactors:show', true)
        this.initMobile() // 初始化双因子
      } else {
        G_VARIABLE.g_hintOver = 0
        this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      }
      return true
    },
    /**
     * 修改密码
     */
    openPassword({ username, msg }) {
      EventBus.$emit('openPassword', {
        username,
        msg
      })
    },
    initMobile() {
      const mobile =
        _.get(this.authInfo, 'basic.Tel') ||
        _.get(this.clientInfo, 'detail.Tel')
      if (mobile) {
        this.smsMobile = mobile + ''
      } else {
        this.smsMobile = ''
      }
    },
    /**
     * 双因子关闭前回调
     * @returns {Promise<boolean>}
     */
    async smsBeforeClose(isSuccess = false) {
      // 802.1x双因子取消前通知小助手
      if (authIndex.isDot1xMode()) {
        // 失败才通知小助手。成功不需要通知
        if (!isSuccess) {
          const apiParam = {
            CheckCode: '',
            Result: '1'
          }
          const res = await dot1xCommon.setUIContext(apiParam)
          if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
            this.$message.error(_.get(res, 'ASM.Message', '失败'))
            this.$emit('loading', false)
            return false
          }
        }

        // 802.1x如果已失败，则取消加载中
        this.$emit('loading', this.authing)
        return true
      }
      // 普通则失败才取消
      if (!isSuccess && !authIndex.isDot1xMode()) {
        await commonUtil.towFactoryCutoff()
      }
      this.$emit('loading', false)
      return true
    },
    /**
     * 勾选自动登录，需要同时勾选记住信息
     */
    changeAutoLogin() {
      const autoLogin = this.authData.userAutoLogin
      if (autoLogin) {
        this.authData.userAutoAuth = true
      }
    },
    // 取消记住信息，需要同时取消自动登录
    changeAutoAuth() {
      const autoAuth = this.authData.userAutoAuth
      if (!autoAuth) {
        this.authData.userAutoLogin = false
      }
    },
    // 立即注册账号,唤起浏览器打开注册账号页面
    regAccountNow() {
      try {
        // 唤起注册页面
        const managerIP = _.get(this.serveEntiretyConfig, 'server.MANAGER_IP')
        if (managerIP) {
          const url = urlUtils.getBaseIPPort() + '/?component=regAccount'
          agentApi.windowOpenUrl(url)
        } else {
          throw new Error('获取管理IP失败')
        }
      } catch (e) {
        this.$msg({ type: 'error', message: '唤起浏览器升级包' + e.message })
      }
    },
    // 忘记密码
    forgetPassNow() {
      this.showDraw = true
    },
    // 忘记密码
    closeDraw() {
      this.showDraw = false
    },
    /**
     * 选择输入建议时触发
     */
    handleAutocomplete() {
      this.isAutocomplete = true
    },
    /**
     * 当时选择输入建议时阻止事件冒泡，反之则不阻止
     */
    keyDown(event) {
      console.log('事件e', event.keyCode)
      if (this.isAutocomplete && parseInt(event.keyCode) === 13) {
        event.stopPropagation()
        this.isAutocomplete = false
      }
    },
    hideAutoCompletePanel() {
      const autoCompleteCom = this.$refs.autoComplete
      if (autoCompleteCom && autoCompleteCom.activated) {
        try {
          autoCompleteCom.close()
          autoCompleteCom.$refs.input.blur()
        } catch (e) {
          console.log('关闭el-autocomplete组件下拉选择面板报错')
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.count-auth-page {
  width: 360px;
  margin: 0 auto;
  .checkbox-wrapper {
    display: flex;
    justify-content: space-between;
    padding-top: 8px;
    padding-bottom: 32px;
  }
  .after-auth-box {
    width: 360px;
    *position: relative;
    *z-index: 10;
    margin-top: 16px;
  }
  .u-reg-account {
    display: inline-block;
    font-size: 14px;
    font-weight: 400;
    float: right;
    margin-right: 32%;
    color: $disabled-color;
    line-height: 20px;
    text-align: center;
    a {
      color: $--color-primary;
      cursor: pointer;
      text-decoration: underline;
    }
  }
  .u-forget-password {
    display: inline-block;
    font-size: 14px;
    color: #9a9ca6;
    float: left;
    a {
      color: $--color-primary;
      text-decoration: underline;
    }
  }
  .bottom-wrapper {
    font-size: 14px;
    font-weight: 400;
    color: $disabled-color;
    line-height: 20px;
    text-align: center;
    a {
      color: $--color-primary;
      cursor: pointer;
      text-decoration: underline;
    }
  }
  .specal-form-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .form-item-wrapper {
      width: 228px;
    }

    .sms-wrapper {
      width: 120px;
      height: 40px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
    }
  }
  .bind-submit {
    margin-top: 8px;
  }
  ::v-deep input[type='password']{
    ime-mode:inactive
  }
  ::v-deep .custom-submit-mb{
    margin-bottom: 16px;
  }
}

.count-auth-page ::v-deep .el-input__prefix .iconfont {
  font-size: 16px;
}
.text-bord{
  font-size: 16px;
  font-weight: bold;
}
</style>
