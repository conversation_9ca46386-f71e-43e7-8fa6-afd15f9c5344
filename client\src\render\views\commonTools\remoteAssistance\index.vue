<!-- 远程协助 -->
<template>
  <div class="assist-wrap">
    <div class="content">
      <img :src="statusImgSrc" alt="">
      <p
        :class="status === 0 ? 'public-btn' : 'info'"
        @click="requestFn(status)"
      >
        {{ infoSwitch(status) }}
      </p>
    </div>
  </div>
</template>

<script>
import agent<PERSON><PERSON> from '@/service/api/agentApi'
const empty = require('@/render/assets/empty.png')
const remoteAssistanceSuccess = require('@/render/assets/remoteAssistanceSuccess.png')

export default {
  name: 'Assist',
  components: {},
  data() {
    return {
      status: 0
    }
  },
  computed: {
    statusImgSrc() {
      if (this.status === 2) {
        return remoteAssistanceSuccess
      }
      return empty
    }
  },

  created() {
    this.init()
  },

  methods: {
    async init() {
      const res = await agentApi.checkRemoteCtrlState()
      // 0正在远程
      if (parseInt(res) === 0) {
        this.status = 2
      }
    },
    // 申请远程协助
    async requestFn(status) {
      if (status === 0) {
        this.status = 1
        const res = await agentApi.requestRemoteScreen()
        if (parseInt(res) === 0) {
          this.status = 2
        } else {
          this.status = 0
        }
      }
    },
    // 文案信息匹配
    infoSwitch(status) {
      let str = ''
      switch (status) {
        case 0:
          str = this.$t('remoteAssistance.info_1')
          break
        case 1:
          str = this.$t('remoteAssistance.info_8')
          break
        case 2:
          str = this.$t('remoteAssistance.info_4')
          break
        default:
          str = ''
          break
      }
      return str
    }
  }
}
</script>
<style lang="scss">
.assist-wrap {
  height: calc(100% - 50px);
  text-align: center;
  overflow: auto;
  .content {
    display: inline-block;
    margin-top: 100px;
    img {
      display: inline-block;
      width: 326px;
      height: 235px;
    }
    .public-btn {
      margin-top: 40px;
    }
    .info {
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: $default-color;
      line-height: 40px;
      margin-top: 40px;
    }
  }
  .assist-dialog {
    background: $light-color;
    border-radius: 5px;
    box-shadow: 0px 0px 14px 0px rgba(46, 60, 128, 0.2);
    margin-top: 280px !important;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      text-align: left;
      padding: 30px 37px 27px;
      border-bottom: 1px solid $line-color;
      font-size: 13px;
      color: $title-color;
      p {
        line-height: 24px;
      }
    }
    .el-dialog__footer {
      padding: 0;
      span {
        display: inline-block;
        width: 50%;
        box-sizing: border-box;
        font-size: 13px;
        text-align: center;
        color: $--color-primary;
        line-height: 32px;
        cursor: pointer;
        &.reject-btn {
          border-right: 1px solid $line-color;
          color: $default-color;
          border-bottom-left-radius: 4px;
        }
        &.agree-btn{
           border-bottom-right-radius: 4px;
        }
        &:hover {
          background: $--color-primary;
          color: $light-color;
        }
      }
    }
  }
}
</style>
