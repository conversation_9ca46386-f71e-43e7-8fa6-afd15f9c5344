<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" :has-fixed="checkData.hasFixed" :loading="loading" :show-btn="checkFail" @fix="fixHandle" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

export default {
  name: 'CheckCustom',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No' && _.get(G_VARIABLE, 'os_browser_info.os_type') !== 'linux'
    }
  },
  methods: {
    fixHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
        },
        RepairType: 0,
        CreateProgress: 1
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.customCheck.js_1_s')
      })
    }
  }
}
</script>
