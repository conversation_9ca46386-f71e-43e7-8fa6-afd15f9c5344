<template>
  <div v-loading="!inited && loading" class="apply-log-content">
    <empty v-if="inited && !tableData.length" />
    <el-table
      v-if="inited && tableData.length"
      v-loading="inited && loading"
      :data="tableData"
      border
      stripe
      height="calc( 100% - 56px)"
      style="width: 100%"
      class="guest-formate-style-table"
    >
      <el-table-column
        prop="ResName"
        show-overflow-tooltip
        min-width="130"
        :label="$t('applyLog.name')"
      />
      <el-table-column
        prop="InsertTime"
        width="158"
        :label="$t('applyLog.time')"
      />
      <el-table-column
        prop="Content"
        show-overflow-tooltip
        :label="$t('applyLog.reson')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Content || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="Status"
        width="80"
        :label="$t('applyLog.result')"
      >
        <template slot-scope="scope">
          <el-tooltip
            effect="light"
            :visible-arrow="false"
            popper-class="source-tool-tip"
            class="item"
            :content="calcContent(scope.row.Status)"
            placement="bottom-start"
            :open-delay="1000"
          >
            <i v-if="parseInt(scope.row.Status) === 1" :id="`ui-ztp-application--application_${scope.row.ApplyID}`" class="iconfont icon-zhengchang" />
            <i v-if="parseInt(scope.row.Status) === 2" :id="`ui-ztp-application--application_${scope.row.ApplyID}`" class="iconfont icon-guanbi2" />
            <i v-if="parseInt(scope.row.Status) === 0" :id="`ui-ztp-application--application_${scope.row.ApplyID}`" class="iconfont icon-daishenhe" />
          </el-tooltip>

        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="inited && tableData.length"
      :current-page.sync="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size.sync="pageSize"
      class="public-pag-bag"
      layout="prev, pager, next, sizes, jumper"
      :total="total"
      :pager-count="5"
      background
      @size-change="handleSizeChange"
      @current-change="getList"
    >
      <!-- hide-on-single-page -->
    </el-pagination>
  </div>

</template>
<script>
import '@/render/styles/guestTable.scss'
import empty from '@/render/views/accessNetwork/guestAuth/guestReceive/components/audit/components/empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapState } from 'vuex'
export default {
  components: {
    empty
  },
  props: {
    maxHeight: {
      default: '',
      type: [String, Number]
    }
  },
  data() {
    return {
      inited: false,
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    ...mapState(['clientInfo'])
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const start = (this.currentPage - 1) * this.pageSize
      const ret = await proxyApi.getResApplyLog({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        start,
        limit: this.pageSize
      })
      console.log('结果', ret)
      const data = ret.data.list || []
      this.tableData = data
      this.total = parseInt(ret.data.total)
      this.loading = false
      this.inited = true
    },
    handleSizeChange(val) {
      console.log(val)
      this.currentPage = 1 // 当前页码
      this.pageSize = val
      this.getList()
    },
    calcContent(state) {
      const dic = [this.$t('applyLog.wait'), this.$t('applyLog.pass'), this.$t('applyLog.refuse')]
      return dic[parseInt(state)]
    }
  }
}
</script>
<style lang="scss" scoped>
.apply-log-content{
  padding: 14px 32px 34px 32px;
  height: 100%;
  &::v-deep .el-table__header .el-table__cell{
    padding: 8px 0;
    color: $title-color;
  }
  .icon-daishenhe{
    color: $disabled-color;
  }
  .icon-guanbi2{
    color: $error-1;
  }
  .icon-zhengchang{
    color: $green-3;
  }
}
.public-pag-bag{
  margin-top: 24px;
}
</style>
<style lang="scss">
  .source-tool-tip {
    padding: 4px 8px;
    background: $gray-3;
    border: 1px solid $gray-2 !important;
    border-radius: 1px;
    box-shadow: 0px 0px 6px 0px rgba(16, 36, 66, 0.2);
    font-size: 12px;
    color: $title-color;
    line-height: 17px;
    max-width: 170px;
  }
</style>
