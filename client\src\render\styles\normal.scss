//和通用样式有关的
/*===reset===*/
body,
h1,
h2,
h3,
h4,
h5,
h6,
div,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td,
iframe {
  margin: 0;
  padding: 0;
}
body {
  font-family: 'PingFang SC', 'PingFangSC-Regular', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  line-height: 1.5em;
  font-size: 14px;
  font-weight: 400;
}
body,
html {
  overflow: unset;
  height: 100%;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

fieldset,
img {
  border: 0;
}
img {
  display: block;
  max-width: 100%;
  height: auto;
}
address,
caption,
cite,
dfn,
em,
th,
var {
  font-style: normal;
  font-weight: 400;
}

ol,
ul,
li {
  list-style: none;
}

a {
  text-decoration: none;
}
a:hover {
  text-decoration: none;
}
a:focus {
  outline: none;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

input,
select,
button {
  font: 100% tahoma, \5b8b\4f53, arial;
  vertical-align: baseline;
  *vertical-align: middle;
}

input[type='checkbox'],
input[type='radio'] {
  vertical-align: middle;
}
.mt0{
  margin-top: 0;
}
.mb0{
  margin-bottom:0;
}
textarea {
  overflow: auto;
  font: 100% tahoma, \5b8b\4f53, arial;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*HTML5*/
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  display: block;
  margin: 0;
  padding: 0;
}

audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}

time,
mark {
  display: inline;
}

mark {
  background-color: #ff0;
  color: #000;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

* {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
*:before,
*:after {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background-color: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
mark {
  color: #000;
  background: #ff0;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  height: 0;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font: inherit;
  color: inherit;
  outline: none;
  font-weight: 400;
}
button {
  overflow: visible;
  border: none;
}
button,
select {
  text-transform: none;
  outline: none;
}
button,
html input[type='button'],
input[type='reset'],
input[type='submit'] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
input {
  line-height: normal;
}
input[type='checkbox'],
input[type='radio'] {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  height: auto;
}
input[type='search'] {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  -webkit-appearance: textfield;
}
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  padding: 0.35em 0.625em 0.75em;
  margin: 0 2px;
  border: 1px solid #c0c0c0;
}
legend {
  padding: 0;
  border: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}
td,
th {
  padding: 0;
}

/*---滚动条默认显示样式--*/
::-webkit-scrollbar-thumb {
  height: 50px;
  // outline-offset: -2px;
  // outline: 3px solid #f5f6f8;
  border-radius: 6px;
  // border: 3px solid #f5f6f8;
  outline: none;
}

/*---鼠标点击滚动条显示样式--*/
::-webkit-scrollbar-thumb:hover {
  height: 50px;
  border-radius: 6px;
}

/*---滚动条大小--*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/*---滚动框背景样式--*/
::-webkit-scrollbar-track-piece {
  border-radius: 0;
}

::-webkit-scrollbar-track {
  background-color: #f5f6f8;
} /* 滚动条的滑轨背景颜色 */
::-webkit-scrollbar-thumb {
  background-color: #B3B6C1;
} /* 滑块颜色 */
::-webkit-scrollbar-button {
  background-color: #f5f6f8;
} /* 滑轨两头的监听按钮颜色 */
::-webkit-scrollbar-corner {
  background-color: #f5f6f8;
} /* 横向滚动条和纵向滚动条相交处尖角的颜色 */

//清除浮动
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  content: '';
  display: table;
}
.clearfix:after {
  clear: both;
}
.f-fl {
  float: left;
}
.f-fr {
  float: right;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  /*WebKit browsers*/
  font-weight: 400;
}

input::-moz-input-placeholder,
textarea::-moz-input-placeholder {
  /*Mozilla Firefox*/
  font-weight: 400;
}

input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  /*Internet Explorer*/
  font-weight: 400;
}
// 多行溢出隐藏
.text-clamp {
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 下拉选择dropdown-menu样式修改
body .el-dropdown-menu {
  padding: 0;
  box-shadow: 0px 0px 14px 0px rgba(46, 60, 128, 0.2);
  border: none;
  .el-dropdown-menu__item {
    line-height: 40px;
    padding: 0 16px;
  }
}
body .el-drawer__header {
  padding:10px 30px 10px 31px;
  padding-top: 24px;
  margin-bottom: 0;
  & > span {
    font-size: 16px;
    font-weight: 600;
    color: $title-color;
    line-height: 20px;
  }
  .el-drawer__close-btn {
    font-size: 16px;
    i{
      color: $disabled-color;
    }
  }
}
.el-form-item__content .el-input--prefix {
  .el-input__inner {
    padding-left: 46px;
  }
  .el-input__prefix {
    left: 16px;
  }
}

body .el-form-item {
  margin-bottom: 24px;
  .el-form-item__error {
    position: relative;
    top: 0;
  }
  .el-form-item__content{
    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
      /*WebKit browsers*/
      color: $disabled-color;
    }

    input::-moz-input-placeholder,
    textarea::-moz-input-placeholder {
      /*Mozilla Firefox*/
      color: $disabled-color;
    }

    input::-ms-input-placeholder,
    textarea::-ms-input-placeholder {
      /*Internet Explorer*/
      color: $disabled-color;
    }
  }
}
body {
  .el-radio__input.is-checked + .el-radio__label,
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: $default-color;
  }
}
.hide-require-tag .el-form-item__label::before {
  display: none;
}
.public-btn {
  width: 360px;
  height: 40px;
  background: -webkit-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
  background: -moz-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
  background: -o-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
  background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
  border-radius: 4px;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  color: $light-color;
  cursor: pointer;
  &:hover {
    background: -webkit-linear-gradient(135deg, $--color-primary-dark-2, $--color-primary-dark-1);
    background: -moz-linear-gradient(135deg, $--color-primary-dark-2, $--color-primary-dark-1);
    background: -o-linear-gradient(135deg, $--color-primary-dark-2, $--color-primary-dark-1);
    background: linear-gradient(315deg, $--color-primary-dark-2, $--color-primary-dark-1);
  }
}
// 二级按钮
.public-medium-btn {
  width: 100px;
  height: 32px;
  background: $--color-primary;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}
.public-medium-btn:hover {
  background-color: $--color-primary-dark-2!important;
}
// 线框二级按钮
.public-line-medium-btn {
  width: 100px;
  height: 32px;
  background-color: $light-color;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid $--color-primary;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  color: $--color-primary;
  cursor: pointer;
}
.public-line-medium-btn:hover {
  color: $--color-primary-dark-2;
  border-color: $--color-primary-dark-2;
}
//禁用按钮
.disabled-button-middle{
  width: 100px;
  height: 32px;
  background-color: $line-color;
  box-sizing: border-box;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  color: $disabled-color;
  cursor: not-allowed;
}
.btn-small{
  width: 80px;
  height: 26px;
  line-height: 24px;
  font-size: 12px;
  text-align: center;
}
.el-public-btn{
  display: inline-block;
  min-width: 100px;
  box-sizing: border-box;
  padding: 0 16px;
  height: 32px;
  border-radius: 5px;
  text-align: center;
  line-height: 32px;
  box-sizing: border-box;
  cursor: pointer;
  background: $--color-primary;
  color: $light-color;
  &:hover {
    background: $--color-primary;
  }
}

.el-disabled-public-btn{
  background-color: $line-color;
  color: $disabled-color;
  cursor: not-allowed;
  &:hover {
    background: $line-color;
  }
}

// input 修改
form .el-form-item.is-error .el-input__inner,  form .el-form-item.is-error .el-textarea__inner{
  border-color: $gray-2;
}

form .el-form-item.is-error .el-input__inner:focus, form .el-form-item.is-error .el-textarea__inner:focus {
  border-color: $--color-primary;
}
.el-textarea .el-textarea__inner {
  padding: 10px 16px;
}
.el-input .iconfont {
  font-size: 16px;
}
// table样式

.public-no-boder-table.el-table {
  border: 1px solid $line-color;
  border-bottom: none;
  &::after,
  &::before {
    background-color: $line-color;
  }
  thead,
  tbody {
    width: inherit;
  }
  th.is-leaf,
  td {
    padding: 0;
    border-color: $line-color;
    .cell {
      padding: 0 16px;
      white-space: nowrap;
      color: $title-color;
      width: 100%;
      height: 40px;
      line-height: 40px;
    }
  }
  thead {
    th.is-leaf {
      background: $line-color;
    }
  }
  .el-table__body {
    tr.el-table__row--striped {
      td {
        background: $row-bg;
      }
    }
    tr {
      &:hover {
        td {
          background: $list-hover-bg;
        }
      }
    }
  }
}

// 提示 表格里省略内容提示
.table-popper.el-tooltip__popper.is-dark {
  max-width: 200px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 8px;
  font-size: 12px;
  text-align: justify;
  color: $light-color;
  line-height: 18px;
}
// 下拉选择select
.el-select{
  .el-input__suffix{
    right: 15px;
    .el-select__caret{
      width: 14px;
      color: $disabled-color!important
    }
  }
}
.el-select-dropdown {
  margin: 8px 0;
  &.el-popper[x-placement^='bottom'] {
    margin-top: 8px;
  }
  .el-select-dropdown__item.selected {
    font-weight: 400;
  }
  .popper__arrow {
    display: none !important;
  }
  .el-select-dropdown__list {
    padding: 0;
  }
  .el-select-dropdown__item {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    margin: 4px 8px 0 8px;
    border-radius: 4px;
    color: $title-color;
    &:last-child{
      margin-bottom: 8px;
    }
    &:first-child{
      margin-top: 8px;
    }
    &:hover{
      background-color: $gray-3;
    }
    &.selected {
      background-color: $--color-primary;
      color: white;
    }
  }
  .el-select-dropdown__item.hover{
    background-color: $gray-3;
  }
  .el-select-dropdown__item.selected{
    background-color: $--color-primary;
  }
}
// 表单select 样式
.select-form-box {
  .el-form-item__content {
    .el-select {
      width: 100%;
    }
    .el-input__inner {
      padding-left: 46px !important;
    }
    .el-select__tags{
      padding-left: 32px;
    }
    i.iconfont {
      position: absolute;
      left: 16px;
      z-index: 2;
      color: #b3b6c1;
      font-size: 16px;
    }
  }
}

// 带背景的分页
body .el-pagination.public-pag-bag {
  color: $disabled-color;
  text-align: center;
  .btn-prev:disabled,
  .btn-next:disabled,
  .el-pager .number,
  .el-pager .more,
  .el-pagination__jump,
  .el-input__inner {
    color: $disabled-color;
  }
  .btn-prev,
  .btn-next {
    background-color: $light-color !important;
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    .el-icon-arrow-right, .el-icon-arrow-left{
      color: $disabled-color;
    }
  }
  .btn-prev:disabled,
  .btn-next:disabled{
    min-width: 32px;
  }
  .btn-prev{
    margin-left: 0;
  }
  .btn-prev,
  .btn-next,
  .el-pager .number,
  .el-pager .more {
    border: 1px solid $gray-2;
    border-radius: 5px;
    background: $light-color;
    font-size: 14px;
    font-weight: 400;
  }
  .el-pagination__jump {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: $default-color;
    margin-left: 0;
    .el-input {
      margin: 0 8px;
    }
    input{
      color: $default-color;
    }
  }
  .el-pager li{
    height: 32px;
    line-height: 32px;
    min-width: 32px;
    color: $default-color;
    margin: 0 4px;
  }
  .el-pagination__total, .el-input--mini .el-input__inner{
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: $default-color;
  }
  .el-select .el-input{
    margin: 0 4px;
  }
  .el-pagination__sizes{
    margin-right: 8px;
  }
}
.full-loading{
  
  .el-loading-spinner{
    color: white;
  }
}

// dialog 弹框样式改造，改成垂直居中
.el-dialog__wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog{
    margin: 0!important;
  }
}

// 简单文字提示弹框样式
.el-dialog__wrapper{
  .yg-confirm-modal{
    height: 140px;
    border-radius: 5px!important;
    margin: 0!important;
    .el-dialog__header{
      padding: 0;
      .el-dialog__headerbtn{
        display: none;
      }
    }
    .el-dialog__body{
      padding: 32px;
      color: $default-color;
      font-size: 13px;
      line-height: 18px;
      height: 100%;
      display: flex;
      align-items: center;
      .el-dialog__close{
        position: absolute;
        top: 15px;
        right: 15px;
        font-size: 16px;
        cursor: pointer;
        width: 16px;
        height: 18px;
        background-color: white;
        &:hover{
          color: $--color-primary;
        }
      }
      .tip-content{
        word-break: break-word;
      }
    }
  }
}

// message 组件样式改造
body .el-message{
  border: 1px solid #e6e6e6;
  border-radius: 21px;
  border-radius: 21px;
  box-shadow: 0px 1px 10px 0px rgba(46, 60, 128, 0.2);
  backdrop-filter: blur(1px);
  background-color: white;
  min-width: 30px;
  padding: 9px 15px;
  .el-message__icon{
    font-size: 16px;
  }
  .el-message__content{
    font-size: 14px;
    font-weight: 400;
    color: $title-color;
    line-height: 20px;
  }
}

// message-box 立即返回确认取消结果弹框
.el-message-box__wrapper{
  .self-confirm-box{
    width: 260px;
    padding: 0;
    border: none;
    border-radius: 5px;
    box-shadow: 0px 0px 14px 0px rgba(46,60,128,0.20); 
    .el-message-box__header{
      display: none;
    }
    .el-message-box__content{
      padding: 29px 32px 26px 32px;
      .el-message-box__container{
        .el-message-box__message{
          padding: 0!important;
          p{
            font-size: 13px;
            line-height: 26px;
            color: $title-color;
          }
        }
        .el-message-box__status{
          display: none;
        }
      }
    }
    .el-message-box__btns{
      padding: 0;
      display: flex;
      border-top: 1px solid $line-color;
      .el-button{
        width: 50%;
        border: none;
        height: 32px;
        margin: 0;
        color: $--color-primary;
        border-radius: 0;
        &:hover{
          background-color: $--color-primary;
          color: white
        }
        &:nth-child(2){
          border-left: 1px solid $line-color;
        }
      }
      .el-button--primary{
        background-color: white;
        color: $default-color;
      }
    }
  }
}
// check-box
.el-checkbox {
  .el-checkbox__label{
    padding-left: 8px;
  }
  .el-checkbox__inner{
    border-color: $disabled-color;
    width: 16px;
    height: 16px;
    &::after{
      height: 8px;
      left: 5px;
    }
  }
}

.el-radio .el-radio__inner{
  border-color: $disabled-color;
}

.el-cascader-panel .el-cascader-menu{
  color: $title-color;
}

.u-loading-dialog{
  display:flex;
  justify-content: center;
  .el-loading-spinner{
    background:#fff;
    padding:20px 0;
    width:230px;
    border-radius:5px;
    .el-loading-text,.el-icon-loading{
      color: #686e84;
    }
  }
}

body .el-cascader-menu__list{
  padding: 8px;
  .el-cascader-node{
    padding: 0 22px 0 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    .el-cascader-node__postfix{
      right: 6px;
    }
    .el-cascader-node__label{
      padding: 0;
      font-weight: 400;
    }
    .el-icon-check{
      display: none;
    }
  }
  .el-cascader-node:not(.is-disabled):hover, .el-cascader-node:not(.is-disabled):focus{
    background-color: $gray-3;
  }
  .el-cascader-node.is-active{
    background-color: #536CE6;
    color: white;
    &:hover{
      background-color: #536CE6;
      color: white;
    }
  }
}

.el-cascader__suggestion-list{
  padding: 4px 0;
  .el-cascader__suggestion-item{
    height: 32px;
    line-height: 32px;
    margin: 4px 8px 0 8px;
    border-radius: 4px;
    padding: 0 8px;
    color: $title-color;
    &:last-child{
      margin-bottom: 4px;
    }
    &:hover{
      background-color: $gray-3;
    }
  }
} 
body .el-tooltip__popper{
  max-width: 700px;
}