import Vue from 'vue'
import Vuex from 'vuex'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import _ from 'lodash'
import { TestQtModule } from '@/render/utils/global'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    serveEntiretyConfig: { // 服务器配置信息(包含认证方式；提示信息等）
      server: {}, // 服务器接口返回的配置信息,
      client: {}, //  802.1x客户端返回配置信息
      scene: {}, // 场景信息(基础的场景信息，是否认证，是否注册，是否案件等)
      sceneConfig: {} // 详细的场景信息，控件安装信息等
    },
    clientInfo: {
      online: false,
      basic: {}, // 调用小助手的WebCall_GetClientBaseInfo返回的基本设备信息(版本号，ip,mac等)
      detail: {}, // 调用获取设备信息接口返回的详细设备信息（主要包括设备是否注册，是否违规等信息）
      accessStatus: {}, // 调用获取入网状态接口返回的入网状态信息(主要包括是否入网，上次入网认证方式等)
      webSlot: { // 敲端口信息
        portConncet: true, // 443端口是否通
        isKnockPort: false // 是否需要敲端口
      }
    },
    authInfo: {
      isAuth: true, // 是否开启身份认证
      basic: {},
      dot1x: {},
      tokenInfo: ''
    },
    redirectInfo: {
      firstUrl: '',
      haveRedirect: false
    }, // 入网成功重定向数据
    msgCenter: {
      unRead: 0
    },
    gateWayInfos: {
      state: 2, // 1 正常 0 异常 2 正在连接
      gateWayMap: {},
      total: 0, // 网关个数
      VPNStatus: 1 // 隧道连接开关
    }
  },
  getters: {
    serveEntiretyConfig(state) {
      return state.serveEntiretyConfig
    },
    computeServeEntiretyConfig(state) {
      // 优先使用在线配置
      let config = _.get(state.serveEntiretyConfig, 'server', {})
      if (!_.isEmpty(config)) {
        return config
      }
      // 802.1x模式下如果配置为空，则使用在线配置
      if (parseInt(_.get(state.clientInfo, 'basic.AgentMode', 0)) === 1) {
        config = _.get(state.serveEntiretyConfig, 'client.server', {})
        if (!_.isEmpty(config)) {
          return config
        }
      }
      return _.get(state.serveEntiretyConfig, 'server', {})
    },
    clientInfo(state) {
      return state.clientInfo
    },
    // 核心函数。它根据服务器配置的信息，当前设备的入网状态，当前设备的设备状态来计算出欢迎页的状态
    computeNetAccessStatus(state) {
      return (type = 'status') => {
        return commonUtil.computeNetAccessStatus(state, type)
      }
    },
    authInfo(state) {
      return state.authInfo
    },
    msgCenter(state) {
      return state.msgCenter
    },
    gateWayInfos(state) {
      return state.gateWayInfos
    },
    other(state) {
      return state.other
    }
  },
  mutations: {
    setServeEntiretyConfig(state, value) {
      state.serveEntiretyConfig = value
    },
    setClientInfo(state, value) {
      state.clientInfo = value
      if (parseInt(_.get(value, 'basic.AgentMode', 0)) === 1 || !TestQtModule('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer')) { // 8021x模式或不能敲端口
        state.clientInfo.online = parseInt(_.get(value, 'basic.IsOnline', 0)) === 1
      } else {
        state.clientInfo.online = _.get(value, 'webSlot.portConncet', false)
      }
    },
    setAuthInfo(state, value) {
      state.authInfo = value
    },
    setRedirectInfo(state, value) {
      state.redirectInfo = { ...state.redirectInfo, ...value }
    },
    setMsgCenter(state, value) {
      state.msgCenter = value
    },
    setGateInfos(state, value) {
      state.gateWayInfos = value
    },
    setOther(state, value) {
      state.other = value
    }
  }
}
)
