/*
 * @Author: <EMAIL>
 * @Date: 2021-08-19 13:43:57
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-08-27 10:19:27
 * @Description: 数组的扩展功能
 */
const arrayExtend = {

  /**
   * 替换数组对象中的某些key
   * @param {*} array 执行被替换的数组对象
   * @param {*} mapping 需要被执行替换的key和映射被替换的key [{'target':'destination'}]
   * @param {*} overwrite 是否完全覆盖原来的数组对象
   * @param {*} forceValidateMap 强制检查映射关系里面的被替换key是否存在
   * overwrite:true的替换
   * [{item:2333,dep:'长沙'}]-->映射关系：[{item:'value',dep:'label'}]-->[{value:2333,label:'长沙}]
   * overwrite:false的替换
   * [{item:2333,dep:'长沙'}]-->映射关系：[{item:'value',dep:'label'}]-->[{value:2333,label:'长沙,item:2333,dep:'长沙'}]
   */
  replaceArrObjKey(array, mapping, overwrite = false, forceValidateMaping = false) {
    let newArr = []
    if (!_.isArray(array)) {
      throw new Error(`替换对象${array}必须为数组对象`)
    }
    if (!_.isObject(mapping)) {
      throw new Error(`替换对象的映射关系${mapping}必须为数组对象`)
    }

    newArr = array.map(item => {
      return this.replaceObjKey(item, mapping, overwrite, forceValidateMaping)
    })

    return newArr
  },
  /**
   * 替换对象中的key,使用方法见上面的replaceArrObjKey
   */
  replaceObjKey(object, mapping, overwrite = false, forceValidateMaping = false) {
    const newItem = {}
    if (!_.isObject(object)) {
      throw new Error('替换对象必须为数组对象')
    }
    Object.keys(mapping).forEach(target => {
      const destination = mapping[target]
      if (!_.get(object, target) && forceValidateMaping) {
        throw new Error(`替换对象的映射关系中的key${target}不存在`)
      }
      newItem[destination] = _.get(object, target)
    })

    // 是否用新的映射覆盖旧的
    if (!overwrite) {
      return { ...newItem, ...object }
    } else {
      return newItem
    }
  },
  /**
   * 数组转换成数组对象
   * @param {*} array 待转换的数组
   * @param {*} keys  待加上的keys
   *
   * ['长沙','郑州']-->['lablel','value']--->[{label:长沙，value:'长沙'},{label:郑州:'郑州'}]
   *
   */
  transformArrToArrObj(array, keys) {
    if (!_.isArray(array)) {
      throw new Error(`转换对象${array}必须为数组`)
    }
    let newArr = []
    newArr = array.map(item => {
      const newItem = {}
      keys.forEach(keys => {
        newItem[keys] = item
      })
      return newItem
    })
    return newArr
  }

}

export default arrayExtend
