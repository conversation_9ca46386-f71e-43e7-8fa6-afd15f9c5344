<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.agentInfo.h_3_rs") }}
          </p>
          <div v-for="(item, index) in agentInfoList" :key="item.AgentName" class="pc-info">
            <img :src="agentImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.agentInfo.h_5_rd") }}
                  <span>{{ item.AgentName }}</span>
                </div>
                <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.agentInfo.h_13_rd") }}
                </button>
              </div>
              <div class="optional-item margin">
                {{ $t("check.agentInfo.h_9_rd") }}
                <span>{{ item.IsInstallText }}</span>
              </div>
              <div class="optional-item" :class="{'margin-style': item.version_display}">
                {{ $t("check.agentInfo.h_11_rd") }}
                <span>{{ item.IsRuningText }}</span>
              </div>
              <div v-if="item.version_display" class="optional-item margin-style">
                {{ $t("check.agentInfo.h_14_rd") }}
                <span>{{ item.CurrentVersion }}</span>
              </div>
              <div v-if="item.version_display" class="optional-item margin-style">
                {{ $t("check.agentInfo.h_15_rd") }}
                <span>{{ item.ConfigVersion }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckAgentInfo',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },

  data() {
    return {
      collapseFlag: true,
      agentInfoList: [],
      agentImgSrc: require('@/render/assets/CheckAgentInfo.gif')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getAgentInfoList()
  },
  methods: {
    getAgentInfoList() {
      let infoList = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!infoList) {
        return
      }
      if (!_.isArray(infoList)) {
        infoList = [infoList]
      }
      const newList = []
      infoList.forEach(item => {
        if (item.IsInstall === 'No' || item.IsRuning === 'No' || item.IsVersionOk === 'No') {
          item.IsInstallText = item.IsInstall === 'Yes' ? this.$t('check.agentInfo.js_3_s') : this.$t('check.agentInfo.js_4_s')
          item.IsRuningText = item.IsRuning === 'Yes' ? this.$t('check.agentInfo.js_6_s') : this.$t('check.agentInfo.js_7_s')
          item.version_display = item.ConfigVersion && item.ConfigVersion
          item.hasFixed = false
          newList.push(item)
        }
      })
      this.agentInfoList = newList
    },
    async fixHandle(item, index) {
      if (this.checkData.CheckType.Repair[item.ScreenName + 'Path'].length > 0) {
        const params = {
          ItemID: this.checkData.ItemID,
          InsideName: this.checkData.InsideName,
          RepairParam: {
            filepath: item.ScreenName
          },
          RepairType: 0,
          CreateProgress: 1
        }
        await this.submitHandle({
          params,
          CheckItem: item,
          needProccess: true,
          tip: this.$t('check.AgentInfo.js_1_s')
        })
        this.$set(this.agentInfoList, index, item)
      } else {
        this.$message({
          message: this.$t('check.AgentInfo.js_2_s'),
          type: 'error'
        })
      }
    }
  }
}
</script>

