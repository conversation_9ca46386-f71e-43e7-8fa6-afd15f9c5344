<template>
  <div class="third-code-wrapper">
    <p v-if="!account" class="not-bind-tip">{{ $t('auth.feishuBindTip') }}</p>
    <div v-if="account" class="get-code-wrapper">
      <span class="account-info">{{ $t('auth.feishuAccount') }} <span class="account-text text-clamp">{{ account }}</span></span>
      <button
        id="ui-accessNetwork-twoFactors-button-get_code"
        :class="{'public-line-medium-btn':!smsCodeIsDisabled, 'disabled-button-middle':smsCodeIsDisabled }"
        @click="getSmsCode"
      >
        {{ getSmsCodeButtonMsg }}
      </button>
    </div>
    <div v-if="account" class="check-wapper">
      <el-input id="ui-accessNetwork-twoFactors-third-input-code" v-model="code" class="towFactors-code-input" :placeholder="$t('auth.inputPlaceholder')">
        <i slot="prefix" class="iconfont icon-yanzhengma" /></el-input>

      <button
        id="ui-accessNetwork-twoFactors-button-check_submit"
        class="sms-wrapper"
        :class="{'public-medium-btn':!checkDisabled,'disabled-button-middle':checkDisabled}"
        :disabled="checkDisabled"
        @click="submitTwoFactor"
      >
        {{ $t('auth.check') }}
      </button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
import regular from '@/render/utils/regular'
import TwoFactor from '@/render/utils/auth/twoFactor'
export default {
  name: 'ThirdCode',
  data() {
    return {
      code: '',
      nowTime: 0,
      timer: null,
      maxGetSmsCodeInterval: 60,
      isSubmiting: false,
      getSmsCodeLoading: false
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeServeEntiretyConfig', 'authInfo']),
    account() {
      return _.get(this.authInfo, 'basic.User.FeiShu')
    },
    /**
     * 是否禁用获取验证码按钮
     */
    smsCodeIsDisabled() {
      const account = this.account
      const nowTime = this.nowTime
      const getSmsCodeLoading = this.getSmsCodeLoading
      if (!account || nowTime > 0 || getSmsCodeLoading) {
        return true
      }
      return false
    },
    /**
     * 是否禁用验证按钮
     */
    checkDisabled() {
      const code = this.code
      const isSubmiting = this.isSubmiting
      const account = this.account
      return !code || isSubmiting || !account
    },
    /**
       * 验证码按钮文字
       */
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    }
  },
  beforeDestroy() {
    this.beforeClose()
  },
  methods: {
    async getSmsCode() {
      if (this.getSmsCodeLoading || this.nowTime > 0) {
        return
      }
      const apiParam = {
        mobile_phone: _.get(this.authInfo, 'basic.User.FeiShu'),
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        codeType: 'feishu',
        userid: _.get(this.authInfo, 'basic.UserID')
      }
      this.getSmsCodeLoading = true
      try {
        const ret = await proxyApi.smsSend(apiParam)
        this.getSmsCodeLoading = false
        if (_.get(ret, 'errcode') !== '0') {
          this.$message({
            message: _.get(ret, 'errmsg'),
            type: 'error'
          })
        } else {
          this.countDown(true)
          this.$message({
            message: this.$t('auth.sendSMSOK'),
            type: 'success'
          })
        }
      } catch (err) {
        console.error(err)
      }
      this.getSmsCodeLoading = false
    },
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    async submitTwoFactor() {
      if (this.isSubmiting) {
        return
      }
      const code = this.code
      if (!regular.phone(code) || code.length < 6) {
        this.$message({
          message: this.$t('auth.phoneCodeValidateErr'),
          type: 'warning'
        })
        return false
      }
      const twoFactor = new TwoFactor()
      this.isSubmiting = true
      const res = await twoFactor.auth({
        smsMobile: _.get(this.authInfo, 'basic.User.FeiShu'),
        smsCode: code,
        userName: _.get(this.authInfo, 'basic.UserName'),
        factorType: 'FeiShuCode'
      })
      this.isSubmiting = false
      if (res === false) {
        return false
      }
      this.$emit('towFactorSuccess')
    },
    /**
     * 关闭时处理
     * 重置参数
     * 清除定时
     */
    beforeClose() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.third-code-wrapper{
    min-height: 100px;
    .get-code-wrapper{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24px;
        margin-bottom: 16px;
        .account-info{
            display: flex;
            .account-text{
                width: 177px;
            }
        }
    }
    .not-bind-tip{
        padding: 24px 0;
    }
    #ui-accessNetwork-twoFactors-button-get_code, #ui-accessNetwork-twoFactors-button-check_submit{
        width: 120px;
        height: 40px;
    }
    .towFactors-code-input{
        width: 252px;
        margin-right: 12px;
    }
    .check-wapper{
        display: flex;
    }
    ::v-deep .el-input__prefix {
    left: 16px;
    line-height: 40px;
    }
    ::v-deep .el-input__inner {
    padding-left: 46px;
    }
}
</style>
