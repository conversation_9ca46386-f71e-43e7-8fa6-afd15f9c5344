import _ from 'lodash'
import { i18n } from '@/render/lang'

/**
 * 定时后返回
 * @param int timeout 超时时间 默认：3000
 * @param Object timeoutReturn 自定义超时返回数据内容 默认：{ overtime: true }
 * @returns Promise
 */
export const sleep = (config) => {
  config = _.merge({
    time: 60 * 1000,
    timeoutReturn: { overtime: true }
  }, config)

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve(config.timeoutReturn)
    }, config.time)
  })
}

/**
 * 超时处理
 * @param {object} config 配置
 * @returns Promise
 */
export const overtime = async(config) => {
  config = _.merge({
    request: null, // 标识超时请求
    callback: null,
    time: 60 * 1000,
    timeoutReturn: { errcode: 11100003, errmsg: i18n.t('errcode.11100003') }, // 自定义超时返回数据内容
    retry: 3, // 超时重试次数
    retryDelay: 1000 // 超时重试延迟
  }, config)

  if (_.isNil(config.callback)) {
    return false
  }

  const done = () => {
    return Promise.race([new Promise(config.callback), sleep(config)])
  }

  let result
  for (let i = 0; i < config.retry; i++) {
    result = await done(config)
    console.log('overtime:request:result',
      {
        result,
        nowTry: i
      }
    )

    // 如果超时则重试
    if (_.get(result, 'overtime', false)) {
      // 记录超时请求
      console.error('overtime:request:fail')
      console.error(JSON.stringify({
        ..._.omit(config, ['callback']),
        nowTry: i
      }))

      // 如果是最后一次，则返回失败，否则等待后重试
      if (i === config.retry - 1) {
        result = config.timeoutReturn
      } else {
        // 超时等待
        await sleep({
          time: config.retryDelay
        })
      }
    } else {
      break
    }
  }
  return result
}

export default overtime
