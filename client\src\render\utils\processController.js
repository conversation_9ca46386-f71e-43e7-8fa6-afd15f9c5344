/**
 * 总流程控制函数
 */
import { Message } from 'element-ui'
import router from '@/render/router'
import store from '@/render/store'
import _ from 'lodash'
import { i18n } from '@/render/lang'
import opform from '@/render/utils/opform'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import authUtils from '@/render/utils/auth/index'
import localStorage from '@/render/utils/cache/localStorage'
import scene from './bussiness/scene'

const processController = {
  // 流程的下一跳
  async next(params = {}) {
    const current = router.currentRoute
    console.log('当前路由：' + current.path)
    switch (current.path) {
      case '/access/message': // 欢迎页
        await this.messageNext()
        break
      case '/access/submit': // 欢迎页
        await this.submitNext()
        break
      case '/access/auth': // 员工认证
        await this.authNext(params)
        break
      case '/access/guestAuth': // 来宾认证
        await this.guestAuthNext(params)
        break
      case '/access/register': // 设备注册
        await this.regNext()
        break
      case '/access/guestRegister': // 来宾注册
        await this.guestRegisterNext(params)
        break
      case '/access/guestAudit':
        await this.guestAuditNext(params)
        break
      case '/access/audit': // 设备注册
        this.auditNext(params)
        break
      case '/access/check': // 安检
        this.checkNext()
        break
      default:
        this.set('/access/message')
    }
  },
  // 直接设置当前流程为哪个流程。
  // prcess格式为{strig|object}
  // string格式:'/access/message'
  // object格式为 {path:'/access/message',query:{fromGuest:1} }
  set(process) {
    let nextPath = process
    if (_.isObject(process) && !_.isNil(process.path)) {
      nextPath = process.path
    }
    console.log('下一跳路由：' + nextPath)
    this.addJumpLog(nextPath)
    router.push(process)
  },

  // 当前是欢迎页，下一跳是(点击欢迎页里面的立即入网按钮触发)
  async messageNext() {
    // 场景是否应用来宾
    if (authUtils.isOpenGuestAuth() && authUtils.whetherDefaultGuest()) {
      this.set({ path: '/access/guestAuth' })
      return true
    }

    // 默认来宾、上次认证方式是来宾&&场景里面不是免认证
    this.set({ path: '/access/auth', hadFetchScene: 1 })
  },
  // 当前是认证页，下一跳是
  async authNext(params = {}) {
    // 不是来自authSucess方法里面调用的,不继续下去
    if (!_.get(params, 'fromAuth', false)) {
      this.set('/access/message')
      return true
    }

    // 如果未注册。跳转到注册页面
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) < 0) {
      // 这里再一次请求场景信息,判断在注册页面是否需要自动注册
      const sceneRet = await scene.getDeviceScene()
      if (parseInt(_.get(store, 'state.serveEntiretyConfig.scene.IsAccess', 1)) === 0 || !_.get(sceneRet, 'SceneID', '')) {
        this.set('/access/message')
        return false
      }
      this.set('/access/register')
      return true
    } else {
      await this.hadRegister()
    }
  },
  // 来宾认证的下一跳
  async guestAuthNext(params = {}) {
    const sceneRet = await scene.getDeviceScene()
    if (parseInt(_.get(store, 'state.serveEntiretyConfig.scene.IsAccess', 1)) === 0 || !_.get(sceneRet, 'SceneID', '')) {
      this.set('/access/message')
      return false
    }
    this.set('/access/guestRegister')
  },
  async regNext() {
    // 如果这里还有未注册的设备，跳转到欢迎页(可以算是异常情况了)
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === -2) {
      Message.warning({ Message: i18n.t('reg.noRegTips'), duration: 3 })
      return true
    }

    this.hadRegister()
  },
  async guestRegisterNext(params) {
    // 判断是否需要审核
    if (parseInt(_.get(store, 'state.authInfo.basic.IsNeedAudit', 0)) === 1) {
      const params = {
        guestselfid: _.get(store, 'state.authInfo.basic.guestSelfID', 0),
        deviceId: _.get(store, 'state.clientInfo.detail.DeviceID', 0),
        is_guest: 1
      }
      localStorage.setItem('guestAuditStatus', JSON.stringify(params), 3600)
      this.set('/access/guestAudit')
      return
    }
    this.hadRegister()
  },
  async guestAuditNext(params) {
    this.hadRegister()
  },
  // 审核完成后的下一跳
  async auditNext(params) {
    this.hadRegister(params)
  },

  // 设备状态是已注册完成后
  async hadRegister(params = {}) {
    const sceneRet = await scene.getDeviceScene()
    if (parseInt(_.get(store, 'state.serveEntiretyConfig.scene.IsAccess', 1)) === 0 || !_.get(sceneRet, 'SceneID', '')) {
      this.set('/access/message')
      return false
    }
    // 当前设备注册状态是待审核
    if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === 0) {
      // 不需要安检后审核或者需要安检后审核但是没有开启安检,跳转进入网状态页面展示待审核
      if (parseInt(_.get(store, 'state.serveEntiretyConfig.server.CLIENTCHECK.ControlPostion', 0)) === 0 ||
        parseInt(_.get(sceneRet, 'IsSafeCheck'), 0) === 0) {
        if (!_.isEmpty(opform.get('authEndParams'))) {
          localStorage.setItem('auditNextStatus', JSON.stringify(opform.get('authEndParams')), 3600)
        }
        Message.success({ description: i18n.t('reg.regSuccess'), duration: 15 })
        this.set('/access/message')
        return true
      }
    }

    // 无需安检(或者是需要安检，但是是安检后审核的)直接放开网络
    if (parseInt(_.get(sceneRet, 'IsSafeCheck')) === 0 || _.get(params, 'auditCheckNextStatus', false)) {
      await commonUtil.setDevAccessInfo()
      await commonUtil.securityCheckRePort()
      // IPAM隔离可入网设备进入message
      if (_.get(store, 'state.clientInfo.detail.CutOffStopTime') >= _.get(store, 'state.serveEntiretyConfig.server.TIME')) {
        this.set('/access/message')
        return true
      }
      this.set('/access/submit')
      return true
    } else {
      this.set('/access/check', { isSafeCheck: true })
      return true
    }
  },
  checkNext() {
    // 8021.x的认证前安检
    const CheckSource = _.get(router.currentRoute, 'query.CheckSource', 0)
    const is8021x = parseInt(CheckSource) === 1
    if (is8021x) {
      this.set({ path: '/access/auth', query: { checkSuccess: true }})
      return true
    } else {
      // 是否隔离 - IPAM隔离可入网走完一次入网立即显示隔离状态
      const isCutoff = _.get(store, 'state.clientInfo.detail.CutOffStopTime') >= _.get(store, 'state.serveEntiretyConfig.server.TIME')
      // 先安检后审核的流程
      if (parseInt(_.get(store, 'state.clientInfo.detail.Registered', -2)) === 0 || isCutoff) {
        this.set('/access/message')
        return
      } else {
        this.set('/access/submit')
        return true
      }
    }
  },
  async submitNext() {
    this.messageNext()
  },
  addJumpLog(nextPath) {
    const { clientInfo, authInfo, redirectInfo } = store.state
    try {
      const info = {
        path: '下一跳路由：' + nextPath,
        clientInfo,
        authInfo,
        redirectInfo
      }
      console.debug(JSON.stringify(info))
    } catch (e) {
      console.debug('下一跳路由：' + nextPath)
    }
  }
}

export default processController
