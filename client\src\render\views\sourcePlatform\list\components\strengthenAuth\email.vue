<template>
  <!-- 邮箱登录 -->
  <div class="mobile-auth-page">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="0px"
      class="rule-form"
    >
      <el-form-item label="" prop="email">
        <el-input
          v-model="ruleForm.email"
          maxlength="50"
          disabled
        >
          <i slot="prefix" class="iconfont icon-youxiang" />
        </el-input>
      </el-form-item>
      <div class="specal-form-item">
        <div class="form-item-wrapper">
          <el-form-item label="" prop="checkCode">
            <el-input
              :id="calcId('-input-email_code')"
              v-model="ruleForm.checkCode"
              maxlength="6"
              :placeholder="$t('auth.enterCode')"
            >
              <i slot="prefix" class="iconfont icon-yanzhengma" />
            </el-input>
          </el-form-item>
        </div>

        <div
          v-if="!smsCodeIsDisabled && ruleForm.email"
          :id="calcId('-div-get_email_code')"
          class="sms-wrapper public-line-medium-btn"
          @click="sendPhoneCode"
        >
          {{ $t('auth.phoneCode') }}
        </div>

        <div v-else class="sms-wrapper public-line-medium-btn sms-disabled">
          {{ getSmsCodeButtonMsg }}
        </div>
      </div>
    </el-form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import _ from 'lodash'
import regular from '@/render/utils/regular'
import proxyApi from '@/service/api/proxyApi'

export default {
  name: 'EmailAuth',
  props: {
    idPre: {
      type: String,
      default: 'ui-ztp-email'
    },
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    const emailValidator = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('accountBind.notEmail')))
      } else {
        callback()
      }
    }
    const codeValidator = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error(this.$t('auth.enterCode')))
      } else if (!regular.phone(value) || value.length < 6) {
        return callback(
          new Error(this.$t('guestAuth.guest.phoneCodeValidateErr'))
        )
      } else {
        callback()
      }
    }
    return {
      ruleForm: {
        email: '',
        checkCode: ''
      },
      getCodeLoading: false,
      rules: {
        email: [{ validator: emailValidator, trigger: 'blur' }],
        checkCode: [{ validator: codeValidator, trigger: 'blur' }]
      },
      getSmsCodeLoading: false,
      maxGetSmsCodeInterval: 30,
      timer: null,
      nowTime: 0
    }
  },
  computed: {
    ...mapGetters(['clientInfo']),
    /**
     * 是否禁用获取验证码按钮
     */
    smsCodeIsDisabled() {
      if (this.getSmsCodeLoading) {
        return true
      }
      return this.nowTime > 0
    },
    /**
     * 验证码按钮文字
     */
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.ruleForm.email = this.authData.Email
      this.$refs['ruleForm'].validateField('email')
    },
    // 发送验证码
    async sendPhoneCode() {
      const email = this.ruleForm.email
      if (_.isEmpty(email)) {
        this.$message.error(this.$t('sourcePlatform.pEmail'))
        return false
      }

      this.getSmsCodeLoading = true
      const apiParam = {
        mobile_phone: email,
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        codeType: 'email'
      }
      const res = await proxyApi.smsSend(apiParam)
      this.getSmsCodeLoading = false
      if (res) {
        this.countDown(true)
      }
    },
    /**
     * 触发校验，校验通过则调用实际认证
     */
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.auth()
        }
      })
    },
    /**
     * 实际认证，增加防抖处理，防止重复认证
     */
    auth: _.debounce(
      async function() {
        this.$emit('loading', true)
        const { email, checkCode } = this.ruleForm
        const apiParam = {
          type: 'Mobile',
          mobile_phone: email,
          deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
          check_code: checkCode,
          isGuestAuth: 0,
          authFrom: this.authData.authFrom
        }
        const ret = await proxyApi.authIndex(apiParam)
        this.$emit('loading', false)
        const errorCode = parseInt(_.get(ret, 'errcode', -1))
        if (errorCode === 0) {
          this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    calcId(suf) {
      return this.idPre + suf
    }
  }
}
</script>
<style lang="scss">
.mobile-auth-page {
  width: 360px;
  margin: 0 auto;

  .rule-form {
    .specal-form-item {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .form-item-wrapper {
        width: 228px;
      }

      .sms-wrapper {
        width: 120px;
        height: 40px;
        line-height: 38px;
      }

      .sms-disabled {
        border-color: $line-color;
        background: $line-color;
        color: $disabled-color;
        cursor: not-allowed;
      }
    }

    .public-btn {
      margin-top: 8px;
    }
  }
  .el-input .iconfont {
    font-size: 16px;
  }
}
</style>
