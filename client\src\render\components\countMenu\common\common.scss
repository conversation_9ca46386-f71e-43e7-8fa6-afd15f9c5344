.mark-input .el-textarea__inner {
  height: 104px;
}
.my-device-page .count-menu-table.el-table, .change-count-page .count-menu-table.el-table {
  .has-gutter th.is-leaf {
    height: 39px;
    line-height: 39px;
    padding: 0;
  }
  td {
    height: 40px;
    line-height: 40px;
    padding: 0;
  }
}
body .table-pover {
  padding: 32px 0 0 0;
  border: none;
  .el-popconfirm__main {
    font-size: 13px;
    text-align: center;
    padding:0 32px 24px 32px;
    justify-content: center;
    word-wrap: break-word;
    word-break: normal;
  }
  .el-popconfirm__action {
    display: flex;
    button {
      display: flex;
      width: 50%;
      justify-content: center;
      padding: 11px 15px;
      border: none;
      border-radius: 0;
      font-size: 13px;
      font-weight: 400;
    }
    button:hover {
      background: $--color-primary;
      color: $light-color;
    }
    button:hover .el-button--primary {
      border-left: none;
    }
    .el-button + .el-button {
      margin-left: 0;
    }
    .el-button--text {
      border-top: 1px solid $line-color;
      color: $default-color;
      border-bottom-left-radius: 4px;
    }
    .el-button--primary {
      color: $--color-primary;
      border-bottom-right-radius: 4px;
      background-color: $light-color;
      border-top: 1px solid $line-color;
      border-left: 1px solid $line-color;
    }
  }
}
.table-current-tag {
  font-size: 12px;
  color: $--color-primary;
  line-height: 12px;
  padding: 1px 6px;
  text-align: center;
  background: $list-hover-bg;
  border: 1px solid rgba(83, 108, 230, 0.1);
  border-radius: 11px;
  margin-right: 8px;
}
 
.u-current-tag {
  color: $--color-primary;
  font-size: 13px;
  text-align: left;
  span {
    height: 28px;
    line-height: 28px;
    background: linear-gradient(360deg, $light-color 47%, $list-hover-bg);
    border-radius: 15px;
    display: inline-block;
    padding: 0 16px;
    .iconfont {
      margin-right: 3px;
      font-size: 14px;
    }
  }
}