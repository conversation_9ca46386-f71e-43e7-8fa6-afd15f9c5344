export default [
  // 列表页
  {
    path: '/source/list',
    name: 'sourceList',
    component: () => import('@/render/views/sourcePlatform/list'),
    platform: ['windows', 'mac', 'linux'],
    meta: {
      code: '501',
      menu: {
        name: 'nav.sourcePlatform',
        icon: 'icon-ziyuanpingtai', // 侧边栏显示图标
        moduleName: 'nav.commonFun', // 所属模块名称
        uiId: 'ui-menu-sourceList-li-resource_platform'
      }
    }
  }
]
