<!-- 消息吐丝 -->
<template>
  <transition enter-active-class="animate__animated animate__fadeInDown" leave-active-class="animate__animated animate__fadeOutUp">
    <p v-show="visible" class="toast">
      <span>
        <i class="iconfont" :class="[infoType === 'success' ? 'icon-chenggong' : infoType === 'error' ?'icon-shibai':'icon-putongxiangbuhegui']" />
        {{ message }}
      </span>
    </p>
  </transition>
</template>

<script>
export default {
  name: 'NewsHint',
  components: {},
  props: {
    // 成功失败 [success, error]
    infoType: {
      type: String,
      default: 'success'
    },
    // 提示消息
    message: {
      type: String,
      default: ''
    },
    // 多久消失
    duration: {
      type: Number,
      default: 3000
    },
    // 显示隐藏
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.timer = setTimeout(() => {
            clearTimeout(this.timer)
            this.timer = null
            this.$emit('update:visible', false)
          }, this.duration)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.toast {
  position: absolute;
  width: 100%;
  top: 24px;
  text-align: center;
  animation-duration: 200ms !important;
  span {
    display: inline-block;
    min-width: 146px;
    min-height: 40px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #e6e6e6;
    box-sizing: border-box;
    padding: 0 16px;
    border-radius: 21px;
    box-shadow: 0px 1px 10px 0px rgba(46, 60, 128, 0.2);
    backdrop-filter: blur(1px);
    line-height: 40px;
    text-align: center;
    color: $title-color;
    i {
      color: $success;
      margin-right: 10px;
      font-size: 16px;
    }
    .icon-shibai {
      color: $error;
    }
    .icon-putongxiangbuhegui{
      color: $waring
    }
  }
}
</style>
