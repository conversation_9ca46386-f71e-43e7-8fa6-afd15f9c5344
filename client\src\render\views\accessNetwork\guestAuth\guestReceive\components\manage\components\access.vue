<template>
  <div v-loading="!inited && loading" class="team-content">
    <empty v-if="inited && !tableData.length" />
    <el-table
      v-if="inited && tableData.length"
      v-loading="inited && loading"
      :data="tableData"
      border
      stripe
      :max-height="maxHeight"
      style="width: 100%"
      class="guest-formate-style-table"
    >
      <el-table-column
        prop="GustCodeType"
        fixed
        show-overflow-tooltip
        min-width="105"
        :label="$t('guestAuth.guest.info_90')"
      >
        <template slot-scope="scope">
          <span :class="['type-tag', scope.row.GustCodeType === 'reserve'?'reserve-type':'' ]">{{ scope.row.GustCodeType === 'reserve'? $t('guestAuth.guest.info_100') : $t('guestAuth.guest.info_91') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="Code"
        width="120"
        :label="$t('guestAuth.guest.info_91')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Code || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="Name"
        show-overflow-tooltip
        :label="$t('guestAuth.guest.info_92')"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="Tel"
        width="180"
        :label="$t('guestAuth.guest.info_93')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Tel || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="start"
        width="200"
        :label="$t('guestAuth.guest.info_94')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Start + $t('guestAuth.guest.info_48') + scope.row.End }}</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('guestAuth.guest.info_64')"
        width="100"
      >
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :visible-arrow="false" :content="$t('guestAuth.guest.info_95')" placement="bottom">
            <i :id="`ui-guest-receive-access-i-opt_cancel_${scope.row.ID}`" class="iconfont icon-a-qingchushanchu-02" @click="cancelHandle(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <verticalDialog
      :show="showOutLine"
      :show-close="false"
      :show-foot="true"
      width="384px"
      :show-cancel="reserveTip"
      class="guest-m-confirm-dialog"
      :loading="submiting"
      pop-name="guest-receive-access"
      @cancel="showOutLine = false"
      @confirm="confirmHandle"
    >
      <div slot="header" class="v-header">
        <i class="iconfont icon-putongxiangbuhegui" />
        {{ $t('guestAuth.guest.info_76') }}
      </div>
      <div class="g-s-diaolog-content">
        <div class="form-content">
          <p id="ui-guest-receive-access-p-alter_msg" class="outline-tips">{{ reserveTip?$t('guestAuth.guest.info_96'):$t('guestAuth.guest.info_97') }}</p>
        </div>
      </div>
    </verticalDialog>
  </div>

</template>
<script>
import '@/render/styles/guestTable.scss'
import empty from '../../audit/components/empty.vue'
import proxyApi from '@/service/api/proxyApi'
import { mapState } from 'vuex'
export default {
  components: {
    empty
  },
  props: {
    maxHeight: {
      default: '',
      type: [String, Number]
    }
  },
  data() {
    return {
      inited: 0,
      showOutLine: false,
      loading: false,
      tableData: [],
      submiting: false,
      currentRow: {},
      tip: '',
      reserveTip: true
    }
  },
  computed: {
    ...mapState(['clientInfo'])
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const ret = await proxyApi.reqReserveList({
        GreetUserID: _.get(this.clientInfo, 'accessStatus.UserID'),
        DeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0)
      })
      const data = ret.data || []
      data.forEach(item => {
        item.Start = item.GuestStartTime && item.GuestStartTime.split(' ')[0]
        item.End = item.GuestEndTime && item.GuestEndTime.split(' ')[0]
      })
      this.tableData = data
      this.loading = false
      this.inited = true
    },
    cancelHandle(row) {
      this.reserveTip = true
      this.currentRow = row
      this.showOutLine = true
    },
    async confirmHandle() {
      if (!this.reserveTip) {
        this.showOutLine = false
        return
      }
      this.showOutLine = false
      const { UserID } = _.get(this.clientInfo, 'accessStatus')
      const params = {
        GreetUserID: UserID,
        RelationID: this.currentRow.ID,
        DeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0)
      }
      const ret = await proxyApi.reqCancelReserve(params)
      if (parseInt(ret.errcode) === 0) {
        this.$message({
          message: this.$t('guestAuth.guest.info_102'),
          type: 'success'
        })
        this.getList()
      } else if (parseInt(ret.errcode) === 21126034) {
        this.reserveTip = false
        this.showOutLine = true
        this.getList()
      } else {
        this.$message({
          message: ret.errmsg,
          type: 'error'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.team-content{
  height: 100%;
}
.icon-a-qingchushanchu-02{
    color: $error;
    padding: 0 6px;
    cursor: pointer;
}
.type-tag{
  line-height: 22px;
  border-radius: 11px;
  background: $blue-1;
  color: $blue-2;
  padding: 0 8px;
  font-size: 13px;
}
.reserve-type{
  background: $green-1;
  color: $green-2;
}
.guest-m-confirm-dialog{
    .v-header{
        line-height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 600;
        color: $title-color;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 6px;
            font-weight: 400;
        }
    }
    .outline-tips{
        padding: 34px 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
}
</style>
