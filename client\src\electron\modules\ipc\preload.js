import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

const exportObj = {
  info: {
    node: () => process.versions.node,
    chrome: () => process.versions.chrome,
    electron: () => process.versions.electron,
    getProcessId: () => getCurrentProcessId()
  },
  // 渲染进程单向发送,不需要收到回应.
  // 但是我们会使用序列化的办法。通过信号发送回来。
  send: (eventName, params = {}) => {
    ipcRenderer.send('request', { eventName, params })
  },
  sendAsync: (eventName, params = {}) => {
    ipcRenderer.send('requestAsync', { eventName, params })
  },
  on: (channel, listener = () => { }) => {
    ipcRenderer.on('response', { channel, listener })
  }
}

const getCurrentProcessId = () => {
  return process.pid
}

contextBridge.exposeInMainWorld('eleIPC', exportObj)
