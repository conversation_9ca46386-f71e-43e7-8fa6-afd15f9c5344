<template>
  <div id="f-formate">
    <div class="f-message">
      <el-alert
        title="主要为了格式化小助手AssUI里面的请求信息"
        type="info"
        description="Windows路径：C:\Windows\SysWOW64\IsAgent\AssUI\Log 苹果路径：/Applications/ASM.app/Contents/Resources/NAC\ Assistant.app/Contents/MacOS/Log"
        show-icon
      />
    </div>
    <div class="f-textare">
      <el-input v-model="formatContent" type="textarea" />
    </div>
    <div class="u-select">
      <el-select v-model="format" style="width:300px" size="mini" placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button size="mini" type="primary" plain>格式化</el-button>
    </div>

    <pre>
      {{ afterFormatContent }}
    </pre>

  </div>
</template>

<script>

import _ from 'lodash'
import { Base64Decode } from '@/render/utils/global'

export default {
  data() {
    return {
      format: 'url',
      formatContent: '',
      options: [
        { value: 'url', label: 'urlDecode' },
        { value: 'base64', label: 'base64Decode' }
      ]
    }
  },
  computed: {
    afterFormatContent() {
      if (!_.isEmpty(this.formatContent)) {
        try {
          if (this.format === 'url') {
            return decodeURIComponent(this.formatContent)
          } else if (this.format === 'base64') {
            return Base64Decode(this.formatContent)
          }
        } catch (error) {
          return error
        }
      }
      return ''
    }
  }
}
</script>
<style lang="scss">
#f-formate{
  padding:10px;
  color:#000;
  display: block;
  height:100%;
  overflow-y: scroll;
  .f-message{
  }
  .f-textare{
    margin-top: 10px;
    .el-textarea__inner{
      height: 200px;
    }
  }
  .u-select{
    margin-top: 10px;
  }
}

</style>
