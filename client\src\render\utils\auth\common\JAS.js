import localStorage from '@/render/utils/cache/localStorage'
import ReconnectingWebSocket from 'reconnecting-websocket'
import { EventBus } from '@/render/eventBus'

/**
 * 久安世指纹
 */
class JAS {
  constructor() {
    const curIP = localStorage.getItem('curIP') || ''
    const ip = (curIP.indexOf('local') !== -1 ? curIP : 'localhost')
    this.baseUrl = 'ws://' + ip + ':9235/finger'
    this.connectionTimeout = 5000
  }

  /**
   * 初始化获取版本
   * 监听 JAS:Version 事件
   * @returns false 已初始化
   */
  initVersion() {
    if (!_.isNil(this.getVersion)) {
      return false
    }
    this.getVersion = new ReconnectingWebSocket(this.baseUrl + '/getversion', [], {
      // 重试间隔
      connectionTimeout: this.connectionTimeout
    })
    this.getVersion.addEventListener('message', (e) => {
      EventBus.$emit('JAS:Version', e)
    })
    this.getVersion.addEventListener('error', () => {
      console.error('getJASVersion:error')
      console.error(...arguments)
    })
    return true
  }

  /**
   * 初始化获取指纹
   * 监听 JAS:Finger 事件
   * @returns false 已初始化
   */
  initFinger() {
    if (!_.isNil(this.getFinger)) {
      return false
    }
    this.getFinger = new ReconnectingWebSocket(this.baseUrl + '/getimage?fopt=find', [], {
      // 重试间隔
      connectionTimeout: this.connectionTimeout
    })
    this.getFinger.addEventListener('message', (e) => {
      EventBus.$emit('JAS:Finger', e)
    })
    this.getFinger.addEventListener('error', () => {
      console.error('getJASFinger:error')
      console.error(...arguments)
    })
    return true
  }

  /**
   * 关闭版本websocket连接
   */
  closeVersion(isClear = false) {
    if (!_.isNil(this.getVersion)) {
      this.getVersion.close()
      if (isClear) {
        this.getVersion = null
      }
    }
  }

  /**
   * 关闭指纹websocket连接
   */
  closeFinger(isClear = false) {
    if (!_.isNil(this.getFinger)) {
      this.getFinger.close()
      if (isClear) {
        this.getFinger = null
      }
    }
  }

  /**
   * 关闭所有连接
   */
  close() {
    this.closeVersion(true)
    this.closeFinger(true)
  }
}
const jas = new JAS()

export default jas
