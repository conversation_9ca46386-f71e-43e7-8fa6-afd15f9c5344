
import { i18n } from '@/render/lang'
import { Loading } from 'element-ui'
import Vue from 'vue'

const dialogInstance = {
  loadingInstance: null,
  start(options) {
    const { msg } = options
    this.show = true
    const msgTxt = msg || i18n.t('dialogFoot.loading')

    this.loadingInstance = Loading.service({
      lock: true,
      text: msgTxt,
      spinner: 'el-icon-loading',
      customClass: 'u-loading-dialog',
      background: 'rgba(0, 0, 0, 0.5)'
    })
  },
  destory() {
    Vue.nextTick(() => {
      this.loadingInstance.close()
    })
  }

}

export default dialogInstance

