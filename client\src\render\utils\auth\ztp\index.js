/*
 * @Author: <EMAIL>
 * @Date: 2023-02-242 16:20:23
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-12-05 17:21:18
 * @Description: zpt(零信任)的辅助函数
 */

import store from '@/render/store'
import { TestQtModule } from '@/render/utils/global'
import authIndex from '@/render/utils/auth'
const ztpUtils = {
  /*
    判断是否是ztp模式
    @return {Bool}
  */
  whetherZTPMode() {
    if (_.get(store, 'state.clientInfo.online', true)) { // 443端口连通则不调用
      return false
    }

    if (!TestQtModule('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer')) { // 客户不存在该模块不调接口
      return false
    }

    // 8021.x模式不判断为零信任模式
    if (authIndex.isDot1xMode()) {
      return false
    }

    if (!_.get(store, 'state.clientInfo.webSlot.isKnockPort', false)) {
      return false
    }

    return true
  }
}

export default ztpUtils

