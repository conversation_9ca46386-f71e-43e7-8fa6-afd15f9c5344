<template>
  <!-- 账号密码登录 -->
  <div class="count-auth-page">
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="0px"
      class="rule-form"
    >
      <el-form-item label="" prop="userName">
        <el-input
          v-model="formData.userName"
          disabled
          maxlength="50"
        >
          <i slot="prefix" class="iconfont icon-zhanghu" />
        </el-input>
      </el-form-item>

      <el-form-item label="" prop="password">
        <el-input
          id="ui-ztp-account-input-user_passwd"
          v-model="formData.password"
          type="password"
          maxlength="50"
          :placeholder="customConf.clientPassword"
        >
          <i slot="prefix" class="iconfont icon-mima" />
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import _ from 'lodash'
import proxyApi from '@/service/api/proxyApi'
import { Base64Encode } from '@/render/utils/global'
import authTypes from '@/render/utils/auth/authTypes'
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import JSEncrypt from 'jsencrypt'

export default {
  name: 'AccountAuth',
  props: {
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    authType: {
      type: String,
      default: authTypes.User
    }
  },
  data() {
    const validateUserName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('accountBind.notAccount')))
      } else {
        callback()
      }
    }

    const validatePasswordName = (rule, value, callback) => {
      if (value === '') {
        callback(
          new Error(
            this.$t('auth.inputPassword', {
              password: this.customConf.clientPassword
            })
          )
        )
      } else {
        callback()
      }
    }
    return {
      rules: {
        password: [{ validator: validatePasswordName, trigger: 'blur' }],
        userName: [{ validator: validateUserName, trigger: 'blur' }]
      },
      nowTime: 0,
      isAutocomplete: false,
      formData: {
        userName: '',
        password: ''
      }
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'computeServeEntiretyConfig',
      'clientInfo',
      'authInfo'
    ]),
    /**
     * 是否需要修改用户名和密码前面的名称  自定义输入框标题
     * @returns {{clientUser, clientPassword}}
     */
    customConf() {
      const clientUser = this.$t('auth.ClientUser')
      const clientPassword = this.$t('auth.ClientPassword')
      return {
        clientUser,
        clientPassword
      }
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {},
  methods: {
    ...mapMutations(['setAuthInfo']),
    init() {
      this.formData.userName = this.authData.UserName
      this.$refs['ruleForm'].validateField('userName')
    },
    /**
     * 触发校验，校验通过则调用实际认证
     */
    submitForm() {
      this.$refs['ruleForm'].validate(async(valid) => {
        if (valid) {
          const { userName: UserName, password } = this.formData
          const { AddAuthType } = this.authData
          const crypt = new JSEncrypt()
          crypt.setPublicKey(this.computeServeEntiretyConfig.pubKey)
          const cryPassword = crypt.encrypt(password)
          const apiParam = {
            user_name: Base64Encode(UserName),
            deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
            password: cryPassword,
            authserver: Base64Encode(
              sourceListUtil.accountAuthTypeDic[AddAuthType]
            ),
            authFrom: 'addition',
            type: 'User'
          }
          this.$emit('loading', true)
          const res = await proxyApi.authIndex(apiParam)
          this.$emit('loading', false)
          const errorCode = parseInt(_.get(res, 'errcode', -1))
          if (errorCode === 0) {
            this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.count-auth-page {
  width: 360px;
  margin: 0 auto;
}

.count-auth-page ::v-deep .el-input__prefix .iconfont {
  font-size: 16px;
}
</style>
