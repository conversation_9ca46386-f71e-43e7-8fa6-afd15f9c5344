@import 'mixins/mixins';
@import 'common/var';

@include b(descriptions-item) {
  vertical-align: top;
  
  @include e(container) {
    display: flex;

    .el-descriptions-item__label,
    .el-descriptions-item__content {
      display: inline-flex;
      align-items: baseline;
    }
    .el-descriptions-item__content {
      flex: 1;
    }
  }

  @include e(label) {
    &.has-colon {
      &::after {
        content: ':';
        position: relative;
        top: -0.5px;
      }
    }
    &.is-bordered-label {
      font-weight: bold;
      color: $--color-text-secondary;
      background: $--descriptions-item-bordered-label-background;
    }
    &:not(.is-bordered-label) {
      margin-right: 10px;
    }
  }

  @include e(content) {
    word-break: break-word;
    overflow-wrap: break-word;
  }
}
