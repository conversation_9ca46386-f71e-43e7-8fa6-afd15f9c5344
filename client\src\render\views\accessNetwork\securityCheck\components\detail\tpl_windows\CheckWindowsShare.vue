<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" :loading="loading" :btn-text="btnName" :has-fixed="checkData.hasFixed" :show-btn="true" @fix="fixHandle" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.WindowsShare.h_3_rs") }}
          </p>
          <div v-for="item in forbidInfo" :key="item.ShareName" class="pc-info">
            <img :src="forImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item margin">
                  {{ $t("check.WindowsShare.h_5_rd") }}
                  <span>{{ item.ShareName }}</span>
                </div>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.WindowsShare.h_6_rd") }}
                <span>{{ item.ShareType }}</span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.WindowsShare.h_8_rs") }}
                <span>{{ item.SharePrivilege }} </span>
              </div>
              <div class="optional-item margin-style">
                {{ $t("check.WindowsShare.h_10_rs") }}
                <span>{{ item.ConNum }}</span>
              </div>
              <div class="optional-item">
                {{ $t("check.WindowsShare.h_12_rs") }}
                <span>{{ item.ShareAddr }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckWindowsShare',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {},
      collapseFlag: true,
      forbidInfo: [],
      btnName: this.$t('check.WindowsShare.js_1_d'),
      forImgSrc: require('@/render/assets/forbidSoft.png')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.getForbidInfo()
  },
  methods: {
    fixHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {},
        RepairType: 0,
        CreateProgress: 0
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.WindowsShare.js_4_s')
      })
    },
    getForbidInfo() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info.ShareInfo')
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      this.forbidInfo = list
    }
  }
}
</script>
