
import Auth from './auth'
import proxyApi from '@/service/api/proxyApi'
import _ from 'lodash'
import dot1xCommon from '@/render/utils/auth/dot1x'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import store from '@/render/store'
import { Message } from 'element-ui'
import authTypes from './authTypes'
import { Base64Encode } from '@/render/utils/global'
import index from './index'

class TwoFactor extends Auth {
  constructor() {
    super()
    this.type = authTypes.TwoFactor
  }

  isDot1xAuth() {
    const isMacWireLessAuth = _.get(store.state.authInfo, 'dot1x.isMacWireLessAuth', false)
    return index.isDot1xMode() && !isMacWireLessAuth
  }

  /**
  * 普通
   * @returns {Promise<boolean>}
  */
  async common(params) {
    const apiParam = {
      mobile_phone: params.smsMobile,
      deviceid: _.get(store.state.clientInfo.detail, 'DeviceID', 0),
      check_code: params.smsCode,
      authtypes: this.type,
      user_name: Base64Encode(params.userName),
      BeforeAuthType: _.get(store.state.authInfo.basic, 'BeforeAuthType'),
      isRecord: 0,
      hintOver: G_VARIABLE.g_hintOver,
      autoAuth: 0,
      type: 'SMS',
      factorType: params.factorType
    }
    const res = await proxyApi.authIndex(apiParam)
    return parseInt(res.errcode) === 0
  }

  /**
  * 802.1x
  * @returns {Promise<boolean>}
  */
  async dot1x(params) {
    const apiParam = {
      CheckCode: params.smsCode,
      Result: '0'
    }
    const res = await dot1xCommon.setUIContext(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', '失败'))
      return false
    }
    return true
  }

  /**
  * OTP双因子认证
   * @returns {Promise<boolean>}
  */
  async otpCommon(params) {
    const apiParam = {
      deviceid: _.get(store.state.clientInfo.detail, 'DeviceID', 0),
      user_name: Base64Encode(params.userName),
      check_code: params.checkCode,
      authtypes: this.type,
      userid: params.userid,
      BeforeAuthType: _.get(store.state.authInfo.basic, 'BeforeAuthType'),
      type: 'OTP',
      factorType: 'OTP'
    }
    const res = await proxyApi.authIndex(apiParam)
    return parseInt(res.errcode) === 0
  }

  /**
  * OTP强化认证
   * @returns {Promise<boolean>}
  */
  async otpAddition(params) {
    const apiParam = {
      deviceid: _.get(store.state.clientInfo.detail, 'DeviceID', 0),
      user_name: Base64Encode(params.userName),
      check_code: params.checkCode,
      userid: params.userid,
      authFrom: 'addition',
      type: 'OTP'
    }
    const res = await proxyApi.authIndex(apiParam)
    return parseInt(res.errcode) === 0
  }
}

export default TwoFactor
