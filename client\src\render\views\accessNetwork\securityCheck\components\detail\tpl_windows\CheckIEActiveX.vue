<template>
  <div class="check-ie-active">
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <!-- 安装插件模块 -->
      <div v-if="installList.length">
        <p class="model-title">
          {{ $t("check.IEActiveX.h_1_rs")
          }}<i
            :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
            @click="collapseFlag = !collapseFlag"
          />
        </p>
        <el-collapse-transition>
          <div v-show="collapseFlag" class="model-content">
            <div v-for="(item, index) in installList" :key="item.Name" class="pc-info">
              <div class="pc-info-rit">
                <div class="custom-name">
                  <div class="optional-item">
                    {{ $t("check.IEActiveX.h_3_rs") }}
                    <span>{{ item.Name }}</span>
                  </div>
                  <span v-if="item.btnText && item.RepairType === 'url'" :class="[checkData.hasFixed ? 'disable-link-btn': 'link-btn']" @click="installHandle(item, index)">{{ item.btnText }}</span>
                  <button v-if="item.btnText && item.RepairType === 'path'" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="installHandle(item, index)">
                    {{ item.btnText }}
                  </button>
                </div>
                <div class="optional-item">
                  {{ $t("check.IEActiveX.h_4_rs") }} {{ item.Desc }}
                </div>
                <div v-if="item.IsForbid === '1'" class="optional-item">
                  {{ $t("check.IEActiveX.js_2_rd") }}
                  <span>{{ $t('check.IEActiveX.js_21_rd') }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>

      <!-- 卸载插件模块 -->
      <div v-if="uninstallList.length">
        <p class="model-title">
          {{ $t("check.IEActiveX.h_6_rs")
          }}<i
            :class="collapseFlag_un ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
            @click="collapseFlag_un = !collapseFlag_un"
          />
        </p>
        <el-collapse-transition>
          <div v-show="collapseFlag_un" class="model-content">
            <div v-for="(item, index) in uninstallList" :key="item.CLSID" class="pc-info">
              <div class="pc-info-rit">
                <div class="custom-name">
                  <div class="optional-item">
                    {{ $t("check.IEActiveX.h_3_rs") }}
                    <span>{{ item.Name }}</span>
                  </div>
                  <button v-if="item.btnText" :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="unInstallHandle(item, index)">
                    {{ item.btnText }}
                  </button>
                </div>
                <div class="optional-item">
                  {{ $t("check.IEActiveX.h_4_rs") }}
                  <span>{{ item.Desc }} </span>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'
export default {
  name: 'CheckIEActiveX',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      fixData: {},
      installList: [],
      uninstallList: [],
      collapseFlag: true,
      collapseFlag_un: true
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.initFixTip()
    this.getInstallList()
    this.getUninstallList()
  },
  methods: {
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.IEActiveX.h_11_rs'),
        fixSteps: [this.$t('check.IEActiveX.h_13_rs'), this.$t('check.IEActiveX.h_15_rs')]
      }
    },
    getInstallList() {
      let mustInstall = _.get(this.checkData, 'CheckResult.CheckType.Info.MustInstall.ActiveX')
      let policy = _.get(this.checkData, 'CheckType.Option.MustInstall.ActiveX')
      if (!mustInstall || !policy) {
        return
      }
      if (!_.isArray(mustInstall)) {
        mustInstall = [mustInstall]
      }
      if (!_.isArray(policy)) {
        policy = [policy]
      }
      mustInstall.forEach(item => {
        for (let p = 0; p < policy.length; p++) {
          if (item.Name === policy[p].Name) {
            item.RepairType = policy[p].RepairType
            if (policy[p].RepairType === 'url' && policy[p].Url) {
              item.btnText = this.$t('check.IEActiveX.js_5_s')
              item.url = policy[p].Url
            }
            if (policy[p].RepairType === 'path' && policy[p].Path) {
              item.btnText = this.$t('check.IEActiveX.js_7_s')
              item.path = policy[p].Path
              item.param = policy[p].Param
              this.$set(item, 'hasFixed', this.checkData.hasFixed)
            }
            // 目前好像没有这个字段标记，情况是：已经安装的IE控件，状态是禁用，修复方式应该是将状态变成启用
            if (item.IsForbid === '1') {
              item.btnText = this.$t('check.IEActiveX.js_11_s')
              item.RepairType = '1'
              this.$set(item, 'hasFixed', this.checkData.hasFixed)
            }
          }
        }
      })

      this.installList = mustInstall
    },
    getUninstallList() {
      let ForbidInstall = _.get(this.checkData, 'CheckResult.CheckType.Info.ForbidInstall.ActiveX')
      if (!ForbidInstall) {
        return
      }
      if (!_.isArray(ForbidInstall)) {
        ForbidInstall = [ForbidInstall]
      }
      ForbidInstall.forEach(item => {
        if (item.CLSID !== '') {
          item.btnText = this.$t('check.IEActiveX.js_16_d')
          this.$set(item, 'hasFixed', this.checkData.hasFixed)
        }
      })
      this.uninstallList = ForbidInstall
    },
    async installHandle(item, index) {
      switch (item.RepairType) {
        case 'url':
          this.openUrl(item.url)
          break
        case 'path': { // 下载安装
          const params = {
            ItemID: this.checkData.ItemID,
            InsideName: this.checkData.InsideName,
            RepairParam: {
              path: item.path,
              param: item.param
            },
            RepairType: 0,
            CreateProgress: 1
          }
          await this.submitHandle({
            params,
            CheckItem: item,
            needProccess: true,
            tip: this.$t('check.IEActiveX.js_7_s')
          })
          this.$set(this.installList, index, item)
          break
        }
        case '1': { // 启用控件
          const params = {
            ItemID: this.checkData.ItemID,
            InsideName: this.checkData.InsideName,
            RepairParam: {
              CLSID: item.CLSID,
              IsEnable: '1',
              IsUninstall: '0'
            },
            RepairType: 0,
            CreateProgress: 0
          }
          this.submitHandle({
            params,
            CheckItem: item,
            tip: this.$t('check.IEActiveX.js_11_s')
          })
          this.$set(this.installList, index, item)
          break
        }
      }
    },
    unInstallHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          CLSID: item.CLSID,
          IsEnable: '0',
          IsUninstall: '1'
        },
        RepairType: 0,
        CreateProgress: 0
      }
      this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.IEActiveX.js_16_d')
      })
      this.$set(this.uninstallList, index, item)
    }
  }
}
</script>
<style lang="scss" scoped>
.check-ie-active{
  .computer-details-modle{
    .pc-info-rit{
    padding-left: 0;
  }
  }
}
</style>

