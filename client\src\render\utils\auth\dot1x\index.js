import agentApi from '@/service/api/agentApi'
import _ from 'lodash'
import { Base64Decode } from '@/render/utils/global'
import localStorage from '@/render/utils/cache/localStorage'
import store from '@/render/store'
import authIndex from '../index'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import common from '../common'

const dot1xCommon = {
  /**
   * 认证前安检
   */
  preSecCheck: {
    /**
     * 是否开启
     * @returns bool 需要
     */
    is() {
      if (!authIndex.isDot1xMode()) {
        return false
      }
      return parseInt(_.get(store.state.serveEntiretyConfig, 'client.IsDot1xPreSecCheck', 0)) === 1
    },
    /**
     * 是否需要认证前安检
     * 如果已进行过，则不需要
     * @returns bool 需要
     */
    cacheIs() {
      if (!authIndex.isDot1xMode()) {
        return false
      }
      const dot1xPreSecCheck = localStorage.getItem('dot1xPreSecCheck')
      console.log('dot1xPreSecCheck', dot1xPreSecCheck)
      if (parseInt(dot1xPreSecCheck) === 1) {
        return false
      }
      return this.is()
    },
    /**
     * 认证前安检
     * 保存5分钟
     */
    save() {
      localStorage.setItem('dot1xPreSecCheck', 1, 5 * 60)
    }
  },

  /**
   * 通用802.1x认证接口
   * @param param
   * @returns {*}
   */
  async auth(param) {
    const apiParam = {
      IsAsynCall: 0,
      AuthParam: {
        Dot1xEnable: 1,
        UserAuthHintOver: '1',
        ServerUseAlias: '0',
        ServerInsideName: '',
        DeviceId: _.get(store.state.clientInfo, 'basic.AgentID', 0),
        Ip: _.get(store.state.clientInfo, 'basic.Ip', ''),
        Mac: _.get(store.state.clientInfo, 'basic.Mac', ''),
        HardId: _.get(store.state.clientInfo, 'basic.HardId', '')
      }
    }
    _.merge(apiParam, param)

    const result = await agentApi.dot1xAuth(apiParam)

    this.setEmergentModeUserName(apiParam, result)
    // 保存是否IP变化
    authIndex.config.IsServerIPChanged = parseInt(_.get(result, 'ASM.IsServerIPChanged', 0)) === 1

    return result
  },

  handleAuthResponse(res) {
    let result = {}
    if (!_.isEmpty(res) && _.isString(res)) {
      try {
        result = JSON.parse(Base64Decode(res))
      } catch (error) {
        result = false
      }
    }
    // 没有errcode且存在code时采用code
    if (!_.has(result, 'errcode') && _.has(result, 'code')) {
      const code = parseInt(_.get(result, 'code'))
      if (code === 200) {
        result.errcode = 0
      } else {
        result.errcode = code
      }
    }
    return result
  },

  // 初始化802.1x配置
  async initDot1xConfig() {
    const res = await agentApi.getAuthBaseInfo()
    const client = _.get(res, 'ASM', {})
    const serverInfoConfig = _.get(client, 'AuthCtrl.ServerInfoConfig')
    if (_.isString(serverInfoConfig) && serverInfoConfig !== '') {
      client.server = JSON.parse(Base64Decode(serverInfoConfig))
    } else {
      client.server = {}
    }
    return client
  },

  /**
   * 双因子通知小助手验证码
   * @param param
   * @returns {*}
   */
  setUIContext(param) {
    const apiParam = {
      Module: 'Dot1x',
      Command: 'SetCheckCode',
      Type: 'SMSFactor'
    }
    _.merge(apiParam, param)
    return agentApi.setUIContext(apiParam)
  },

  /**
   * 802.1x认证后调用
   */
  async authEnd(params) {
    // 802.1x模式默认在线，不在线修改成在线
    this.changeOnline()
    // 服务器切换切换IP的话，由客户端重启
    if (!authIndex.config.IsServerIPChanged) {
      await Promise.all([
        commonUtil.detail(),
        commonUtil.server()
      ])
      // 认证完调用authEnd
      await common.authEnd(params)
    }
  },

  /**
   *  修改在线
   * @returns
   */
  changeOnline() {
    if (_.get(store.state, 'clientInfo.online', false)) {
      return true
    }
    store.commit('setClientInfo', _.merge(
      {},
      store.state.clientInfo,
      {
        basic: {
          IsOnline: 1
        }
      }
    ))
    return true
  },

  /**
   * 更新802.1x紧急模式下认证账户名
   */
  async setEmergentModeUserName(apiParam, result) {
    const isSuccess = parseInt(_.get(result, 'ASM.Result', -1)) === 0
    // 保存紧急模式 1 紧急 0 正常 默认正常模式
    const isAsmEmergentMode = parseInt(_.get(result, 'ASM.IsAsmEmergentMode', 0)) === 1

    if (isAsmEmergentMode && isSuccess) {
      const userName = _.get(apiParam, 'AuthParam.UserName', _.get(apiParam, 'AuthParam.CertInfo.IssueTo', _.get(apiParam, 'AuthParam.Phone', '')))
      const config = {
        UserName: userName
      }
      await agentApi.fileTools.ActionLocalFile('WebEmergentModeConfig', 'save', JSON.stringify(config))
      store.commit('setAuthInfo', _.merge(
        {},
        store.state.authInfo,
        {
          dot1x: {
            emergentModeUserName: userName
          }
        }
      ))
    }
  },

  /**
   * 获取802.1x紧急模式下认证账户名
   */
  async getEmergentModeUserName() {
    let emergentModeUserName = ''
    if (authIndex.isDot1xMode()) {
      const webEmergentModeConfig = await agentApi.fileTools.ActionLocalFile('WebEmergentModeConfig', 'read')
      emergentModeUserName = _.get(webEmergentModeConfig, 'UserName', '')
    }
    store.commit('setAuthInfo', _.merge(
      {},
      store.state.authInfo,
      {
        dot1x: {
          emergentModeUserName
        }
      }
    ))
  }
}

export default dot1xCommon

