import Vue from 'vue'
import VueRouter from 'vue-router'
import routeMenu from './menu'
import sundryMenu from './sundry'
/*
  侧边导航根据路由自动生成，每个路由必须包含meta里面的code码（包括子路由），侧导航栏通过对比code码前三位显示激活状态
*/
Vue.use(VueRouter)

// 获取原型对象上的push函数
const originalPush = VueRouter.prototype.push
// 修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [
  // 客户端主体流程路由框架(包含侧边栏，顶部菜单的界面)
  // 基础组件为Layout组件
  {
    path: '/',
    redirect: '/access/message',
    name: 'accessIndex',
    component: () => import('@/render/components/layout/index'),
    children: [
      ...routeMenu
    ]
  },
  {
    path: '*', // 404页面跳转
    name: 'notFound',
    redirect: '/access/message'
  },
  // 其他杂七杂八的菜单(不需要layout作为父路由的)
  ...sundryMenu
]

const router = new VueRouter({
  routes
})

export default router
