<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.UnlawfulConnectOut.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <!-- 违规拨号连接 -->
          <p v-if="HaveDialBehavior" class="tit-info">
            {{ $t("check.UnlawfulConnectOut.h_5_rs") }}
          </p>
          <div v-if="HaveDialBehavior" class="pc-info">
            <img :src="diaImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  <span>{{ $t('check.UnlawfulConnectOut.h_7_rd') }}</span>
                </div>
                <button :class="['btn-small', checkData.closeDial ? 'btn-disabled': 'public-medium-btn']" @click="closeDialHandle()">
                  {{ $t("check.UnlawfulConnectOut.js_3_d") }}
                </button>
              </div>
            </div>
          </div>

          <!-- 无线网卡 -->
          <p v-if="IsExistOpenWirelessNetCard" class="tit-info">
            {{ $t("check.UnlawfulConnectOut.h_28_rs") }}
          </p>
          <div v-if="IsExistOpenWirelessNetCard" class="pc-info">
            <img :src="wifiImgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  <span>{{ $t('check.UnlawfulConnectOut.h_29_rs') }}</span>
                </div>
                <button v-if="WirelessNetCard" :class="['btn-small', checkData.closeWifi ? 'btn-disabled': 'public-medium-btn']" @click="closeWifiHandle()">
                  {{ $t("check.UnlawfulConnectOut.js_4_d") }}
                </button>
              </div>
            </div>
          </div>

          <!-- 双网卡 -->
          <p v-if="AdapterInfo" class="tit-info">
            {{ $t("check.UnlawfulConnectOut.h_9_rs") }}
          </p>
          <div v-if="AdapterInfo">
            <div v-for="(item, index) in AdapterList" :key="item.Description" class="pc-info">
              <img :src="netImgSrc" alt="">
              <div class="pc-info-rit">
                <div class="custom-name">
                  <div class="optional-item">
                    {{ $t('check.UnlawfulConnectOut.h_11_rd') }}
                    <span>{{ item.Description }}</span>
                  </div>
                  <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                    {{ $t("check.UnlawfulConnectOut.js_5_d") }}
                  </button>
                </div>
                <div class="optional-item margin">
                  {{ $t("check.UnlawfulConnectOut.h_13_rd") }}
                  <span>{{ item.Mac }}</span>
                </div>
                <div class="optional-item">
                  {{ $t("check.UnlawfulConnectOut.h_15_rd") }}
                  <span>{{ item.Ip }}</span>
                </div>
              </div>
            </div>
          </div>

        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'
import howToFix from '../howToFix/common.vue'
export default {
  name: 'CheckSoftOnlyInstallStat',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      HaveDialBehavior: false,
      IsExistOpenWirelessNetCard: false,
      AdapterInfo: false,
      AdapterList: [],
      WirelessNetCard: '',
      diaImgSrc: require('@/render/assets/dial.gif'),
      wifiImgSrc: require('@/render/assets/netcard_wifi.jpg'),
      netImgSrc: require('@/render/assets/netcard.gif'),
      fixData: {}
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    softList() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return []
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      return list
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.initFixTip()
    this.initData()
  },
  methods: {
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.UnlawfulConnectOut.h_17_rs'),
        fixSteps: [this.$t('check.UnlawfulConnectOut.h_19_rs'),
          this.$t('check.UnlawfulConnectOut.h_23_rs'),
          this.$t('check.UnlawfulConnectOut.h_25_rs'),
          this.$t('check.UnlawfulConnectOut.h_27_rs')
        ]
      }
    },
    initData() {
      const info = _.get(this.checkData, 'CheckResult.Info')
      if (info.HaveDialBehavior === 'Yes') {
        this.HaveDialBehavior = true
        this.$set(this.checkData, 'closeDial', false)
      }
      if (info.IsExistOpenWirelessNetCard === 'Yes') {
        this.IsExistOpenWirelessNetCard = true
        this.WirelessNetCard = info.WirelessNetCard
        this.$set(this.checkData, 'closeWifi', false)
      }
      let list = info.AdapterInfo
      if (!list) {
        return
      }
      if (list && !_.isArray(list)) {
        list = [list]
      }
      this.AdapterInfo = true
      list.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.AdapterList = list
    },
    closeDialHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {},
        RepairMode: 0,
        RepairType: 0,
        CreateProgress: 1
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.UnlawfulConnectOut.js_3_d'),
        fixKey: 'closeDial'
      })
    },
    closeWifiHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: this.WirelessNetCard,
        RepairMode: 1,
        RepairType: 0,
        CreateProgress: 1
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.UnlawfulConnectOut.js_4_d'),
        fixKey: 'closeWifi'
      })
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          Mac: item.Mac,
          Time: '0',
          Ip: item.Ip
        },
        RepairMode: 2,
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.UnlawfulConnectOut.js_5_d')
      })
      this.$set(this.AdapterList, index, item)
    }
  }
}
</script>

