<!-- 802.1x模式下网卡列表-->
<template>
  <el-form-item
    v-show="agentMode"
    v-loading="loading"
    class="select-form-box"
  >
    <el-row>
      <el-col :span="20">
        <i class="iconfont icon-leixing slect-icon" />
        <el-select
          v-model="accessNetwork"
          popper-class="papper-body"
          :placeholder="$t('auth.selectNetworkList')"
        >
          <el-option
            v-for="item in networkOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
      <el-col :span="3" :offset="1">
        <div class="refresh" @click="refresh()">
          <i class="iconfont icon-shuaxin" />
        </div>
      </el-col>
    </el-row>
  </el-form-item>
</template>

<script>
import { mapGetters } from 'vuex'
import accessNetwork from '@/render/utils/accessNetwork'
import { EventBus } from '@/render/eventBus'

export default {
  name: 'NetworkList',
  data: function() {
    return {
      accessNetwork: '',
      networkOptions: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo']),
    agentMode() {
      return parseInt(_.get(this.clientInfo, 'basic.AgentMode', 0)) === 1
    },
    /**
     * 是否无线
     * @returns {boolean|int}
     */
    isWireLess() {
      if (this.agentMode) {
        accessNetwork.store.setAccessNetwork(this.accessNetwork)
        return accessNetwork.store.isWireLess()
      }
      return 0
    }
  },
  watch: {
    agentMode: {
      async handler(newVal) {
        // 802.1x模式才请求
        if (newVal) {
          this.init()
        }
      },
      immediate: false
    }
  },
  created() {
    if (accessNetwork.isInited() && this.agentMode) {
      this.init()
    }
    EventBus.$off('Dot1x:networkList:refresh')
    EventBus.$on('Dot1x:networkList:refresh', () => {
      this.init()
    })
  },
  beforeDestroy() {
    EventBus.$off('Dot1x:networkList:refresh')
    this.saveInfo()
  },
  methods: {
    /**
     * 初始化
     */
    async init() {
      this.networkOptions = await accessNetwork.getNetworkOptions()
      this.accessNetwork = accessNetwork.getAccessNetwork()
      EventBus.$emit('Dot1x:networkList:inited')
    },
    saveInfo() {
      accessNetwork.store.setAccessNetwork(this.accessNetwork)
    },
    /**
     * 刷新网卡
     */
    async refresh() {
      this.loading = true
      try {
        this.networkOptions = await accessNetwork.init(false)
        console.log('networkOptions', this.networkOptions)
        this.accessNetwork = accessNetwork.getAccessNetwork()
        if (_.isEmpty(this.accessNetwork) && _.isArray(this.networkOptions) && !_.isEmpty(this.networkOptions)) {
          const option = this.networkOptions.find((item) => {
            return item.value === this.accessNetwork
          })
          if (_.isUndefined(option)) {
            this.accessNetwork = ''
          }
        }
      } catch (e) {
        console.error(e)
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.select-form-box {
  .el-form-item__content {
    i.iconfont.icon-shuaxin {
      position:inherit;
      border-radius: 4px;
    }

    i.iconfont.icon-shuaxin:hover {
      color:  #536CE6;
    }

    .refresh{
      text-align:center;
      border: 1px solid #ededf1;
      border-radius: 4px;
    }

    .refresh:hover {
      border: 1px solid #536CE6;
      i.iconfont.icon-shuaxin{
        color:  #536CE6;
      }
    }
  }
}
</style>
