<template>
  <div class="empty-wrapper">
    <img :src="empty" alt="">
    <p>{{ $t('reg.noData') }}</p>
  </div>
</template>
<script>
const empty = require('@/render/assets/stateIllustration/empty.png')
export default {
  data() {
    return {
      empty
    }
  }
}
</script>
<style lang="scss" scoped>
    .empty-wrapper{
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img{
            width: 188px;
            height: 180px;
        }
        p{
            font-size: 14px;
            line-height: 20px;
            color: $default-color;
            margin-top: 32px;
        }
    }
</style>
