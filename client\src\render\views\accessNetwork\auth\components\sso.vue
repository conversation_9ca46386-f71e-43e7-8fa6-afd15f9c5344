<template>
  <div :class="[isOpenForceSSo? 'sso-full-page': '']">
    <iframe
      v-if="!isForceBrowser"
      :src="iframeSrc"
      width="360px"
      height="360px"
      sandbox="allow-forms allow-scripts allow-same-origin allow-popups "
      class="sso"
    />
    <layout-state
      v-if="isForceBrowser && isOpened"
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-txt="state['stateBtnTxt']"
      :state-btn-disabled="state['stateBtnDisabled']"
    />
    <p v-if="isForceBrowser&&!isOpened" class="force-browser-tip">{{ stateMsg }}</p>
    <p v-if="isForceBrowser&&!isOpened" :class="['public-btn', isStart?'disabled-public-btn':'']" @click="handleNext">{{ $t('auth.submit') }}</p>
  </div>
</template>
<script>
import { auth, checkLoginState, getThirdPageUrl, reportLoginState, createUniquId } from '@/render/utils/auth/sso'
import state from '@/render/components/layout/state'
import { mapGetters } from 'vuex'
import authIndex from '@/render/utils/auth/index'
import { addEvent, removeEvent } from '@/render/utils/global'
import agentApi from '@/service/api/agentApi'
const loading = require('@/render/assets/stateIllustration/loading.png')

export default {
  components: {
    'layoutState': state
  },
  props: {
    authType: {
      type: String,
      default: ''
    },
    bindData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      iframeSrc: '',
      taskId: '',
      whileFlag: false,
      stateMsg: this.$t('auth.browserAuth'),
      isStart: false,
      isOpened: false,
      uniqueId: '',
      isListen: false,
      timer: null
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig'
    ]),
    state() {
      const state = {
        stateImg: loading,
        stateMsg: '',
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      }

      if (this.isOpened) {
        state.stateMsg = this.$t('auth.sso.openedSsoAuthByDefaultBrowser')
      } else {
        state.stateMsg = this.$t('auth.sso.openSsoAuthByDefaultBrowser')
      }
      return state
    },
    /**
     * 是否802.1x认证
     */
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    isForceBrowser() {
      const SSOConfig = _.get(this.serveEntiretyConfig.server, 'SSOConfig', {})
      const authType = this.authType
      return SSOConfig[authType] && parseInt(SSOConfig[authType].ForceBrowser) === 1
    },
    isOpenForceSSo() {
      const defaultAuthType = _.get(this.serveEntiretyConfig.server, 'AUTHPARAM.DefaultAuthType')
      return parseInt(_.get(this.serveEntiretyConfig.server, 'SSO.ForceSSO', 0)) === 1 && this.authType === defaultAuthType
    }
  },
  mounted() {
    this.uniqueId = createUniquId()
    this.initSso()
  },
  beforeDestroy() {
    if (this.isListen) {
      removeEvent(window, 'message', this.listenHandle)
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    initSso() {
      if (this.isForceBrowser && !this.isOpenForceSSo) {
        return
      }
      this.submitForm()
    },
    async submitForm() {
      if (!this.allowAuthType()) {
        this.$message.error(this.$t('auth.notAllowAuthType'))
        return
      }
      const isLogin = await checkLoginState({ label: this.authType, uniqueId: this.uniqueId }) // 第三方是否是已登录状态
      if (isLogin) {
        const ret = await auth({ label: this.authType, uniqueId: this.uniqueId, loginState: isLogin, bindData: this.bindData }) // 入网登录
        if (ret) {
          this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
        }
        return true
      } else if (isLogin === false) {
        if (this.isForceBrowser) {
          if (this.isOpened) {
            return
          }
          await agentApi.windowOpenUrl(getThirdPageUrl(this.authType, this.uniqueId), -1)
          agentApi.minimizeWnd()
          this.tryAuth()
          this.isOpened = true
        } else {
          if (this.isListen) {
            return
          }
          this.iframeSrc = getThirdPageUrl(this.authType, this.uniqueId)
          this.isListen = true
          addEvent(window, 'message', this.listenHandle)
        }
      }
    },
    async listenHandle(e) {
      if (e.data && await reportLoginState(this.authType, e.data)) {
        this.submitForm()
      }
    },
    handleNext() {
      if (this.isStart) {
        return
      }
      this.stateMsg = this.$t('auth.sso.openSsoAuthByDefaultBrowser')
      this.isStart = true
      this.submitForm()
    },
    tryAuth() {
      this.timer = setInterval(async() => {
        const ret = await this.submitForm()
        if (ret) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 3000)
    },
    isAllowAuthType() {
      const allowAuthType = _.get(this.serveEntiretyConfig.server, 'AUTHPARAM.AllowAuthType')
      return allowAuthType.includes(this.authType)
    }
  }
}
</script>
<style scoped lang="scss">
.sso{
  margin: 0 auto;
  border:none;
  background-color:#FFFFFF;
  overflow-y: hidden;
}
.force-browser-tip{
  margin-top: -8px;
  margin-bottom: 16px;
  color: $default-color;
}
.sso-full-page{
  height: 100%;
}
.public-btn{
  margin-bottom: 16px;
}
.disabled-public-btn{
  opacity: 0.7;
  cursor: not-allowed;
  &:hover{
    background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
  }
}
</style>
