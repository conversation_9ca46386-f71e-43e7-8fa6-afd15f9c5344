<!--
 * @Author: <EMAIL>
 * @Date: 2021-09-20 09:33:30
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-28 15:49:29
 * @Description: debugger工具条里面的store状态管理查看
-->
<template>
  <div id="f-debugger-store">
    <div class="g-left">
      <div class="u-state">
        <h3>state</h3>
        <!-- <json-viewer -->
        <!--   :value="$store.state" -->
        <!--   :expand-depth="1" -->
        <!--   copyable -->
        <!--   boxed -->
        <!--   sort -->
        <!-- /> -->
        <pre>
          {{ $store.state }}
        </pre>
      </div>

      <div class="u-state">
        <h3>getters</h3>
        <pre>
          {{ $store.getters }}
        </pre>
        <!-- <json-viewer -->
        <!--   :value="$store.getters" -->
        <!--   :expand-depth="1" -->
        <!--   copyable -->
        <!--   boxed -->
        <!--   sort -->
        <!-- /> -->
      </div>
    </div>
  </div>
</template>
<script>
// import dateUtil from '@/render/utils/date/dateUtil'
export default {
  data() {
    return {}
  },

  methods: {
    init() {
      console.log(this.state)
    }
  }
}
</script>
<style lang="scss">
#f-debugger-store{
    display: flex;
    height:100%;
    h3{
        padding: 0 10px;
    }
    .g-right{
        position: relative;
        width: 40%;
        height:100%;
    }
    .g-left{
        position: relative;
        width: 100%;
        height: 100%;
        // border-right: 1px solid #eee;

     .u-state{
            height:50%;
            overflow: scroll;
            border-bottom: 1px solid #eee;
        }
    }
    .jv-container.boxed{
        border: 0;
        border-radius: 0;
    }
    .jv-container .jv-code {
        padding: 10px;
    }
}
</style>
