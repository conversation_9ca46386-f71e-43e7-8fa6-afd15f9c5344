<template>
  <div class="group-title">
    {{ title }}
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.group-title{
    position: relative;
    padding-left: 16px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    margin-left: 9px;
    color: $title-color;
    font-family: PingFang SC, PingFang SC-Medium;
    &::before{
        content:'';
        position: absolute;
        top: 7px;
        left: 0;
        width: 6px;
        height: 6px;
        background: $--color-primary-light-2;
    }
}
.first-title{
  margin-top: 0;
}
</style>
