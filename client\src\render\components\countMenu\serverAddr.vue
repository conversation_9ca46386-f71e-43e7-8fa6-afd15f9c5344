<template>
  <div v-loading="loading" class="server-address">
    <ul class="net-list">
      <li
        v-for="(item, index) in addrList"
        :key="item.addr + index"
        :style="{ top: item.top + 'px' }"
        :class="{ 'is-cur': item.isCur, 'delay-move': item.isCur }"
      >
        <div v-show="item.isCur" class="cur-link">
          <i :class="['point', !item.isLink ? 'fail' : '']" />
          <span>{{ $t('header.curLink') }}</span>
        </div>
        <div class="link-type">
          {{ item.isOut ? $t('header.outAddr') : $t('header.innerAddr') }}
          <span
            :id="`ui-set-ser_addr-span-conn_${
              item.isOut ? 'extranet' : 'intranet'
            }`"
            :class="['tag', item.isLink ? 'su-tag' : '']"
          >
            {{ item.isLink ? $t('header.connect') : $t('header.connectFail') }}
          </span>
        </div>
        <p
          v-if="isShowNetworkAddress"
          :id="`ui-set-ser_addr-p-${
            item.isOut ? 'extranet' : 'intranet'
          }_address`"
          class="addr"
        >
          {{ item.addr }}
        </p>
        <button
          v-show="!item.isCur && showChangeBtn"
          class="el-public-btn"
          @click="change(index)"
        >
          {{ $t('header.switch') }}
          <i class="iconfont icon-qiehuan" />
        </button>
      </li>
    </ul>

    <div v-if="qrCode.OpenQrCode === '1'" class="mark-box">
      <div class="title">
        {{ $t('header.mobileConf') }}
      </div>
      <img id="ui-set-ser_addr-img-iphone_scan_img" :src="scanImg" alt="">
    </div>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import agentApi from '@/service/api/agentApi'
import proxyApi from '@/service/api/proxyApi'
import urlUtils from '@/render/utils/url'
export default {
  data() {
    return {
      addrList: [],
      qrCode: {},
      loading: true
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo']),
    scanImg() {
      return urlUtils.getBaseIPPort() + this.qrCode.URL
    },
    showChangeBtn() {
      return (
        _.get(this.serveEntiretyConfig, 'server.EnvSwitch.AllowUserSwitch') ===
        'on'
      )
    },
    isShowNetworkAddress() {
      return _.get(this.serveEntiretyConfig, 'server.EnvSwitch.HideNetworkAddress') !== 'on'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations(['setClientInfo']),
    async init() {
      const ret = await agentApi.spaToAuth({
        // 获取服务器信息
        SPAType: 'Test',
        ServerIP: '',
        AuthData: '',
        DevData: ''
      })
      if (ret.ASM) {
        const webSlot = ret.ASM
        const curIsOut = parseInt(webSlot.CurrentUse) === 1
        this.addrList = [
          {
            addr: curIsOut ? webSlot.ServerIPEx : webSlot.ServerIPIn,
            isCur: true,
            isLink: curIsOut
              ? webSlot.ConnectedEx === 'true'
              : webSlot.ConnectedIn === 'true',
            isOut: curIsOut,
            top: 0
          },
          {
            addr: curIsOut ? webSlot.ServerIPIn : webSlot.ServerIPEx,
            isCur: false,
            isLink: curIsOut
              ? webSlot.ConnectedIn === 'true'
              : webSlot.ConnectedEx === 'true',
            isOut: !curIsOut,
            top: 112
          }
        ]
        if (webSlot.Result === 'true') {
          this.getMobileScanConf()
        } else {
          this.loading = false
        }
      }
    },
    async getMobileScanConf() {
      const ret = await proxyApi.getQrCode()
      this.loading = false
      if (parseInt(_.get(ret, 'errcode')) === 0) {
        this.qrCode = ret.data
      }
    },
    change(idx) {
      const list = this.addrList
      if (!list[idx].isLink) {
        this.$dialogTip({
          content: this.$t('serverChangeTip'),
          popName: 'ui-server-addr',
          success: () => {
            this.changeHandle(idx)
          }
        })
        return
      }
      this.changeHandle(idx)
    },
    changeHandle(idx) {
      const aimIdx = idx === 1 ? 0 : 1
      const list = this.addrList
      list[idx].top = 0
      list[idx].isCur = true
      list[aimIdx].isCur = false
      list[aimIdx].top = 112
      const IsUseEx = list[idx].isOut ? 1 : 0
      this.setClientInfo(
        _.merge({}, this.clientInfo, { basic: { ServerIP: list[idx].addr }})
      )
      agentApi.changeNetMode({ IsUseEx, IsManual: 1, SwitchToLinkable: list[idx].isLink ? 1 : 0 })
    }
  }
}
</script>
<style lang="scss" scoped>
    .server-address{
        padding: 14px 32px;
        color: $title-color;
        font-size: 14px;
        height: 100%;
        position: relative;
        .net-list{
            position: relative;
            li{
                border: 1px solid $line-color;
                border-radius: 4px;
                padding: 10px 16px;
                width: 100%;
                position: absolute;
                background: white;
                transition: top .3s ease 0.1s;
                .cur-link{
                    display: flex;
                    align-items: center;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    text-align: left;
                    line-height: 20px;
                    margin-bottom: 6px;
                    .point{
                        width: 8px;
                        height: 8px;
                        background: $success;
                        border-radius: 50%;
                        margin-right: 6px;
                    }
                    .fail{
                        background: $error-1;
                    }
                }
                .link-type{
                    font-family: PingFang SC, PingFang SC-Regular;
                    line-height: 24px;
                    margin-bottom: 2px;
                    display: flex;
                    align-items: center;
                    .tag{
                        font-size: 13px;
                        line-height: 20px;
                        padding: 0 4px;
                        border-radius: 2px;
                        background: $error-2;
                        color: $error-1;
                        margin-left: 6px;
                    }
                    .su-tag{
                        color: $green-4;
                        background: $green-1;
                    }
                }
                .addr{
                    font-family: PingFang SC, PingFang SC-Regular
                }
                .el-public-btn{
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 16px;
                    min-width: 80px;
                    padding: 0 21px;
                    i{
                        font-size: 12px;
                        margin-left: 6px;
                    }
                    &:hover{
                        background: linear-gradient(315deg, $--color-primary-dark-2, $--color-primary-dark-1);
                    }
                }
            }
            .is-cur{
                box-shadow: 0px 8px 20px 0px rgba(16,36,66,0.10);
            }
            .delay-move{
                transition: top .3s ease;
                z-index: 1;
            }
        }
        .mark-box{
            position: absolute;
            bottom: 40px;
            left: 0;
            right: 0;
            padding: 0 32px;
            .title{
              display: flex;
              align-items: center;
              color: $title-color;
              font-size: 14px;
              line-height: 24px;
                &::before, &::after{
                    content: '';
                    flex: 1;
                    height: 1px;
                }
                &::before{
                  margin-right: 6px;
                  background: linear-gradient(90deg,#ffffff, #e2e2e6);
                }
                &::after{
                  margin-left: 6px;
                  background: linear-gradient(90deg,#e2e2e6, #ffffff);
                }
            }
            img{
                width: 120px;
                height: 120px;
                margin: 16px auto 0 auto;
            }
        }
    }
</style>
