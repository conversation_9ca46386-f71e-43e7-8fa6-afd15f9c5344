<!--
 * @Author: <EMAIL>
 * @Date: 2021-09-19 15:58:41
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-07-05 18:51:48
 * @Description: file content
-->
<template>
  <div :id="[max ? 'f-debuuger-max' : 'f-debuuger']" @click="showHandle">
    <template v-if="showDebbuger">

      <debuggerIndex v-show="maxOrMin === 'max'" @mini="maxOrMinHandle" @close="close" />

      <div v-show="maxOrMin === 'mini'" class="u-maxOrmin">
        <a href="javascript:void(0)" @click="maxOrMinHandle"><i class="el-icon-orange" /></a>
      </div>
    </template>
  </div>
</template>
<script>
import debuggerIndex from '@/render/views/debugger/index.vue'
import localStorage from '@/render/utils/cache/localStorage'
export default {
  components: {
    debuggerIndex
  },
  data() {
    return {
      maxOrMin: 'mini',
      showDebbuger: false,
      lastTime: '',
      count: 0
    }
  },
  computed: {
    max() {
      return this.showDebbuger && this.maxOrMin === 'max'
    }
  },
  watch: {
    showDebbuger(val) {
      if (val) {
        document.getElementById('eruda').style = 'display:block'
      } else {
        document.getElementById('eruda').style = 'display:none'
      }
    }
  },
  created() {
    this.initState()
  },
  methods: {
    initState() {
      this.showDebbuger = localStorage.getItem('showDebuger') || false
    },
    maxOrMinHandle() {
      this.maxOrMin = this.maxOrMin === 'mini' ? 'max' : 'mini'
    },
    close() {
      this.showDebbuger = false
      this.maxOrMinHandle()
      localStorage.removeItem('showDebuger')
    },
    showHandle() { // 击左下角5次显示
      if (this.showDebbuger) {
        return
      }
      const current = new Date().getTime()
      if (!this.lastTime || current - this.lastTime < 300) {
        this.count += 1
        this.lastTime = current
        if (this.count > 4) {
          this.showDebbuger = true
          this.lastTime = ''
          this.count = 0
          localStorage.setItem('showDebuger', true, 86400)
        }
      } else {
        this.lastTime = ''
        this.count = 0
      }
    }
  }
}
</script>

<style  lang="scss">
#f-debuuger {
  width: 50px;
  height: 50px;
  position: fixed;
  z-index: 999;
  left: 0;
  bottom: 0;

  .u-maxOrmin {
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: #42b983;
  }

}

#f-debuuger-max {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80%;
  width: 100%;
  z-index: 2001;
  background: #fff;
}
</style>

