<template>
  <el-dialog
    :visible.sync="show"
    :width="width"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="0"
    custom-class="yg-confirm-dialog"
  >
    <slot>
      <div class="default-tip">
        <span>{{ msg }}</span>
      </div>
    </slot>
    <div v-if="show" slot="footer" class="dialog-footer">
      <button
        v-if="showCancelButton"
        :id="`ui-${popName}-cancel`"
        :style="buttonStyle"
        @click="closeHanlde(0)"
      >
        {{ noText }}
      </button>
      <button
        v-if="showConfirmButton"
        :id="`ui-${popName}-confirm`"
        :style="buttonStyle"
        @click="closeHanlde(1)"
      >
        {{ okText }}
      </button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'ConfirmDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    /**
     * 是否展示确定按钮
     */
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    /**
     * 是否展示取消按钮
     */
    showCancelButton: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '260px'
    },
    confirmText: {
      type: String,
      default: null
    },
    cancelText: {
      type: String,
      default: null
    },
    msg: {
      type: String,
      default: ''
    },
    popName: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 直接使用props的默认值会有缓存，中英切换后显示不正确
    okText() {
      if (this.show && this.confirmText) {
        return this.confirmText
      } else {
        return this.$t('confirmDialog.yes')
      }
    },
    noText() {
      if (this.show && this.cancelText) {
        return this.cancelText
      } else {
        return this.$t('confirmDialog.no')
      }
    },
    buttonStyle() {
      return {
        width: (!this.showCancelButton || !this.showConfirmButton) ? '100%' : '50%'
      }
    }
  },
  mounted() {},
  methods: {
    closeHanlde(val) {
      if (val > 0) {
        this.$emit('ok')
      } else {
        this.$emit('no')
      }
      this.$emit('update:show', false)
    }
  }
}
</script>
<style lang="scss">
.el-dialog__wrapper .yg-confirm-dialog {
  border-radius: 5px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
    line-height: 26px;
    font-size: 13px;
    color: $title-color;
    .default-tip{
      padding: 26px 32px 22px 32px;
      word-break: keep-all;
      word-wrap: break-word; // 只对英文起作用，以单词作为换行依据。
      white-space: pre-wrap; //只对中文起作用，强制换行。
    }
  }
  .el-dialog__footer {
    padding: 0;
    .dialog-footer{
      display:flex;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      overflow: hidden;
    }
    button {
      text-align: center;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      color: $default-color;
      background: $light-color;
      border-top: 1px solid $line-color;
      &:first-child {
        border-bottom-left-radius: 4px;
        color: $--color-primary;
      }
      &:nth-child(2){
        border-bottom-right-radius: 4px;
        border-left: 1px solid $line-color;
      }
      &:hover {
        background: $--color-primary;
        color: $light-color;
      }
    }
  }
}
</style>
