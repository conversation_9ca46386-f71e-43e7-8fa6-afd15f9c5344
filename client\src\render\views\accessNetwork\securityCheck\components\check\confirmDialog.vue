<template>
  <div class="yg-confirm-dialog">
    <el-dialog
      custom-class="yg-confirm-modal"
      width="260px"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showDialog"
      @close="closeHandle"
    >
      <slot />
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    colseHandle() {
      this.$emit('close')
    }
  }
}
</script>
