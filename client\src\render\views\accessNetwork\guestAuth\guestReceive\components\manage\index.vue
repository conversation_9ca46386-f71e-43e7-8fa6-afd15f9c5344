<template>
  <div class="guest-audit-page">
    <div class="content">
      <tab v-model="activeTab" />
      <div ref="tableContent" class="table-wrapper">
        <component :is="manageType" :max-height="maxHeight" />
      </div>
    </div>
  </div>
</template>
<script>
import tab from './components/tab.vue'
import single from './components/singleManage.vue'
import team from './components/teamManage'
import access from './components/access'
export default {
  components: {
    tab,
    single,
    team,
    access
  },
  data() {
    return {
      activeTab: 'single',
      maxHeight: 0
    }
  },
  computed: {
    manageType() {
      return this.activeTab
    }
  },
  mounted() {
    this.initTableHeight()
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.maxHeight = this.$refs.tableContent.offsetHeight - 16
      })
    },
    initTableHeight() {
      this.setTableHeight()
      window.addEventListener('resize', this.setTableHeight)
      this.$once('hook:destroyed', () => {
        window.removeEventListener('resize', this.setTableHeight)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .guest-audit-page{
    height: 100%;
    .content{
      padding-top: 24px;
      height: 100%;
      .table-wrapper{
        height: calc(100% - 34px);
        padding-top: 16px;
      }
    }
    &::v-deep .el-table__header .el-table__cell{
      padding: 8px 0;
      color: $title-color;
    }
  }

</style>
