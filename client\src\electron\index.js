'use strict'
const beforeCreate = require('./modules/window/lifecycle/beforeCreate')
const create = require('./modules/window/lifecycle/create')
const afterCreate = require('./modules/window/lifecycle/afterCreate')

const rootDir = __dirname

/**
  * --------------beforeCreate------------------
  *  1.初始化日志组件,写入electron安装下的Log;
  *  2.捕获electron的全局错误,写入日志;
  *  3.单实例锁定,如果触发单实例锁,直接退出。不会进入下一个流程
  *  4.格式化和获取所有的命令行参数
  *
  * --------------create-------------------------
  *  这个生命周期里面的内容要求尽量简单
  *  1.实例化BrowserWindow
  *  2.绑定窗口状态保持组件
  *
  * ---------------afterCreate------------------
  *  1.opendevtools的处理;
  *  2.主进程和渲染进程的通信绑定;
  *  3.注册window和app的事件
  *
**/

beforeCreate().then(({ log, argv }) => {
  return create({ log, argv })
}).then(({ mainWindow, log, argv }) => {
  return afterCreate({ mainWindow, log, argv, rootDir })
}).then(() => {
  // beforeDestory()
})

