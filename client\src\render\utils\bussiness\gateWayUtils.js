import store from '@/render/store'
import { Base64 } from 'js-base64'

const gateWayUtils = {
  gateWayCache: [],
  cacheTime: 0,
  timer: null,
  creatTime() { // 5s钟没推送数据且存了异常网关则展示异常网关
    gateWayUtils.timer && clearTimeout(gateWayUtils.timer)
    gateWayUtils.timer = setTimeout(() => {
      if (gateWayUtils.cacheTime === 1) {
        console.log('5s后更新数据')
        gateWayUtils.updateStore(gateWayUtils.gateWayCache)
      }
      clearTimeout(gateWayUtils.timer)
      gateWayUtils.timer = null
      gateWayUtils.cacheTime = 0
      gateWayUtils.gateWayCache = []
    }, 5000)
  },
  analyzeMsg(base64Str) {
    try {
      const str_json = Base64.decode(base64Str)
      return JSON.parse(str_json).data
    } catch (error) {
      console.log('解析报错', error, base64Str)
      return []
    }
  },
  // 是否存在网关异常项，网关异常延需要延时展示
  isAbnormalGateWay(gateWays) {
    if (!gateWays || !gateWays.length) {
      return true
    }

    const unseableItems = gateWays.filter(item => parseInt(item.usable) === 0) // 不可用
    const normalItems = gateWays.filter(item => parseInt(item.usable) === 1) // 可用
    return unseableItems.length > 0 || normalItems.length === 0 // 有不可用项展示异常（红点）,没有不可用项有一个正常项就展示正常（绿点）
  },

  gateWayhandle(qMsgRequest) {
    // console.log('gateWay返回结果', qMsgRequest)
    gateWayUtils.creatTime()
    const base64Str = _.get(qMsgRequest, 'result.ASM.Data')
    const list = gateWayUtils.analyzeMsg(base64Str)
    // console.log('解析后数据', list)
    if (!gateWayUtils.isAbnormalGateWay(list) || gateWayUtils.cacheTime > 0) { // 正常网关数据或者连续二次异常直接更新Store数据
      gateWayUtils.updateStore(list)
      gateWayUtils.gateWayCache = []
      gateWayUtils.cacheTime = 0
    } else {
      // console.log('延迟一次')
      gateWayUtils.cacheTime = 1
      gateWayUtils.gateWayCache = list || []
    }
  },
  updateStore(gateWays) {
    const oldVpnStatus = _.get(store, 'state.gateWayInfos.VPNStatus', 0)
    if (!gateWays || gateWays.length === 0) {
      store.commit('setGateInfos', { state: 2, gateWayMap: {}, total: 0, VPNStatus: oldVpnStatus })
      return
    }
    const old_map = _.get(store, 'state.gateWayInfos.gateWayMap', {})
    const new_map = {}
    const stateInfo = {}
    gateWays.forEach(item => {
      stateInfo[item.usable + ''] = true
      const oldItem = old_map[String(item.gwid)]
      if (oldItem) {
        new_map[String(item.gwid)] = { ...oldItem, ...item }
      } else {
        new_map[String(item.gwid)] = item
      }
    })
    const state = stateInfo['0'] ? 0 : stateInfo['1'] ? 1 : 2
    store.commit('setGateInfos', { state, gateWayMap: new_map, total: gateWays.length, VPNStatus: oldVpnStatus })
  }

}

export default gateWayUtils
