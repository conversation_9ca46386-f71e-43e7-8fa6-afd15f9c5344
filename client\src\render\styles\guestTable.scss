.guest-formate-style-table {
  border: 1px solid $line-color;
  .el-table__fixed-right-patch {
    background: $gray-3;
  }
  .el-table__header {
    .el-table__cell {
      background: $gray-3;
      border-right: 1px solid $line-color;
      border-bottom: 1px solid $line-color;
      padding: 13px 0;
      color: $gray-4;
      .cell {
        line-height: 22px;
        font-weight: 500;
      }
    }
    th.el-table__cell.is-leaf{
      border-bottom: 1px solid $line-color;
    }
    .has-gutter{
      tr{
        .is-leaf:nth-last-of-type(2){
          border-right: none;
        }
      }
    }
  }
  .el-table__row {
    .el-table__cell {
      padding: 8px 0;
      color: $title-color;
      border-right: none;
      &:first-child {
        .cell {
          text-align: left;
        }
      }
      .cell {
        line-height: 21px;
      }
    }
  }
  &::after {
    width: 0px;
  }
  &::before{
    width: 0px;
  }

  .el-table__header-wrapper {
    background: none;
  }
}
