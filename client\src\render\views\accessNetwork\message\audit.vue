<!--
 * @Author: <EMAIL>
 * @Date: 2021-11-23 16:05:04
 * @LastEditors: gening <EMAIL>
 * @LastEditTime: 2023-08-26 16:41:59
 * @Description: 设备审核和审核后组件，以下两种情况，会进入该审核和审核后过渡页面
    1.设备注册状态是0进入；
    2.设备注册状态是1(已注册)，但是有auditNextStatus 或者 auditCheckNextStatus
-->
<template>
  <div id="f-audit">
    <layout-state
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-txt="state['stateBtnTxt']"
      :state-btn-disabled="state['stateBtnDisabled']"
      :state-btn-fun="state['stateBtnFun']"
      state-id="ui-accessNetwork-audit-div-check_msg"
      state-btn-id="ui-accessNetwork-audit-button-refresh"
    />
  </div>
</template>

<script>
import state from '@/render/components/layout/state'
import processController from '@/render/utils/processController'
import { mapGetters, mapState } from 'vuex'
import localStorage from '@/render/utils/cache/localStorage'
import agentApi from '@/service/api/agentApi'
import { EventBus } from '@/render/eventBus'
const loading = require('@/render/assets/stateIllustration/loading.png') // loading 图片
const auditing = require('@/render/assets/stateIllustration/auditing.png') // 审核中
export default {
  components: {
    'layoutState': state
  },
  data() {
    return {
      state: {
        stateImg: loading,
        stateMsg: this.$t('netExamine'),
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      },
      taskId: null
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo', 'serveEntiretyConfig', 'redirectInfo']),
    ...mapGetters(['computeNetAccessStatus'])
  },
  watch: {
    '$i18n.locale': function() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  destroyed() {
    this.clearRoadClock()
  },
  methods: {
    async init() {
      // 已注册待审核状态
      if (_.get(this.computeNetAccessStatus(), 'registeredStatus.needAduit')) {
        // 先安检后审核杀进程或重启auditCheckNextStatus标记丢失补刀
        if (parseInt(_.get(this.serveEntiretyConfig, 'server.CLIENTCHECK.ControlPostion', 0)) === 1 && !localStorage.getItem('auditCheckNextStatus')) {
          console.debug('auditCheckNextStatus补刀')
          localStorage.setItem('auditCheckNextStatus', JSON.stringify({ DeviceID: _.get(this.clientInfo, 'detail.DeviceID') }), 3600)
        }
        this.setAuditStatus()
      } else {
        EventBus.$emit('client:show')
        await this.auditNextHandle()
      }
    },
    // 设置定时器刷新
    setReloadClock(times = 1000) {
      if (!this.taskId) {
        this.taskId = setTimeout(() => {
          processController.set('/access/message')
        }, times)
      }
    },
    // 清除定时器刷新
    clearRoadClock() {
      if (this.taskId) {
        clearTimeout(this.taskId)
        this.taskId = null
      }
    },
    // 设备未审核状态
    setAuditStatus() {
      const state = {}
      state['stateMsg'] = this.$t('oWelcome.WaitAudit')
      state['stateBtnTxt'] = this.$t('networkStatus.refreshStaus')
      state['stateImg'] = auditing
      state['stateBtnFun'] = () => {
        this.clearRoadClock()
        processController.set('/access/message')
      }
      this.state = state
      this.setReloadClock(12000)
    },
    // 审核后的步骤(一般是有标记cookie才能进入)
    async auditNextHandle() {
      const params = { auditCheckNextStatus: false }
      if (localStorage.getItem('auditNextStatus')) {
        const authEndParams = JSON.parse(localStorage.getItem('auditNextStatus'))
        localStorage.removeItem('auditNextStatus')
        // 重新调用authEnd告诉客户端参数
        await agentApi.AuthEnd(authEndParams)
      }
      // 审核后安检的步骤
      if (localStorage.getItem('auditCheckNextStatus')) {
        localStorage.removeItem('auditCheckNextStatus')
        params.auditCheckNextStatus = true
      }

      processController.next(params)
    }
  }
}
</script>
<style>
#f-audit {
  position: relative;
  height: 100%;
}
</style>
