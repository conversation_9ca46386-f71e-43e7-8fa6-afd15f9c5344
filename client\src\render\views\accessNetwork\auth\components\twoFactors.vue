<!-- 双因子认证 -->
<template>
  <VerticalDialog
    :show.sync="show"
    :title="showDownloadAPPTitle ? $t('auth.downloadAPP'): $t('auth.smsCode')"
    width="480px"
    @beforeClose="smsBeforeClose"
  >
    <div
      :class="['dialog-content', twofactorAuthType === 'OTP' ? 'otp-dialog-content': '']"
    >
      <div class="towFactors-select-wrapper">
        <el-select id="ui-accessNetwork-towFactors-select-authtype" v-model="twofactorAuthType" class="towFactors-select" popper-class="papper-body">
          <el-option v-for="item in towFactorsOptions" :id="`ui-accessNetwork-towFactor-li-${item.value}_auth`" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <div v-if="twofactorAuthType === 'Mobile'" class="mobile-fa-content">
        <div class="item">
          <div class="left">
            {{ $t('auth.firstStep') }}
          </div>
          <div class="right">
            <p v-if="mobile" class="text">{{ $t('auth.sysjh') }}</p>
            <p v-if="mobile" class="text">{{ $t('auth.regTel') }} {{ displayMobile }}</p>
            <p v-if="!mobile" class="text">{{ $t('auth.notTelTip') }}</p>
            <div class="custom-input-wrapper">
              <el-input v-if="!mobile" id="ui-accessNetwork-twoFactors-input-tel" v-model="customTel" class="towFactors-tel-input" :placeholder="$t('auth.inputPlaceholder')">
                <i slot="prefix" class="iconfont icon-shoujihao" /></el-input>
              <button
                v-if="!isDot1xMode"
                id="ui-accessNetwork-twoFactors-button-get_code"
                style="width: 120px;height: 40px;"
                :class="{'public-line-medium-btn':!smsCodeIsDisabled, 'disabled-button-middle':smsCodeIsDisabled }"
                @click="getSmsCode"
              >
                {{ getSmsCodeButtonMsg }}
              </button>
            </div>

          </div>
        </div>
        <div class="item">
          <div class="left">
            {{ $t('auth.secondStep') }}
          </div>
          <div class="right">
            <p class="text">{{ $t('auth.inputVerifyCode') }}</p>
            <div class="double-check-wrapper custom-input-wrapper">
              <el-input id="ui-accessNetwork-twoFactors-input-code" v-model="code" maxlength="6" :placeholder="$t('auth.inputVerifyCode')"><i
                slot="prefix"
                class="iconfont icon-yanzhengma"
              />
              </el-input>
              <button
                id="ui-accessNetwork-twoFactors-button-check_submit"
                class="sms-wrapper"
                :class="{'public-medium-btn':code,'disabled-button-middle':!code}"
                @click="submitTwoFactor"
              >
                {{ $t('auth.check') }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="scan-fa-content">
        <component
          :is="twofactorAuthType"
          is-qrcode
          :auth-data="authData"
          :is-show="show"
          :tow-factor="true"
          :get-overtime-msg="getOvertimeMsg"
          @towFactorSuccess="scanSuccess"
          @bind="bindHandle"
        />
      </div>
      <div v-if="twofactorAuthType !== 'OTP'" class="item">
        <span>
          {{ getOvertimeMsg }}
        </span>
      </div>

      <!-- 登出提示弹框 -->
      <verticalDialog
        :show="showBind"
        :show-close="false"
        :show-foot="true"
        width="384px"
        class="loginout-m-confirm-dialog"
        :cancel-text=" $t('dialogFoot.no')"
        :confirm-text="$t('dialogFoot.yes')"
        pop-name="bind-header"
        @cancel="cancelHandle"
        @confirm="confirmHandle"
      >
        <div slot="header" class="v-header">
          <i class="iconfont icon-putongxiangbuhegui" />
          {{ $t('tips') }}
        </div>
        <div class="g-s-diaolog-content">
          <div class="form-content">
            <p class="outline-tips">{{ bindTip }}</p>
          </div>
        </div>
      </verticalDialog>
    </div>
  </VerticalDialog>
</template>

<script>
import { mapGetters } from 'vuex'
import authIndex from '@/render/utils/auth/index'
import Mobile from '@/render/utils/auth/mobile'
import TwoFactor from '@/render/utils/auth/twoFactor'
import regular from '@/render/utils/regular'
import { EventBus } from '@/render/eventBus'
import WeWork from './wework'
import DingTalk from './dingtalk'
import FeiShu from './feishu'
import OTP from './otp'
import FeiShuCode from './thirdCode.vue'

export default {
  name: 'TwoFactors',
  components: {
    DingTalk,
    WeWork,
    FeiShu,
    OTP,
    FeiShuCode
  },
  props: {
    mobile: {
      type: String,
      default: ''
    },
    userName: {
      type: String,
      default: ''
    }
  },
  data: function() {
    return {
      show: false,
      code: '',
      getSmsCodeLoading: false,
      showDownloadAPPTitle: false,
      maxGetSmsCodeInterval: 60,
      timer: null,
      nowTime: 0,
      maxCloseInterval: 4,
      showCloseTime: (10 * 60 - 5) * 1000, // 即将关闭消息
      closeTime: -1, // 关闭倒计时
      closeTimer: null,
      isSuccess: false, // 是否成功
      authData: { isTwoFactors: true },
      twofactorAuthType: '',
      customTel: '',
      showBind: false,
      bindTip: '',
      bindInfo: ''
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'computeServeEntiretyConfig', 'authInfo']),
    /**
     * 是否8020.1x模式
     */
    isDot1xMode() {
      const isMacWireLessAuth = _.get(this.authInfo, 'dot1x.isMacWireLessAuth', false)
      return authIndex.isDot1xMode() && !isMacWireLessAuth
    },
    /**
     * 是否禁用获取验证码按钮
     */
    smsCodeIsDisabled() {
      if ((!this.mobile && !this.customTel) || this.closeTime >= 0 || this.nowTime > 0 || this.getSmsCodeLoading) {
        return true
      }
      return false
    },
    /**
     * 验证码按钮文字
     */
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    },
    /**
     * 超时提示
     */
    getOvertimeMsg() {
      const closeTime = this.closeTime
      if (closeTime > 0) {
        return this.$t('auth.twoFactorsOvertime', { second: closeTime })
      } else if (closeTime === 0) {
        return this.$t('auth.noEnterCode')
      }
      return ''
    },
    /**
     * 只展示后四位
     */
    displayMobile() {
      return authIndex.formatPhoneNumber(this.mobile)
    },
    towFactorsOptions() {
      const type = _.get(this.authInfo, 'basic.User.MFAServer')
      if (!type) {
        return []
      }
      const typeArr = type.split('|')
      return typeArr.map((item) => {
        return {
          value: item,
          label: this.$t(`strenthAuth.${item}`)
        }
      })
    }
  },
  watch: {
    show: function(val) {
      if (val) {
        this.isSuccess = false
        this.init()
      }
    }
  },
  created() {
    this.init()
    this.initEvent()
  },
  beforeDestroy() {
    this.beforeClose()
    EventBus.$off('TwoFactors:show')
    EventBus.$off('titleDownloadAPP')
  },
  mounted() {
    // 监听OTP发送是否为下载APP 修改title
    EventBus.$on('titleDownloadAPP', (args) => {
      if (args) {
        this.showDownloadAPPTitle = true
      } else {
        this.showDownloadAPPTitle = false
      }
    })
  },
  methods: {
    init() {
      this.customTel = ''
      this.twofactorAuthType = _.get(this.authInfo, 'basic.User.defaultMFAServer', '')
    },
    /**
     * 初始化双因子框是否展示框事件
     * authFail:代表外部关闭的时候已经认证失败了
     */
    initEvent() {
      EventBus.$on('TwoFactors:show', (show, authFail = false) => {
        this.showSms(!!show, authFail)
      })
    },
    /**
     * 获取验证码
     */
    async getSmsCode() {
      if (this.smsCodeIsDisabled) {
        return false
      }
      const tel = this.mobile || this.customTel
      if (!regular.looseMobile(tel)) {
        this.$message({
          message: this.$t('auth.phoneValidateErr'),
          type: 'warning'
        })
        return
      }
      this.getSmsCodeLoading = true
      const mobile = new Mobile()
      const res = await mobile.commonSmsSend({ phone: tel, twoFactor: true })
      console.log('getSmsCode', res)
      if (res) {
        this.countDown(true)
      }
      this.getSmsCodeLoading = false
    },
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    smsBeforeClose(authFail) {
      if (!authFail) {
        this.$emit('beforeClose', this.isSuccess)
      }
      return
    },
    /**
     * 双因子提交
     */
    submitTwoFactor: _.debounce(async function() {
      const tel = this.mobile || this.customTel
      if (!tel) {
        return false
      }
      if (this.code.length < 6 || !regular.phone(this.code)) {
        this.$message({
          message: this.$t('auth.phoneCodeValidateErr'),
          type: 'warning'
        })
        return false
      }
      const twoFactor = new TwoFactor()
      const res = await twoFactor.auth({
        smsMobile: tel,
        smsCode: this.code,
        userName: this.userName,
        factorType: 'Mobile'
      })
      if (res === false) {
        return false
      }
      this.isSuccess = true
      this.showSms(false)
      this.$emit('smsAuthSuccess')
    }, 1000, { 'leading': true, 'trailing': false }),
    scanSuccess() {
      this.isSuccess = true
      this.showSms(false)
      this.$emit('smsAuthSuccess')
    },
    showSms(show = false, authFail = false) {
      this.show = show
      if (!show) {
        this.beforeClose()
        this.smsBeforeClose(authFail)
        return
      }
      this.showCloseMsg()
    },
    /**
     * 关闭时处理
     * 重置参数
     * 清除定时
     */
    beforeClose() {
      this.code = ''
      this.closeTime = -1
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      if (this.closeTimer) {
        clearTimeout(this.closeTimer)
        this.closeTimer
      }
    },
    /**
     * 展示即将关闭消息
     */
    showCloseMsg() {
      this.closeTimer = setTimeout(() => {
        console.log('init closeDown')
        this.closeDown(true)
      }, this.showCloseTime)
    },
    /**
     * 关闭提示倒计时
     */
    closeDown(isFirst = false) {
      let closeTime = 0
      if (isFirst) {
        closeTime = this.maxCloseInterval
        this.closeTime = closeTime
      } else {
        closeTime = this.closeTime
      }
      if (closeTime > 0) {
        this.closeTime = closeTime - 1
        this.closeTimer = setTimeout(() => {
          this.closeDown()
        }, 1000)
      } else {
        this.showSms(false)
      }
    },
    bindHandle(data) {
      this.showBind = true
      this.bindInfo = data
      this.bindTip = this.$t(`auth.${data.type}BindConfirm`)
    },
    confirmHandle() {
      this.bindInfo.resolve(1)
      this.showBind = false
    },
    cancelHandle() {
      this.showBind = false
      this.bindInfo.resolve(0)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 24px 48px 0 48px;
  color: $title-color;
  .towFactors-select-wrapper{
    text-align: center;
    .towFactors-select{
      width: 240px;
    }
  }
  .mobile-fa-content{
    margin-top: 24px;
  }
  .custom-input-wrapper{
    display: flex;
    align-items: center;
    .el-input{
      width: 232px;
    }
    .towFactors-tel-input{
      width: 190px;
      margin-right: 15px;
    }
  }
  .item {
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    line-height: 20px;
    padding-bottom: 24px;
    .left{
      flex-shrink: 0;
      width: 60px;
      word-break: break-word;
      padding-right: 4px;
    }
    .text {
      margin-bottom: 8px;
      word-break: break-word;
      &:last-child {
        margin-top: 0;
      }
    }

    .public-line-medium-btn {
      height: 40px;
      line-height: 40px;
      width: 120px;
    }

    .double-check-wrapper {
      display: flex;
      align-items: center;

      .sms-wrapper {
        width: 78px;
        height: 40px;
        line-height: 38px;
        margin-left: 12px;
      }
    }
  }
}

.otp-dialog-content{
  padding: 0;
  padding-top: 24px;
}

.custom-input-wrapper ::v-deep .el-input__prefix {
  left: 16px;
  line-height: 40px;
}

.custom-input-wrapper ::v-deep .el-input__inner {
  padding-left: 46px;
}

.scan-fa-content ::v-deep #feishu_qrcode_login{
  text-align: center;
}
.scan-fa-content ::v-deep #ding_qrcode_login{
  text-align: center;
  margin-top: -24px;
}
.scan-fa-content ::v-deep #wework_qrcode_login{
  text-align: center;
}
</style>
