<!-- 设备基本信息 -->
<template>
  <div class="dev-info-template">
    <div ref="devInfoWrap" class="dev-info-wrap">
      <el-row>
        <el-col :span="24">
          <span :class="['item-key', $i18n.locale === 'en' ? 'wide': '']">{{ $t('pcName') }}：</span>
          <el-tooltip :content="devInfo.computername" :visible-arrow="false" popper-class="dev-info-tool" :open-delay="300">
            <span class="info-text">{{ devInfo.computername }}</span>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <span :class="['item-key', $i18n.locale === 'en' ? 'wide': '']">{{ $t('local') }}IP：</span>
          <el-tooltip :content="devInfo.ipaddress" :visible-arrow="false" popper-class="dev-info-tool" :open-delay="300">
            <span class="info-text">{{ devInfo.ipaddress }}</span>
          </el-tooltip>
        </el-col>
        <el-col :span="12">
          <span :class="['item-key', $i18n.locale === 'en' ? 'wide': '']">{{ $t('system.gateIP') }}：</span>
          <el-tooltip :content="devInfo.linkIp" :visible-arrow="false" popper-class="dev-info-tool" :open-delay="300">
            <span class="info-text">{{ devInfo.linkIp }}</span>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <span :class="['item-key', $i18n.locale === 'en' ? 'wide': '']">MAC{{ $t('system.address') }}：</span>
          <el-tooltip :visible-arrow="false" popper-class="dev-info-tool" :open-delay="300">
            <div slot="content">
              <p v-for="mac in devInfo.macaddress" :key="mac" class="tool-item-info">{{ mac }}</p>
            </div>
            <div class="tool-wrap">
              <p v-for="(mac,index) in devInfo.macaddress" :key="mac">{{ mac }} <span v-if="index === 0" class="use-tag">{{ $t('system.currentUse') }}</span></p>
            </div>
          </el-tooltip>
        </el-col>
        <el-col :span="12">
          <span :class="['item-key', $i18n.locale === 'en' ? 'wide': '']">{{ $t('system.cardName') }}：</span>
          <el-tooltip :visible-arrow="false" popper-class="dev-info-tool" :open-delay="300">
            <div slot="content">
              <p v-for="name in devInfo.networkCard" :key="name" class="tool-item-info">{{ name }}</p>
            </div>
            <div class="tool-wrap">
              <p v-for="name in devInfo.networkCard" :key="name">{{ name }}</p>
            </div>
          </el-tooltip>
        </el-col>
      </el-row>
    </div>
    <div class="btn-wrap">
      <p id="ui-dev-info-p-copy" class="dev-info-btn" @click="doCopy">{{ $t('system.copy') }}</p>
      <el-popover
        placement="bottom"
        width="184"
        trigger="hover"
        popper-class="dev-info-pop"
        :open-delay="300"
        @show="popShow"
      >
        <div id="qrCode" ref="qrCodeDiv" />
        <p id="ui-dev-info-p-scan" slot="reference" class="dev-info-btn primary">{{ $t('system.scan') }}</p>
        <!-- <el-button slot="reference">click 激活</el-button> -->
      </el-popover>
    </div>
  </div>
</template>
<script>
import QRCode from 'qrcodejs2'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      devInfo: {},
      qrCodeInfo: {},
      aa: '82:AC:37:89:14:04<br>82:AC:37:89:14:00<br>F0:18:98:80:F8:BE<br>AC:DE:48:00:11:22<br>82:AC:37:89:14:01<br>82:AC:37:89:14:05<br>FE:F1:9C:DC:EC:3E<br>F2:18:98:80:F8:BE'
    }
  },
  computed: {
    ...mapState(['clientInfo'])
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const ipaddress = this.clientInfo.basic.Ip
      const curMac = this.clientInfo.basic.Mac || this.clientInfo.detail.Mac
      const computername = this.clientInfo.basic.DeviceName
      const AdapterList = _.get(this.clientInfo, 'basic.AdapterList.Adapter', [])
      const macaddress = [], networkCard = []
      let list = []
      // 要先判断是不是数组  因为当只有一个网卡的时候 返回的是json格式的 当只有多个网卡才会是数组形式
      if (_.isArray(AdapterList)) {
        list = AdapterList
      } else if (AdapterList) {
        list = [AdapterList]
      }
      if (list.length > 1) {
        const idx = list.findIndex(item => item.MAC === curMac)
        if (idx > 0) { // 将当前项放到第一位
          list[0] = list.splice(idx, 1, list[0])[0]
        }
      }
      list.forEach(v => {
        macaddress.push(v.MAC)
        networkCard.push(v.Desc)
      })
      this.devInfo = {
        ipaddress,
        computername,
        macaddress,
        networkCard,
        linkIp: _.get(this.clientInfo, 'detail.GateIP', '')
      }
      this.qrCodeInfo = {
        ipaddress,
        computername,
        macaddress: macaddress.join(',')
        // networkCard: networkCard.join(',')
      }
    },
    qrcode(text) {
      document.getElementById('qrCode').innerHTML = ''
      setTimeout(() => {
        // eslint-disable-next-line no-new
        new QRCode(this.$refs.qrCodeDiv, {
          text,
          width: 160,
          height: 160,
          colorDark: '#333333', // 二维码颜色
          colorLight: '#ffffff', // 二维码背景色
          // correctLevel: QRCode.CorrectLevel.H // 容错率L/M/H
          correctLevel: 3 // 容错率L/M/H
        }, 100)
        // removeAttribute
        document.getElementById('qrCode').removeAttribute('title')
      })
    },
    // 复制
    doCopy() {
      const copyData = this.$refs.devInfoWrap.innerHTML
      const re = new RegExp('</p><p>', 'g')
      const re1 = new RegExp('</div> ', 'g')
      const re2 = new RegExp('<[^<>]+>', 'g')
      const text = copyData.replace(re, ',')
      const text1 = text.replace(re1, '\n')
      const text2 = text1.replace(re2, '')
      this.$copyText(text2).then(message => {
        this.$message({
          message: this.$t('system.copySuccess'),
          type: 'success'
        })
      }).catch(err => {
        console.log(err)
      })
    },
    popShow() {
      this.qrcode(JSON.stringify(this.qrCodeInfo))
    }
  }
}
</script>
<style lang="scss">
  .dev-info-template{
    .dev-info-wrap{
      border-top: 1px solid $line-color;
      border-left: 1px solid $line-color;
      .el-row{
        display: flex;
      }
      .el-col{
        display: flex;
        color: $title-color;
        line-height: 20px;
        min-height: 40px;
        padding: 10px 16px;
        background: #FBFBFC;
        border-right: 1px solid $line-color;
        border-bottom: 1px solid $line-color;
        .item-key{
          width: 84px;
          color:$default-color;
          margin-right: 8px;
          flex-shrink: 0;
        }
        .info-text{
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .tool-wrap{
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          p{
            width: 100%;
            line-height: 22px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .use-tag{
              font-size: 12px;
              line-height: 17px;
              padding: 0 4px;
              color: $green-4;
              background: $green-7;
              border-radius: 2px;
              margin-left: 3px;
            }
          }
        }
        .wide{
          width: 124px;
        }
      }
    }
    .btn-wrap{
      display: flex;
      justify-content: center;
      margin-top: 24px;
      & > .dev-info-btn:first-of-type{
        margin-right: 10px;
      }
    }
    .dev-info-btn{
      padding: 5px 15px;
      border: 1px solid #536ce6;
      border-radius: 4px;
      color: $--color-primary;
      display: inline-block;
      cursor: pointer;
      &.primary{
        background: $--color-primary;
        color: #fff;
      }
      // &:hover{
      //   background: -webkit-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
      //   background: -moz-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
      //   background: -o-linear-gradient(135deg, $--color-primary, $--color-primary-light-1);
      //   background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
      // }
    }
  }
.dev-info-pop{
  background: rgba(60,64,77,.9);
  .popper__arrow{
    border-bottom-color: rgba(60,64,77,.9) !important;
    &::after{
      border-bottom-color: rgba(60,64,77,.9) !important;
    }
  }
}
.el-tooltip__popper.is-dark.dev-info-tool{
  background: rgba(60,64,77,.9);
  .tool-item-info{
    margin-bottom: 4px;
    &:last-of-type{
      margin-bottom: 0;
    }
  }
}
</style>
