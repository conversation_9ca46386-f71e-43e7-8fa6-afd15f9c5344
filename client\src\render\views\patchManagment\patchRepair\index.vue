<!-- 补丁修复 -->
<template>
  <div v-loading="loading" class="patch-repair-wrap">
    <div
      v-if="patchData.length && inited"
      class="patch-see-content"
    >
      <div class="header-wrapper">
        <div class="left-wrapper">
          <p class="title">{{ $t('patchRepair.info_4') }}</p>
          <p class="sbu-title">{{ sbuTitle }}</p>
        </div>
        <p :class="['el-public-btn', !canRepire ? 'el-disabled-public-btn':'']" @click="repairHandle">{{ $t('patchRepair.info_3') }}</p>
      </div>
      <div class="table-wrap">
        <!-- border -->
        <el-table
          ref="patchRepairTable"
          :data="patchData"
          stripe
          height="100%"
          style="width: 100%"
          class="public-no-boder-table"
          :row-class-name="tableRowClassName"
          :cell-class-name="setCellClass"
        >
          <el-table-column show-overflow-tooltip prop="title" :label="$t('patchView.info_3')" />
          <el-table-column
            prop="level"
            width="100"
            :label="$t('patchView.info_4')"
          >
            <template slot-scope="scope">
              <span :class="scope.row.levelClass">{{ scope.row.level }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="KBNumber"
            width="112"
            :label="$t('patchView.info_5')"
          />
          <el-table-column
            prop="date"
            width="106"
            :label="$t('patchRepair.info_1')"
          />
          <el-table-column
            prop="state"
            width="106"
            show-overflow-tooltip
            :label="$t('patchRepair.info_2')"
          />
        </el-table>
      </div>
      <div class="bottom-wrapper">
        <el-checkbox
          v-model="isRestart"
        >
          {{ $t('patchRepair.info_5') }}
        </el-checkbox>
      </div>
    </div>
    <div v-if="!patchData.length && inited" class="empty-wrap">
      <img src="../../../assets/stateIllustration/empty.png" alt="">
      <p class="empty-info">{{ $t('patchRepair.info_6') }}</p>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import agentApi from '@/service/api/agentApi'
import agentListening from '@/service/api/agentListening'

export default {
  name: 'PatchRepair',
  data() {
    return {
      patchData: [],
      inited: false,
      loading: true,
      isRestart: false,
      repairState: 0, // 0 全部未安装，1 正在修复， 2 全部安装成功 3 部分安装成功
      progressInfo: null,
      waitTime: '',
      timer: null
    }
  },
  computed: {
    sbuTitle() {
      if (this.repairState === 0) {
        if (this.waitTime) {
          return this.$t('patchRepair.info_8', { time: this.waitTime })
        }
        return this.$t('patchRepair.info_10', { total: this.patchData.length })
      } else {
        const total = this.patchData.length
        const installed = this.getSuccessNumber()
        return this.$t('patchRepair.info_9', { total, installed, install: total - installed })
      }
    },
    canRepire() {
      return this.repairState === 0 || this.repairState === 3
    }
  },
  created() {
    this.getPatch()
    this.listenProccess()
  },
  beforeDestroy() {
    this.clearTimer
  },
  methods: {
    async getPatch() {
      const res = await agentApi.getPatchRepairDetailInfo()
      let patchArr = _.get(res, 'ASM.UpdateList.Update', [])
      this.waitTime = _.get(res, 'ASM.WaitTime', '')
      const PathArr = []
      if (_.isObject(patchArr) && !_.isArray(patchArr)) {
        patchArr = [patchArr]
      }
      // 计算初始状态
      this.handleInitState(patchArr)
      if (patchArr.length > 0) {
        patchArr.forEach(element => {
          // date只需要年月日
          const date = _.replace(_.get(element, 'Date', '').slice(0, 10), /-/g, '.')
          const level = _.get(element, 'Level', '初级')
          PathArr.push(
            {
              title: _.get(element, 'Title', ''),
              level: level,
              date: date,
              KBNumber: _.get(element, 'KB', ''),
              state: element.State || this.$t('check.Patch.h_24_rd')
            }
          )
        })
        this.patchData = PathArr
      }
      this.inited = true
      this.loading = false
    },
    // 计算初始状态
    handleInitState(list) {
      const total = list.length
      const notInstalls = list.filter(item => !item.State)
      // 全部未安装
      if (notInstalls.length === total) {
        this.repairState = 0
        return
      }
      const installingDic = ['下载中', '正在安装', 'Downloading', 'Installation in progress']
      const installingItem = list.filter(item => installingDic.indexOf(item.State) > -1)
      // 正在安装
      if (installingItem.length) {
        this.repairState = 1
        return
      }
      // 全部安装成功
      const successNumber = this.getSuccessNumber(list)
      if (successNumber === total) {
        this.repairState = 2
        return
      }

      // 部分安装成功
      if (successNumber !== total) {
        this.repairState = 3
        return
      }
    },
    // 设置table行样式
    tableRowClassName({ row, rowIndex }) {
      if (this.progressInfo && rowIndex === parseInt(this.progressInfo.current)) {
        return 'repairing-row'
      }
      return ''
    },
    // 设置单元格样式
    setCellClass(row) {
      if (row.columnIndex === 1) {
        if (row.row.level === this.$t('check.Patch.js_1_d')) {
          return 'error-color'
        } else if (row.row.level === this.$t('check.Patch.js_2_d')) {
          return 'waring-color'
        }
      } else if (row.columnIndex === 4) {
        if (row.row.state === this.$t('check.Patch.h_24_rd') || row.row.state === this.$t('check.Patch.js_5_s')) {
          return 'error-color'
        } else if (row.row.level === this.$t('check.Patch.js_8_s')) {
          return 'success-color'
        }
      }
    },
    // 监听安检项自动修复下载进度
    listenProccess() {
      const _this = this
      agentListening.addStrWhatToDo('ProcessNotice', function(qMsgRequest) {
        const ASM = _.get(qMsgRequest, 'result.ASM', '')
        console.log('补丁修复ASM', ASM)
        _this.progressInfo = ASM
        _this.setRepireingState(ASM)
      })
    },
    // 设置正在修复项状态
    setRepireingState(info) {
      this.repairState = 1
      if (info.Title) {
        const patchData = this.patchData
        const obj = patchData.find(item => item.KBNumber === info.Title)
        if (obj) {
          if (info.Progress.indexOf('Rate') === -1) {
            obj.state = info.Progress
            this.patchData = patchData
          }
          const isComplete = this.completeItem(info.Progress)

          if (isComplete && parseInt(info.current) + 1 === patchData.length) {
            if (this.getSuccessNumber() === patchData.length) {
              this.repairState = 2
              this.$message({
                message: this.$t('check.Patch.h_12_rd'),
                type: 'success'
              })
            } else {
              this.repairState = 3
            }
          }
        }
      }
    },
    getSuccessNumber(list) {
      const arr = list || this.patchData
      const dic = ['安装成功', 'Successful installation', '安装完成']
      const success = arr.filter(item => dic.indexOf(item.state) > -1)
      return success.length
    },
    completeItem(progress) {
      const completeArr = ['成功', 'Successful', '失败', 'failed']
      for (let i = 0; i < completeArr.length; i++) {
        if (progress.indexOf(completeArr[i]) > -1) {
          return true
        }
      }
      return false
    },
    // 立即修复
    async repairHandle() {
      if (this.isRepairing) {
        return
      }
      this.isRepairing = true
      await agentApi.doRepairPatch({ AutoShutdown: this.isRestart ? 1 : 0 })
      this.isRepairing = false
    },
    clearTimer() {
      this.timer && clearTimeout(this.timer)
      this.timer = null
    }
  }
}
</script>
<style lang="scss">
.patch-repair-wrap {
  padding: 0 24px;
  box-sizing: border-box;
  position: relative;
  height: 100%;
  text-align: center;
  overflow: auto;
  .header-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-wrapper{
      padding-right: 16px;
      .title {
        font-size: 16px;
        font-weight: 500;
        color: $title-color;
        line-height: 22px;
        padding-top: 24px;
        padding-bottom: 4px;
        text-align: left;
      }
      .sbu-title{
        padding-bottom: 16px;
        text-align: left;
        color: $default-color;
      }
    }
  }
  .patch-see-content{
    height: 100%;
  }
  .table-wrap {
     height: calc(100% - 136px);
    .el-table {
      .cell {
        .patch-title {
          cursor: pointer;
          display: inline-block;
          width: auto;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .el-table__body {
        tr {
          .error-color .cell {
            color: $error;
          }
          .waring-color .cell {
            color: $waring;
          }
          .success-color .cell {
            color: $success;
          }
        }
      }
    }
  }
  .repairing-row{
    background: $list-hover-bg;
  }
  .bottom-wrapper{
    margin-top: 16px;
    text-align: left;
    .el-checkbox__label{
      color: $title-color;
    }
  }
  .empty-wrap {
    text-align: center;
    display: inline-block;
    margin-top: 100px;
    img {
      display: inline-block;
      width: 246px;
      height: 236px;
    }
    .empty-info {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: $default-color;
      line-height: 22px;
      margin-top: 40px;
      font-weight: 500;
    }
  }
}
</style>
