
// 生成环境下的配置项

const baseWebpackConf = require('./webpack.base.config.js')
const { merge } = require('webpack-merge')

// Configure for webpack server

const devConf = merge(
  baseWebpackConf.setMode('development'),
  baseWebpackConf.bootstrap('development'),
  baseWebpackConf.setPlugins(),
  baseWebpackConf.setEslints(),
  baseWebpackConf.setLoaders(),
  baseWebpackConf.setBabelLoader(),
  {
    devServer: {
      client: {
        progress: true,
        logging: 'warn',
        overlay: {
          errors: true,
          warnings: false
        }
      },
      hot: true, // xp下的webkit不生效可以改成false提升体验
      port: 9528
    },
    performance: {
      hints: 'warning',
      // 入口起点的最大体积
      maxEntrypointSize: 50000000,
      // 生成文件的最大体积
      maxAssetSize: 30000000,
      // 只给出 js 文件的性能提示
      assetFilter: function(assetFilename) {
        return assetFilename.endsWith('.js')
      }
    }
  },
  {}
)

module.exports = devConf
