<template>
  <!-- 短信登录 -->
  <div
    class="mobile-auth-page"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="0px"
      class="rule-form"
    >
      <el-form-item
        label=""
        prop="phone"
      >
        <el-input
          :id="calcId('-input-phone_number')"
          v-model="ruleForm.phone"
          maxlength="50"
          :disabled="isAddition"
          :placeholder="isAddition && !ruleForm.phone ? '': $t('auth.guestPhonePlaceHolder')"
          @change="phoneChange"
        >
          <i
            slot="prefix"
            class="iconfont icon-shoujihao"
          />
        </el-input>
      </el-form-item>
      <div class="specal-form-item">
        <div class="form-item-wrapper">
          <el-form-item label="" prop="checkCode">
            <el-input
              :id="calcId('-input-code')"
              v-model="ruleForm.checkCode"
              maxlength="6"
              :placeholder="$t('auth.enterCode')"
            >
              <i slot="prefix" class="iconfont icon-yanzhengma" />
            </el-input>
          </el-form-item>
        </div>

        <div v-if="!smsCodeIsDisabled && (!isAddition || isAddition&&ruleForm.phone)" :id="calcId('-div-get_code')" class="sms-wrapper public-line-medium-btn" @click="sendPhoneCode">
          {{ $t('auth.phoneCode') }}
        </div>

        <div v-else class="sms-wrapper public-line-medium-btn sms-disabled">
          {{ getSmsCodeButtonMsg }}
        </div>
      </div>

      <networkList v-if="isAccessAuth" ref="networkList" />

      <el-form-item v-if="authData.authFrom !== 'addition'">
        <p class="public-btn" @click="submitForm">{{ $t('auth.submit') }}</p>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import networkList from './networkList'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import Mobile from '@/render/utils/auth/mobile'
import authIndex from '@/render/utils/auth/index'
import regular from '@/render/utils/regular'
import { EventBus } from '@/render/eventBus'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'

export default {
  name: 'MobileAuth',
  components: {
    networkList
  },
  props: {
    idPre: {
      type: String,
      default: 'ui-accessNetwork-mobile'
    },
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      ruleForm: {
        phone: '',
        checkCode: '',
        smsSource: ''
      },
      getCodeLoading: false,
      rules: {
        phone: [{
          required: true,
          validator: this.checkPhone,
          trigger: 'blur'
        }],
        checkCode: [{
          required: true,
          validator: this.codeValidator,
          trigger: 'blur'
        }]
      },
      mobile: new Mobile(),
      getSmsCodeLoading: false,
      maxGetSmsCodeInterval: 30,
      timer: null,
      nowTime: 0
    }
  },
  computed: {
    ...mapGetters(['computeServeEntiretyConfig', 'clientInfo', 'authInfo']),
    trustPhone() {
      let phone = this.ruleForm.phone + ''
      if (_.isString(phone) && phone.indexOf('*') !== -1 && this.ruleForm.smsSource !== '') {
        phone = this.ruleForm.smsSource
      }
      return phone
    },
    /**
     * 是否禁用获取验证码按钮
     */
    smsCodeIsDisabled() {
      if (this.getSmsCodeLoading) {
        return true
      }
      return this.nowTime > 0
    },
    /**
     * 验证码按钮文字
     */
    getSmsCodeButtonMsg() {
      const nowTime = this.nowTime
      if (nowTime > 0) {
        return this.$t('auth.recapture', { second: nowTime })
      }
      return this.$t('auth.getSmsCode')
    },
    // 是否是入网认证，（该组件可能被强化认证，账号绑定复用）
    isAccessAuth() {
      return !this.authData.authFrom
    },
    isAddition() {
      return this.authData.authFrom === 'addition'
    }
  },
  watch: {
    // 中英文切换时重新校验失败条目
    '$i18n.locale': function() {
      this.$refs['ruleForm'].fields.forEach(item => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 短信不支持自动认证，直接展示
      EventBus.$emit('client:show')
      console.log('serveEntiretyConfig', this.serveEntiretyConfig)
      console.log(this.authData)
      const authData = this.authData
      if (authData.authFrom === 'addition') { // 强化认证
        this.mobile.isDot1xAuth = () => false // 修改mobile实例方法强化认证强制不走8021x模式
        this.ruleForm.smsSource = authData.Tel
        this.ruleForm.phone = this.ruleForm.smsSource && authIndex.formatPhoneNumber(this.ruleForm.smsSource)
        this.$refs['ruleForm'].validateField('phone')
      } else {
        if (parseInt(_.get(this.computeServeEntiretyConfig, 'SMS.write_devtel', 0)) === 1) {
          this.ruleForm.smsSource = (_.get(this.authInfo, 'basic.Tel') || _.get(this.clientInfo, 'detail.Tel', '')) + ''
          this.ruleForm.phone = this.ruleForm.smsSource && authIndex.formatPhoneNumber(this.ruleForm.smsSource)
        }
      }
    },
    // 发送验证码
    async sendPhoneCode() {
      const phone = this.trustPhone
      if (_.isEmpty(phone)) {
        this.$message.error(this.$t('auth.phoneEmptyErr'))
        return false
      }
      const regFun = this.authData.authFrom === 'addition' ? regular.looseMobile : regular.phone
      if (!regFun(phone)) {
        this.$message.error(this.$t('auth.phoneValidateErr'))
        return false
      }

      if (authIndex.isDot1xMode() && this.isAccessAuth) {
        const accessNetwork = this.$refs.networkList.accessNetwork
        if (!_.isString(accessNetwork) || accessNetwork === '') {
          this.$message.error(this.$t('auth.selectNetworkList'))
          return false
        }
      }
      this.getSmsCodeLoading = true
      const res = await this.mobile.smsSend({
        phone,
        accessNetwork: this.$refs.networkList && this.$refs.networkList.accessNetwork,
        isWireLess: this.$refs.networkList && this.$refs.networkList.isWireLess
      })
      this.getSmsCodeLoading = false
      if (res) {
        this.countDown(true)
      }
    },
    checkPhone(rule, value, callback) {
      if (this.authData.authFrom === 'addition') {
        if (!value) {
          callback(this.$t('accountBind.notPhone'))
        } else {
          callback()
        }
        return
      }
      if (this.trustPhone === '') {
        callback(new Error(this.$t('auth.phoneEmptyErr')))
      } else if (!regular.phone(this.trustPhone)) {
        callback(new Error(this.$t('auth.phoneValidateErr')))
      }
      callback()
    },
    codeValidator(rule, value, callback) {
      if (value === '') {
        return callback(new Error(this.$t('auth.enterCode')))
      } else if (!regular.phone(value) || value.length < 6) {
        return callback(new Error(this.$t('guestAuth.guest.phoneCodeValidateErr')))
      } else {
        callback()
      }
    },
    /**
     * 电话号码改变时清空真实电话
     */
    phoneChange() {
      this.ruleForm.smsSource = ''
    },
    /**
     * 触发校验，校验通过则调用实际认证
     */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.auth()
        }
      })
    },
    /**
     * 实际认证，增加防抖处理，防止重复认证
     */
    auth: _.debounce(async function() {
      if (authIndex.isDot1xMode() && this.isAccessAuth) {
        const accessNetwork = this.$refs.networkList.accessNetwork
        if (!_.isString(accessNetwork) || accessNetwork === '') {
          this.$message.error(this.$t('auth.selectNetworkList'))
          return false
        }
      }

      const params = {
        phone: this.trustPhone,
        checkCode: this.ruleForm.checkCode,
        accessNetwork: this.$refs.networkList && this.$refs.networkList.accessNetwork,
        isWireLess: this.$refs.networkList && this.$refs.networkList.isWireLess,
        authFrom: this.authData.authFrom
      }
      this.$emit('loading', true)
      // 敲门-无需敲门或者敲门成功返回true
      const knockParam = ['Mobile', params.phone, params.checkCode]
      const knockIsSuccess = await commonUtil.knockPort(knockParam)
      if (knockIsSuccess === false) {
        this.$emit('loading', false)
        return false
      }

      const res = await this.mobile.auth(params)
      if (res === false) {
        this.$emit('loading', false)
        return false
      } else if (_.isObject(res) && _.get(res, 'revoke', false) === true) {
        this.$emit('loading', false)
        this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
        return false
      }
      if (this.isAddition) {
        this.$emit('loading', false)
      }
      this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
    }, 1000, { 'leading': true, 'trailing': false }),
    /**
     * 倒计时
     * @param isFirst 首次设置为最大时间
     */
    countDown(isFirst = false) {
      let nowTime = 0
      if (isFirst) {
        nowTime = this.maxGetSmsCodeInterval
        this.nowTime = nowTime
      } else {
        nowTime = this.nowTime
      }
      if (nowTime > 0) {
        this.nowTime = nowTime - 1
        this.timer = setTimeout(() => {
          this.countDown()
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    calcId(suf) {
      return this.idPre + suf
    }
  }
}
</script>
<style lang="scss">
.mobile-auth-page {
  width: 360px;
  margin: 0 auto;

  .rule-form {
    .specal-form-item {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .form-item-wrapper {
        width: 228px;
      }

      .sms-wrapper {
        width: 120px;
        height: 40px;
        line-height: 38px;
      }

      .sms-disabled{
        border-color: $line-color;
        background: $line-color;
        color: $disabled-color;
        cursor: not-allowed;
      }
    }

    .public-btn {
      margin-top: 8px;
    }
  }
  .el-input .iconfont {
    font-size: 16px;
  }
}

</style>
