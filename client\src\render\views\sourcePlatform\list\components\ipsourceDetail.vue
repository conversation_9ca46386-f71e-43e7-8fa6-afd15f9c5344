<template>
  <div v-loading="loading" class="drawer-content">
    <div v-if="showLinkState" :class="['link-state', testGateway === 1 ? 'link-ok':'link-fail']">
      <i v-if="testGateway === 1" class="iconfont icon-anjianheguixiang" />
      <i v-else class="el-icon-loading" />
      {{ testGateway === 1 ?$t('sourcePlatform.vpnPass') : $t('sourcePlatform.vpnFail') }}
    </div>
    <div class="item"><span class="label" :style="{'width': labelWidth}">{{ $t('sourcePlatform.contact') }}</span><span>{{ msg.Username }}</span></div>
    <div class="item"><span class="label" :style="{'width': labelWidth}">{{ $t('sourcePlatform.tel') }}</span><span>{{ msg.Tel }}</span></div>
    <div v-if="!isHideResDetail" class="item">
      <span class="label" :style="{'width': labelWidth}">{{ $t('sourcePlatform.address') }}</span>
      <ul>
        <li v-for="(item,index) in fromateIP" :key="item+index" class="item-ip">
          <el-tooltip
            :visible-arrow="false"
            popper-class="ip-detail-tip"
            :content="$t('sourcePlatform.clickCopy')"
            placement="bottom-start"
            :open-delay="500"
          >
            <span class="text" @click="copyHandle(item, index)">
              {{ item }}
            </span>
          </el-tooltip>
          <div v-if="index === tipsIdx" class="pop-content">
            <i :class="['iconfont', infoType === 'success'? 'icon-anjianheguixiang':'icon-guanjianxiangbuhegui']" />
            {{ infoType === 'success'?$t('guestAuth.administrator.info_8'):$t('guestAuth.administrator.info_20') }}
          </div>
        </li>
      </ul>
    </div>
  </div>

</template>
<script>
import commonUtil from '@/render/utils/bussiness/commonUtil'
import proxyApi from '@/service/api/proxyApi'
import { mapGetters } from 'vuex'
export default {
  props: {
    msg: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: true,
      testGateway: '',
      tipsIdx: '',
      infoType: 'success',
      timer: null,
      showLinkState: false,
      linkId: null
    }
  },
  computed: {
    ...mapGetters([
      'serveEntiretyConfig',
      'gateWayInfos'
    ]),
    isProxy() {
      return _.get(this.serveEntiretyConfig, 'server.ZTP.VpnType') === 'proxy'
    },
    fromateIP() {
      if (!this.msg.IP) {
        return ''
      }
      return this.msg.IP.split('##')
    },
    isHideResDetail() {
      if (!this.msg.HideResDetail) {
        return false
      }
      return parseInt(this.msg.HideResDetail) === 1
    },
    labelWidth() {
      return this.$i18n.locale === 'zh' ? '71px' : '61px'
    }
  },
  mounted() {
    this.checkGatewayPass()
  },
  beforeDestroy() {
    this.claerTime()
    this.linkId && clearTimeout(this.linkId)
  },
  methods: {
    async checkGatewayPass() {
      const resId = this.msg.ResID
      Promise.all([this.getGateWayList(), proxyApi.getResGateway({ ResID: resId })]).then(res => {
        console.log('对比数据', res)
        const netCardList = res[0]
        const resGates = _.get(res[1], 'data', [])
        if (resGates.length && netCardList.length) {
          let isUseable = false
          for (let i = 0; i < netCardList.length; i++) {
            const itemId = netCardList[i].ID || netCardList[i].gwid
            const state = netCardList[i].Status === 'true' || parseInt(netCardList[i].usable) === 1
            if (resGates.indexOf(itemId) > -1 && state) {
              isUseable = true
              break
            }
          }
          if (isUseable) {
            this.testGateway = 1
            if (parseInt(_.get(this.gateWayInfos, 'VPNStatus')) === 1) {
              this.showLinkState = true
              this.cutdownLink()
            }
            return
          }
        }
        this.testGateway = 0
        if (parseInt(_.get(this.gateWayInfos, 'VPNStatus')) === 1) {
          this.showLinkState = true
          this.cutdownLink()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async getGateWayList() {
      if (this.isProxy) {
        const gateWayMap = _.get(this.gateWayInfos, 'gateWayMap', {})
        const list = Object.values(gateWayMap)
        if (list.length === 0) { // 可能客户端还未推送数据，从web接口取一次（web接口可能不准确）
          const ways = await commonUtil.getWebGateWay()
          ways.forEach(item => {
            item.Status = parseInt(item.SysStatus) === 1 ? 'false' : 'true'
          })
          return ways
        }
        return list
      } else {
        const ret = await commonUtil.getNetCardInfo()
        return ret.Gateway || []
      }
    },
    copyHandle(text, idx) {
      console.log(text)
      this.$copyText(text).then(e => {
        this.infoType = 'success'
        this.tipsIdx = idx
        this.cutDownHiden()
        console.log('复制成功')
      }, e => {
        this.infoType = 'fail'
        this.tipsIdx = idx
        this.cutDownHiden()
        console.log('复制失败')
      })
    },
    cutDownHiden() {
      this.claerTime()
      this.timer = setTimeout(() => {
        this.tipsIdx = ''
      }, 1000)
    },
    claerTime() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    cutdownLink() {
      this.linkId = setTimeout(() => {
        this.showLinkState = false
        clearTimeout(this.linkId)
        this.linkId = null
      }, 3000)
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-content{
  padding: 10px 32px;
  position: relative;
  height: 100%;
  overflow-y: auto;
  .link-state{
    line-height: 18px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid $green-5;
    color: $title-color;
    background: $green-6;
    margin-bottom: 16px;
    i{
      font-size: 14px;
      margin-right: 8px;
    }
    .icon-anjianheguixiang{
      font-size: 16px;
      color: $green-3
    }
  }
  .link-fail{
    border-color: $gray-2;
    background: $gray-6;
  }
  .item{
    color: $title-color;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: Regular;
    line-height: 20px;
    margin-bottom: 8px;
    display: flex;
    .label{
      color: $default-color;
      flex-shrink: 0;
      text-align: right;
      line-height: 20px;
      white-space: nowrap;
    }
    .item-ip{
      line-height: 20px;
      color: $title-color;
      margin-bottom: 8px;
      position: relative;
      .text{
        cursor: pointer;
      }
      &:hover .pop-content{
          color: $title-color;
        }
      .pop-content{
        position: absolute;
        top: 22px;
        left: 100%;
        white-space: nowrap;
        height: 25px;
        display: flex;
        align-items: center;
        border-radius: 13px;
        background: white;
        font-size: 12px;
        box-shadow: 0px 2px 8px 0px rgba(16,36,66,0.20);
        padding: 0 8px;
        .iconfont{
          font-size: 14px;
          margin-right: 4px;
          color: $green-3;
        }
        .icon-guanjianxiangbuhegui{
          color: $error-1;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.ip-detail-tip {
    padding: 4px 8px;
    border-radius: 1px;
    font-size: 12px;
    line-height: 17px;
}
</style>

