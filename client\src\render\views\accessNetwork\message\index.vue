<!-- 入网状态 -->
<template>
  <div id="f-message">
    <layout-state
      :state-img="state['stateImg']"
      :state-msg="state['stateMsg']"
      :state-btn-txt="state['stateBtnTxt']"
      :state-btn-disabled="state['stateBtnDisabled']"
      state-id="ui-accessNetwork-message-div-network_msg"
      state-btn-id="ui-accessNetwork-message-button-enter_network"
      @stateBtnHandle="handleNext"
    >
      <div
        v-if="showCheckRepair"
        slot="stateMsg"
        class="slot-state-msg"
        @click="toSecurity"
        v-html="$t('check.checkFalse')"
      />
    </layout-state>
  </div>
</template>
<script>
import state from '@/render/components/layout/state'
import { mapGetters, mapMutations, mapState } from 'vuex'
import processController from '@/render/utils/processController'
import authIndex from '@/render/utils/auth'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import localStorage from '@/render/utils/cache/localStorage'
import scene from '@/render/utils/bussiness/scene'
import authUtils from '@/render/utils/auth/index'

const loading = require('@/render/assets/stateIllustration/loading.png') // loading 图片
const noAccess = require('@/render/assets/stateIllustration/noAccess.png') // 已入网的图片
const auditing = require('@/render/assets/stateIllustration/auditing.png') // 审核中
const cutOff = require('@/render/assets/stateIllustration/cutoff.png') // 隔离图片
const iIllegal = require('@/render/assets/stateIllustration/iIllegal.png') // 违规状态
const serverNotWorking = require('@/render/assets/serverNotWorking.png') // 网络连接不通的问题
const checkFail = require('@/render/assets/stateIllustration/checkFail.png') // 未入网安检失败
const forbid = require('@/render/assets/stateIllustration/forbid.png')

export default {
  components: {
    'layoutState': state
  },
  data() {
    return {
      taskId: null,
      serverNotWorking: serverNotWorking,
      auditNextLock: false,
      state: {
        stateImg: loading,
        stateMsg: this.$t('netExamine'),
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      },
      auditAlarm: false,
      throttleRequesstTime: 0,
      showCheckRepair: false
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo', 'serveEntiretyConfig', 'redirectInfo']),
    ...mapGetters(['computeNetAccessStatus']),
    accessStatus() {
      return _.get(this.clientInfo, 'accessStatus.deviceStatus', null)
    },
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    IsTrustDev() {
      // 是否可信
      return _.get(this.clientInfo, 'detail.IsTrustDev') === '1'
    }
  },
  watch: {
    // 当入网状态发生变化的时候，重新计算
    accessStatus: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.state.stateImg !== loading) {
          this.handleMessageStatus()
        }
      },
      deep: true
    },
    '$i18n.locale': function() {
      this.handleMessageStatus()
    },
    'clientInfo.online': function(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.init()
      }
    }
  },
  created() {
    this.init()
  },
  beforeDestroy() {
    this.clearRoadClock()
    if (this.auditAlarm) {
      this.auditAlarm.close()
    }
  },
  methods: {
    ...mapMutations(['setClientInfo', 'setServeEntiretyConfig', 'setAuthInfo', 'setRedirectInfo']),
    init() {
      Promise.all([commonUtil.server(), commonUtil.detail()]).then(() => {
        Promise.all([commonUtil.netRet(), scene.getDeviceScene(false, false)]).then(() => {
          this.updateMenuWidth()
          this.handleMessageStatus()
        })
      })
    },
    // 点击这个页面刷新，一般是网络不通的时候点击
    refresh() {
      window.location.reload()
    },
    // 设置当前状态为请求loading状态
    setLoadingStatus() {
      this.state = {
        stateImg: loading,
        stateMsg: this.$t('netExamine'),
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      }
    },
    // 根据设备当前状态；服务器配置信息=>计算出最终的设备状态=>返回不同的状态图片和文字
    async handleMessageStatus() {
      const state = {
        stateImg: loading,
        stateMsg: this.$t('netExamine'),
        stateBtnTxt: '',
        stateBtnHandle: undefined,
        stateBtnDisabled: false
      }
      // 计算入网状态详情
      const status = this.computeNetAccessStatus()
      const g_showName = _.get(this.serveEntiretyConfig, 'server.UserName') || _.get(this.serveEntiretyConfig, 'server.DevName') || this.$t('g_ShowName')
      const showWelcomePage = parseInt(_.get(this.serveEntiretyConfig, 'server.CLIENTCHECK.AutoCheck', 1)) === 1 // 是否开了欢迎页
      if (this.forbidNextStatus() && !this.IsTrustDev) {
        const stateMsg = this.$t('oWelcome.Web_OnAccesss')
        state['stateMsg'] = stateMsg === 'oWelcome.Web_OnAccesss' ? this.$t('oWelcome.Web_OnAccesss_default') : this.$t('oWelcome.Web_OnAccesss')
        state['stateImg'] = forbid
        this.state = state
        return true
      }
      // 802.1x模式下且离线或者敲端口离线
      if (_.get(status, 'isOfflineDot1x') || _.get(status, 'isOfflineKnockPort')) {
        if (!showWelcomePage) {
          this.gotoAuth()
          return true
        }
        state['stateMsg'] = this.$t('oWelcome.Registered').replace(/{username}/g, g_showName)
        state['stateImg'] = noAccess
        state['stateBtnTxt'] = this.$t('networkStatus.info_3')
        state['stateBtnHandle'] = () => {
          this.gotoAuth()
        }
        this.state = state
        return true
      }

      // 如果是离线模式下(优先上面的离线且是8021.x)。显示网络不通
      if (_.get(this.clientInfo, 'online', false) === false) {
        state['stateMsg'] = this.$t('auth.unableConnectToNetwork')
        state['stateImg'] = serverNotWorking
        state['stateBtnTxt'] = this.$t('refresh')
        state['stateBtnHandle'] = () => {
          this.refresh()
        }
        this.state = state
        return true
      }

      if (_.isNull(_.get(status, 'isAccess'))) {
        this.state = state
        return true
      }
      if (this.auditOrAuditNextStatus()) {
        // 防止组件被卸载后(跳转到其他路由后)，这里继续跳转(因为卸载后异步方法仍然进行)
        if (_.get(this.$router, 'currentRoute.path') === '/access/message') {
          if (localStorage.getItem('guestAuditStatus')) {
            processController.set('/access/guestAudit')
            return false
          }
          processController.set('/access/audit')
          return false
        }
      }

      // 已入网
      if (_.get(status, 'isAccess')) {
        // 设备即将过期提醒。通过notifaction弹出提示
        this.auditAlert()
        // 异常放开网络的情况下的处理
        /**
         if (parseInt(_.get(this.clientInfo, 'accessStatus.SessionStatus', 0)) === 2) {
          const ret = await commonUtil.updateAuthInfo()
          if (ret) { // 同步认证信息成功
            await processController.next({fromAuth: true, needCheck: true})
          }
        }
         */
        // 防止组件被卸载后(跳转到其他路由后)，这里继续跳转(因为卸载后异步方法仍然进行)
        if (_.get(this.$router, 'currentRoute.path') === '/access/message') {
          const isGuestApproval = this.$route.query.GuestApproval || 0
          if (parseInt(isGuestApproval) === 1) { // 通过来宾审批弹框唤起客户端需要进入来宾接待
            processController.set({ path: '/access/guestReceive', query: { activeType: 'audit' }})
            return true
          }
          if (authUtils.isOpenZeroTrust() && !this.$route.query.menuClick && _.get(this.clientInfo, 'basic.CheckResult') !== 'fault' && parseInt(_.get(this.clientInfo, 'detail.Registered', -2)) === 1) {
            commonUtil.setLoginRetPatch()
            processController.set({ path: '/source/list' })
            return
          }
          processController.set({ path: '/access/submit', query: { fromeMessage: 1 }})
          return true
        }
      } else {
        // 设备被隔离
        if (_.get(status, 'cutOff') && !this.isIPAMauth()) {
          state['stateMsg'] = this.$t('oWelcome.CutOff')
          state['stateImg'] = cutOff
          state['stateBtnTxt'] = this.$t('networkStatus.refreshStaus')
          state['stateBtnHandle'] = () => {
            this.setLoadingStatus()
            this.init()
          }
          this.state = state

          commonUtil.IPAMRedirect() // IPAM重定向
          return true
        }

        // IP,MAC绑定违规
        if (_.get(status, 'ipMacBindIllegal')) {
          state['stateMsg'] = this.$t('ipMacBindIllegal') + '<br/>' + this.$t('ipMacBindIllegalInfo') +
            _.get(this.clientInfo, 'detail.IP') + '/' + _.get(this.clientInfo, 'detail.Mac')
          state['stateImg'] = iIllegal
          state['stateBtnTxt'] = this.$t('networkStatus.refreshStaus')
          state['stateBtnHandle'] = () => {
            this.setLoadingStatus()
            this.init()
          }
          this.state = state
          return true
        }

        // 指纹违规
        if (_.get(status, 'fingerIllegal')) {
          state['stateMsg'] = this.$t('fingerIllegal')
          state['stateImg'] = iIllegal
          state['stateBtnTxt'] = this.$t('networkStatus.refreshStaus')
          state['stateBtnHandle'] = () => {
            this.setLoadingStatus()
            this.init()
          }
          this.state = state
          return true
        }

        // 未注册设备
        if (!_.get(status, 'registered')) {
          if (!showWelcomePage) {
            this.gotoAuth()
            return true
          }
          state['stateMsg'] = this.$t('oWelcome.UnRegistered').replace(/{username}/g, g_showName)
          state['stateBtnTxt'] = this.$t('networkStatus.info_3')
          state['stateImg'] = noAccess
          state['stateBtnHandle'] = () => {
            this.gotoAuth()
          }
          this.state = state
          return true
        } else {
          // 设备即将过期提醒。通过notifaction弹出提示
          this.auditAlert()

          // 设备已过期
          if (_.get(status, 'registeredStatus.auditEnd')) {
            state['stateMsg'] = this.$t('oWelcome.AuditStop').replace(/{username}/g, g_showName)
            state['stateBtnTxt'] = this.$t('networkStatus.refreshStaus')
            state['stateImg'] = auditing
            state['stateBtnHandle'] = () => {
              // 点击刷新按钮的时候必须把之前设置的定时器给删除了
              this.clearRoadClock()
              this.setLoadingStatus()
              this.init()
            }
            this.setReloadClock(10000)
            this.state = state
            return true
          }
          // 已认证安检失败
          if (_.get(this.clientInfo, 'accessStatus.userName') && _.get(this.clientInfo, 'basic.CheckResult') === 'false') {
            this.showCheckRepair = true
            state['stateMsg'] = ''
            state['stateImg'] = checkFail
            state['stateBtnTxt'] = this.$t('networkStatus.info_7')
            state['stateBtnHandle'] = () => {
              this.gotoAuth()
            }
            this.state = state
            return true
          }
          if (!showWelcomePage) {
            this.gotoAuth()
            return true
          }
          state['stateMsg'] = this.$t('oWelcome.Registered').replace(/{username}/g, g_showName)
          state['stateImg'] = noAccess
          state['stateBtnTxt'] = this.$t('networkStatus.info_3')
          state['stateBtnHandle'] = () => {
            this.gotoAuth()
          }
          this.state = state
          return true
        }
      }
    },

    // 是否是待审核或者带cookie的待审核后的下一步状态
    auditOrAuditNextStatus() {
      // 待审核状态
      if (_.get(this.computeNetAccessStatus(), 'registeredStatus.needAduit')) {
        return true
      }

      // 非待审核但是有cookie状态(表示一个小时内才审核通过的待审核状态or设备在待审核状态删除)
      if (localStorage.getItem('auditNextStatus') || localStorage.getItem('auditCheckNextStatus')) {
        let params = {}
        if (localStorage.getItem('auditNextStatus')) {
          params = JSON.parse(localStorage.getItem('auditNextStatus'))
        } else if (localStorage.getItem('auditCheckNextStatus')) {
          params = JSON.parse(localStorage.getItem('auditCheckNextStatus'))
        }

        // cookie里面的deviceID和当前的deviceID一致，且是已注册状态
        if ((parseInt(_.get(params, 'DeviceID')) === parseInt(_.get(this.clientInfo, 'detail.DeviceID'))) &&
          parseInt(_.get(this.clientInfo, 'detail.Registered')) === 1
        ) {
          return true
        } else {
          localStorage.removeItem('auditNextStatus')
          localStorage.removeItem('auditCheckNextStatus')
        }
      }

      if (localStorage.getItem('guestAuditStatus')) {
        const params = JSON.parse(localStorage.getItem('guestAuditStatus'))
        const { guestSelfID } = _.get(this.clientInfo, 'accessStatus', {})
        if (!guestSelfID || guestSelfID !== _.get(params, 'guestselfid')) {
          localStorage.removeItem('guestAuditStatus')
        } else {
          return true
        }
      }
      return false
    },
    // 设备即将超期提醒
    auditAlert() {
      const status = this.computeNetAccessStatus()
      if (_.get(status, 'registeredStatus.auditEndAlarn')) {
        // 因为这里compute会变动几次，为了防止多次提示，加个计数器
        if (!this.auditAlarm) {
          this.auditAlarm = this.$msg({
            message: _.get(status, 'registeredStatus.auditEndAlarn'),
            type: 'warning',
            showClose: true,
            duration: 10000
          })
        }
      }
    },
    // 场景禁用
    forbidNextStatus() {
      return parseInt(_.get(this.serveEntiretyConfig, 'scene.IsAccess', 1)) === 0
    },
    // 设置定时器刷新
    setReloadClock(times = 1000) {
      if (!this.taskId) {
        this.taskId = setTimeout(() => {
          this.setLoadingStatus()
          this.init()
        }, times)
      }
    },
    // 清除定时器刷新
    clearRoadClock() {
      if (this.taskId) {
        clearTimeout(this.taskId)
        this.taskId = null
      }
    },
    handleNext() {
      const ret = this.state['stateBtnHandle']
      if (_.isFunction(ret)) {
        ret()
      }
    },
    // 跳转到立即入网的认证页面
    gotoAuth() {
      // 点击立即入网的话，把入网状态重新变成未入网
      if (parseInt(_.get(this.clientInfo, 'accessStatus.deviceStatus')) === 1) {
        const accessStatus = { ...this.clientInfo.accessStatus, ...{ deviceStatus: 0 }}
        this.setClientInfo({ ...this.clientInfo, ... { accessStatus: accessStatus }})
      }

      // 防止组件被卸载后(跳转到其他路由后)，这里继续跳转(因为卸载后异步方法仍然进行)
      if (_.get(this.$router, 'currentRoute.path') === '/access/message') {
        processController.next()
      }
    },
    // IPAM隔离可入网
    isIPAMauth() {
      const conf = _.get(this.serveEntiretyConfig, 'server.ipamConfig', {})
      if (parseInt(_.get(conf, 'linkage')) === 1 && parseInt(_.get(conf, 'dev_regulation_access_on_off')) === 1 && _.get(this.clientInfo, 'accessStatus.checkTime', null) === '') {
        return true
      }
      return false
    },
    toSecurity(event) {
      this.$router.push({ path: '/access/check', query: { CheckSource: 3 }})
    },
    /* reopen assui重新设置一下右上角操作区域宽度*/
    updateMenuWidth() {
      console.log('upp', this.$route.query)
      if (this.$route.query.from === 'reopen') {
        this.$nextTick(() => {
          const addWidth = authIndex.isOpenZeroTrust() ? 44 : 0
          const width = parseInt(document.getElementById('u-avator') ? document.getElementById('u-avator').offsetWidth : 0)
          this.$ipcSend('UIPlatform_Window', 'SetTitleDimension', { nHeight: 50, nNameWidth: parseFloat(width) + addWidth })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#f-message {
  position: relative;
  height: 100%;

  .u-networe-status-user {
    margin-top: 24px;
    width: 130px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #686e84;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;

    span {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;

      &:nth-last-child(1):first-child {
        padding: 0;
        text-align: center;
      }

      &:nth-of-type(1) {
        padding-right: 16px;
        text-align: right;
        flex: 0 1 auto;
      }

      &:nth-of-type(2) {
        padding-left: 16px;
        text-align: left;
        flex: 0 0 auto;
      }
    }
  }

  .u-networe-status-handle {
    margin-top: 32px;
    width: 100%;
    text-align: center;

    a {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      text-decoration: underline;
      color: #b3b6c1;
      line-height: 20px;
    }
  }
  ::v-deep .slot-state-msg {
    span {
      color: $--color-primary;
      cursor: pointer;
    }
  }
}
</style>
