<template>
  <div id="single-receive-page" @keydown="keyDown">
    <activeForm ref="activeForm" class="active-form" form-name="guest-receive-receive" submit-btn-id="ui-guest-receive-receive-button-submit" :form-fields="formFields" :form="form" :loading="loading" @submit="submitForm">
      <el-checkbox id="ui-guest-receive-receive-checkbox-send_code" v-model="issendsms" class="s-send-sms" true-label="1" false-label="0">{{ $t('guestAuth.guest.info_88') }}</el-checkbox>
    </activeForm>
  </div>
</template>
<script>
import guestFormUtils from '@/render/utils/bussiness/guestFormUtils'
import activeForm from '../../activeForm'
import { mapState } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
export default {
  components: {
    activeForm
  },
  props: {
    propRegions: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      formFields: [],
      form: {},
      loading: false,
      issendsms: '1'
    }
  },
  computed: {
    ...mapState(['serveEntiretyConfig', 'clientInfo']),
    allowSendSMS() {
      return parseInt(_.get(this.serveEntiretyConfig, 'server.GUESTAUTH.IsSendCode', 0)) === 1
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.form = {}
      this.formFields = []
      this.$refs.activeForm.$refs.ruleForm.resetFields()
      this.initForm()
    }
  },
  mounted() {
    this.initForm()
  },
  methods: {
    async initForm() {
      const CLIENTCHECK = _.get(this.serveEntiretyConfig, 'server.CLIENTCHECK')
      const params = _.cloneDeep(CLIENTCHECK)
      params[this.addSuffix('AllowRegionIDs')] = `1|${this.$t('guestAuth.allowRegion')}|||select|`
      params[ this.addSuffix('AllowTime')] = `1|${this.$t('guestAuth.allowTime')}|${this.$t('guestAuth.pEnter')}|1-999Num|hour|`
      params[this.addSuffix('GuestStartTime')] = `1|${this.$t('guestAuth.allowRange')}|${this.$t('guestAuth.pSelect')}||dayRange|`
      const { formFields, form } = guestFormUtils.createForm(params, { isApply: true })
      formFields.forEach(item => {
        if (item.Name === 'AllowRegionIDs') {
          item.Options = this.propRegions
        }
        if (item.Column === 'GuestRequireTel' && this.allowSendSMS) {
          item.addSlot = true
        }
      })
      this.formFields = formFields
      this.form = this.initFormVal(form)
      console.log('form', this.form, this.formFields, this.propRegions)
    },
    initFormVal(form) {
      const newForm = {}
      for (const k in form) {
        if (k === 'AllowRegionIDs') {
          newForm[k] = []
        } else if (k === 'AllowTime') {
          newForm[k] = '8'
        } else if (k === 'IsNeedAudit') {
          newForm[k] = this.$t('guestAuth.noAudit')
        } else {
          if (!form[k] || typeof form[k] === 'string') {
            newForm[k] = ''
          } else {
            newForm[k] = []
          }
        }
      }
      return newForm
    },
    async submitForm() {
      const _params = guestFormUtils.formateFormParams(this.form)
      const { UserID, userName, roleID } = _.get(this.clientInfo, 'accessStatus')
      const { DeviceID } = _.get(this.clientInfo, 'detail')
      const fixedParams = {
        roleid: roleID,
        deviceId: DeviceID,
        UserID,
        user_name: userName,
        issendsms: '0'
      }
      const telInfo = this.formFields.find(i => i.Column === 'GuestRequireTel')
      if (this.issendsms === '1' && !_params.guestmobile && this.allowSendSMS) {
        const title = telInfo && telInfo.Title
        this.$message({
          message: this.$t('guestAuth.guest.info_98', { name: title }),
          type: 'warning'
        })
        return
      }
      if (this.allowSendSMS && telInfo) {
        fixedParams.issendsms = this.issendsms
      }

      this.loading = true
      const ret = await proxyApi.reqGeustNetcode({ ..._params, ...fixedParams })
      this.loading = false
      if (parseInt(ret.errcode) === 0) {
        this.$emit('applySuccess', {
          type: 'single',
          data: ret.data
        })
      }
    },
    // 回车提交
    keyDown(event) {
      if (parseInt(event.keyCode) !== 13 || event.srcElement.type === 'textarea') {
        return
      }
      // 非加载中
      if (!this.loading) {
        this.$refs.activeForm.submitForm()
      }
    },
    addSuffix(key) {
      return this.$i18n.locale === 'zh' ? key : key + '_en'
    }
  }
}
</script>
<style lang="scss" scoped>
  #single-receive-page {
    width: calc(100% + 48px);
    margin: 0 auto;
    height: 100%;
    margin-right: -24px;
    margin-left: -24px;
    ::v-deep .active-form{
      .form-wrapper{
        &::-webkit-scrollbar-track{
          background-color: #fff;
        }
        &::-webkit-scrollbar-button{
          background-color: #fff;
        } /* 滑轨两头的监听按钮颜色 */
        &::-webkit-scrollbar-corner{
          background-color: #fff;
        }
      }
      .guest-active-form{
        width: 380px;
        margin: 0 auto;
      }
      .submit-wrapper{
        display: flex;
        justify-content: center;
        padding-left: 0;
      }
    }
    ::v-deep .s-send-sms .el-checkbox__label{
      color: $title-color
    }
    ::v-deep .el-radio__label{
      color: $title-color
    }
}
</style>
