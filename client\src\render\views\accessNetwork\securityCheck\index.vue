<!-- 安检页面 -->
<template>
  <div class="security-check-wrap">
    <div class="summarize">
      <img :src="securityStateImg" alt="">
      <div class="message-box">
        <!-- 正在安检 -->
        <div class="checking">
          <div>
            <el-tooltip :content="headerTip.title" placement="bottom-start">
              <div slot="content" class="el-tool-tip" v-html="headerTip.title" />
              <span id="ui-check-index-span-title" class="course-info" v-html="headerTip.title" />
            </el-tooltip>
          </div>
          <el-tooltip :content="headerTip.title" placement="bottom-start">
            <div slot="content" class="el-tool-tip" v-html="headerTip.subTitle" />
            <span id="ui-check-index-div-sub_title" class="result-info" v-html="headerTip.subTitle" />
          </el-tooltip>
          <div v-if="count>=0" class="visit-web" @click="redirectTo">{{ isSpecUrl ? $t('check.js_28_s') : $t('check.js_27_s') }} {{ count }}s</div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="operation-box">
        <div
          v-if="securityCheckType === 3 || securityCheckType === 4"
          class="operation_1"
        >
          <p id="ui-check-index-p-re_check" class="algin" @click="againCheckFn">
            {{ $t("accessNetwork.securityCheck.info_6") }}
          </p>
          <p id="ui-check-index-p-repair" class="repair" @click="repairFn(false)">
            {{ $t("accessNetwork.securityCheck.info_7") }}
          </p>
        </div>
        <div v-if="securityCheckType === 5" class="operation_2">
          <p id="ui-check-index-p-cancel_repair" class="algin" @click="cancelRepairFn">
            {{ $t("accessNetwork.securityCheck.info_8") }}
          </p>
        </div>
      </div>
      <proccess-bar v-if="proccessFlag" id="ui-check-progress-div-check_tips" class="proccess-wrapper" :percentage="percentage" />
    </div>
    <!-- 安检项 -->
    <div class="security-check-item">
      <Loading v-if="!policyList.length&&!AllCheckResultData.length" />
      <checking
        v-if="securityCheckType === 1 && policyList.length"
        :checking.sync="checkingIndex"
        :check-list="policyList"
        :proccess.sync="percentage"
        :show-proccess.sync="proccessFlag"
        :is-agin="isRecheck"
        :is-check-report.sync="isCheckReport"
        @changeCheckTypeFn="changeCheckTypeFn"
        @checkError="showConfirmDialog = true"
      />
      <security-item
        v-if="securityCheckType !== 1 && AllCheckResultData.length"
        ref="securityItem"
        :fixed-num.sync="fixedNumber"
        :check-result-list="AllCheckResultData"
        :security-check-type="securityCheckType"
        :proccess.sync="percentage"
        :show-proccess.sync="proccessFlag"
      />
    </div>

    <div v-if="showAutoFixTip" class="tip-banner">
      <i class="iconfont icon-putongxiangbuhegui" />
      <span>{{ autoCheckTip }}</span>
    </div>
    <ConfirmDialog id="c-fix-dialog" pop-name="check-index-handle_fix" :show.sync="dialogTableVisible" :show-cancel-button="false" width="285px" @ok="closeHandle">
      <p id="ui-check-index-p-handle_fix_tip" class="c-tip-content">{{ autoCheckTip }}</p>
    </ConfirmDialog>

    <!-- 安检异常退出确认框 -->
    <ConfirmDialog id="check-confirm-dialog" :show.sync="showConfirmDialog" :show-cancel-button="false" width="285px" @ok="errorHandle">
      <p class="check-error-confirm-tips">{{ $t('safecheck_js_langObj.js_13_s') }}</p>
    </ConfirmDialog>
  </div>
</template>

<script>
import Loading from './components/check/loading.vue'
import checking from './components/check/checking.vue'
import securityItem from './components/check/securityItem.vue'
import agentApi from '@/service/api/agentApi'
import proccessBar from './components/check/progress.vue'
import checkMixins from './mixins/mixins'
import { mapGetters, mapMutations, mapState } from 'vuex'
import processController from '@/render/utils/processController'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import localStorage from '@/render/utils/cache/localStorage'
import proxyApi from '@/service/api/proxyApi'
import qs from 'qs'
import scene from '@/render/utils/bussiness/scene'
import { EventBus } from '@/render/eventBus'
import securityUtil from '@/render/utils/bussiness/securityUtil'
import _ from 'lodash'
import store from '@/render/store'

export default {
  components: {
    checking,
    securityItem,
    Loading,
    proccessBar
  },
  mixins: [checkMixins],
  data() {
    return {
      imgList: [
        '',
        require('@/render/assets/examineing.png'),
        require('@/render/assets/examineSuccess.png'),
        require('@/render/assets/examineError.png'),
        require('@/render/assets/examineDanger.png'),
        require('@/render/assets/repairing.png')
      ],
      checkingIndex: 0, // 当前检查项
      fixedNumber: 0, // 已修复数量
      securityCheckType: 1, // 安检类型 1安检进行 2 通过 3/安检不通过 4安检存在隐患 5修复
      beforSecurityCheckType: '', // 修复前安检类型，中途取消修复，或者修复后不进行安检恢复显示之前安检结果页面用到该参数
      policyList: [], // 安检策略列表数据
      proccessFlag: false,
      percentage: 0,
      AllCheckResultData: [],
      checkResult: {},
      dialogTableVisible: false,
      autoCheckTip: '',
      timer: null,
      needAutoRepaire: false, // 安检完成是否需要自动修复
      haveAutoRepaire: false, // 是否进行过自动修复
      AllPassTip: '',
      initPolicys: [], // 安检策略备份
      is8021x: false, // 是否是802.1x模式-认证前安检，无关键安检项失败则跳转到认证页面
      RepairConf: 0, // 安全隐患修复期限
      securityAllSuccess: false, // 安检成功
      lastFault: '', // 上次安检失败时间
      freshRegiste: '', // 实时注册状态
      initRegiste: '', // 进入安检时注册状态
      canShowAuditTip: true, // 是否显示审核状态提示语
      waitOpenNetwork: false, // 等待放开网络
      regTimer: null,
      haveReverify: false, // 安全加固安检项为关键安检项且设置了违规重审核
      isSpecUrl: false, // 跳转指定网站地址
      redirectUrl: '',
      count: -1, // 倒计时
      cutTimer: null,
      isRecheck: false, // 是否再次安检
      initLoading: true, // 获取安检策略loading
      isCheckReport: false, // 安检完成安检上报loading
      notCheckItem: false, // 未配置安检项
      showConfirmDialog: false,
      checkError: false, // 安检异常
      showAutoFixTip: false
    }
  },
  computed: {
    ...mapState(['redirectInfo']),
    ...mapGetters(['clientInfo', 'serveEntiretyConfig', 'authInfo', 'computeNetAccessStatus']),
    // 是否为可信设备
    IsTrustDev() {
      return _.get(this.clientInfo, 'detail.IsTrustDev') === '1'
    },
    registerStatus() {
      return this.freshRegiste === '' ? parseInt(_.get(this.clientInfo, 'detail.Registered', -2)) : this.freshRegiste
    },
    // 是否已入网
    isAccess() {
      return !!_.get(this.computeNetAccessStatus(), 'isAccess', false)
    },
    // 用户自定义安检提示
    selfTip() {
      const defaultVal = { // 8021认证前安检没网拿不到配置信息使用默认提示
        PromptWords1: '安检通过!',
        PromptWords1_en: 'The security check is passed',
        PromptWords3: '您的终端存在违规问题，安检不通过，请及时解决!',
        PromptWords3_en: 'Your terminal has a violation problem, and the security check fails, please solve it in time!',
        PromptWords5: '您的设备现在可以正常使用网络，但存在安全隐患，请及时进行修复！',
        PromptWords5_en: 'Your device now can use the network normally,but there are still security risks, please fix.',
        PromptWords6: '请等待管理员审核，审核通过后才能接入网络！',
        PromptWords6_en: 'Please wait for administrator audit， in order to access the network after finishing audit.'
      }
      const tips = _.get(this.serveEntiretyConfig, 'server.CLIENTCHECK', defaultVal)
      // 增加审核通过提示
      tips.PromptWords_1_en = 'Audit passed, you can continue to access the network'
      tips.PromptWords_1 = '审核通过，您可以继续访问网络！'
      return tips
    },
    securityStateImg() {
      return this.imgList[this.securityCheckType]
    },
    headerTip() { // 顶部提示
      const ret = {
        title: this.$t('check.loading'),
        subTitle: ''
      }
      if (this.initLoading) { // 正在获取安检策略
        return {
          title: this.$t('check.poLoadTitle'),
          subTitle: this.$t('check.poLoadsubTitle')
        }
      }
      if (this.isCheckReport) { // 正在安检上报
        return {
          title: this.$t('check.checkReportTitle'),
          subTitle: this.$t('check.checkReportSubTitle')
        }
      }
      const isOpen = parseInt(_.get(this.serveEntiretyConfig, 'server.CLIENTCHECK.ControlPostion', 0)) === 1 // 是否开启先安检后审核
      const lang = this.$i18n.locale
      switch (this.securityCheckType) {
        case 1: // 正在安检
          ret.title = this.$t('accessNetwork.securityCheck.info_1')
          if (this.notCheckItem) {
            ret.subTitle = this.$t('check.js_29_s')
          } else {
            ret.subTitle = `${this.$t('check.total') + this.policyList.length + this.$t('check.currentCheck')}<span class="checking">${this.checkingIndex
            }</span>${this.$t('check.item')}`
          }
          break
        case 2: // 安检通过
          ret.title = lang === 'zh' ? this.selfTip['PromptWords1'] : this.selfTip['PromptWords1_en']
          if (isOpen && this.canShowAuditTip && this.initRegiste === 0) {
            if (this.registerStatus === 0) {
              ret.title = lang === 'zh' ? this.selfTip['PromptWords6'] : this.selfTip['PromptWords6_en']
            } else if (this.registerStatus === 1) {
              ret.title = lang === 'zh' ? this.selfTip['PromptWords_1'] : this.selfTip['PromptWords_1_en']
            }
          }
          ret.subTitle = this.AllPassTip || this.$t('accessNetwork.securityCheck.info_32', { total: this.checkResult.success })
          break
        case 3: // 安检不通过
          ret.title = lang === 'zh' ? this.selfTip['PromptWords3'] : this.selfTip['PromptWords3_en']
          if (this.IsTrustDev) { // 可信设备
            ret.title = lang === 'zh' ? this.selfTip['PromptWords2'] : this.selfTip['PromptWords2_en']
          }
          ret.subTitle = `${this.$t('check.checkCompleteErr')}<span class="error">${
            this.checkResult.waring + this.checkResult.error || 0
          }</span>${this.$t('check.checkCompleteErrTow')}`
          break
        case 4: // 存在隐患
        {
          const expire = this.expireTime()
          if (expire >= 0) {
            if (isOpen && this.canShowAuditTip && this.initRegiste === 0) {
              if (this.registerStatus === 0) {
                ret.title = lang === 'zh' ? this.selfTip['PromptWords6'] : this.selfTip['PromptWords6_en']
              } else if (this.registerStatus === 1) {
                ret.title = lang === 'zh' ? this.selfTip['PromptWords_1'] : this.selfTip['PromptWords_1_en']
              }
            } else {
              ret.title = lang === 'zh' ? this.selfTip['PromptWords5'] : this.selfTip['PromptWords5_en']
              if (expire > 0) {
                ret.title += this.$t('check.js_25_s', { day: expire })
              }
            }
          } else {
            ret.title = lang === 'zh' ? this.selfTip['PromptWords4'] : this.selfTip['PromptWords4_en']
          }
          if (this.is8021x) {
            ret.title = this.$t('accessNetwork.securityCheck.info_4')
          }
          ret.subTitle = `${this.$t('check.checkCompleteErr')}<span class="error">${this.checkResult.waring}</span>${this.$t('check.checkCompleteErrTow')}`
          break
        }
        case 5: // 修复
          ret.title = this.$t('accessNetwork.securityCheck.info_5')
          ret.subTitle = `${this.$t('check.autoFixTip1')} ${this.checkResult.waring + this.checkResult.error || 0}
          ${this.$t('check.autoFixTip2')}<span class="checking">${this.fixedNumber}</span>${this.$t('check.autoFixTip3')}`
      }
      return ret
    },
    // 是否隔离
    isCutOff() {
      return _.get(this.clientInfo, 'detail.CutOffStopTime') >= _.get(this.serveEntiretyConfig, 'server.TIME')
    }
  },
  watch: {
    isAccess(val) {
      if (val && this.securityCheckType === 4) {
        this.setRedirectConf() // 倒计时重定向
      }
    }
  },
  async mounted() {
    await this.getIsZtpUser()
    await this.getScene()
    this.getCheckItem()
    this.freshRegistHandle()
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
    this.timer = null
    this.regTimer && clearInterval(this.regTimer)
    this.regTimer = null
    this.clearRedirectTime()
  },

  methods: {
    ...mapMutations(['setRedirectInfo', 'setClientInfo']),
    // 从浏览器唤起安检的，获取当前场景信息
    async getScene() {
      const query = qs.parse(location.search.substring(1))

      if (parseInt(_.get(query, 'fromBrowser', 0)) === 1 &&
      !_.get(this.serveEntiretyConfig, 'scene.SceneID', false)) {
        await scene.getDeviceScene(_.get(query, 'userType', ''))
        if (!_.get(this.serveEntiretyConfig, 'scene.SceneID', false)) {
          console.debug('获取不到场景信息跳转到message')
          processController.set('/access/message')
        }
      }
    },
    async getIsZtpUser() { // #5069项目测试发现问题，内外网切换时若停留在安检页面则重新获取一遍isZtpUser
      const query = qs.parse(location.search.substring(1))
      const urlZtp = _.get(query, 'ZtpUser', '')
      const authZtp = _.get(store, 'getters.authInfo.basic.ZtpUser', '') + ''
      const netStatusZtp = _.get(store.state, 'clientInfo.accessStatus.ZtpUser.ZtpUser', '') + ''
      if (urlZtp === '' && authZtp === '' && netStatusZtp === '') {
        await commonUtil.netRet()
      }
    },
    // 获取安检策略
    async getCheckItem() {
      const CheckSource = _.get(this.$route, 'query.CheckSource', 0)
      this.is8021x = CheckSource && Number(CheckSource) === 1

      // 来自submit.vue里面的安检修复和查看安检详情，不需要调用setDevAccessInfo
      if (parseInt(CheckSource) !== 3 && parseInt(CheckSource) !== 2) {
        await commonUtil.setDevAccessInfo()
      }
      const res = await agentApi.getSecurityCheckPolicy({ CheckSource })
      this.lastFault = _.get(res, 'ASM.LastFaultTime')
      console.log('安检项', res)
      if (res.ASM && res.ASM.SecurityCheckPolicy) {
        const policyKey = securityUtil.getPolicysKey()
        let checkItems = _.get(res, `ASM.SecurityCheckPolicy.CheckItems.${policyKey}`)
        this.RepairConf = _.get(res, 'ASM.SecurityCheckPolicy.RepairConf')
        const autoRepaireFlag = _.get(res, 'ASM.SecurityCheckPolicy.CheckParam.IsAutoRepair')
        if (checkItems && parseInt(_.get(res, 'ASM.SecurityCheckPolicy.CheckParam.IsSafeCheck', 0)) === 1) {
          if (Object.prototype.toString.call(checkItems) === '[object Object]') {
            checkItems = [checkItems]
          }
          for (let i = 0; i < checkItems.length; i++) {
            const item = checkItems[i]
            item.checkStep = 0 // 0 未开始安检 1 安检中 2 安检结束
            item.itemResultType = 0 // 安检状态 0成功 1警告 2 不通过
            item.hasFixed = false // 是否修复
            // 是否为关键检查项
            const isKeyItem = item.Key === 'Yes'
            // 是否为安全加固安检重审核
            const isReverify = _.get(item, 'CheckType.Repair.Reverify') === 'Yes'
            if (isKeyItem && isReverify) {
              this.haveReverify = true
            }
            if (item.IsAutoRepair === '1' && autoRepaireFlag === '1') {
              this.needAutoRepaire = true
            }
            if (item.InsideName === 'CheckNicBind') {
              agentApi.callAgentOneFunc({
                WhatToDo: 'CallOneFunc',
                WhereIsModule: 'MsacCheckSecuritySet.dll',
                WhatFuncToCall: 'DeleteNicInfoToDB',
                RequestParam: item.CheckType.Option.DistributeTime
              })
            }
          }
          this.initPolicys = _.cloneDeep(checkItems)
          const CheckParam = _.get(res, 'ASM.SecurityCheckPolicy.CheckParam')
          // 极速入网安检或修复模式
          if (this.isFastCheck(CheckParam) || this.isFixHistoryTrobule(CheckSource)) {
            this.fastNetworkCheck(checkItems)
          } else {
            this.initLoading = false
            this.policyList = checkItems
            this.securityCheckType = 1
          }
          console.log('安检策略', checkItems)
        } else {
          this.initLoading = false
          // 只配置策略没有配置检查项
          const registerStatus = parseInt(_.get(this.clientInfo, 'detail.Registered', -2)) === 0 // 是否处于待审核状态
          if (registerStatus) { // 待审核
            localStorage.setItem('auditCheckNextStatus', JSON.stringify({ DeviceID: _.get(this.clientInfo, 'detail.DeviceID') }), 3600)
          }
          this.securityAllSuccess = true
          this.notCheckItem = true
          if (parseInt(CheckSource) !== 3) { // 从入网成功进入不进行安检上报
            await commonUtil.securityCheckRePort()
          }
          processController.next()
          return
        }
      } else {
        this.showConfirmDialog = true
      }
    },
    // 极速入网安检
    async fastNetworkCheck(checkItems) {
      const res = await agentApi.getSecCheckResult()
      if (res.ASM.ErrCode === '0' && res.ASM.Result) {
        let resultArr = res.ASM.Result
        if (Object.prototype.toString.call(resultArr) === '[object Object]') {
          resultArr = [resultArr]
        }
        checkItems.forEach(policy => {
          const itemResult = resultArr.find(obj => obj.ItemID === policy.ItemID)
          if (itemResult) {
            policy.CheckResult = itemResult
            this.checkResultHandle(itemResult, policy) // 根据安检结果处理数据
          }
        })
        const CheckSource = _.get(this.$route, 'query.CheckSource', 0)
        if (parseInt(CheckSource) !== 3) {
          await commonUtil.securityCheckRePort()
        }
        this.initLoading = false
        const checkRetInfo = this.dataFormat(checkItems)
        this.fixOpenSuccess(checkRetInfo)
        this.changeCheckTypeFn(checkRetInfo) // 展示结果
        // 安检上报
      } else { // 数据获取失败走正常安检
        this.initLoading = false
        this.policyList = checkItems
        this.securityCheckType = 1
      }
    },
    // 是否需要展开安检成功项 从入网成功页面进入且全部安检通过自动展开
    fixOpenSuccess(ret) {
      if (ret.resultType === 2 && parseInt(_.get(this.$route, 'query.CheckSource', 0)) === 3) {
        ret.data[0].showChildren = true
      }
    },
    // 重新安检
    againCheckFn() {
      this.stopAutoRepaire() // 清除自动修复
      if (this.registerStatus === 1) {
        this.canShowAuditTip = false
      }
      if (this.count > -1) { // 清除入网成功重定向打开浏览器页面倒计时
        this.count = -1
        this.clearRedirectTime()
      }

      this.policyList = _.cloneDeep(this.initPolicys)
      this.securityCheckType = 1
      this.AllCheckResultData = []
      this.checkingIndex = 0
      this.isRecheck = true
    },
    // 一键/自动修复
    repairFn(isAuto) {
      this.stopAutoRepaire() // 清除自动修复
      if (this.registerStatus === 1) {
        this.canShowAuditTip = false
      }
      if (this.count > -1) { // 清除入网成功重定向打开浏览器页面倒计时
        this.count = -1
        this.clearRedirectTime()
      }
      this.haveAutoRepaire = true
      this.beforSecurityCheckType = this.securityCheckType
      this.securityCheckType = 5
      this.fixedNumber = 0
      this.$refs.securityItem.startFix(isAuto)
    },
    // 取消修复
    cancelRepairFn() {
      this.securityCheckType = this.beforSecurityCheckType
      this.proccessFlag = false
      console.debug('取消修复')
      this.$refs.securityItem.cancelRepairFn()
    },
    // 安检结果
    changeCheckTypeFn(val) {
      this.securityCheckType = val.resultType
      this.AllCheckResultData = val.data
      this.checkResult = val.checkCount
      const registerStatus = parseInt(_.get(this.clientInfo, 'detail.Registered', -2)) === 0 // 是否处于待审核状态
      if (registerStatus || (this.securityCheckType === 3 && this.haveReverify)) { // 进入安检待审核或安全加固安检项为关键安检项且设置了违规重审核
        localStorage.setItem('auditCheckNextStatus', JSON.stringify({ DeviceID: _.get(this.clientInfo, 'detail.DeviceID') }), 3600)
      }
      if (this.is8021x && this.securityCheckType === 2) { // 802.1x模式安检成功
        this.securityAllSuccess = true
        processController.next()
      } else if (!this.is8021x && this.securityCheckType === 2) { // 普通模式安检成功
        this.checkAllSuccess()
      } else {
        EventBus.$emit('client:show')
        if (!this.is8021x && this.securityCheckType === 4) {
          if (this.waitOpenNetwork) {
            this.openNetwork()
          } else {
            // 查询并存入入网状态到store,用于控制头像显示-存在隐患会放开网络此时留在安检页面需要显示头像
            commonUtil.netRet()
            if (!registerStatus) {
              commonUtil.IPAMRedirect() // IPAM重定向
            }
          }
        }
        if (!this.is8021x && this.securityCheckType === 3) { // 安检不通过展示头像 bugID=13671
          commonUtil.netRet()
        }
        // 安检存在问题
        if (!this.haveAutoRepaire && this.needAutoRepaire) { // 没有进行过自动修复且存在自动修复项进行一次自动修复
          this.autoRepaire()
        } else {
          // this.autoCheckTip = this.$t('check.handleFixTip')
          // this.dialogTableVisible = true
        }
      }
    },
    // 普通模式所有项安检成功
    checkAllSuccess() {
      if (parseInt(_.get(this.$route, 'query.CheckSource', 0)) === 3) {
        return
      }
      localStorage.setItem('needRedirect', true, 360000)
      this.securityAllSuccess = true
      this.AllPassTip = this.$t('accessNetwork.securityCheck.info_31', { total: this.checkResult.success })
      processController.next()
    },
    // 自动修复倒计时提示
    autoRepaire() {
      let second = 5
      this.autoCheckTip = this.$t('check.autoFix', { second: second })
      this.showAutoFixTip = true
      this.timer = setInterval(() => {
        if (second === 0) {
          clearInterval(this.timer)
          this.timer = null
          this.autoCheckTip = this.$t('check.autoFix', { second: second })
          this.showAutoFixTip = false
          this.repairFn(true)
        } else {
          second -= 1
          this.autoCheckTip = this.$t('check.autoFix', { second: second })
        }
      }, 1000)
    },
    stopAutoRepaire() {
      this.timer && clearInterval(this.timer) // 清除自动修复倒计时
      this.showAutoFixTip = false
      this.haveAutoRepaire = true
    },
    // 自动弹窗关闭回调
    closeHandle() {
      console.log('关闭')
      this.dialogTableVisible = false
      this.timer && clearInterval(this.timer)
      this.timer = null
    },
    // 是否超过修复周期
    expireTime() {
      console.debug(`安检超时计算参数${new Date().getTime()}-${this.lastFault}-${this.RepairConf}`)
      if (this.RepairConf > 0 && this.lastFault) {
        if (new Date(this.lastFault).getFullYear() < 2000) { // 初始值为1970表示未进行安检
          return this.RepairConf
        }
        const currentTime = new Date()
        const currentDay = `${currentTime.getFullYear()}/${currentTime.getMonth() + 1}/${currentTime.getDate()}`

        let diff = new Date(currentDay).getTime() - new Date(this.formateDate(this.lastFault)).getTime()
        console.debug('计算日期' + currentDay + '-' + this.formateDate(this.lastFault))
        if (diff > 0) {
          diff = diff / 86400000
          const ret = this.RepairConf - diff
          return ret < 0 ? ret : Math.ceil(ret)
        } else {
          return this.RepairConf
        }
      } else if (this.RepairConf === '0') {
        return 0
      }
      return -1
    },
    // 去掉时分秒
    formateDate(date) {
      if (!date) {
        return
      }
      let _date = date.replace(/-/g, '/')
      _date = new Date(_date)
      return `${_date.getFullYear()}/${_date.getMonth() + 1}/${_date.getDate()}`
    },
    // 先安检后审核停留在安检页面需实时获取审核状态
    freshRegistHandle() {
      this.initRegiste = parseInt(_.get(this.clientInfo, 'detail.Registered', -2))
      console.log('initRegiste', this.initRegiste)
      const isOpen = parseInt(_.get(this.serveEntiretyConfig, 'server.CLIENTCHECK.ControlPostion', 0)) === 1 // 是否开启先安检后审核
      if (isOpen && this.registerStatus === 0) {
        const deviceid = _.get(this.clientInfo, 'basic.AgentID')
        this.regTimer = setInterval(async() => {
          const res = await proxyApi.getDeviceIsReg({ deviceid })
          const Registered = res.data.Registered
          this.freshRegiste = parseInt(Registered)
          if (Registered === '1' || Registered === false || this.securityCheckType === 2) {
            if (Registered === '1') {
              if (this.securityCheckType === 4) { // 放开网络
                this.openNetwork()
              } else {
                this.waitOpenNetwork = true
              }
              localStorage.removeItem('auditCheckNextStatus')
            }
            clearInterval(this.regTimer)
            this.regTimer = null
          }
        }, 3000)
      }
    },
    async openNetwork() {
      // 直接调用放开网络接口
      await commonUtil.securityCheckRePort()
      // 查询并存入入网状态到store,用于控制头像显示-存在隐患会放开网络此时留在安检页面需要显示头像
      await commonUtil.detail()
      await commonUtil.netRet()
      commonUtil.IPAMRedirect() // IPAM重定向
    },

    // 判断是否需要跳转
    setRedirectConf() {
      const query = qs.parse(location.search.substring(1))
      const firstUrl = _.get(query, 'firsturl', '')
      const count = _.get(this.serveEntiretyConfig, 'server.Redirect.redirectTime', 0)
      const redirect = _.get(this.serveEntiretyConfig, 'server.Redirect.redirectUrl', '')
      if (this.redirectInfo.haveRedirect ||
        parseInt(_.get(this.$route, 'query.fromeMessage', 0)) === 1 || // 从message跳转过来的
        count < 1 ||
        redirect.length === 0) {
        return
      }
      this.redirectUrl = firstUrl
      const specUrl = decodeURIComponent(redirect) // 指定网站
      if (firstUrl.length > 0 && specUrl.indexOf(firstUrl) !== -1) {
        this.isSpecUrl = false
      } else {
        this.isSpecUrl = true
        this.redirectUrl = this.parestRedirectUrl(specUrl)
      }
      if (commonUtil.isExceptionRedirect(this.redirectUrl)) { // 特定的url不用跳转
        return
      }
      this.setRedirectInfo({ haveRedirect: true })
      this.cutDown(count)
    },
    cutDown(count) {
      this.count = count
      this.cutTimer = setInterval(() => {
        this.count--
        if (this.count === -1) {
          clearInterval(this.cutTimer)
          this.cutTimer = null
          this.redirectTo()
        }
      }, 1000)
    },
    redirectTo() {
      agentApi.windowOpenUrl(this.redirectUrl)
      this.count = -1
      this.clearRedirectTime()
    },
    /**
   *  替换跳转url里面的宏变量(只有认证后的需要替换，因为只有认证后才能拿到username和password)
   *  格式如：http://*************/?ip={ipaddress}&username={username}&password={password}，
   *  其中{ipaddress}为当前认证ip，{username}和{password}分别为认证用户名和密码的宏变量。
  **/
    parestRedirectUrl(redirectUrl) {
      const ipaddress = _.get(this.clientInfo, 'detail.IP', '')
      const username = _.get(this.authInfo, 'basic.UserName', '') || _.get(this.clientInfo, 'detail.UserName', '')
      const passwordMd5 = _.get(this.authInfo, 'basic.passwordMd5', '')
      redirectUrl = redirectUrl.replace('{ipaddress}', ipaddress)
      redirectUrl = redirectUrl.replace('{username}', username)
      redirectUrl = redirectUrl.replace('{password}', passwordMd5)
      return redirectUrl
    },
    clearRedirectTime() {
      this.cutTimer && clearInterval(this.cutTimer)
      this.cutTimer = null
    },
    isFastCheck(CheckParam = {}) {
      return CheckParam.SafetyMonitor === '1' && CheckParam.FastCheck === '1'
    },
    // 入网状态页点击立即修复进入直接修复情况
    isFixHistoryTrobule(CheckSource = 0) {
      return parseInt(CheckSource) === 3
    },
    // 存安检总结果
    saveSecurityResult() {
      const dic = {
        '2': 'success',
        '3': 'false',
        '4': 'fault'
      }
      const basic = { ...this.clientInfo.basic, ...{ CheckResult: dic[this.securityCheckType + ''] }}
      this.setClientInfo({ ...this.clientInfo, ... { basic: basic }})
    },
    // 安检存在隐患并且审核通过且查询网络状态为未放开，用户点击进入入网状态页，强制进入入网成功页面（为了解决网络放开延时问题，入网成功页面有定时刷新入网状态机制）
    isForceToSubmit(toRoute) {
      const expire = this.expireTime()
      const isAccess = parseInt(_.get(this.clientInfo, 'accessStatus.deviceStatus', 0)) === 1
      return this.securityCheckType === 4 && this.registerStatus === 1 && toRoute.name === 'message' && !isAccess && expire > 0 && !this.isCutOff
    },
    errorHandle() {
      this.checkError = true
      processController.next()
    }
  },
  beforeRouteLeave(to, from, next) {
    this.saveSecurityResult() // 存储安检结果
    const isForce = _.get(to, 'params.forceTo', false)
    // 未安检通过提示是否进行跳转
    if (!this.securityAllSuccess && this.policyList.length && !isForce && !this.checkError && parseInt(this.securityCheckType) === 1) {
      this.$dialogTip({
        content: this.$t('check.js_26_s'),
        popName: 'ui-accessNetwork-security',
        success: () => {
          if (this.isForceToSubmit(to)) {
            console.log('直接进入submit')
            this.securityAllSuccess = true
            next({ path: '/access/submit' })
          } else {
            next()
          }
        },
        cancel: () => {
          next(false)
        }
      })
    } else {
      next()
    }
  }
}
</script>
<style lang="scss">
.security-check-wrap {
  height: 100%;
  box-sizing: border-box;
  position: relative;
  .summarize {
    padding: 10px 24px;
    height: 120px;
    display: flex;
    align-items: center;
    background: $check-bg;
    flex-shrink: 0;
    position: relative;
    .proccess-wrapper{
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }
    img {
      width: 72px;
      height: 72px;
    }
    .message-box {
      flex: 1;
      padding: 0 18px;
      line-height: 20px;
      text-align: left;
      .course-info {
        font-size: 16px;
        font-weight: 500;
        color: $title-color;
        line-height: 22px;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        span{
          color: $error-1
        }
      }
      .result-info {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: $default-color;
        span{
          font-weight: 500;
        }
        .waring{
          color: $waring;
        }
        .checking{
          color: $--color-primary;
        }
        .error{
          color: $error
        }
      }
      .visit-web{
        font-size: 14px;
        font-weight: 400;
        color: $--color-primary;
        line-height: 20px;
        text-decoration: underline;
        margin-top: 8px;
        cursor: pointer;
        &:hover{
          color:$--color-primary-dark-2
        }
      }
    }
    .operation-box {
      text-align: right;
      .operation_1 {
        display: flex;
        align-items: center;
      }
      p {
        display: inline-block;
        min-width: 88px;
        box-sizing: border-box;
        padding: 0 12px;
        height: 32px;
        border-radius: 5px;
        text-align: center;
        line-height: 32px;
        box-sizing: border-box;
        cursor: pointer;
        &.algin {
          border: 1px solid $--color-primary;
          color: $--color-primary;
          &:hover {
            border-color: $--color-primary-dark-2;
            color: $--color-primary-dark-2;
          }
        }
        &.repair {
          background: $--color-primary;
          color: $light-color;
          margin-left: 10px;
          &:hover {
            background: $--color-primary-dark-2;
          }
        }
      }
      .operation {
        padding-left: 24px;
      }
    }
  }
  .security-check-item {
    padding: 24px 0 8px 24px;
    box-sizing: border-box;
    height: calc(100% - 120px);
  }
  .tip-banner{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-content: center;
    justify-content: center;
    line-height: 19px;
    padding: 10px 20px;
    background: $yellow-2;
    color: $waring;
    i{
      line-height: 19px;
      font-size: 16px;
      margin-right: 10px;
    }
  }

}
.el-tool-tip{
    max-width: 300px;
    font-size: 12px;
    line-height: 18px;
  }
.check-error-confirm-tips{
  padding: 24px;
}
.c-tip-content{
  padding: 24px;
}
</style>
