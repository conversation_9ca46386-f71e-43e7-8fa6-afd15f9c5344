import { Base64Encode, GetUrlParam } from '@/render/utils/global'
import store from '@/render/store'
import authIndex from '@/render/utils/auth/index'
import proxyApi from '@/service/api/proxyApi'
import opform from '@/render/utils/opform'
import common from './common'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import agent<PERSON>pi from '@/service/api/agentApi'
import authTypes from './authTypes'
import { Message } from 'element-ui'
import { i18n } from '@/render/lang'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'

const qrCodeCommon = {
  async auth(params) {
    const { guestType, username, password, isQrcode, authFrom, factorType, autoBind } = params
    if (!isQrcode) {
      return
    }
    const isknockSuccess = await this.qrKnockPort(params) // 敲端口
    if (!isknockSuccess) {
      return
    }
    const apiParam = {
      user_name: Base64<PERSON>ncode(username),
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID') || _.get(store.state.clientInfo, 'basic.AgentID'),
      idp: Base64Encode(GetUrlParam('idp')),
      tokenId: Base64Encode(GetUrlParam('tokenId')),
      remoteIp: GetUrlParam('remoteIp'),
      password: authIndex.passwordEncrypt(password),
      hintOver: G_VARIABLE.g_hintOver,
      autoAuth: 0,
      type: authTypes.User
    }
    if (params.appid) {
      apiParam.appid = params.appid
    }
    if (authFrom) {
      apiParam.authFrom = authFrom
    }

    if (params.isTwoFactors) { // 双因子
      apiParam.authtypes = 'TwoFactor'
      apiParam.BeforeAuthType = _.get(store.state.authInfo.basic, 'BeforeAuthType')
      apiParam.factorType = factorType
      apiParam.userid = _.get(store.state.authInfo.basic, 'ID')
      if (autoBind !== undefined) {
        apiParam.autoBind = autoBind
      }
    }
    if (params.redirect_uri) {
      apiParam['redirect_uri'] = params.redirect_uri
    }

    if (apiParam.authFrom === 'bind') { // 主账号绑定
      apiParam.bindAuthServer = params.bindAuthServer
      apiParam.userid = params.userid
    }

    if (guestType === 'qrlogin') {
      authIndex.config.g_isQrLogin = true
    }
    apiParam[guestType] = 1

    if (authIndex.config.isRecord !== '') {
      apiParam['isRecord'] = authIndex.config.isRecord
    }
    if (authIndex.config.callfrom !== '') {
      apiParam['callfrom'] = authIndex.config.callfrom
    }
    if (authIndex.config.from !== '') {
      apiParam['from'] = authIndex.config.from
    }

    const res = await proxyApi.authIndex(apiParam, { showError: false })

    const errorCode = parseInt(_.get(res, 'errcode', -1))
    if (!isNaN(errorCode) && errorCode !== 0) {
      switch (errorCode) {
        case 21120030:
          // 超出最大可登录设备数
          return { revoke: true, data: {
            msg: res.errmsg,
            ...res.data
          }}
        default:
          Message.error(res.errmsg, i18n.t('auth.dot1xAuthFail'))
          return false
      }
    }
    if (authFrom !== 'addition' && !params.isTwoFactors) {
      opform.set('user_name', username)
      opform.set('auth_type', authTypes.User)
      opform.set('password', password)
      opform.set('is_auto_auth', 'No')
      if (parseInt(_.get(res, 'not_save_pw', 0)) !== 1) {
        await agentApi.fileTools.DeleteOneFileNever('WebAuthTypeTmp')
      }
      store.commit('setAuthInfo', { ...store.state.authInfo, ...{ basic: res.data }})

      // 是否需要双因子，不需要的话通过
      if (res.data['FactorAuth']) {
        return { factorAuth: true }
      }

      G_VARIABLE.g_hintOver = 0
      await common.authEnd({
        type: authTypes.User
      })
    }

    return true
  },
  async qrKnockPort(params) {
    const qrDic = { 'dingqrlogin': 'DingTalk', 'feishuqrlogin': 'FeiShu', 'weworkqrlogin': 'WeWork' }
    const authserver = qrDic[params.guestType]
    if (authserver) {
      // 敲门
      const knockParam = ['User', authserver, params.username,
        params.password, '']
      if (authserver === 'FeiShu') {
        knockParam.push(params.appid)
        knockParam.push(params.redirect_uri)
      }
      const ret = await commonUtil.knockPort(knockParam)
      if (!ret) {
        return false
      }
    }
    return true
  },
  serverHost() { // 敲端口模式使用外置服务器
    const outServerIp = _.get(store, 'state.serveEntiretyConfig.server.OutServer', '')
    return outServerIp ? 'https://' + outServerIp : ''
  }
}
export default qrCodeCommon
