<template>
  <div v-loading="initLoading" class="source-list-page">
    <layout-state
      v-if="!clientInfo.online && !isDot1xMode && !isKnockPort"
      :state-img="serverNotWorking"
      :state-msg="$t('auth.unableConnectToNetwork')"
      :state-btn-txt="$t('refresh')"
      @stateBtnHandle="refresh"
    />
    <template v-else>
      <div v-if="reqListErroCode === 0 || reqListErroCode === 404" class="list-container">
        <div class="top-wrapper">
          <!-- 搜索 -->
          <div class="search-container">
            <el-input
              id="ui-ztp-resource-input-search_keyword"
              v-model="searchVal"
              :placeholder="$t('sourcePlatform.searchTip')"
              prefix-icon="iconfont icon-sousuo2"
              size="small"
              @input="searchHandle"
            />
          </div>
          <div class="filte-container">
            <el-select id="ui-ztp-resource-select-search_choice" v-model="filterVal" :popper-append-to-body="false" size="small" placeholder="请选择" @change="filterListHandle">
              <el-option
                v-for="item in filterList"
                :id="`ui-ztp-resource-li-${item.id}`"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </div>
          <div v-if="showDivide" class="filte-container">
            <div class="net-name">
              <img :src="selectedNet.Icon" alt="">
              <span :title="selectedNet.DivideName" class="text-clamp">{{ selectedNet.DivideName }}</span>
            </div>
          </div>
          <div class="right-wrapper">
            <div v-if="showProxyLink" class="proxy-link-wrapper">
              <el-switch
                id="ui-ztp-resource-switch-proxyFlag"
                v-model="openProxy"
                :width="32"
                :disabled="changingMode"
                class="source-custom-switch"
                :inactive-text="$t('sourcePlatform.proxyLink')"
                @change="proxyFlagChange"
              />
            </div>
            <i v-if="showZtpSet" id="ui-ztp-resource-i-openSet" class="iconfont icon-peizhishichang" @click="openProxySet" />
            <i id="ui-ztp-resource-div-refresh" :class="['iconfont', 'icon-CheckWSUS', iconLoading?'icon-fresh': '']" @click="fresh" />
          </div>
        </div>
        <div class="s-list-content">
          <!-- 无资源 -->
          <as-emptyCom v-if="isEmpty" :msg="reqSearchVal ? $t('sourcePlatform.searchNull'):''" class="s-platform-empty" />

          <!-- 接口失败资源 -->
          <as-emptyCom v-if="reqListErroCode === 404" class="s-platform-empty">
            <p class="api-fail-tip" @click="tryFresh" v-html="$t('sourcePlatform.listApiFail')" />
          </as-emptyCom>

          <div v-for="(group, index) in resourceList" :key="group.GroupName" class="source-group-wrapper">
            <group-title :title="group.GroupName" :class="{'first-title':index===0}" />
            <div class="group-list-container">
              <source-item v-for="item in group.Data" :key="item.ResID" :group-res-id="group.GroupResId" :url-prefix="urlPrefix" :data-item="item" :open-proxy="openProxy" :ztp-user-exceed="ztpUserExceed" :is-internal="isInternal" @childEvent="childEventHandle" />
            </div>
          </div>
        </div>

      </div>

      <!-- 未登录、登录过期 -->
      <layout-state
        v-if="showNotLogin"
        :state-img="serverNotLogin"
        :state-msg="stateMsg.stateText"
        :state-btn-txt="stateMsg.btnText"
        state-id="ui-sourcePlatform-list-div-network_msg"
        state-btn-id="ui-sourcePlatform-list-button-enter_network"
        @stateBtnHandle="gotoAuth"
      />
    </template>

    <!-- 编辑账户 -->
    <edit-account v-model="showEditAccount" :edit-account-data="changePassData" />

    <!-- 强化认证 -->
    <strengthen-auth v-model="showStrenAuth" :auth-data="authData" @success="authSuccess" />

    <!-- 文件资源认证 -->
    <file-res-auth v-model="showFileAuth" :file-data="fileData" />

    <!-- 申请资源 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawer"
      :append-to-body="true"
      :size="drawerWidth"
      :wrapper-closable="true"
      :destroy-on-close="true"
      @close="closeDrawer"
    >
      <component :is="drawerType" :info="resData" :msg="detailMsg" @changeVisible="changeVisible" />
    </el-drawer>

    <!-- 自定义资源访问出错提示/操作框 -->
    <verticalDialog
      :show="showDialog"
      :show-close="true"
      :show-foot="true"
      width="384px"
      class="custom-res-confirm-dialog"
      :cancel-text="cancelText"
      :confirm-text="confirmText"
      pop-name="custom-opt"
      :title="$t('tips')"
      @cancel="noHandle"
      @confirm="okHandle"
      @beforeClose="showDialog = false"
    >
      <div class="custom-content">
        <i class="iconfont icon-putongxiangbuhegui" />
        <div class="right-wrapper">
          <p class="c-title">{{ tipTitle }}</p>
          <p v-for="(item,index) in subTip" :key="index" class="s-title">{{ item }}</p>
        </div>
      </div>
    </verticalDialog>
  </div>
</template>
<script>
import emptyCom from './components/empty.vue'
import sourceItem from './components/sourceItem.vue'
import groupTitle from './components/groupTitle.vue'
import sourceListUtil from '@/render/utils/bussiness/sourceListUtil.js'
import strengthenAuth from './components/strengthenAuth'
import state from '@/render/components/layout/state'
import editAccount from './components/editAccount.vue'
import agentApi from '@/service/api/agentApi'
import { mapGetters, mapMutations } from 'vuex'
import authIndex from '@/render/utils/auth/index'
import processController from '@/render/utils/processController'
import commonUtil from '@/render/utils/bussiness/commonUtil'
import proxyApi from '@/service/api/proxyApi'
import applyRes from './components/applyRes.vue'
import msgDetail from './components/msgDetail.vue'
import ipsourceDetail from './components/ipsourceDetail'
import customSourceDetail from './components/customResDetail.vue'
import { EventBus } from '@/render/eventBus'
import os_browser_info from '@/render/utils/os_browser_info'
import proxySet from './components/proxySet.vue'
import fileResAuth from './components/fileResAuth.vue'
import dateHelper from '@/render/utils/date/dateUtil'
const serverNotLogin = require('@/render/assets/sourcePlatform/notLogin.png')
const serverNotWorking = require('@/render/assets/serverNotWorking.png')
const netImg = require('@/render/assets/net.png')

export default {
  components: {
    'as-emptyCom': emptyCom,
    'source-item': sourceItem,
    'group-title': groupTitle,
    'layout-state': state,
    'edit-account': editAccount,
    'strengthen-auth': strengthenAuth,
    'layoutState': state,
    'apply-res': applyRes,
    'msg-detail': msgDetail,
    'source-detail': ipsourceDetail,
    'proxy-set': proxySet,
    'file-res-auth': fileResAuth,
    'custom-res-detail': customSourceDetail
  },
  data() {
    return {
      resourceList: [],
      backUpResourceList: [],
      changePassVisible: false,
      sourceData: {},
      inited: false,
      authVisible: false,
      authData: {},
      reqListErroCode: 0,
      serverNotLogin,
      showEditAccount: false,
      changePassData: {},
      showStrenAuth: false,
      serverNotWorking,
      urlPrefix: '',
      searchVal: '',
      reqSearchVal: '',
      loading: false,
      timer: null,
      filterVal: '',
      filterList: [],
      iconLoading: false,
      initLoading: true,
      drawer: false,
      resData: '',
      drawerWidth: 450,
      drawerType: 'apply-res',
      detailMsg: '',
      detailMsgMap: {},
      ztpUserExceed: true,
      isInternal: 0,
      divideID: '',
      showDivide: false,
      divideList: [],
      netImg,
      openProxy: true,
      VPNInterceptMode: 1,
      changingMode: false,
      showFileAuth: false,
      fileData: {},
      showDialog: false,
      confirmText: '',
      cancelText: '',
      tipTitle: '',
      subTip: [],
      customInfo: {},
      cancelLoading: false,
      failCustomParams: {},
      realTimeGataway: [],
      unwatch: null, // 存储监听函数
      timeoutId: null// 存储定时器ID
    }
  },
  computed: {
    ...mapGetters([
      'clientInfo',
      'authInfo',
      'serveEntiretyConfig',
      'computeNetAccessStatus',
      'gateWayInfos'
    ]),
    isProxy() {
      return _.get(this.serveEntiretyConfig, 'server.ZTP.VpnType') === 'proxy'
    },
    /**
     * 是否802.1x认证
     */
    isDot1xMode() {
      return authIndex.isDot1xMode()
    },
    // 是否是敲端口模式
    isKnockPort() {
      return _.get(this.clientInfo, 'webSlot.isKnockPort', false)
    },
    stateMsg() {
      const msg = {
        btnText: '',
        stateText: ''
      }
      if (this.reqListErroCode === 21120054) {
        msg.stateText = this.$t('sourcePlatform.notLogin')
        msg.btnText = this.$t('networkStatus.info_3')
      }
      if (this.reqListErroCode === 21120055) {
        msg.stateText = this.$t('sourcePlatform.loginExpeir')
        msg.btnText = this.$t('networkStatus.info_7')
      }
      return msg
    },
    isEmpty() {
      return (this.inited && this.resourceList.length === 0 && this.reqListErroCode === 0) || this.reqListErroCode === 21120061
    },
    isOnline() {
      return this.clientInfo.online
    },
    drawerTitle() {
      const titleMap = {
        'apply-res': this.$t('applyRes.add'),
        'source-detail': this.$t('sourcePlatform.seeDetail'),
        'msg-detail': this.$t('sourcePlatform.seeDetail'),
        'proxy-set': this.$t('sourcePlatform.ZTPSet')
      }
      return titleMap[this.drawerType] || this.$t('sourcePlatform.seeDetail')
    },
    // 是否需要切换网络
    netIsoLate() {
      const osType = os_browser_info.os_type || ''
      return parseInt(_.get(this.serveEntiretyConfig, 'server.ZTP.DivideGroup')) === 1 && osType !== 'mac'
    },
    // 当前网络名称
    selectedNet() {
      const net = this.divideList.filter(item => item.DivideID === this.divideID)
      let info = {}
      if (net.length) {
        info = { ...net[0] }
        info.Icon = info.Icon ? this.urlPrefix + info.Icon : this.netImg
      }
      return info
    },
    showZtpSet() {
      return os_browser_info.os_type === 'windows' && this.isProxy
    },
    showProxyLink() {
      let haveIp = false
      for (let i = 0; i < this.backUpResourceList.length; i++) {
        const data = this.backUpResourceList[i].Data
        if (data && data.length && this.backUpResourceList[i].GroupResId !== 'applyRes') {
          const ips = data.filter(item => item.isIP)
          if (ips.length > 0) {
            haveIp = true
            break
          }
        }
      }
      return haveIp && _.get(this.serveEntiretyConfig, 'server.ZTP.VpnType') === 'proxy'
    },
    userId() {
      return _.get(this.clientInfo, 'accessStatus.UserID') || _.get(this.authInfo, 'basic.ID')
    },
    showNotLogin() {
      return this.reqListErroCode !== 0 && this.reqListErroCode !== 21120061 && this.reqListErroCode !== 404
    }
  },
  watch: {
    '$i18n.locale': function() {
      this.searchVal = ''
      this.reqSearchVal = ''
      this.filterVal = 'all'
      this.init()
    },
    'isOnline': function(newVal, oldVal) {
      console.log(newVal, oldVal)
      if (newVal && this.reqListErroCode === 21120054) {
        this.initLoading = true
        this.inited = false
        this.init()
        this.updateNetStatus()
      }
    }
  },
  beforeDestroy() {
    this.clearTimer()
    EventBus.$off('ChangeResGroup')
    EventBus.$off('receiveGateway')
    this.closeLoading()
    this.cleanup()
  },
  mounted() {
    this.init().then(() => {
      this.browserOpenRes() // url带了资源id是浏览器过来的需要打开资源
    })
    this.listenEventBus()
    this.updateMsgInfo()
  },
  methods: {
    ...mapMutations(['setMsgCenter', 'setGateInfos']),
    async init() {
      const isOutLine = this.outLineHandle() // 离线状态处理
      if (isOutLine) {
        return
      }
      if (_.get(this.clientInfo, 'accessStatus.deviceStatus') === undefined) { // 从浏览器直接跳客户端资源平台
        await commonUtil.netRet()
      }
      if (parseInt(_.get(this.clientInfo, 'accessStatus.deviceStatus')) !== 1) { // 未入网跳回立即入网
        processController.set('/access/message')
        return
      }
      await this.getSourceList({ 'handleLoginDate': false }, 'init')
      if (this.netIsoLate) {
        this.updateDividInfo()
      }
      if (this.isProxy) {
        await commonUtil.getWebGateWay()
        this.openProxy = parseInt(_.get(this.gateWayInfos, 'VPNStatus')) === 1
      }
    },
    fresh() {
      if (this.iconLoading) {
        return
      }
      this.iconLoading = true
      this.searchVal = ''
      this.reqSearchVal = ''
      this.filterVal = 'all'
      const isOutLine = this.outLineHandle() // 离线状态处理
      if (isOutLine) {
        return
      }
      if (this.isProxy) {
        commonUtil.getWebGateWay()
        this.openProxy = parseInt(_.get(this.gateWayInfos, 'VPNStatus')) === 1
      }
      agentApi.refreshVPNPolicy()
      const delay = ms => new Promise((resolve, _) => setTimeout(resolve, ms)) // 防止接口返回太快，频繁刷新，限制300ms
      Promise.all([sourceListUtil.getSourceInfo({ queryStr: '' }, { 'handleLoginDate': false }), delay(300), commonUtil.server()]).then(rets => {
        this.iconLoading = false
        this.setStatus(rets[0])
        this.sourceListHandle(rets[0], '')
        if (this.netIsoLate) {
          this.updateDividInfo()
        }
      })
    },
    // 离线处理
    outLineHandle() {
      if (!_.get(this.clientInfo, 'online', false)) {
        this.reqListErroCode = 21120054
        this.inited = true
        this.iconLoading = false
        this.initLoading = false
        return true
      }
      return false
    },
    updateNetStatus() {
      commonUtil.netRet()
    },
    // 判断连接数是否达到上限
    setStatus(ret) {
      const errorCode = parseInt(_.get(ret, 'errcode'))
      if (errorCode === 21105003 || errorCode === 21104019 || errorCode === 21104020) {
        return
      }
      if (this.isAccess()) {
        const ztpUserExceed = _.get(ret, 'data.ztpUserExceed', true)
        // 是否是内网环境
        this.isInternal = parseInt(_.get(ret, 'data.IsInternal'))
        this.ztpUserExceed = ztpUserExceed
        if (!ztpUserExceed) {
          this.$message.error(this.$t('sourcePlatform.ztpUserExceed'))
        }
      }
    },
    // 是否已入网
    isAccess() {
      return !!_.get(this.computeNetAccessStatus(), 'isAccess', false)
    },
    async getSourceList(config = {}, type) {
      const searchVal = this.searchVal
      const ret = await sourceListUtil.getSourceInfo({ queryStr: this.searchVal }, config)
      if (type === 'init') {
        this.setStatus(ret)
      }
      this.sourceListHandle(ret, searchVal)
    },
    sourceListHandle(ret, searchVal) {
      this.initLoading = false
      const errorCode = parseInt(_.get(ret, 'errcode'))
      if (errorCode === 21105003 || errorCode === 21104019 || errorCode === 21104020) {
        processController.set('/access/message')
        return
      }
      if (errorCode === 404) {
        this.reqListErroCode = 404
        return
      }

      this.backUpResourceList = ret.list
      if (!searchVal) {
        this.updateFilter(ret.list)
      }
      this.filterListHandle()

      this.urlPrefix = _.get(ret, 'data.urlPrefix')
      this.reqListErroCode = parseInt(ret.errcode)
      this.inited = true
    },
    updateFilter(list) {
      const filterList = list.reduce((arr, item) => {
        arr.push({ label: item.GroupName, id: item.GroupResId })
        return arr
      }, [{ label: this.$t('sourcePlatform.all'), id: 'all' }])
      this.filterList = filterList
      if (!this.filterVal) {
        this.filterVal = 'all'
      }
    },
    filterListHandle(val) {
      if (val) {
        this.filterVal = val
      }
      if (this.filterVal === 'all') {
        this.resourceList = [...this.backUpResourceList]
      } else {
        this.resourceList = this.backUpResourceList.filter(item => item.GroupResId === this.filterVal)
      }
    },
    async childEventHandle(data) {
      switch (data.type) {
        case 'freshEvent':
          this.getSourceList()
          break
        case 'editAccountEvent':
          this.editAccountHandle(data.data)
          break
        case 'openUrlEvent':
          this.openUrlHandle(data.data)
          break
        case 'opentacticErr':
          this.detailMsgMap[_.get(data, 'data.ResID') + ''] = _.get(data, 'data.Reason')
          this.openMsg(_.get(data, 'data.ResID') + '')
          break
        case 'applyRes':
          this.drawerType = 'apply-res'
          this.drawerWidth = 450
          this.drawer = true
          this.resData = data.data
          break
        case 'IPSourceDetail':
          if (_.get(data, 'data.IsInternet') !== '0') {
            this.drawerType = 'source-detail'
            this.drawer = true
            this.drawerWidth = 450
            this.detailMsg = data.data
          }
          break
        case 'customResEvent':
          this.customEventHandle(data.data)
          break
        default:
      }
    },
    async editAccountHandle(sourceData) {
      const ret = await sourceListUtil.getAtpAccount(sourceData)
      if (parseInt(ret.errcode) === 0) {
        this.showEditAccount = true
        this.changePassData = { ...sourceData, ...ret.data }
      }
    },
    // 用浏览器打开或跳转应用
    async openUrlHandle(sourceItem) {
      if (sourceItem.IsInternet !== '0') {
        this.loading = this.$loading({
          lock: true,
          target: '#layoutMain'
        })
        if (this.netIsoLate) { // 需要切换网络
          const id = await this.changeDivide(sourceItem.ResID)
          if (id) {
            const isOk = await this.gatewayIsUseful(id)
            if (!isOk) {
              const isChangeSuccess = await this.listenGatawayChangeOk(id)
              if (!isChangeSuccess) {
                this.$message.warning(this.$t('sourcePlatform.isolationChangeFail'))
                this.closeLoading()
                return
              }
            }
          }
        }

        // 如果是在windows端运行的独立应用，则在windows打开
        let isOpenSuccess
        if (this.isWindowsRunRemoteApp(sourceItem)) {
          isOpenSuccess = await this.runWindowsRemoteApp(sourceItem)
        } else {
          isOpenSuccess = await this.routerTo(sourceItem)
        }
        this.closeLoading()
        isOpenSuccess && this.recodLogHandle(sourceItem.ResID)
      }
    },
    openMsg(id) {
      const h = this.$createElement
      this.$message({
        type: 'error',
        customClass: 'custom-offset-msg',
        message: h(
          'span',
          { style: 'color:#3C404D; ' },
          [
            this.$t('sourcePlatform.sourceFail'),
            h(
              'span',
              {
                style: 'cursor:pointer;color:#536CE6',
                on: {
                  click: this.openErrDetail(id)
                }
              },
              this.$t('sourcePlatform.seeDetail')
            )
          ]
        )
      })
    },

    openErrDetail(id) {
      return function() {
        this.drawerType = 'msg-detail'
        this.detailMsg = this.detailMsgMap[id]
        this.drawerWidth = 450
        this.drawer = true
      }.bind(this)
    },
    // 独立运行windows端远程应用
    async runWindowsRemoteApp(data) {
      // 查询远程应用的账号和密码
      const ret = await proxyApi.getZtpResourceInfo({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        resId: _.get(data, 'ResID')
      })
      const errcode = parseInt(_.get(ret, 'errcode', 200))

      if (errcode === 21148004) { // 附加认证
        this.openAddAuthModal(data, ret.data)
        return false
      }

      if (errcode !== 0 || _.isEmpty(_.get(ret, 'data'))) {
        const errmsg = _.get(ret, 'errmsg') || this.$t('serverError')
        this.$message.error(errmsg)
        return
      }

      const params = { ...ret.data, ...data }

      return await this.openRDC(params)
    },
    async openRDC(params) {
      // 调用客户端的接口打开远程应用
      const config = {
        ServerIP: _.get(params, 'remoteSerIP'),
        ServerPort: _.get(params, 'remoteSerPort'),
        RemoteAppPath: _.get(params, 'remoteAppPath'),
        TrueName: '盈高科技',
        ResID: _.get(params, 'ResID'),
        ResName: _.get(params, 'ResName'),
        UserName: _.get(params, 'remoteUserName'),
        UserPSW: _.get(params, 'remoteUserPwd'),
        RedirectPrinters: {
          'Enable': _.get(params, 'redirectPrinters', false)
        },
        RedirectClipboard: {
          'Enable': _.get(params, 'remoteCopy', false)
        },
        RedirectDrives: {
          'Enable': _.get(params, 'redirectDrives', false),
          'Driver': ''
        },
        RedirectPorts: {
          'Enable': _.get(params, 'redirectPorts', false)
        },
        WaterMark: {
          'Enable': false,
          'ShowInfo': ''
        }
      }
      // 水印
      if (_.get(params, 'watermark.switch', false)) {
        config['WaterMark'] = {
          'Enable': true,
          'ShowInfo': _.get(params, 'watermark.info.username') + '/' + _.get(params, 'watermark.info.ip') + '/' + _.get(params, 'watermark.info.content')
        }
      }
      console.log(_.clone(config))
      const ret = await agentApi.openRDC(config)
      if (_.get(ret, 'ASM.Result') === 'false' && _.get(ret, 'ASM.ResultMsg')) {
        this.$message({
          message: _.get(ret, 'ASM.ResultMsg'),
          type: 'error'
        })
      }
      console.log(ret)
    },
    // 是windows端独立运行的app
    isWindowsRunRemoteApp(data) {
      if (parseInt(_.get(data, 'ResType')) === 3 && parseInt(_.get(data, 'ActionType')) === 0) {
        return true
      }
      return false
    },
    async policyCheckHandle(sourceItem) {
      const ret = await sourceListUtil.policyCheck(sourceItem.ResID)
      console.log(ret)
    },
    // 强化认证成功
    async authSuccess() {
      const sourceItem = this.authData
      this.showStrenAuth = false
      this.$message({ message: this.$t('sourcePlatform.appendAuthTitle') + this.$t('sourcePlatform.success'), type: 'success' })
      if (sourceItem.isIP) {
        this.getSourceList()
        return
      }
      let isOpenSuccess
      if (this.isWindowsRunRemoteApp(sourceItem)) {
        isOpenSuccess = await this.runWindowsRemoteApp(sourceItem)
      } else {
        isOpenSuccess = await this.routerTo(sourceItem)
      }
      isOpenSuccess && this.recodLog(sourceItem.ResID)
    },
    openAddAuthModal(sourceItem, authParams) {
      const authData = { ...sourceItem, ...authParams }
      authData.authFrom = 'addition'
      this.authData = authData
      this.showStrenAuth = true
    },
    async routerTo(sourceItem) {
      if (!this.loading) {
        this.loading = this.$loading({
          lock: true,
          target: '#layoutMain'
        })
      }
      const id = sourceItem.ResID
      const ret = await proxyApi.getZtpResourceInfo({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        resId: id
      })
      const errcode = parseInt(ret.errcode)
      if (errcode === 0) {
        if (parseInt(sourceItem.AccessTypeID) === 312) { // 自定义应用
          await this.openCustomApp(sourceItem, ret.data)
        } else if (ret.data.GrantType && ret.data.DocumentServerUrl) { // 文件服务器资源
          await this.openFileSource(ret.data, sourceItem) // 打开文件资源
        } else {
          if (_.get(ret, 'data.accessUrl')) {
            const res = await agentApi.windowOpenUrl(ret.data.accessUrl, -1)
            if (res.ASM.ErrMsg) {
              this.$message.error(res.ASM.ErrMsg)
            }
          } else if (sourceItem.isIP) {
            this.drawerType = 'source-detail'
            this.drawer = true
            this.detailMsg = sourceItem
            this.closeLoading()
            return false
          }
        }
        this.closeLoading()
        return true
      }
      this.closeLoading()
      if (errcode === 21148004) { // 附加认证
        this.openAddAuthModal(sourceItem, ret.data)
        return false
      }
      if (errcode !== 0 || _.isEmpty(_.get(ret, 'data'))) {
        if (errcode === 21148068) {
          this.openMsg(id + '')
          this.detailMsgMap[id + ''] = ret.errmsg
          return false
        }
        const errmsg = _.get(ret, 'errmsg') || this.$t('serverError')
        this.$message.error(errmsg)
        return false
      }
      return false
    },
    // 浏览器打开指定资源
    browserOpenRes() {
      const resId = this.$route.query.ResID
      if (!resId) {
        return
      }
      let resSourceInfo = null
      for (let i = 0; i < this.backUpResourceList.length; i++) {
        const data = this.backUpResourceList[i].Data
        if (data && data.length) {
          const arr = data.filter(item => item.ResID === resId)
          if (arr.length > 0) {
            resSourceInfo = arr[0]
            break
          }
        }
      }
      if (resSourceInfo) {
        this.openUrlHandle(resSourceInfo) // 这里不做判断了，在浏览器做了
      }
    },
    // 能否直接打开FTP
    isCanOpenFTP(accesInfo) {
      const { GrantType, Account, Alias } = accesInfo
      return GrantType === 'FTP' && Account && Alias
    },
    // 能否直接打开SMB资源
    isCanOpenSMB(accesInfo) {
      const { GrantType, Driver } = accesInfo
      return GrantType === 'SMB' && Driver
    },
    async openFileSource(accesInfo = {}, itemInfo) {
      if (this.isCanOpenFTP(accesInfo) || this.isCanOpenSMB(accesInfo)) { // 能否直接打开资源
        const { GrantType, Password, Account, Driver, DocumentServerUrl, Alias } = accesInfo
        const params = {
          File: GrantType,
          Params: DocumentServerUrl,
          ShowType: '0',
          ResType: '1',
          NeedWait: 0,
          InfosFrom: '1',
          UserName: Account,
          MountDriverLetter: GrantType === 'SMB' ? Driver : Alias
        }
        if (GrantType === 'SMB') {
          params.UserPSW = Password
        }
        const ret = await agentApi.windowFileServeOpenUrl(params)
        if (parseInt(_.get(ret, 'ASM.ErrCode')) !== 0) {
          if (parseInt(_.get(ret, 'ASM.PromptPSW')) === 1) { // 打开弹框认证
            this.fileData = { ...itemInfo, ...{ GrantType: accesInfo.GrantType, DocumentServerUrl: accesInfo.DocumentServerUrl }}
            this.showFileAuth = true
          } else {
            this.$message.error(ret.ASM.ErrMsg)
          }
        }
      } else {
        this.fileData = { ...itemInfo, ...{ GrantType: accesInfo.GrantType, DocumentServerUrl: accesInfo.DocumentServerUrl }}
        this.showFileAuth = true
      }
    },
    async recodLogHandle(id) {
      const ret = await sourceListUtil.recodLog(id)
      if (parseInt(ret.errcode) === 0) {
        this.getSourceList()
        return
      }
    },
    gotoAuth() {
      this.$router.push({ path: '/access/auth' })
    },
    /**
     * 刷新整个页面，依旧在认证页
     */
    refresh() {
      window.location.reload()
    },
    searchHandle() {
      this.clearTimer()
      this.timer = setTimeout(async() => {
        await this.getSourceList()
        this.reqSearchVal = this.searchVal
      }, 500)
    },
    clearTimer() {
      if (this.timer !== null) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    closeDrawer() {

    },
    changeVisible(isClose) {
      console.log('guanbi ', isClose)
      this.drawer = isClose
    },
    async updateMsgInfo() {
      if (!_.get(this.clientInfo, 'online', false)) {
        return
      }
      const ret = await proxyApi.getResMsg({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        start: 0,
        limit: 1
      }, { showError: false, handleLoginDate: false })
      this.setMsgCenter({ ...this.msgCenter, ...{ unRead: _.get(ret, 'data.unReadCount', 0) }})
    },
    // 尝试切换网关
    async changeDivide(resID) {
      const ret = await proxyApi.ztpDivideChange({ resID, deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0) })
      // 切换成功失败都调一下并通知客户端只是更新数据的关键
      this.updateDividInfo()
      return _.get(ret, 'data.CurrDivideID', '')
    },
    async updateDividInfo() {
      const { list, curId, UrlPrefix } = await sourceListUtil.getDivideList()
      if (_.isArray(list)) {
        this.divideList = list
        this.$nextTick(() => {
          this.divideID = curId
          this.showDivide = true
        })
      }
      sourceListUtil.setNotifyResGroup({ list, curId, UrlPrefix })
    },
    listenEventBus() {
      EventBus.$on('ChangeResGroup', () => {
        this.getSourceList()
        this.updateDividInfo()
      })
      EventBus.$on('receiveGateway', (val) => {
        this.realTimeGataway = val
      })
    },
    async openProxySet() {
      this.drawerType = 'proxy-set'
      this.drawer = true
    },
    async proxyFlagChange(val) {
      if (this.changingMode) {
        return
      }
      this.changingMode = true
      const ret = await agentApi.setVPNStatus({ VPNStatus: val ? '1' : '0' })
      this.changingMode = false
      if (parseInt(_.get(ret, 'ASM.ErrCode', -1)) === 0) {
        const newVal = { ...this.gateWayInfos, VPNStatus: val ? 1 : 0 }
        this.setGateInfos(newVal)
      } else {
        this.openProxy = !val
        this.$message({
          message: _.get(ret, 'ASM.ErrorMsg'),
          type: 'error'
        })
      }
    },
    // 打开自定义应用资源
    async openCustomApp(sourceItem, accessInfo) {
      if (_.get(accessInfo, 'AccessMode', 'script') === 'browser') { // 浏览器模式
        const res = await agentApi.windowOpenUrl(accessInfo.accessUrl, -1)
        if (res.ASM.ErrMsg) {
          this.$message.error(res.ASM.ErrMsg)
        }
      } else {
        const isPathMode = _.get(accessInfo, 'AccessMode', 'script') === 'path'
        let params = { // 脚本模式
          File: accessInfo.GrantType,
          Params: accessInfo.Content,
          ResType: 1
        }
        if (isPathMode) { // 指定程序模式
          const pathModeParams = await this.getPathModeParams(accessInfo, sourceItem)
          params = pathModeParams
        }
        const res = await agentApi.windowCustomAppOpenUrl(params)
        if (parseInt(_.get(res, 'ASM.ErrCode')) !== 0) { // 返回失败
          if (_.get(accessInfo, 'AccessMode', 'script') === 'script') {
            this.$message.error(res.ASM.ErrMsg)
          } else {
            const tips = accessInfo.NotFindTip.split('\r\n')
            this.cancelText = this.$t('sourcePlatform.installed')
            if (parseInt(accessInfo.AllowSelfCfg) === 1) {
              this.confirmText = this.$t('sourcePlatform.selectOther')
            } else {
              this.confirmText = this.$t('sourcePlatform.ikonw')
            }
            this.tipTitle = tips[0]
            if (tips.length > 1) {
              tips.shift()
              this.subTip = tips
            } else {
              this.subTip = []
            }

            this.customInfo = { ...sourceItem, ...accessInfo }
            this.failCustomParams = params
            this.showDialog = true
          }
        }
      }
    },
    async getPathModeParams(accessInfo, sourceItem) {
      let params = {
        FileName: accessInfo.ProcessByName,
        File: accessInfo.ProcessByPath,
        Params: accessInfo.RunArgs,
        ResType: 2,
        ShowType: 1
      }
      const pathKey = `customConf.${sourceItem.ResID}-${this.userId}`
      const customInfo = await sourceListUtil.readResConfFromClientLocal(pathKey)
      if (customInfo && typeof customInfo === 'object') {
        params = {
          FileName: '',
          File: customInfo.path,
          Params: '',
          ResType: 2,
          ShowType: 1
        }
      }
      return params
    },
    async noHandle() {
      if (this.cancelLoading) {
        return
      }
      this.cancelLoading = true
      const res = await agentApi.windowCustomAppOpenUrl(this.failCustomParams)
      this.cancelLoading = false
      if (parseInt(_.get(res, 'ASM.ErrCode')) !== 0) {
        this.$message.error(res.ASM.ErrMsg)
      } else {
        this.showDialog = false
      }
    },
    async okHandle() {
      if (parseInt(this.customInfo.AllowSelfCfg) === 1) {
        const params = {
          FileName: '',
          File: '',
          Params: '',
          ResType: 2
        }
        const res = await agentApi.windowCustomAppOpenUrl(params) // 选择软件
        if (parseInt(_.get(res, 'ASM.ErrCode')) === 0 && _.get(res, 'ASM.OpenedFile')) {
          const path = _.get(res, 'ASM.OpenedFile')
          this.showDialog = false
          agentApi.windowCustomAppOpenUrl({ // 打开软件
            FileName: '',
            File: path,
            Params: '',
            ResType: 2
          })
          const data = {}
          data[this.customInfo.ResID + '-' + this.userId] = { path, time: dateHelper.timeStampToDate(new Date().getTime()) }
          console.log('数据', data)
          sourceListUtil.saveResConfToClientLocal('customConf', data)
        }
      } else {
        this.showDialog = false
      }
    },
    async customEventHandle(data) {
      const ret = await this.getAccessInfo(_.get(data, 'ResID'))
      if (ret) {
        this.drawerType = 'custom-res-detail'
        this.drawer = true
        this.drawerWidth = 370
        this.resData = { ...data, ...ret }
      }
    },
    async getAccessInfo(id) {
      const ret = await proxyApi.getZtpResourceInfo({
        deviceid: _.get(this.clientInfo, 'detail.DeviceID'),
        resId: id
      })
      const errcode = parseInt(ret.errcode)
      if (errcode === 0) {
        return ret.data
      }
      this.$message.error(_.get(ret, 'errmsg'))
      return false
    },
    tryFresh(event) {
      if (event === 'pass' || event.target.nodeName === 'SPAN') {
        this.fresh()
      }
    },
    // 监听隔离域切换成功
    async listenGatawayChangeOk(id) {
      return new Promise((resolve, reject) => {
        // 清理之前的监听和定时器（防止重复调用）
        this.cleanup()
        // 监听数据变化（成功条件）
        this.unwatch = this.$watch('realTimeGataway', async(newVal, oldVal) => {
          const checkRet = await this.gatewayIsUseful(id)
          if (checkRet) {
            this.cleanup()
            resolve(true) // 返回成功信号
          }
        })
        const waitTime = _.get(this.serveEntiretyConfig, 'server.VpnControl.VpnWaitTime', 10) * 1000
        // 设置超时（失败条件）
        this.timeoutId = setTimeout(() => {
          this.cleanup()
          resolve(false) // 返回失败信号
        }, waitTime)
      })
    },
    // 资源管理的网关是否有可用的
    async gatewayIsUseful(id) {
      console.log('触发校验', id, this.realTimeGataway)
      if (!this.realTimeGataway || !this.realTimeGataway.length) {
        return false
      }
      const normalItems = this.realTimeGataway.filter(item => parseInt(item.usable) === 1)
      if (!normalItems.length) {
        return false
      }
      const ids = normalItems.map(item => item.gwid)
      const ret = await proxyApi.ztpDivideCheckStatus({ divideID: id, deviceid: _.get(this.clientInfo, 'detail.DeviceID'), gwIds: ids.join(',') })
      return parseInt(parseInt(_.get(ret, 'errcode', -1))) === 0
    },
    // 清理资源
    cleanup() {
      if (this.unwatch) {
        this.unwatch()
        this.unwatch = null
      }
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
        this.timeoutId = null
      }
    },
    closeLoading() {
      if (this.loading) {
        this.loading.close()
        this.loading = null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.source-list-page{
  height: 100%;
  padding-top: 24px;
  .list-container{
    padding-top: 48px;
    position: relative;
    height: 100%;
    .top-wrapper{
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      ::v-deep .search-container{
        display: flex;
        align-items: center;
        padding-bottom: 16px;
        margin-left: 24px;
        .el-input{
          width: 180px;
          margin-right: 16px;
        }
        input{
          font-size: 14px;
          border-color: $gray-2;
        }
        input::-webkit-input-placeholder{
          font-size: 14px;
        }
        .el-input__prefix{
          margin-left: 3px;
          i{
            font-size: 16px;
          }
        }
      }
      ::v-deep .filte-container{
        width: 160px;
        margin-right: 16px;
        .el-select-dropdown__item{
          line-height: 32px;
          height: 32px;
        }
        .el-input__inner{
          border-color: $gray-2;
          font-size: 14px;
        }
        .net-name{
          display: flex;
          align-items: center;
          height: 32px;
          color: $disabled-color;
          .text-clamp{
            width: 80px;
          }
          img{
            max-height: 18px;
            max-width: 18px;
            margin-right: 6px;
          }
        }

      }
      .right-wrapper{
        position: absolute;
        line-height: 32px;
        top: 0;
        right: 16px;
        display: flex;
        .proxy-link-wrapper{
          margin-right: 8px;
          display: flex;
          align-items: center;
        }
        .iconfont{
          cursor: pointer;
          color: $disabled-color;
          padding: 0 4px;
          margin-left: 4px;
        }
      }
      .icon-fresh{
        animation: iconRotate 1s linear infinite;
        -webkit-animation-fill-mode:forwards;
        animation-fill-mode:forwards;
      }
      @keyframes iconRotate {
        0% {
          transform: rotate(0);
        }
        50% {
          transform: rotate(180deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    }
    .s-list-content{
      height: 100%;
      padding: 0 16px;
      overflow: auto;
    }
    .group-list-container{
      display: flex;
      flex-wrap: wrap;
    }
  }
  .s-platform-empty{
    height: calc(100% - 56px);
  }
  ::v-deep .api-fail-tip{
    color: $default-color;
    span{
      color: $--color-primary;
      cursor: pointer;
    }
  }
}
.source-group-wrapper{
  margin-bottom: 16px;
}
.custom-content{
  display: flex;
  padding: 24px 20px;
  i{
    font-size: 24px;
    color: $yellow;
  }
  .right-wrapper{
    padding-left: 10px;
    flex: 1;
    .c-title{
      line-height: 16px;
      margin-bottom: 4px;
    }
    .s-title{
      font-size: 12px;
      color: $default-color
    }
  }
}
</style>
<style lang="scss">
.to-tactic-err-detail{
  color: $--color-primary;
  cursor: pointer;
}
.custom-offset-msg{
  margin-top: 45px;
}
.source-custom-switch{
  .el-switch__core{
     height: 16px;
     &::after{
       width: 12px;
       height: 12px;
     }
  }
  .el-switch__label.is-active{
    color: $title-color;
  }
  .el-switch__label--left{
    margin-right: 0;
    user-select: none;
  }
}
.source-custom-switch.is-checked .el-switch__core::after{
    margin-left: -13px;
}
.custom-res-confirm-dialog{
  .d-dialog-foot-wrapper{
    .btn{
      color: $default-color;
      &:hover{
        color: white;
      }
    }
  }
}
</style>
