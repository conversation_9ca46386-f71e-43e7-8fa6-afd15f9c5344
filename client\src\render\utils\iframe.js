/*
 * @Author: <EMAIL>
 * @Date: 2022-04-21 16:35:06
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-21 16:38:12
 * @Description: file content
 */
import $ from 'jquery'

export function destroyIframe(iframeID) {
  const iframe = $('#' + iframeID).prop('contentWindow')

  $('#' + iframeID).attr('src', 'about:blank')

  try {
    iframe.document.write('')
    iframe.document.clear()
  } catch (e) {
    console.log(e)
  }

  // 把iframe从页面移除
  $('#' + iframeID).remove()
}
