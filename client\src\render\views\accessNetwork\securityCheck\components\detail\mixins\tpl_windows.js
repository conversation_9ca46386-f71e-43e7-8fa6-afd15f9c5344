
import agentApi from '@/service/api/agentApi'
const tplMixins = {
  mounted() {
    this.initFixed()
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    initFixed() {
      if (this.checkData) {
        this.checkData.hasFixed = false
      }
    },
    // 打开浏览器指定地址
    openUrl(url) {
      agentApi.windowOpenUrl(url)
    },
    async submitHandle(options) {
      /*
      options参数
        params: 接口所用参数
        CheckItem: 操作项数据，用于修改修复状态不需要可不传
        tip: 提示
        fixKey: 修复状态参数key值，用于一个项多个修复按钮或者多步骤操作默认值 hasFixed
        showTip: 是否展示提示语默认值true
        needProccess 是否创建进度条
      */
      const defaultOptions = {
        params: {},
        CheckItem: {},
        tip: this.$t('check.operate'),
        showTip: true,
        needProccess: false
      }
      // 获取修复状态
      const _options = { ...defaultOptions, ...options }
      const fixKey = _options.fixKey || 'hasFixed'
      const hasFixed = _options.CheckItem && _options.CheckItem[fixKey]
      // 防止连续点击
      if (this.loading || hasFixed) {
        return
      }
      // 创建进度条
      if (_options.needProccess) {
        this.$emit('progress', true)
      }
      this.loading = true
      const res = await agentApi.repairSecCheckItem(_options.params)
      this.$emit('update:canClose', true)
      // 关闭进度条
      if (_options.needProccess) {
        console.log('emit', _options.needProccess)
        this.$emit('progress', false)
      }
      this.loading = false
      const errcode = _.get(res, 'ASM.errcode')
      const errmsg = _.get(res, 'ASM.errmsg')
      // 接口结果处理
      if (_options.showTip) {
        if (errcode === '0') {
          this.$message({
            message: errmsg || _options.tip + this.$t('check.success'),
            type: 'success'
          })
          if (_options.CheckItem) {
            _options.CheckItem[fixKey] = true
          }
        } else {
          this.$message({
            message: errmsg || _options.tip + this.$t('check.fail'),
            type: 'error'
          })
        }
      }
      return res
    }
  }
}

export default tplMixins
