
import Auth from './auth'
import { Message } from 'element-ui'
import store from '@/render/store'
import proxyApi from '@/service/api/proxyApi'
import common from './common'
import _ from 'lodash'
import accessNetwork from '@/render/utils/accessNetwork'
import { i18n } from '@/render/lang'
import dot1xCommon from '@/render/utils/auth/dot1x'
import authTypes from './authTypes'
import { EventBus } from '@/render/eventBus'

class NoAuth extends Auth {
  constructor() {
    super()
    this.type = authTypes.NoAuth
  }

  /**
  * 普通
   * @returns {Promise<boolean>}
  */
  async common(params) {
    // 免认证默认开启自动认证安检
    const apiParam = {
      deviceid: _.get(store.state.clientInfo, 'detail.DeviceID', 0),
      type: this.type
    }
    const { UserType } = _.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo', {})

    if (parseInt(UserType) === 2) {
      apiParam.type = 'Guest'
      apiParam.net_code = ''
      apiParam.guestType = 'NoAuth'
    }
    const res = await proxyApi.authIndex(apiParam)
    if (parseInt(res.errcode) !== 0) {
      return false
    }
    store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: res.data }))
    await common.authEnd({
      type: this.type,
      autoAuth: '1'
    })
    return true
  }

  /**
  * 802.1x
  * @returns {Promise<boolean>}
  */
  async dot1x(params) {
    const { AccessNetwork, IsWireLess } = accessNetwork.getLastAccessNetwork()
    const apiParam = {
      AuthParam: {
        AccessNetwork: AccessNetwork,
        IsWireLess: IsWireLess,
        AuthType: this.type,
        userType: _.get(store.state.serveEntiretyConfig, 'sceneConfig.sceneinfo.UserType')
      }
    }
    const res = await dot1xCommon.auth(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', i18n.t('check.fail')))
      EventBus.$emit('client:show')
      return false
    }
    const authResponse = _.get(res, 'ASM.AuthResponse', '')
    const authResponseJson = dot1xCommon.handleAuthResponse(authResponse)
    store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: authResponseJson }))

    await dot1xCommon.authEnd({
      type: this.type,
      authResponse: false,
      autoAuth: '1'
    })
    return true
  }
}

export default NoAuth
