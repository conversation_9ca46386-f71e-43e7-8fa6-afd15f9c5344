<template>
  <VerticalDialog
    :title="isRevoke?$t('auth.cancelOneDev'):$t('tips')"
    :show.sync="show"
    @beforeClose="cancelHandle"
  >
    <div class="dialog-content">
      <div class="form-content">
        <div v-if="!isRevoke" class="revokeInfo">
          <div>
            {{ revokeData.msg }}
            <p>
              {{ $t('auth.cancelLoginTip') }}
              <a style="color:#0000CC;cursor: pointer" class="devtip" @click="changeIsManager">
                {{ $t('auth.manageMyDevice') }}
              </a>
            </p>
          </div>
        </div>
        <div v-else>
          <el-table
            :data="onlineDevices"
            style="width: 430px"
            header-row-class-name="onlineDevicesTh"
            @selection-change="handleSelectionChange"
          >
            >
            <el-table-column
              type="selection"
            />
            <el-table-column
              prop="DevName"
              :label="$t('auth.equipmentName')"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="TypeName"
              :label="$t('auth.equipmentType')"
              :show-overflow-tooltip="true"
              width="100"
            />
            <el-table-column
              prop="IP"
              :label="$t('auth.ipAddress')"
              width="130"
            />
          </el-table>
        </div>
      </div>
      <div class="dialog-footer">
        <div class="cancel-btn btn" @click="cancelHandle">
          {{ $t("check.VulnerablePassword.h_18_rd") }}
        </div>

        <div class="confirm-btn btn" @click="continueAuth">
          {{ !isRevoke? $t("auth.continueCertification") : $t("auth.revokeDevice") }}
        </div>
      </div>
    </div>
  </VerticalDialog>
</template>

<script>
import { mapGetters } from 'vuex'
import proxyApi from '@/service/api/proxyApi'
// import authIndex from '@/render/utils/auth/index'
import G_VARIABLE from '@/render/utils/G_VARIABLE'
import authTypes from '@/render/utils/auth/authTypes'

export default {
  props: {
    revokeData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data: function() {
    return {
      show: false,
      loading: false,
      isRevoke: false,
      originalDevices: {},
      selectedDevice: [],
      tips: this.$t('tips')
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo']),
    onlineDevices() {
      const originalDevices = this.originalDevices
      if (_.isArray(originalDevices.data) && !_.isEmpty(originalDevices.data)) {
        const data = originalDevices.data
        _.remove(data, (item) => {
          // 移除非在线
          if (item.AccessType !== authTypes.User && item.AccessType !== authTypes.Permit) {
            return true
          }
          // 移除本机
          return parseInt(item.DeviceID) === parseInt(_.get(this.clientInfo.detail, 'DeviceID', 0))
        })
        return data
      }
      return []
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectedDevice = val
      console.log(val)
    },
    async getOnlineDevice() {
      const apiParam = {
        DeviceID: _.get(this.clientInfo.detail, 'DeviceID', 0),
        UserName: this.revokeData.Username,
        AuthType: this.revokeData.AuthType
      }
      const res = await proxyApi.deviceOnline(apiParam)
      if (parseInt(res.errcode) === 0) {
        const originalDevices = res.data
        console.log(originalDevices)
        this.originalDevices = originalDevices
      }
    },
    async continueAuth() {
      const isRevoke = this.isRevoke
      if (isRevoke) {
        // 判断是否选择了设备id
        if (_.isEmpty(this.selectedDevice)) {
          this.$message.error(this.$t('auth.selectSpecifiedDevice'))
          return false
        }

        const deviceIdArr = _.map(this.selectedDevice, 'DeviceID')
        const apiParam = {
          device_id: deviceIdArr.join(),
          msg: this.$t('auth.loginOffline', {
            username: this.revokeData.Username,
            ip: _.get(this.clientInfo, 'detail.IP', '')
          }),
          isSendCutOffMsg: '1',
          currDeviceId: _.get(this.clientInfo, 'detail.DeviceID', 0),
          remark: 'LogOut'
        }
        const res = await proxyApi.cutoffDevice(apiParam)
        if (parseInt(res.errcode) !== 0) {
          return false
        } else {
          this.$message.success(this.$t('auth.revokeSuccess'))
        }
      } else {
        G_VARIABLE.g_hintOver = 1
      }
      this.$emit('emitHandle', { type: 'revoke:success', value: this.isRevoke })
      this.showRevoke(false)
    },
    async changeIsManager() {
      await this.getOnlineDevice()
      this.isRevoke = true
    },
    showRevoke(show = false) {
      this.show = show
      if (!show) {
        this.isRevoke = false
        this.originalDevices = {}
      }
    },
    cancelHandle() {
      this.$emit('emitHandle', { type: 'revoke:cancel', value: this.isRevoke })
      this.showRevoke(false)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content{

  .form-content{

  padding-left: 25px;
  padding-right: 25px;
    min-height: 123px;
    display: flex;
    align-items:center;
    justify-content:center;
    .revokeInfo{
      div,p{
        word-break:normal;
      }
    }
  }

  .onlineDevicesTh{
    background: #f0f0f3;
  }

  .dialog-footer {
    display: flex;
      .btn {
        width: 50%;
        height: 32px;
        line-height: 31px;
        text-align: center;
        font-size: 13px;
        color: $default-color;
        border-top: 1px solid $line-color;
        cursor: pointer;
        &:hover {
        background: $--color-primary;
        color: white;
        border-left-color: $--color-primary;
        border-top-color: $--color-primary;
      }
    }
    .cancel-btn {
      border-bottom-left-radius: 5px;
    }
    .confirm-btn {
      border-left: 1px solid $line-color;
      color: $--color-primary;
      border-bottom-right-radius: 5px;
    }
  }
}
</style>
