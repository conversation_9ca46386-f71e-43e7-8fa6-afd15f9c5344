<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("check.P2PSoft.h_1_rs")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p class="tit-info">
            {{ $t("check.P2PSoft.h_3_rs") }}
          </p>
          <div v-for="(item, index) in P2PList" :key="item.SoftName" class="pc-info">
            <img :src="imgSrc" alt="">
            <div class="pc-info-rit">
              <div class="custom-name">
                <div class="optional-item">
                  {{ $t("check.P2PSoft.h_5_rd") }}
                  <span>{{ item.SoftName }}</span>
                </div>
                <button :class="['btn-small', item.hasFixed ? 'btn-disabled': 'public-medium-btn']" @click="fixHandle(item, index)">
                  {{ $t("check.P2PSoft.js_3_d") }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <!-- 如何修复 -->
    <howToFix :fix-data="fixData" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import howToFix from '../howToFix/common.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckP2PSoft',
  components: {
    checkResult,
    howToFix
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true,
      fixData: {},
      P2PList: [],
      imgSrc: require('@/render/assets/CheckP2PSoft.gif')
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
    this.initFixTip()
    this.getP2PList()
  },
  methods: {
    initFixTip() {
      this.fixData = {
        modelTitle: this.$t('check.P2PSoft.h_7_rs'),
        fixSteps: [this.$t('check.P2PSoft.h_9_rs'), this.$t('check.P2PSoft.h_11_rs'), this.$t('check.P2PSoft.h_13_rs') + '<br>' + this.$t('check.P2PSoft.h_15_rs')]
      }
    },
    getP2PList() {
      let list = _.get(this.checkData, 'CheckResult.CheckType.Info')
      if (!list) {
        return
      }
      if (!_.isArray(list)) {
        list = [list]
      }
      list.forEach(item => {
        item.hasFixed = this.checkData.hasFixed
      })
      this.P2PList = list
    },
    async fixHandle(item, index) {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          ApplicationName: '',
          CommandLine: item.SoftUninstall,
          WaitApplicationExit: 'No'
        },
        RepairType: 0,
        CreateProgress: 1
      }
      await this.submitHandle({
        params,
        CheckItem: item,
        tip: this.$t('check.P2PSoft.js_3_d')
      })
      this.$set(this.P2PList, index, item)
    }
  }
}
</script>

