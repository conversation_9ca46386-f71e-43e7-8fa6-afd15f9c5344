<!--
 * @Author: <EMAIL>
 * @Date: 2021-08-15 14:59:03
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-11 10:44:44
 * @Description: 来宾自助申请的审核页面
-->
<template>
  <div id="f-guest-auth-state">
    <div class="u-current-tag">
      <span>
        <i class="iconfont icon-shenhe" />{{ $t('guestAuth.guest.info_24') }}
      </span>
    </div>
    <div class="u-apply-state">
      <layout-state
        :state-img="computeStateBtn['stateImg']"
        :state-btn-txt="computeStateBtn['btnText']"
        :state-btn-id="computeStateBtn['btnId']"
        :state-btn-loading="loading"
        @stateBtnHandle="handleNext"
      >
        <div slot="stateMsg" class="u-apply-main">
          <template v-if="state==='waite'">
            <p id="ui-guest-audit-p-check_msg">{{ $t('guestAuth.guest.info_25') }}</p>
            <p class="u-not-reboot-tips">{{ $t('guestAuth.guest.info_26') }}</p>
          </template>

          <template v-else-if="state === 'refuse' ">
            <p id="ui-guest-audit-p-check_msg"> {{ $t('guestAuth.guest.info_28') }} </p>
            <p id="ui-guest-audit-p-refuse_result" class="u-not-reboot-tips">
              {{ $t('guestAuth.guest.info_29') }} {{ refuseReason }}
            </p>
          </template>

          <template v-else>
            <p id="ui-guest-audit-p-check_msg">{{ $t('guestAuth.guest.info_30') }}</p>
          </template>
        </div>
        <template>
          <div v-if="state === 'success' || state==='waite'" class="u-give-up">
            <a id="ui-guest-audit-a-cancel_network" href="javascript:void(0)" @click="giveUpOrFail">{{ $t('guestAuth.guest.info_32') }}</a>
          </div>
        </template>
      </layout-state>
    </div>
  </div>
</template>

<script>
import state from '@/render/components/layout/state'
import localStorage from '@/render/utils/cache/localStorage'
import proxyApi from '@/service/api/proxyApi'
import { mapState, mapMutations } from 'vuex'
// import authIndex from '@/render/utils/auth/index'
// import commonAuth from '@/render/utils/auth/common/index'

import processController from '@/render/utils/processController'
// import commonUtil from '@/render/utils/bussiness/commonUtil'
const auditing = require('@/render/assets/stateIllustration/auditing.png')
const refuse = require('@/render/assets/stateIllustration/refuse.png')
const ok = require('@/render/assets/stateIllustration/applyOk.png')
let taskiId = null
export default {
  components: {
    layoutState: state
  },
  data() {
    return {
      state: 'waite',
      stateImg: auditing,
      guestselfid: null,
      loading: false,
      refuseReason: '',
      guestAuthType: ''
    }
  },
  computed: {
    ...mapState(['clientInfo', 'authInfo']),
    computeStateBtn() {
      const state = {
        btnText: this.$t('guestAuth.guest.info_27'),
        stateImg: auditing
      }
      if (this.state === 'waite') {
        state['btnText'] = this.$t('guestAuth.guest.info_27')
        state['btnId'] = 'ui-guest-audit-button-refres_status'
      } else if (this.state === 'refuse') {
        state['btnText'] = this.$t('guestAuth.administrator.info_18')
        state['stateImg'] = refuse
        state['btnId'] = 'ui-guest-audit-button-confirm'
      } else {
        state['btnText'] = this.$t('guestAuth.guest.info_31')
        state['stateImg'] = ok
        state['btnId'] = 'ui-guest-audit-button-continue'
      }
      return state
    }
  },
  created() {
    this.loopAuditStatus()
  },
  destroyed() {
    this.stopLoop()
  },
  methods: {
    ...mapMutations(['setAuthInfo']),
    handleNext() {
      if (this.state === 'waite') {
        this.refresh()
      } else if (this.state === 'refuse') {
        this.giveUpOrFail()
      } else {
        // 进入下一步。根据设备判断是否需要注册等？？？？
        this.successNext()
      }
    },
    async successNext() {
      processController.next()
    },
    // 轮询审核状态
    loopAuditStatus() {
      if (localStorage.getItem('guestAuditStatus')) {
        const { guestselfid, type } = JSON.parse(localStorage.getItem('guestAuditStatus'))
        this.guestselfid = guestselfid
        this.guestAuthType = type
      } else {
        this.$router.push({ path: '/access/guestAuth' })
        return
      }

      if (!taskiId) {
        taskiId = setInterval(async() => {
          this.refresh()
        }, 3000)
      }
    },
    // 查询当前审核状态
    async refresh() {
      const apiParam = {
        action: 'State',
        guestselfid: this.guestselfid
      }

      this.loading = true
      try {
        const ret = await proxyApi.guestSelfApply(apiParam)
        console.log(ret)
        if (_.get(ret, 'data.state') === 'OK') {
          // 申请成功，进入下一步
          this.state = 'success'
          this.stopLoop(true)
        } else if (_.get(ret, 'data.state') === 'refuse') {
          // 入网申请被拒绝，返回自助申请页面
          this.state = 'refuse'
          this.refuseReason = _.get(ret, 'data.Reason') || ''
          this.stopLoop(true)
        }
        this.loading = false
      } catch (error) {
        this.stopLoop()
        this.giveUpOrFail()
        console.error(error)
        this.loading = false
      }
    },

    stopLoop(driving = false) {
      if (driving) {
        localStorage.removeItem('guestAuditStatus')
      }
      if (taskiId) {
        clearInterval(taskiId)
        taskiId = null
      }
    },
    // 放弃或者申请被拒
    async giveUpOrFail() {
      let ret = {}
      try {
        ret = await proxyApi.reqGuestRequired({
          action: 'Cancel',
          guestselfid: this.guestselfid
        })
      } catch (error) {
        console.error(error)
      }
      if (this.state === 'waite') {
        if (parseInt(_.get(ret, 'errcode')) === 0) {
          this.stopLoop(true)
          this.setAuthInfo({ ...this.authInfo, ...{ basic: {}}})
          processController.set('/access/guestAuth')
        }
      } else {
        processController.set('/access/guestAuth')
      }
    }
  }
}
</script>

<style lang="scss">
 #f-guest-auth-state{
    padding: 24px;
  box-sizing: border-box;
  text-align: center;
  height: 100%;

  .u-apply-state{
    height:calc(100% - 28px);
    width:100%;
    .u-apply-main{
      margin-top:10px;
    }
    .u-not-reboot-tips{
      text-align:center;
      margin-top: 16px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      color: #b3b6c1;
    }

    .u-give-up{
      margin-top: 16px;
      a{
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      text-decoration: underline;
      color: #536ce6;
      text-align: center;
      }

    }
  }
 }
</style>
