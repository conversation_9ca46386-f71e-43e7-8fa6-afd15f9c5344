import Vue from 'vue'
import App from './render/App.vue'
import router from './render/router'
import store from './render/store'
import Bootstrap from './render/utils/bootstrap'
import './render/styles/index.scss'
import '@/render/components/globalComponents'
import ipc from '@/service/utils/ipcPlugin/index'

Vue.config.productionTip = false

ipc.init().then(() => {
  new Bootstrap((i18n) => {
    window.$vm = new Vue({
      i18n,
      router,
      store,
      render: h => h(App)
    }).$mount('#app')
  }).bindAfter()
})

