# electron项目打包脚本

`yarn buildAsar`

* 1.electron项目打包的时候,实际上是打包成一个.asar文件。这个文件是控制electron的单元程序。 *
* 2.打包后的app.asar文件会和原来client的Qrc.zip合并。 *
* 3.客户端发现Qrc.zip更新后,会去下载Qrc.zip文件,用来更新和修复界面功能*

# electron的参数说明

    标准调用参数为:

    ```
        Ass_ele.exe app.asar --target C:\installFile\Qrc\dist\index.html

    ```

    ### 详细参数列表

        ```
            --disableGPU  关闭硬件加速
            --target 资源文件(http://localhost:9527/index.html |  C:\Windows\SysWOW64\IsAgent\Qrc\dist\index.html )
            --devTools 打开调试工具
            --SupportMutilProcess 支持多进程方式打开(开机自启动使用)
            --HideWindow  后台隐藏方式打开electron (开机自启动使用)
        ```

# electron的渲染进程和主进程通信方法
    
    在渲染进程里面注册了对象"eleIPC",里面暴露了几个方法:
    
    ```
    {
        info:{

        },
        send:(event,params)=>{},
        on:()=>{}
    }
    ```
    在渲染进程调用electron的api可以使用方法:
        
        `Windows.eleIPC.send("max")`
    

