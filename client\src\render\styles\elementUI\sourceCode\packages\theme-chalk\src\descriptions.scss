@import 'mixins/mixins';
@import 'common/var';
@import 'descriptions-item';

@include b(descriptions) {
  box-sizing: border-box;
  font-size: $--font-size-base;
  color: $--color-text-primary;

  @include e(header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $--descriptions-header-margin-bottom;

    @include e(title) {
      font-size: $--descriptions-title-font-size;
      font-weight: bold;
    }
  }

  @include e(body) {
    color: $--color-text-regular;
    background-color: $--color-white;

    .el-descriptions__table {
      border-collapse: collapse;
      width: 100%;
      table-layout: fixed;

      .el-descriptions-item__cell {
        box-sizing: border-box;
        text-align: left;
        font-weight: normal;
        line-height: 1.5;

        @include when(left) {
          text-align: left;
        }

        @include when(center) {
          text-align: center;
        }

        @include when(right) {
          text-align: right;
        }
      }
    }
  }

  .is-bordered {
    table-layout: auto;
    .el-descriptions-item__cell {
      border: $--descriptions-table-border;
      padding: 12px 10px;
    }
  }

  :not(.is-bordered) {
    .el-descriptions-item__cell {
      padding-bottom: 12px;
    }
  }

  @include m(medium) {
    &.is-bordered {
      .el-descriptions-item__cell {
        padding: 10px;
      }
    }

    &:not(.is-bordered) {
      .el-descriptions-item__cell {
        padding-bottom: 10px;
      }
    }
  }

  @include m(small) {
    font-size: 12px;

    &.is-bordered {
      .el-descriptions-item__cell {
        padding: 8px 10px;
      }
    }

    &:not(.is-bordered) {
      .el-descriptions-item__cell {
        padding-bottom: 8px;
      }
    }
  }

  @include m(mini) {
    font-size: 12px;

    &.is-bordered {
      .el-descriptions-item__cell {
        padding: 6px 10px;
      }
    }

    &:not(.is-bordered) {
      .el-descriptions-item__cell {
        padding-bottom: 6px;
      }
    }
  }
}
