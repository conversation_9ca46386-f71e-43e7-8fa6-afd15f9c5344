<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" />
    <!-- 电脑当前信息 -->
    <div v-if="checkFail" class="computer-details-modle">
      <p class="model-title">
        {{ $t("accessNetwork.securityCheck.info_17")
        }}<i
          :class="collapseFlag ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
          @click="collapseFlag = !collapseFlag"
        />
      </p>
      <el-collapse-transition>
        <div v-show="collapseFlag" class="model-content">
          <p>{{ $t("check.IEHomePage.h_3_rs") }}</p>
          <p class="last-item">{{ $t("check.IEHomePage.h_5_rd") }}{{ homePage }}</p>
          <button style="width:100px" :class="[checkData.hasFixed ? 'btn-max-disabled': 'public-medium-btn', loading ? 'loading-disable': '']" @click="fixHandle">
            <i v-if="loading" class="el-icon-loading" />
            {{ $t("check.IEHomePage.js_1_d") }}
          </button>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  name: 'CheckIEHomePage',
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      collapseFlag: true
    }
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    },
    homePage() {
      return _.get(this.checkData, 'CheckType.Option.HomePage', '')
    },
    checkFail() {
      return _.get(this.checkData, 'CheckResult.CheckType.Result') === 'No'
    }
  },
  mounted() {
  },
  methods: {
    fixHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
          homePage: this.homePage
        },
        RepairType: 0,
        CreateProgress: 0
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.IEHomePage.js_1_d')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-content{
  p{
    margin-bottom: 10px;
    color:$title-color;
  }
  .last-item{
    margin-bottom: 24px;
  }
  .public-medium-btn {
    width: auto;
    padding: 0 16px;
  }
}

</style>
