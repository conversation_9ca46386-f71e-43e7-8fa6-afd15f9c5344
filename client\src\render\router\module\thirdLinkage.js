/*
 * @Author: <EMAIL>
 * @Date: 2022-03-01 14:26:47
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-02 09:30:40
 * @Description: 动态路由占坑，注意code:4开头的已经被使用
 */

export default [
  // {
  //   path: '/thirdLinkAgeMenu/:id',
  //   name: 'thirdLinkAgeMenu',
  //   component: () => import('@/render/views/thirdLinkAgeMenu/index'),
  //   meta: {
  //     code: '401', // 保证唯一性，用于侧边栏判断是否显示激活状态（通过前3位判断）
  //     menu: { // 不需要显示到侧边导航栏不配
  //       name: 'nav.pathView',
  //       icon: 'icon-disanfangyingyong', // 侧边栏显示图标
  //       moduleName: 'nav.thirdLinkAgeMenu' // 所属模块名称
  //     }
  //   }
  // }
]

