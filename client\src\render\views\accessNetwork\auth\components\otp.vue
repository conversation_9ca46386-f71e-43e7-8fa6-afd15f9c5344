<template>
  <!-- OTP的组件-->
  <div :class="['otp-auth-page', towFactor?'tow-factor-otp':'']">
    <div
      v-if="show.type === 1"
      class="input-code-temp"
    >
      <div class="needOTPPassword">
        {{ $t('auth.needOTPPassword') }}
      </div>
      <el-input
        v-model="checkCode"
        class="input-checkCode"
        maxlength="50"
      />
      <p v-if="getOvertimeMsg" class="cut-down">{{ getOvertimeMsg }}</p>
      <div v-if="isActive === 0" class="otp-bottom-wrapper">
        <p class="re-scan-btn" @click="reScan()">{{ $t('auth.reScan') }}</p>
        <p class="ok-btn" @click="submitOTP('ruleForm')">{{ $t('auth.ok') }}</p>
      </div>
      <p v-if="isActive === 1" class="public-btn otp-btn" @click="submitOTP('ruleForm')">{{ $t('auth.ok') }}</p>
    </div>

    <div
      v-if="show.type === 2"
    >
      <!-- APP扫码激活令牌 -->
      <div class="otpActivationToken">
        {{ $t('auth.otpActivationToken') }}
      </div>
      <div>
        <img v-if="otpQrCodeSrc.length > 0" :id="calcId('-img-OTP_img')" class="otpQrCodeSrc" :src="otpQrCodeSrc" alt="二维码">
      </div>
      <div class="noInstallNAC">
        <a href="javascript:void(0)" class="close-btn" @click="installClient">
          {{ $t('auth.noInstallNAC') }}
        </a>
      </div>
      <p v-if="getOvertimeMsg" class="cut-down">{{ getOvertimeMsg }}</p>

      <div :id="calcId('-div-refresh')" class="public-btn otp-btn" @click="scanned()">{{ $t('auth.scanned') }}</div>
    </div>

    <div
      v-if="show.type === 3"
    >
      <!-- 下载APP -->
      <div class="down-tip" style="text-align:left;">
        {{ $t('auth.otpDownloadClient') }}
      </div>
      <div class="IosAndroidQr">
        <div>
          <img v-if="iosQrCodeSrc.length > 0" :src="iosQrCodeSrc" alt="二维码">
          <div>iOS</div>
        </div>
        <div>
          <img v-if="androidQrCodeSrc.length > 0" :src="androidQrCodeSrc" alt="二维码">
          <div>Android、Harmony</div>
        </div>
      </div>
      <p v-if="getOvertimeMsg" class="cut-down">{{ getOvertimeMsg }}</p>
      <p class="public-btn otp-btn" @click="close()">{{ $t('auth.close') }}</p>
    </div>
  </div>
</template>
<script>
import authMixin from '../mixins'
import _ from 'lodash'
import { mapGetters } from 'vuex'
import qs from 'qs'
import urlUtils from '@/render/utils/url'
import proxyAjax from '@/service/utils/proxyAjax'
import proxyApi from '@/service/api/proxyApi'
import TwoFactor from '@/render/utils/auth/twoFactor'
import { EventBus } from '@/render/eventBus'
import store from '@/render/store'

export default {
  name: 'OTPAuth',
  components: {

  },
  mixins: [authMixin],
  props: {
    authData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    isShow: {
      type: Boolean,
      default: false
    },
    idPre: {
      type: String,
      default: 'ui-accessNetwork-twoFactors'
    },
    towFactor: {
      type: Boolean,
      default: false
    },
    getOvertimeMsg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: {
        type: 0
      },
      isActive: 0, // 是否已激活
      iosQrCodeSrc: '', // ios客户端二维码
      androidQrCodeSrc: '', // 安卓客户端二维码
      otpQrCodeSrc: '', // OTP激活令牌二维码
      checkCode: '',
      userId: 0,
      isSuccess: false
    }
  },
  computed: {
    ...mapGetters(['authInfo'])
  },
  watch: {
    // 中英文切换时重新校验失败条目
    '$i18n.locale': function() {
      this.$refs['ruleForm'].fields.forEach(item => {
        if (item.validateState === 'error') {
          this.$refs['ruleForm'].validateField(item.labelFor)
        }
      })
    },
    isShow(value) {
      if (value) {
        this.show.type = 0
        this.checkCode = ''
        this.initOTP()
        this.drawQrCode()
      }
    }
  },
  async mounted() {
    await this.initOTP()
    this.drawQrCode()
  },
  beforeDestroy() {
  },
  methods: {
    async initOTP() {
      const userApiParam = {
        deviceId: _.get(store.state.clientInfo.detail, 'DeviceID', '') || _.get(store.state.clientInfo, 'basic.AgentID', 0)
      }
      const userResult = await proxyApi.getNacOnlineUserID(userApiParam)
      if (userResult.data) {
        this.userId = userResult.data.UserID // 获取用户ID
      }
      const apiParam = {
        userid: _.get(this.authInfo, 'basic.UserID', this.userId)
      }
      const result = await proxyApi.checkActivationKey(apiParam)
      if (result.data && result.data.state === true) {
        this.show.type = 1 // 激活了令牌
        this.isActive = 1 // 已经激活
      } else {
        this.show.type = 2 // 没有激活令牌
      }
    },
    // 获取二维码
    drawQrCode() {
      const otpApiParam = {
        userid: _.get(this.authInfo, 'basic.UserID', this.userId),
        cache: Math.random()
      }
      this.otpQrCodeSrc = urlUtils.getBaseIPPort() + proxyAjax.formatUrl('OTP/qrcode?' + qs.stringify(otpApiParam, { encode: false }))
      const apiParam = {
        cache: Math.random()
      }
      this.iosQrCodeSrc = urlUtils.getBaseIPPort() + proxyAjax.formatUrl('OTP/iosQrcode?' + qs.stringify(apiParam, { encode: false }))
      this.androidQrCodeSrc = urlUtils.getBaseIPPort() + proxyAjax.formatUrl('OTP/androidQrcode?' + qs.stringify(apiParam, { encode: false }))
    },
    close() {
      EventBus.$emit('titleDownloadAPP', false)
      this.show.type = 2
    },
    installClient() {
      EventBus.$emit('titleDownloadAPP', true)
      this.show.type = 3 // 展示下载客户端的页面
    },
    async scanned() {
      const apiParam = {
        userid: _.get(this.authInfo, 'basic.UserID', this.userId)
      }
      const result = await proxyApi.checkActivationKey(apiParam)
      if (result.data && result.data.state === true) {
        this.isActive = 1 // 已经激活
      } else {
        this.isActive = 0 // 未激活
      }
      this.show.type = 1 // 激活了令牌
    },
    // 重新扫码
    async reScan() {
      this.show.type = 2 // 跳到重新扫码页面
    },
    /**
     * 双因子提交
     */
    submitOTP: _.debounce(async function() {
      const patrn = /^\d{6}$/
      if (!patrn.exec(this.checkCode)) {
        this.$message({
          message: this.$t('auth.phoneCodeValidateErr'),
          type: 'warning'
        })
        return false
      }
      const twoFactor = new TwoFactor()
      if (this.authData.authFrom === 'addition') {
        const additionRes = await twoFactor.otpAddition({
          checkCode: this.checkCode,
          userid: _.get(store.state.authInfo.basic, 'UserID', this.userId),
          userName: _.get(this.authInfo, 'basic.UserName', '') ? _.get(this.authInfo, 'basic.UserName', '') : _.get(this.clientInfo, 'accessStatus.userName', '')
        })
        if (additionRes === false) {
          return false
        }
      } else {
        const res = await twoFactor.otpCommon({
          checkCode: this.checkCode,
          userid: _.get(store.state.authInfo.basic, 'UserID', this.userId),
          userName: _.get(this.authInfo, 'basic.UserName', '') ? _.get(this.authInfo, 'basic.UserName', '') : _.get(this.clientInfo, 'accessStatus.userName', '')
        })
        if (res === false) {
          return false
        }
      }
      this.isSuccess = true
      this.$emit('towFactorSuccess')
      this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
    }, 1000, { 'leading': true, 'trailing': false }),
    calcId(suf) {
      return this.idPre + suf
    }
  }
}
</script>
<style lang="scss" >
.otp-auth-page {
  width: 100%;
  margin: 0 auto;
  text-align: center;

  .otpQrCodeSrc{
    margin: 24px auto 16px auto;
    width: 140px;
    height: 140px;
  }
  .IosAndroidQr{
    margin-top: 24px;
    img{
      width: 140px;
      height: 140px;
      margin: 0 12px;
    }
    &>div{
      display: inline-block;
    }
  }
  .otpActivationToken{
    line-height: 24px;
    text-align: left;
  }
  .otpActivationToken, .down-tip{
    padding: 24px 24px 0 24px;
  }
  .noInstallNAC{
    text-align: center;
    a{
      color: #536CE6;
    }
  }
  .otp-btn{
    width: 100%;
    margin-top: 24px;
    background: #FFFFFF;
    color: #536CE6;
    border: 1px solid #ededf1;
    font-size: 14px;
  }
  .input-checkCode {
    padding: 0 44px;
    input{
      font-size: 24px;
      margin-top: 24px;text-align: center;
    }
  }
  .otp-bottom-wrapper{
    display: flex;
    border-top: 1px solid $line-color;
    p{
      width: 50%;
    }
  }
  .needOTPPassword{
    margin-top: 24px;
  }
  .input-code-temp{
    .otp-bottom-wrapper{
      display: flex;
      margin-top: 24px;
      p{
        width: 50%;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        color: $default-color;
        &:hover{
          background: $--color-primary;
          color: white;
        }
      }
      .ok-btn{
        color: $--color-primary;
        border-bottom-right-radius: 5px;
      }
      .re-scan-btn{
        border-right: 1px solid $line-color;
        border-bottom-left-radius: 5px;
      }
    }
  }
}
.tow-factor-otp{
  padding: 0;
  .cut-down{
    padding: 0 24px;
    color: $default-color;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    line-height: 17px;
    margin-top: 16px;
  }
  .otp-btn{
    border:none;
    border-top: 1px solid #ededf1;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
  }
}

</style>

