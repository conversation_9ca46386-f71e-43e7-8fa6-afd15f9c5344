<template>
  <div>
    <div id="ewm-box" class="ewm-box">
      <img v-if="qrCodeSrc.length > 0" class="nac-ewm" :src="qrCodeSrc" alt="二维码">
    </div>
    <p class="hint">
      {{ $t('guestAuth.guest.info_21') }}
    </p>

    <!-- 双因子认证 -->
    <two-factor
      ref="sms"
      :mobile="smsMobile"
      :user-name="username"
      @smsAuthSuccess="smsAuthSuccess"
      @beforeClose="twoFactorClose"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import qs from 'qs'
import proxyAjax from '@/service/utils/proxyAjax'
import proxyApi from '@/service/api/proxyApi'
import qrCodeCommon from '@/render/utils/auth/qrCodeCommon'
import twoFactor from './twoFactors.vue'
import { EventBus } from '@/render/eventBus'
import urlUtils from '@/render/utils/url'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'
import authIndex from '@/render/utils/auth/index'

export default {
  components: {
    twoFactor
  },
  props: {
    isQrcode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      qrCodeSrc: '',
      taskId: '',
      smsMobile: '',
      username: '',
      password: ''
    }
  },
  computed: {
    ...mapGetters(['serveEntiretyConfig', 'clientInfo', 'authInfo'])
  },
  watch: {
    isQrcode: function(value) {
      if (value) {
        this.drawQrCode()
      } else {
        this.clearQrInterval()
      }
    }
  },
  mounted() {
    this.drawQrCode()
    EventBus.$on('revoke:refresh', this.drawQrCode)
  },
  beforeDestroy() {
    this.clearQrInterval()
    EventBus.$off('revoke:refresh', this.drawQrCode)
  },
  methods: {
    // 绘制来宾二维码
    drawQrCode() {
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      this.clearQrInterval()

      const apiParam = {
        deviceid: _.get(this.clientInfo.detail, 'DeviceID', 0),
        action: 'img',
        cache: Math.random()
      }
      this.qrCodeSrc = urlUtils.getBaseIPPort() + proxyAjax.formatUrl('scan/index?' + qs.stringify(apiParam, { encode: false }))
      if (!_.get(this.clientInfo, 'online', false)) {
        return
      }
      this.queryScanRes()
    },
    /**
     * 周期查询
     */
    queryScanRes() {
      this.clearQrInterval()
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      const taskId = setInterval(() => {
        this.qrCheck()
      }, 3000)
      this.taskId = taskId
    },
    /**
     * 清除定时
     */
    clearQrInterval() {
      const taskId = this.taskId
      if (taskId) {
        clearInterval(taskId)
      }
    },
    /**
     * 查询扫码结果
     */
    async qrCheck() {
      const isQrcode = this.isQrcode
      if (!isQrcode) {
        return
      }
      const apiParam = {
        deviceid: _.get(this.clientInfo, 'detail.DeviceID', 0),
        action: 'check'
      }
      const result = await proxyApi.scanIndex(apiParam)

      if (result.data && result.data.state === false && result.data.type === 'overtime') {
        this.drawQrCode()
        return
      }
      if (result.data && result.data.state === true) {
        // 扫描成功
        this.clearQrInterval()
        this.username = result.data.username
        this.password = result.data.token
        this.submitForm()
      }
    },
    /**
     * 提交（支持撤销登录）
     */
    async submitForm() {
      const res = await qrCodeCommon.auth({
        guestType: 'qrlogin',
        username: this.username,
        password: this.password,
        isQrcode: this.isQrcode
      })
      console.log(res)
      if (res) {
        if (_.isObject(res)) {
          if (_.get(res, 'factorAuth', false) === true) {
            this.initMobile()
            this.$refs.sms.showSms(true)
            return
          }
          if (_.get(res, 'revoke', false) === true) {
            this.$emit('emitHandle', { type: 'revoke:show', value: res.data })
            return
          }
        }
        this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
      } else {
        this.drawQrCode()
      }
    },
    initMobile() {
      const mobile = _.get(this.authInfo, 'basic.Tel') || _.get(this.clientInfo, 'server.Tel')
      if (mobile) {
        this.smsMobile = mobile + ''
      } else {
        this.smsMobile = ''
      }
    },
    /**
     * 双因子关闭
     * 判断是否认证成功，未成功的话刷新二维码
     */
    async twoFactorClose(isSuccess) {
      console.log('isSuccess', isSuccess)
      if (!isSuccess) {
        // 普通则失败才取消
        if (!authIndex.isDot1xMode()) {
          await commonUtil.towFactoryCutoff()
        }
        this.drawQrCode()
      }
    },
    /**
     * 双因子认证成功
     */
    smsAuthSuccess() {
      this.$emit('emitHandle', { type: 'AuthSuccess', value: '' })
    }
  }
}
</script>
