<template>
  <div>
    <!-- 检查结果 -->
    <checkResult :result-data="resultData" :loading="loading" :has-fixed="checkData.hasFixed" :show-btn="true" @fix="fixHandle" />
  </div>
</template>
<script>
import checkResult from '../checkResult/checkResult.vue'
import tplMixins from '../mixins/tpl_windows'

export default {
  components: {
    checkResult
  },
  mixins: [tplMixins],
  props: {
    checkData: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    resultData() {
      return {
        checkType: this.checkData.itemResultType,
        Message: _.get(this.checkData, 'CheckResult.CheckType.Message')
      }
    }
  },
  methods: {
    fixHandle() {
      const params = {
        ItemID: this.checkData.ItemID,
        InsideName: this.checkData.InsideName,
        RepairParam: {
        },
        RepairType: 0,
        CreateProgress: 1
      }
      this.submitHandle({
        params,
        CheckItem: this.checkData,
        tip: this.$t('check.fix')
      })
    }
  }
}

</script>
