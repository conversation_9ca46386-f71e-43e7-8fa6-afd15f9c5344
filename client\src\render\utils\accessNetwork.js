import agentApi from '@/service/api/agentApi'
import _ from 'lodash'
import store from '@/render/store'

const dot1x = {
  store: {
    data: {
      networkOptions: [],
      accessNetwork: '',
      isWireLess: ''
    },
    setNetworkOptions(data) {
      this.data.networkOptions = dot1x.handleNetworkOptions(data)
    },
    getNetworkOptions() {
      return this.data.networkOptions
    },
    setAccessNetwork(data) {
      this.data.accessNetwork = data
    },
    getAccessNetwork() {
      return this.data.accessNetwork
    },
    /**
     * 是否无线
     * @returns {boolean|int}
     */
    isWireLess() {
      // 如果未获取到网卡列表信息，则返回上次
      if (_.isEmpty(this.data.networkOptions)) {
        return this.data.isWireLess
      }
      const accessNetwork = this.data.accessNetwork
      const selectedItem = _.find(this.data.networkOptions, (item) => {
        return item.value === accessNetwork
      })
      if (_.isNil(selectedItem) || !_.has(selectedItem, 'type')) {
        return '0'
      }
      return selectedItem.type
    }
  },
  /**
    * 处理原始数据
    * @param origin
    * @returns {*[]}
    */
  handleNetworkOptions(origin) {
    const options = []

    // 有线
    let wireList = _.get(origin, 'Asm.WireList.Item', [])
    if (_.isObject(wireList) && !_.isArray(wireList)) {
      wireList = [wireList]
    }
    if (_.isArray(wireList) && !_.isEmpty(wireList)) {
      _.forEach(wireList, (value) => {
        if (_.has(value, 'AdapterName')) {
          options.push(dot1x.handleWireList(value, 'AdapterName', 0))
        }
      })
    }

    // 无线
    let wirelessList = _.get(origin, 'Asm.WirelessList.Item', [])
    if (_.isObject(wirelessList) && !_.isArray(wirelessList)) {
      wirelessList = [wirelessList]
    }
    if (_.isArray(wirelessList) && !_.isEmpty(wirelessList)) {
      _.forEach(wirelessList, (value) => {
        if (_.has(value, 'SSID')) {
          options.push(dot1x.handleWireList(value, 'SSID', 1))
        }
      })
    }
    return options
  },
  /**
    * 处理数据
    * @param value
    * @param index
    * @param type
    * @returns {{label, type, value}}
    */
  handleWireList(value, index, type) {
    return {
      value: value[index],
      label: value[index],
      type: type
    }
  },
  /**
   * 初始化
   * @returns {Promise<void>}
   */
  async init(isFirst = true) {
    const res = await agentApi.getNetworkList({ ReScanSSID: isFirst ? 0 : 1 })
    this.store.setNetworkOptions(res)
    const selectNetwork = _.get(res, 'Asm.SelectNetwork')
    await this.save(selectNetwork)
    return dot1x.store.getNetworkOptions()
  },
  /**
    * 保存上次网卡信息
    */
  async save(selectNetwork = null) {
    let accessNetwork
    let isWireLess = _.get(store.state.serveEntiretyConfig, 'client.IsDot1xWireLess', '0')

    const networkOptions = dot1x.store.getNetworkOptions()
    if (_.isArray(networkOptions) && !_.isEmpty(networkOptions)) {
      // 网卡是否存在于列表中
      const networkIsExist = (network) => {
        const option = networkOptions.find((item) => {
          return item.value === network
        })
        return option
      }
      // 优先推荐选择，再上次使用
      accessNetwork = selectNetwork
      let option = networkIsExist(accessNetwork)
      if (_.isUndefined(option)) {
        accessNetwork = _.get(store.state.serveEntiretyConfig, 'client.Dot1xNetwork', '')
        option = networkIsExist(accessNetwork)
      }
      if (_.isUndefined(option)) {
        accessNetwork = ''
        isWireLess = ''
      } else {
        isWireLess = option.type
      }
    } else {
      // 如果不存在网卡可选，也清空
      accessNetwork = ''
      isWireLess = ''
    }
    this.store.data = {
      ...this.store.data,
      accessNetwork,
      isWireLess
    }
  },
  /**
   * 网卡可选
   * @returns
   */
  async getNetworkOptions() {
    let networkOptions = this.store.getNetworkOptions()
    if (_.isEmpty(networkOptions)) {
      await dot1x.init()
      networkOptions = this.store.getNetworkOptions()
    }
    return networkOptions
  },
  /**
   * 已选择网卡
   * @returns
   */
  getAccessNetwork() {
    return this.store.getAccessNetwork()
  },
  /**
   * 是否已初始化
   */
  isInited() {
    return _.isArray(this.store.data.networkOptions) && !_.isEmpty(this.store.data.networkOptions)
  },
  /**
    * 获取上次选择网卡
    * @returns {Promise<Object>}
    */
  getLastAccessNetwork() {
    return {
      AccessNetwork: dot1x.store.getAccessNetwork(),
      IsWireLess: dot1x.store.data.isWireLess
    }
  }
}

export default dot1x
