
import Auth from './auth'
import store from '@/render/store'
import proxy<PERSON>pi from '@/service/api/proxyApi'
import common from './common'
import _ from 'lodash'
import { i18n } from '@/render/lang'
import authTypes from './authTypes'
import agentApi from '@/service/api/agentApi'
import { Base64Encode } from '@/render/utils/global'
import dot1xCommon from './dot1x'
import { Message } from 'element-ui'
import accessNetwork from '@/render/utils/accessNetwork'
import { EventBus } from '@/render/eventBus'
import commonUtil from '@/render/utils/bussiness/commonUtil.js'
import G_VARIABLE from '@/render/utils/G_VARIABLE'

class AdAuto extends Auth {
  constructor() {
    super()
    this.type = authTypes.ADAutoLogin
  }

  /**
  * 普通
   * @returns {Promise<boolean>}
  */
  async common(params) {
    const checkDomainUserResult = await this.getDomainUserInfo()
    console.log('checkDomainUserResult', checkDomainUserResult)
    if (!_.isObject(checkDomainUserResult) || _.isEmpty(checkDomainUserResult)) {
      return false
    }
    let domain_name = _.get(checkDomainUserResult, 'Result.Info.DomainName', '')
    if (_.isString(domain_name)) {
      domain_name = domain_name.toLowerCase()
    }
    const cur_logon_user = _.get(checkDomainUserResult, 'Result.Info.CurLogonUser', '')
    const apiParam = {
      type: this.type,
      deviceid: _.get(store.state.clientInfo, 'basic.AgentID', 0),
      domain_name: Base64Encode(domain_name),
      user_name: Base64Encode(cur_logon_user),
      hintOver: G_VARIABLE.g_hintOver
    }

    // 未登录到域中
    if (_.get(checkDomainUserResult, 'Result.CheckType.Result', 'No') !== 'Yes') {
      return false
    }

    const adDomainSvr = _.get(store.getters.computeServeEntiretyConfig, 'ADDOMAINSVR', {})
    if (parseInt(adDomainSvr[domain_name]) !== 1) {
      return false
    }

    store.commit('setServeEntiretyConfig', _.merge(
      {},
      store.state.serveEntiretyConfig,
      {
        server: {
          ADDOMAINSVR: {
            AutoLogion: 1
          }
        }
      }
    ))

    const fakeIsInDomain = await common.isDomainNoFake()
    if (fakeIsInDomain !== false) {
      apiParam.fakeIsInDomain = Base64Encode(fakeIsInDomain)
    }

    // 敲门
    const knockParam = ['ADAutoLogin', cur_logon_user, domain_name,
      fakeIsInDomain || ''] // 认证类型、authserver、用户名、密码、验证码
    const knockIsSuccess = await commonUtil.knockPort(knockParam)
    if (!knockIsSuccess) {
      return false
    }

    const res = await proxyApi.authIndex(apiParam)
    if (parseInt(_.get(res, 'errcode', -1)) !== 0) {
      return parseInt(res.errcode) === 21120030 ? res : false
    }
    store.commit('setAuthInfo', _.assign({}, store.state.authInfo, { basic: res.data }))
    await common.authEnd({
      type: this.type,
      autoAuth: '1' // ad域单点登录开启自动登录
    })
    return true
  }

  /**
   * 从小助手获取ad域单点登录账户信息
   * @returns {Promise<*>}
   */
  async getDomainUserInfo() {
    const initParam = {
      WhereIsModule: 'MsacCheckDomain.dll',
      WhatFuncToCall: 'CheckDomainUser',
      RequestParam: JSON.stringify({
        CheckType: {
          InsideName: 'CheckDomainUser',
          OutsideName: i18n.t('auth.domainUserCheck'),
          Desc: '',
          DLL: 'MsacCheckDomain.dll',
          DLLFunc: 'CheckDomainUser',
          Option: {
            DomainName: '',
            SupperUser: '',
            BlackUser: ''
          }
        }
      })
    }
    return await agentApi.callAgentOneFunc(initParam)
  }

  /**
   * 802.1x
   */
  async dot1x() {
    const checkDomainUserResult = await this.getDomainUserInfo()
    console.log('checkDomainUserResult', checkDomainUserResult)
    if (!_.isObject(checkDomainUserResult) || _.isEmpty(checkDomainUserResult)) {
      return false
    }

    // 未登录到域中
    if (_.get(checkDomainUserResult, 'Result.CheckType.Result', 'No') !== 'Yes') {
      return false
    }

    let domain_name = _.get(checkDomainUserResult, 'Result.Info.DomainName', '')
    if (_.isString(domain_name)) {
      domain_name = domain_name.toLowerCase()
    }
    const cur_logon_user = _.get(checkDomainUserResult, 'Result.Info.CurLogonUser', '')

    const adDomainSvr = _.get(store.getters.computeServeEntiretyConfig, 'ADDOMAINSVR', {})
    if (parseInt(adDomainSvr[domain_name]) !== 1) {
      return false
    }

    store.commit('setServeEntiretyConfig', _.merge(
      {},
      store.state.serveEntiretyConfig,
      {
        client: {
          server: {
            ADDOMAINSVR: {
              AutoLogion: 1
            }
          }
        }
      }
    ))

    const { AccessNetwork, IsWireLess } = accessNetwork.getLastAccessNetwork()
    if (!_.isString(AccessNetwork) || AccessNetwork === '') {
      Message.error(i18n.t('auth.dot1xAdAutoFail'))
      return false
    }
    const apiParam = {
      AuthParam: {
        AccessNetwork: AccessNetwork,
        IsWireLess: IsWireLess,
        AuthType: this.type,
        UserName: cur_logon_user + '@' + domain_name,
        CurLogonUser: cur_logon_user,
        DomainName: domain_name
      }
    }

    // const fakeIsInDomain = await common.isDomainNoFake()
    // if (fakeIsInDomain !== false) {
    //   apiParam.fakeIsInDomain = Base64Encode(fakeIsInDomain)
    // }

    const res = await dot1xCommon.auth(apiParam)
    if (parseInt(_.get(res, 'ASM.Result', -1)) !== 0) {
      Message.error(_.get(res, 'ASM.Message', '失败'))
      EventBus.$emit('client:show')
      return false
    }

    const authResponse = _.get(res, 'ASM.AuthResponse', '')
    const authResponseJson = dot1xCommon.handleAuthResponse(authResponse)
    store.commit('setAuthInfo', { ...store.state.authInfo, ...{ basic: authResponseJson }})

    await dot1xCommon.authEnd({
      type: this.type,
      authResponse: false
    })
    return true
  }
}

export default AdAuto
