<template>
  <div class="how-to-fix">
    <p class="model-title">
      {{ fixData.modelTitle }}<i
        :class="modelIsOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
        @click="modelIsOpen = !modelIsOpen"
      />
    </p>
    <el-collapse-transition>
      <div v-show="modelIsOpen" class="fix-model-content">
        <!-- 单模块 -->
        <div v-if="!isMutil">
          <el-row>
            <el-col v-for="(way, index) in fixData.fixSteps" :key="way" class="way">
              {{ index + 1 }}) <span v-html="way" />
            </el-col>
          </el-row>
        </div>

        <!-- 多模块 -->
        <div v-else>
          <el-row v-for="repairWay in fixData.fixSteps" :key="repairWay.question">
            <el-col class="question">
              {{ repairWay.question }}
            </el-col>
            <el-col
              v-for="(way, ind) in repairWay.wayList"
              :key="way"
              class="way"
            >
              {{ ind + 1 }}) <span v-html="way" />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
export default {
  props: {
    fixData: {
      type: Object,
      // 对象或数组默认值必须从一个工厂函数获取
      default: function() {
        return {
          modelTitle: this.$t('accessNetwork.securityCheck.info_19'),
          fixSteps: []
        }
      }
    }
  },
  data() {
    return {
      modelIsOpen: true
    }
  },
  computed: {
    isMutil() {
      if (this.fixData && this.fixData.fixSteps && this.fixData.fixSteps.length && this.fixData.fixSteps[0].wayList) {
        return true
      } else {
        return false
      }
    }
  },
  watch: {
    fixData: {
      handler(val) {
        console.log('newVal', val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.question .error-color{
  color: $error !important;
}
</style>
